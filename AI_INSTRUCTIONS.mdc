
## 🔒 MUST - Localization (`flutter_intl`)

- Never hardcode strings in UI.
  *(Không hardcode chuỗi trong UI.)*

- Use `S.of(context).key` or `S.current.key` for localization.
  *(<PERSON><PERSON>n sử dụng `flutter_intl` để lấy chuỗi.)*

- Add new strings in `.arb` files with meaningful snake_case keys.
  *(Thêm chuỗi mới trong file `.arb`, đặt tên key rõ ràng.)*

- **Always generate or update localization files using:**
  `fvm flutter pub global run intl_utils:generate`
  *(Luôn sử dụng lệnh này để generate/update file localization, không dùng lệnh global mặc định của Flutter hoặc intl_utils mà không qua FVM.)*

description:
globs:
alwaysApply: false
---

## 🔒 MUST - Icon Usage (`AppIcons`)

- **Luôn sử dụng `AppIcons` khi cần icon trong toàn bộ project.**
  - Không sử dụng trực tiếp `TablerIcons.xxx` hoặc `Icons.xxx` trong code UI.
  - Nếu cần icon mới mà chưa có trong `AppIcons`, **phải định nghĩa thêm vào `AppIcons`** tại `lib/core/constants/app_icons.dart`.

- **Quy trình thêm icon mới:**
  1. **Tìm icon phù hợp trên [Tabler Icons](https://tabler-icons.io/) hoặc danh sách icon của package [`flutter_tabler_icons`](https://pub.dev/packages/flutter_tabler_icons).**
  2. **Nếu icon đã có trong package:**  
     - Thêm vào class `AppIcons` với tên rõ ràng, theo nhóm chức năng.
     - Ví dụ:  
       ```dart
       /// Icon cho Like/Yêu thích
       static const like = TablerIcons.heart;
       ```
  3. **Nếu icon chưa có trong package:**  
     - Kiểm tra lại tên icon trên pub.dev, đảm bảo đúng tên và có trong version hiện tại.
     - Nếu vẫn chưa có, chọn icon gần nghĩa nhất hoặc đề xuất cập nhật package.

- **Không được sử dụng trực tiếp các icon ngoài AppIcons** (ví dụ: không dùng `Icon(TablerIcons.camera)` mà phải là `Icon(AppIcons.camera)`).

- **Lý do:**  
  - Đảm bảo tính nhất quán, dễ bảo trì, dễ đổi icon toàn app chỉ cần sửa 1 chỗ.
  - Giúp code UI sạch, dễ đọc, dễ review.

### Ví dụ đúng/sai

```dart
// ✅ Đúng
Icon(AppIcons.camera)
CommonIconButton(icon: AppIcons.add, ...)

// ❌ Sai
Icon(TablerIcons.camera)
Icon(Icons.add)
IconButton(icon: Icon(TablerIcons.trash), ...)
```

---

(Chèn rule này vào phần MUST, ngay sau Localization hoặc trước Barrel Export để team dễ thấy)
