# GitLab CI/CD Configuration for Sales App
# Self-hosted Runner (Mac) for iOS & Android Build

# Remove Docker image - using self-hosted runner
# image: cirrusci/flutter:3.24.5

variables:
  ANDROID_COMPILE_SDK: "34"
  ANDROID_BUILD_TOOLS: "34.0.0"
  ANDROID_SDK_TOOLS: "9477386"
  APP_VERSION: "1.0.0"  # Update this when releasing new version
  FLUTTER_VERSION: "3.27.4"  # Centralized Flutter version
  # Add LC_ALL to avoid locale issues
  LC_ALL: "en_US.UTF-8"
  LANG: "en_US.UTF-8"

# Cache configuration for self-hosted runner
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - android/.gradle/wrapper
    - android/.gradle/caches
    - $HOME/.pub-cache
    - ios/Pods
    - ~/.cocoapods

# Stages definition
stages:
  - prepare
  - test
  - build
  - deploy

# Before script - setup environment for self-hosted runner
before_script:
  # Error handling - stop on any error
  - set -e
  
  # Environment setup
  - export SHELL=/bin/zsh
  - export PRJ_DIR=${PWD}
  - export PATH="$PATH:$HOME/.pub-cache/bin"
  
  # Debug information
  - echo "Project Directory:"
  - echo "🏠 Project Directory:$PRJ_DIR"
  - echo "🔧 Runner OS:$(uname -a)"
  - echo "☕ Java Home:$JAVA_HOME"
  
  # Show runner configuration
  - echo "📋 GitLab Runner Config:"
  - cat ~/Library/LaunchAgents/com.gitlab.runner.plist
  
  # Git setup and cleanup
  - echo "📂 Git status before cleanup:"
  - git status --porcelain || true
  - git submodule foreach 'git stash' || true
  - git stash || true
  - git submodule update --init --recursive
  - echo "📂 Git status after cleanup:"
  - git status --porcelain
  
  # Tool versions check
  - echo "🔍 Checking tool versions..."
  - gem --version
  - pod --version || echo "⚠️ CocoaPods not found"
  
  # SDKMAN setup (conditional) - restored
  - if [[ -s "$HOME/.sdkman/bin/sdkman-init.sh" ]]; then source "$HOME/.sdkman/bin/sdkman-init.sh"; fi
  - if [[ -s "$HOME/.sdkman/bin/sdkman-init.sh" ]]; then sdk install gradle 8.0 || echo "⚠️ Gradle 8.0 already installed"; fi
  - if [[ -s "$HOME/.sdkman/bin/sdkman-init.sh" ]]; then sdk use gradle 8.0 || true; fi
  - if [[ -s "$HOME/.sdkman/bin/sdkman-init.sh" ]]; then sdk install java 17.0.12-oracle || echo "⚠️ Java 17.0.12 already installed"; fi
  - if [[ -s "$HOME/.sdkman/bin/sdkman-init.sh" ]]; then sdk use java 17.0.12-oracle || true; fi
  - if [[ -s "$HOME/.sdkman/bin/sdkman-init.sh" ]]; then gradle --version; else echo "⚠️ SDKMAN not found"; fi
  - gradle --version || echo "⚠️ Gradle not found"
  - java -version || echo "⚠️ Java not found"
  
  # FVM and Flutter setup
  - echo "🎯 Setting up Flutter via FVM..."
  - dart pub global activate fvm
  
  # Check if Flutter version already installed
  - if ! fvm list | grep -q "${FLUTTER_VERSION}"; then echo "📥 Installing Flutter ${FLUTTER_VERSION}..."; fi
  - if ! fvm list | grep -q "${FLUTTER_VERSION}"; then fvm install ${FLUTTER_VERSION}; fi
  - if fvm list | grep -q "${FLUTTER_VERSION}"; then echo "✅ Flutter ${FLUTTER_VERSION} already installed"; fi
  
  - fvm use ${FLUTTER_VERSION} --force
  - fvm flutter --version
  
  # Flutter setup and dependencies
  - echo "🧹 Cleaning Flutter project..."
  - fvm flutter clean
  - echo "📦 Getting Flutter dependencies..."
  - fvm flutter pub get
  
  # iOS specific setup (only on macOS)  
  - if [[ "$OSTYPE" == "darwin"* ]]; then echo "🍎 iOS setup..."; fi
  - if [[ "$OSTYPE" == "darwin"* ]]; then fvm flutter precache --ios; fi
  - if [[ "$OSTYPE" == "darwin"* ]]; then xcodebuild -version; fi
  - if [[ "$OSTYPE" != "darwin"* ]]; then echo "ℹ️ Skipping iOS setup (not on macOS)"; fi
  - if [[ "$OSTYPE" == "darwin"* ]] && [[ -f "ios/Podfile" ]]; then echo "🥥 Setting up CocoaPods..."; fi
  - if [[ "$OSTYPE" == "darwin"* ]] && [[ -f "ios/Podfile" ]]; then cd ios && pod install --repo-update && cd ..; fi
  
  # Final status
  - echo "✅ Environment setup completed!"
  - echo "📊 Final status:"
  - fvm flutter doctor --android-licenses > /dev/null 2>&1 || true
  - fvm flutter doctor -v

# ===================
# PREPARE STAGE
# ===================

flutter_analyze:
  stage: prepare
  tags:
    - banking-mobile  # Tag for self-hosted runner
  script:
    - fvm flutter analyze
  allow_failure: false

# flutter_test:
#   stage: prepare
#   tags:
#     - banking-mobile
#   script:
#     - fvm flutter test
#   coverage: '/lines......: \d+\.\d+\%/'
#   artifacts:
#     reports:
#       coverage_report:
#         coverage_format: cobertura
#         path: coverage/cobertura.xml
#   only:
#     - merge_requests
#     - main
#     - develop
#     - prod
#     - uat

# ===================
# BUILD STAGE - ANDROID
# ===================

build_android_development:
  stage: build
  tags:
    - banking-mobile
  script:
    - cd android
    - fastlane gradle_dev_release
  artifacts:
    paths:
      - build/app/outputs/apk/development/release/app-development-release.apk
    expire_in: 1 month
  only:
    - merge_requests
    - develop
    # - /^feature\/.*$/

build_android_staging:
  stage: build
  tags:
    - banking-mobile
  script:
    - cd android
    - fastlane gradle_staging_release
    - fastlane gradle_staging_aab
  artifacts:
    paths:
      - build/app/outputs/bundle/stagingRelease/app-staging-release.aab
      - build/app/outputs/apk/staging/release/app-staging-release.apk
    expire_in: 1 month
  only:
    - /^release\/.*$/
    - uat

build_android_production:
  stage: build
  tags:
    - banking-mobile
  script:
    - cd android
    - fastlane gradle_prod_release
    - fastlane gradle_prod_aab
  artifacts:
    paths:
      - build/app/outputs/bundle/productionRelease/app-production-release.aab
      - build/app/outputs/apk/production/release/app-production-release.apk
    expire_in: 3 months
  only:
    - main
    - prod
    - tags

# ===================
# BUILD STAGE - iOS (Only on macOS runner)
# ===================

build_ios_development:
  stage: build
  tags:
    - banking-mobile
    # - macos  # Commented out temporarily for testing
  script:
    - cd ios
    - fvm flutter build ios --release --no-codesign --flavor development
  only:
    - merge_requests
    - develop
    # - /^feature\/.*$/

build_ios_staging:
  stage: build
  tags:
    - banking-mobile
    # - macos
  script:
    - fvm flutter build ipa --release --flavor staging --target lib/main_staging.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Staging.plist
  artifacts:
    paths:
      - build/ios/ipa/
    expire_in: 1 month
  only:
    - /^release\/.*$/
    - uat

build_ios_production:
  stage: build
  tags:
    - banking-mobile
    # - macos
  script:
    - fvm flutter build ipa --release --flavor production --target lib/main_production.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Production.plist
  artifacts:
    paths:
      - build/ios/ipa/
    expire_in: 3 months
  only:
    - main
    - prod
    - tags

# ===================
# BUILD ALL FLAVORS (Manual)
# ===================

build_all_android_flavors:
  stage: build
  tags:
    - banking-mobile
  script:
    - cd android
    - fastlane gradle_all_release
  artifacts:
    paths:
      - build/app/outputs/apk/
    expire_in: 1 week
  only:
    - main
    - develop
    - staging

build_all_ios_flavors:
  stage: build
  tags:
    - banking-mobile
    - macos
  script:
    - fvm flutter build ipa --debug --flavor development --target lib/main_development.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Development.plist
    - fvm flutter build ipa --release --flavor staging --target lib/main_staging.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Staging.plist
    - fvm flutter build ipa --release --flavor production --target lib/main_production.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Production.plist
  artifacts:
    paths:
      - build/ios/ipa/*.ipa
    expire_in: 1 week
  only:
    - main
    - develop
    - staging

# ===================
# DEPLOY STAGE - ANDROID
# ===================

# deploy_android_staging:
#   stage: deploy
#   tags:
#     - banking-mobile
#   dependencies:
#     - build_android_staging
#   script:
#     - cd android
#     - fastlane deploy_staging_playstore
#   environment:
#     name: staging-playstore
#     url: https://play.google.com/console/
#   only:
#     - staging
#   when: manual

# deploy_android_production:
#   stage: deploy
#   tags:
#     - banking-mobile
#   dependencies:
#     - build_android_production
#   script:
#     - cd android
#     - fastlane deploy_production_playstore
#   environment:
#     name: production-playstore
#     url: https://play.google.com/console/
#   only:
#     - main
#     - tags
#   when: manual

# ===================
# DEPLOY STAGE - iOS
# ===================

deploy_ios_staging:
  stage: deploy
  tags:
    - banking-mobile
    - macos
  dependencies:
    - build_ios_staging
  script:
    - cd ios
    - bundle install
    - bundle exec fastlane deploy_staging
  environment:
    name: staging-testflight
    url: https://appstoreconnect.apple.com/
  only:
    - uat

deploy_ios_production:
  stage: deploy
  tags:
    - banking-mobile
    - macos
  dependencies:
    - build_ios_production
  script:
    - cd ios
    - bundle install
    - bundle exec fastlane deploy_production
  environment:
    name: production-appstore
    url: https://appstoreconnect.apple.com/
  only:
    - main
    - prod
    - tags


# ===================
# PROMOTE JOBS
# ===================

# promote_android_staging_to_external:
#   stage: deploy
#   tags:
#     - banking-mobile
#   script:
#     - cd android
#     - fastlane promote_staging_to_external
#   environment:
#     name: staging-external-test
#     url: https://play.google.com/console/
#   only:
#     - staging
#   when: manual

# promote_android_production_to_live:
#   stage: deploy
#   tags:
#     - banking-mobile
#   script:
#     - cd android
#     - fastlane promote_production_to_production
#   environment:
#     name: production-live
#     url: https://play.google.com/console/
#   only:
#     - main
#     - tags
#   when: manual

# promote_ios_staging_to_external:
#   stage: deploy
#   tags:
#     - banking-mobile
#     - macos
#   script:
#     - cd ios
#     - fastlane ios promote_staging_to_external
#   environment:
#     name: staging-external-testflight
#     url: https://appstoreconnect.apple.com/
#   only:
#     - staging

# promote_ios_production_to_live:
#   stage: deploy
#   tags:
#     - banking-mobile
#     - macos
#   script:
#     - cd ios
#     - fastlane ios promote_production_to_appstore
#   environment:
#     name: production-live-appstore
#     url: https://appstoreconnect.apple.com/
#   only:
#     - main
#     - tags

# ===================
# UTILITY JOBS
# ===================

version_info:
  stage: prepare
  tags:
    - banking-mobile
  script:
    - cd android
    - fastlane version_info
  only:
    - main
  when: manual

system_info:
  stage: prepare
  tags:
    - banking-mobile
  script:
    - echo "=== System Information ==="
    - uname -a
    - if [[ "$OSTYPE" == "darwin"* ]]; then sw_vers; fi
    - echo "=== Flutter Information ==="
    - fvm flutter doctor -v
    - echo "=== Xcode Information (macOS only) ==="
    - if [[ "$OSTYPE" == "darwin"* ]]; then xcodebuild -version; fi
    - echo "=== Java Information ==="
    - java -version
  when: manual

# ===================
# CLEANUP JOBS
# ===================

cleanup_artifacts:
  stage: deploy
  tags:
    - banking-mobile
  script:
    - echo "Cleaning up old artifacts..."
    - rm -rf build/
    - if [[ "$OSTYPE" == "darwin"* ]]; then rm -rf ios/build/; fi
  only:
    - main
    - develop
    - staging
    - /^feature\/.*$/
  when: always
  dependencies: [] 