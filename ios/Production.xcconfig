#include? "Pods/Target Support Files/Pods-Runner/Pods-Runner.release-production.xcconfig"
#include "Flutter/Release.xcconfig"
#include "Flutter/Generated.xcconfig"

// Production flavor configuration
PRODUCT_BUNDLE_IDENTIFIER = com.kienlongbank.sales_app
PRODUCT_NAME = Sales App
APP_DISPLAY_NAME = Sales App

// App configuration values
APP_FLAVOR = production
API_BASE_URL = https://api.kienlongbank.com

// Build configuration
FLUTTER_BUILD_MODE = release
DEBUG_MODE = 0
LOGGING_ENABLED = 0

// Code signing - Managed by Fastlane Match
// CODE_SIGN_IDENTITY will be set automatically by Match
// PROVISIONING_PROFILE_SPECIFIER will be set automatically by Match

// App icon for production
ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon 