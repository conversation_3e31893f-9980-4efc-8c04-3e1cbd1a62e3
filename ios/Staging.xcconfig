#include? "Pods/Target Support Files/Pods-Runner/Pods-Runner.release-staging.xcconfig"
#include "Flutter/Release.xcconfig"
#include "Flutter/Generated.xcconfig"

// Staging flavor configuration
PRODUCT_BUNDLE_IDENTIFIER = com.kienlongbank.sales_app.staging
PRODUCT_NAME = Sales App Staging
APP_DISPLAY_NAME = Sales App Staging

// App configuration values
APP_FLAVOR = staging
API_BASE_URL = https://staging-api.kienlongbank.com

// Build configuration
FLUTTER_BUILD_MODE = release
DEBUG_MODE = 0
LOGGING_ENABLED = 1

// Code signing - Managed by Fastlane Match
// CODE_SIGN_IDENTITY will be set automatically by Match
// PROVISIONING_PROFILE_SPECIFIER will be set automatically by Match

// App icon suffix for staging
ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon-Staging 