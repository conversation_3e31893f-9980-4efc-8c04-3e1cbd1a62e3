fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios development

```sh
[bundle exec] fastlane ios development
```

Build Development

### ios staging

```sh
[bundle exec] fastlane ios staging
```

Build Staging

### ios production

```sh
[bundle exec] fastlane ios production
```

Build Production

### ios all

```sh
[bundle exec] fastlane ios all
```

Build All Flavors

### ios deploy_staging

```sh
[bundle exec] fastlane ios deploy_staging
```

Deploy Staging to TestFlight

### ios deploy_production

```sh
[bundle exec] fastlane ios deploy_production
```

Deploy Production to App Store Connect

### ios promote_staging_to_external

```sh
[bundle exec] fastlane ios promote_staging_to_external
```

Promote Staging to External TestFlight

### ios promote_production_to_appstore

```sh
[bundle exec] fastlane ios promote_production_to_appstore
```

Promote Production to App Store

### ios setup_codesigning

```sh
[bundle exec] fastlane ios setup_codesigning
```

Code Signing Setup

### ios clean

```sh
[bundle exec] fastlane ios clean
```

Clean Build Artifacts

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
