# Fastlane Match Configuration for Sales App
# More information: https://docs.fastlane.tools/actions/match/

# Git repository URL for storing certificates and profiles
# You need to create a private repository for this
git_url(ENV["GIT_CERT_URL"])


# Storage mode (git is default and recommended)
storage_mode("git")

# App bundle identifiers that we want to manage
app_identifier([
  "com.kienlongbank.sales_app.dev",
  "com.kienlongbank.sales_app.staging", 
  "com.kienlongbank.sales_app"
])

# Apple Developer Team ID and Apple ID are configured in Appfile
# team_id and username will be inherited from Appfile

# Platform (iOS is default)
platform("ios")

# Certificate types to manage (can be overridden in lanes)
# type("development") # Also supports: "adhoc", "appstore", "enterprise"

# Clone the git repository to this path
# git_basic_authorization(ENV['MATCH_GIT_BASIC_AUTHORIZATION'])

# Use a shallow clone to speed up download
shallow_clone(true)

# Clone branch (default is master/main)
git_branch("live")

# Skip generating a certificate if one already exists
skip_certificate_verification(false)

# Skip the verification of the certificates and profiles against Apple
skip_provisioning_profiles(false)

# Renew expired certificates
force_for_new_devices(false)

# Additional customizations

# Use system keychain instead of creating temporary one
# keychain_name and keychain_password not needed when using system keychain

# Skip confirmation dialogs
readonly(false)

# Generate certificates for all specified bundle identifiers
generate_apple_certs(true)

# Skip package name check
skip_google_cloud_account_confirmation(true)

# Verbose output for debugging
verbose(true)

# Additional configuration
# Force refresh when new devices are added
force_for_new_devices(true)

# Skip docs check (faster)
skip_docs(true)

# Template directory for custom templates
# template_name("custom")

# Clone timeout
clone_branch_directly(false) 