# iOS Fastfile for Sales App Flavors

default_platform(:ios)

platform :ios do
  # ==========================================
  # FASTLANE MATCH SETUP
  # ==========================================
  
  desc "Setup Code Signing with Match"
  lane :setup_codesigning do
    UI.header("Setting up Code Signing with Match")
    
    setup_ci if ENV['CI']
    
    # App Store certificates for all environments (dev, staging, production)
    match(
      type: "appstore", 
      app_identifier: [
        "com.kienlongbank.sales_app.dev",
        "com.kienlongbank.sales_app.staging",
        "com.kienlongbank.sales_app"
      ],
      readonly: is_ci
    )
    
    UI.success("✅ Code signing setup complete!")
  end

  desc "Initialize Match (First time setup)"
  lane :match_init do
    UI.header("Initializing Fastlane Match")
    
    match_init(
      git_url: ENV["GIT_CERT_URL"],
      app_identifier: [
        "com.kienlongbank.sales_app.dev",
        "com.kienlongbank.sales_app.staging",
        "com.kienlongbank.sales_app"
      ],
      storage_mode: "git",
      force_for_new_devices: true,
      skip_docs: true,
      verbose: true,
      clone_branch_directly: false
    )
    
    UI.success("✅ Match initialized! Don't forget to commit the Matchfile")
  end



  # ==========================================
  # DEPLOY LANES (Upload IPA to stores)
  # ==========================================

  desc "Deploy Staging IPA to TestFlight"
  lane :deploy_staging do
    UI.header("Deploying Staging IPA to TestFlight")
    
    # Find the IPA file (flutter build ipa creates it with different naming)
    ipa_path = Dir.glob("../build/ios/ipa/*.ipa").first
    UI.user_error!("No IPA file found in build/ios/ipa/") if ipa_path.nil?
    
    UI.message("Found IPA: #{ipa_path}")
    
    # Read changelog from CHANGELOG.md or generate build info
    changelog = ""
    begin
      if File.exist?("../CHANGELOG.md")
        changelog_content = File.read("../CHANGELOG.md")
        
        # Extract staging-specific changelog
        lines = changelog_content.split("\n")
        staging_section = []
        in_staging_section = false
        
        lines.each do |line|
          if line.include?("### Staging Only") || line.include?("### All Flavors")
            in_staging_section = true
            staging_section << line
          elsif line.start_with?("### ") && !line.include?("Staging") && !line.include?("All Flavors")
            in_staging_section = false
          elsif in_staging_section
            staging_section << line
            break if staging_section.length > 15 # Limit to avoid too long changelog
          end
        end
        
        # Fallback to first 20 lines if no staging section found
        if staging_section.empty?
          changelog = lines.first(20).join("\n")
        else
          changelog = staging_section.join("\n")
        end
      else
        changelog = "Staging build ##{ENV['CI_JOB_ID'] || Time.now.strftime('%Y%m%d%H%M')}"
      end
    rescue => e
      UI.message("⚠️ Could not read changelog: #{e.message}")
      changelog = "Staging build for testing"
    end
    
    # Upload to TestFlight
    upload_to_testflight(
      ipa: ipa_path,
      skip_submission: true,
      distribute_external: true,
      groups: ["Internal Testing", "Staging Beta"],
      changelog: changelog,
      beta_app_description: "Sales App Staging - Internal testing build",
      localized_build_info: {
        "default" => {
          whats_new: changelog
        }
      }
    )
    
    UI.success("✅ Staging IPA deployed to TestFlight!")
  end

  desc "Deploy Production IPA to TestFlight"  
  lane :deploy_production do
    UI.header("Deploying Production IPA to TestFlight")
    
    # Find the IPA file (flutter build ipa creates it with different naming)
    ipa_path = Dir.glob("../build/ios/ipa/*.ipa").first
    UI.user_error!("No IPA file found in build/ios/ipa/") if ipa_path.nil?
    
    UI.message("Found IPA: #{ipa_path}")
    
    # Read changelog for production release
    changelog = ""
    release_notes = ""
    begin
      if File.exist?("../CHANGELOG.md")
        changelog_content = File.read("../CHANGELOG.md")
        lines = changelog_content.split("\n")
        
        # Extract production-specific changelog
        production_section = []
        in_production_section = false
        
        lines.each do |line|
          if line.include?("### Production Only") || line.include?("### All Flavors")
            in_production_section = true
            production_section << line
          elsif line.start_with?("### ") && !line.include?("Production") && !line.include?("All Flavors")
            in_production_section = false
          elsif in_production_section
            production_section << line
            break if production_section.length > 20 # Limit for production notes
          end
        end
        
        # Get latest release notes (between first two ## headers)
        release_start = lines.find_index { |line| line.start_with?("##") && !line.include?("Unreleased") }
        release_end = lines.find_index(release_start + 1) { |line| line.start_with?("##") } if release_start
        
        if release_start && release_end
          release_notes = lines[release_start...release_end].join("\n")
        elsif release_start
          release_notes = lines[release_start..-1].join("\n")
        end
        
        # Use production-specific section or fallback to release notes
        if production_section.any?
          changelog = production_section.join("\n")
        elsif release_notes && !release_notes.empty?
          changelog = release_notes
        else
          changelog = lines.first(15).join("\n")
        end
        
        # Fallback for release_notes if empty
        release_notes = changelog if release_notes.nil? || release_notes.empty?
      else
        version = ENV['CI_JOB_ID'] || Time.now.strftime('%Y%m%d%H%M')
        changelog = "Production release build ##{version}"
        release_notes = changelog
      end
    rescue => e
      UI.message("⚠️ Could not read changelog: #{e.message}")
      changelog = "Production build ready for App Store release"
      release_notes = changelog
    end
    
    # Upload to TestFlight for production testing
    upload_to_testflight(
      ipa: ipa_path,
      skip_submission: true,
      distribute_external: false,
      groups: ["Production Testing", "Release Candidates"],
      changelog: release_notes,
      beta_app_description: "Sales App Production - Final testing before App Store release",
      localized_build_info: {
        "default" => {
          whats_new: release_notes
        }
      }
    )
    
    UI.success("✅ Production IPA deployed to TestFlight for final testing!")
  end

  desc "Deploy Production from TestFlight to App Store"
  lane :deploy_to_app_store do
    UI.header("Deploying Production from TestFlight to App Store")
    
    # Upload existing TestFlight build to App Store
    upload_to_app_store(
      submit_for_review: false,
      automatic_release: false,
      force: true,
      skip_binary_upload: true,  # Use existing TestFlight build
      skip_screenshots: true,
      skip_metadata: false
    )
    
    UI.success("✅ Production deployed to App Store Connect (ready for review)!")
  end

  # ==========================================
  # PROMOTION LANES (TestFlight/App Store Management)
  # ==========================================

  desc "Promote Staging to External TestFlight"
  lane :promote_staging_to_external do
    UI.header("Promoting Staging to External TestFlight")
    
    upload_to_testflight(
      skip_submission: true,
      distribute_external: true,
      groups: ["External Beta"],
      changelog: "Beta release for external testing"
    )
    
    UI.success("✅ Staging promoted to external TestFlight!")
  end

  desc "Submit Production for App Store Review"
  lane :submit_for_review do
    UI.header("Submitting Production for App Store Review")
    
    upload_to_app_store(
      submit_for_review: true,
      automatic_release: false,
      force: true,
      submission_information: {
        add_id_info_uses_idfa: false,
        add_id_info_serves_ads: false,
        add_id_info_tracks_install: false,
        add_id_info_tracks_action: false,
        add_id_info_limits_tracking: false
      }
    )
    
    UI.success("✅ Production submitted for App Store review!")
  end

  # ==========================================
  # TESTFLIGHT UTILITY LANES
  # ==========================================
  
  desc "List all TestFlight groups"
  lane :list_testflight_groups do
    UI.header("Listing TestFlight Groups")
    
    # This will list all available TestFlight groups
    pilot(
      skip_submission: true,
      list_groups: true
    )
  end
  
  desc "Create or update TestFlight groups"
  lane :setup_testflight_groups do
    UI.header("Setting up TestFlight Groups")
    
    required_groups = [
      "Internal Testing",
      "Staging Beta", 
      "Production Testing",
      "Release Candidates"
    ]
    
    required_groups.each do |group_name|
      UI.message("📝 Ensuring group exists: #{group_name}")
      # Note: You might need to create these groups manually in App Store Connect
      # This is just for documentation purposes
    end
    
    UI.success("✅ TestFlight groups setup complete!")
    UI.message("📋 Required groups: #{required_groups.join(', ')}")
    UI.message("🔗 Create groups manually at: https://appstoreconnect.apple.com/")
  end
  
  desc "Generate build notes from Git commits"
  lane :generate_build_notes do |options|
    UI.header("Generating Build Notes")
    
    # Get recent commits for build notes
    commit_count = options[:commit_count] || 10
    commits = sh("git log --oneline -#{commit_count}", log: false).strip
    
    # Get current branch and build info
    branch = sh("git rev-parse --abbrev-ref HEAD", log: false).strip
    commit_hash = sh("git rev-parse --short HEAD", log: false).strip
    build_time = Time.now.strftime("%Y-%m-%d %H:%M:%S")
    
    build_notes = <<~NOTES
      🚀 Sales App Build
      
      📅 Build Time: #{build_time}
      🌿 Branch: #{branch}
      🔗 Commit: #{commit_hash}
      🏷️ Pipeline: #{ENV['CI_PIPELINE_ID'] || 'Local'}
      
      📝 Recent Changes:
      #{commits.split("\n").map { |line| "• #{line}" }.join("\n")}
    NOTES
    
    UI.message("Generated build notes:")
    UI.message(build_notes)
    
    return build_notes
  end

  # ==========================================
  # MATCH UTILITY LANES
  # ==========================================

  desc "Update App Store certificates and profiles"
  lane :match_update do
    UI.header("Updating App Store certificates and profiles")
    
    match(type: "appstore")
    
    UI.success("✅ App Store certificates updated!")
  end



  # ==========================================
  # ERROR HANDLING
  # ==========================================

  error do |lane, exception|
    UI.error("❌ Lane #{lane} failed with exception: #{exception}")
    
    # You can add notification logic here (Slack, email, etc.)
    # slack(
    #   message: "iOS build failed for lane: #{lane}",
    #   success: false
    # )
  end
end
