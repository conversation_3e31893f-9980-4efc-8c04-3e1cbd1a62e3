# Fastlane Appfile for iOS - Sales App
# More information about the Appfile: https://docs.fastlane.tools/advanced/#appfile

# Default iOS configuration
app_identifier("com.kienlongbank.sales_app") # Base bundle identifier

# Apple ID for App Store Connect
apple_id ENV["APPLE_ID"] || "<EMAIL>"

# Apple Developer Team ID
team_id("8AWMKJBR89")

# iTunes Connect Team ID (if different from Developer Team)
itc_team_id("*********")

# Development flavor configuration
for_lane :flutter_dev do
  app_identifier("com.kienlongbank.sales_app.dev")
end

for_lane :build_dev_debug do
  app_identifier("com.kienlongbank.sales_app.dev")
end

for_lane :build_dev_release do
  app_identifier("com.kienlongbank.sales_app.dev")
end

for_lane :deploy_dev_firebase do
  app_identifier("com.kienlongbank.sales_app.dev")
end

for_lane :certificates_dev do
  app_identifier("com.kienlongbank.sales_app.dev")
end

# Staging flavor configuration
for_lane :flutter_staging do
  app_identifier("com.kienlongbank.sales_app.staging")
end

for_lane :build_staging_debug do
  app_identifier("com.kienlongbank.sales_app.staging")
end

for_lane :build_staging_release do
  app_identifier("com.kienlongbank.sales_app.staging")
end

for_lane :deploy_staging_firebase do
  app_identifier("com.kienlongbank.sales_app.staging")
end

for_lane :certificates_staging do
  app_identifier("com.kienlongbank.sales_app.staging")
end

# Production flavor configuration (default)
for_lane :flutter_production do
  app_identifier("com.kienlongbank.sales_app")
end

for_lane :build_prod_debug do
  app_identifier("com.kienlongbank.sales_app")
end

for_lane :build_prod_release do
  app_identifier("com.kienlongbank.sales_app")
end

for_lane :deploy_production_appstore do
  app_identifier("com.kienlongbank.sales_app")
end

for_lane :certificates_production do
  app_identifier("com.kienlongbank.sales_app")
end

# Match configuration for certificates
for_lane :certificates_all do
  app_identifier([
    "com.kienlongbank.sales_app.dev",
    "com.kienlongbank.sales_app.staging", 
    "com.kienlongbank.sales_app"
  ])
end 