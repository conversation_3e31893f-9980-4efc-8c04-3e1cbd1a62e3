# ExportOptions Configuration Guide

## Mục đích
<PERSON> file `ExportOptions_*.plist` định nghĩa cách Flutter export IPA cho từng environment với proper code signing và distribution settings.

## Files Structure
- `ExportOptions_Development.plist` - Development builds (TestFlight internal)
- `ExportOptions_Staging.plist` - Staging builds (TestFlight beta)  
- `ExportOptions_Production.plist` - Production builds (TestFlight → App Store)

## Setup Instructions

### 1. Thay thế Team ID
Trong tất cả file ExportOptions_*.plist, thay `YOUR_TEAM_ID` bằng Apple Developer Team ID thực tế:

```xml
<key>teamID</key>
<string>YOUR_ACTUAL_TEAM_ID</string>
```

**Cách lấy Team ID:**
- Apple Developer Portal → Membership tab
- Xcode → Preferences → Accounts → Team ID
- Hoặc chạy: `fastlane match` để xem Team ID

### 2. Verify Provisioning Profiles
Đảm bảo provisioning profile names khớp với Match setup:

```xml
<key>provisioningProfiles</key>
<dict>
    <key>com.kienlongbank.sales_app.dev</key>
    <string>match AppStore com.kienlongbank.sales_app.dev</string>
</dict>
```

### 3. Check Bundle IDs
- Development: `com.kienlongbank.sales_app.dev`
- Staging: `com.kienlongbank.sales_app.staging`
- Production: `com.kienlongbank.sales_app`

## Security Considerations

### 🔍 Current Approach (Safe)
- ✅ Files contain placeholder `YOUR_TEAM_ID` 
- ✅ Safe to commit to Git (no real sensitive data)
- ✅ Each developer replaces with their Team ID locally
- ✅ Team collaboration friendly

### 🔒 Enhanced Security (Optional)
Nếu team muốn security cao hơn:

#### Option 1: Template + Environment Variables
```bash
# Rename to templates
mv ios/ExportOptions_*.plist ios/ExportOptions_*.template.plist

# Add to .gitignore
echo "ios/ExportOptions_*.plist" >> ios/.gitignore

# Generate script
cat > scripts/generate_export_options.sh << 'EOF'
#!/bin/bash
TEAM_ID=${APPLE_TEAM_ID:-"YOUR_TEAM_ID"}
for flavor in Development Staging Production; do
  sed "s/YOUR_TEAM_ID/${TEAM_ID}/g" \
    ios/ExportOptions_${flavor}.template.plist > \
    ios/ExportOptions_${flavor}.plist
done
EOF

chmod +x scripts/generate_export_options.sh
```

#### Option 2: GitLab CI Variables
Set trong GitLab CI/CD → Variables:
- `APPLE_TEAM_ID`: Team ID thực tế
- `APPLE_DEV_PROFILE`: Development provisioning profile
- `APPLE_STAGING_PROFILE`: Staging provisioning profile
- `APPLE_PROD_PROFILE`: Production provisioning profile

## Usage

### Build Commands
```bash
# Development
fvm flutter build ipa --debug --flavor development --target lib/main_development.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Development.plist

# Staging
fvm flutter build ipa --release --flavor staging --target lib/main_staging.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Staging.plist

# Production
fvm flutter build ipa --release --flavor production --target lib/main_production.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Production.plist
```

### Deploy Commands  
```bash
# Deploy staging to TestFlight
cd ios && bundle exec fastlane deploy_staging

# Deploy production to TestFlight (for testing)
cd ios && bundle exec fastlane deploy_production

# Promote production to App Store (manual)
cd ios && bundle exec fastlane deploy_to_app_store
```

## Troubleshooting

### Code Signing Errors
1. Verify Team ID trong ExportOptions
2. Run `fastlane match appstore` để update certificates
3. Check bundle IDs đã register trong Apple Developer Portal

### Build Failures
1. Ensure Flutter dependencies: `fvm flutter pub get`
2. Update CocoaPods: `cd ios && pod install --repo-update`
3. Check ExportOptions syntax (valid XML)
4. Verify provisioning profiles match Match setup

### Common Issues
```bash
# Error: No matching provisioning profiles found
→ Run: fastlane match appstore

# Error: Invalid Team ID
→ Check Team ID trong Apple Developer Portal

# Error: Bundle ID not found
→ Register bundle ID trong Apple Developer Portal
```

## Workflow Summary

```mermaid
graph LR
    A[Flutter Build] --> B[ExportOptions.plist]
    B --> C[Code Signing]
    C --> D[IPA Export]
    D --> E[Fastlane Deploy]
    E --> F[TestFlight/App Store]
```

## Recommendation

**Hiện tại: KHÔNG cần thêm vào .gitignore**

**Lý do:**
- ✅ Template values an toàn
- ✅ Team collaboration dễ dàng  
- ✅ CI/CD setup đơn giản
- ✅ New members onboarding nhanh

**Nếu muốn enhanced security sau này:**
- Implement template approach
- Use environment variables
- Add generated files to .gitignore