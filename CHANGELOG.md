# Changelog

All notable changes to Sales App will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- New loan application flow
- Enhanced KYC document capture

### Changed
- Updated home screen UI design
- Improved performance for loan list

### Fixed
- Fixed crash when uploading documents
- Resolved network timeout issues

## [1.2.0] - 2024-01-15

### All Flavors
#### Added
- Support for multiple loan products
- Enhanced user authentication flow
- New document scanner with AI validation

#### Changed
- Updated Material Design 3 components
- Improved app startup time by 30%

#### Fixed
- Fixed memory leaks in image processing
- Resolved issues with offline mode

### Production Only
#### Added
- Integration with production banking APIs
- Real-time loan status updates

### Staging Only  
#### Added
- Mock data for testing loan scenarios
- Debug panel for QA testing
- Experimental features preview

### Development Only
#### Added
- Developer tools and debugging utilities
- Test data generators
- Performance monitoring tools

## [1.1.0] - 2023-12-20

### All Flavors
#### Added
- Biometric authentication support
- Dark mode theme
- Multi-language support (VI/EN)

#### Changed
- Redesigned login flow
- Updated app icons and branding

#### Fixed
- Fixed crashes on iOS 17
- Resolved Android notification issues

### Production Only
#### Added
- Production security enhancements
- Compliance with banking regulations

### Staging Only
#### Added
- A/B testing framework
- Beta features for user feedback

---

## Release Notes Templates

### For Staging TestFlight
```
🚀 Sales App Staging v1.2.0

This build includes new features and improvements for internal testing.

✨ What's New:
- Enhanced loan application flow
- Improved document capture with AI validation
- Updated UI components

🔧 Staging Features:
- Mock data for testing scenarios
- Debug panel for QA validation
- Performance monitoring tools

📝 Recent Changes:
[Auto-generated from recent commits]
```

### For Production TestFlight/App Store
```
🚀 Sales App v1.2.0

📱 New Features:
- Faster loan application process
- Smart document scanner
- Enhanced security features

🔧 Improvements:
- 30% faster app startup
- Better offline support
- Updated modern design

🐛 Bug Fixes:
- Resolved login issues
- Fixed document upload problems
- Improved app stability
```
