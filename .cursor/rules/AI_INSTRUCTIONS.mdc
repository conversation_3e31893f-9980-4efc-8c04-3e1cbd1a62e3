---
description: 
globs: 
alwaysApply: true
---
# Flutter Development Rules for This Project

---

## 🔒 MUST - Architecture & Folder Structure Compliance (<PERSON>ân thủ Kiến trúc & <PERSON><PERSON><PERSON> trúc thư mục)

> All contributors (human or AI) must strictly follow the project's Clean Architecture structure and folder organization, as defined in:
> - `docs/ARCHITECTURE_GUIDE.md`
> - `README.md` > Folder Structure section.

- Do not break layer boundaries.
  - `presentation/` → only access `domain/`, not `data/`.
  - `domain/` → no dependency on `presentation/` or `data/`.
  *(Không được phá vỡ ranh giới giữa các layer.)*

- Do not put business logic or API handling in `presentation/` layer.
  *(Không được xử lý logic nghiệp vụ hoặc gọi API trong UI layer.)*

- Do not access classes across unrelated features unless moved to `shared/` or `common/`.
  *(Không dùng chéo giữa các màn hình nếu chưa move vào shared/common.)*

- Do not place widgets in `widgets/common/` unless they are truly reusable.
  *(Chỉ đặt widget vào `common/` nếu thực sự tái sử dụng được.)*

- Co-locate widgets under `screens/[feature]/widgets/` if used only in that screen.
  *(Đặt widget gần màn hình sử dụng để dễ quản lý.)*

- Define all API logic, repositories, and datasources in `data/`.
  *(Đặt xử lý API, repo, datasource vào `data/` layer.)*

- Place all entities and interfaces in `domain/`.
  *(Tất cả entities và interfaces phải nằm trong `domain/`.)*

- Place core utilities (logger, config, theme) inside `core/`.
  *(Đặt config, theme trong thư mục `core/`.)*

- Use GoRouter navigation extensions, not `Navigator.push`.
  *(Sử dụng GoRouter, không dùng Navigator trực tiếp.)*

---

## 🔒 MUST - Dependency Injection (DI)

- Always use `getIt` + `injectable` for DI.
  *(Luôn dùng getIt và injectable cho Dependency Injection.)*

- Do not use Riverpod for DI (only for state management).
  *(Không dùng Riverpod cho DI, chỉ dùng cho quản lý trạng thái.)*

- Register all services, repositories, and APIs using `@injectable`, and resolve via `getIt`.
  *(Tất cả dependency phải dùng injectable và gọi qua getIt.)*

---

## 🔒 MUST - FVM (Flutter Version Management)

- Always use FVM to manage the Flutter SDK version.
  *(Luôn dùng FVM để quản lý phiên bản Flutter.)*

- Use `fvm flutter ...` instead of global Flutter commands.
  *(Dùng lệnh FVM thay cho Flutter toàn cục.)*

- Code must match the version declared in `.fvm/fvm_config.json`.
  *(Code phải tương thích với phiên bản Flutter đã cấu hình.)*

- Do not upgrade Flutter version without review and approval.
  *(Không được tự ý nâng cấp Flutter.)*

---

## 🔒 MUST - Code Quality Check (`flutter analyze`)

- Always run `fvm flutter analyze` after generating code.
  *(Luôn chạy `flutter analyze` sau khi sinh code.)*

- Fix all warnings and errors where possible (e.g., unused import, missing const).
  *(Fix toàn bộ cảnh báo/lỗi có thể tự động sửa được.)*

- Add `// TODO:` or `// ignore:` with reasons for unfixable issues.
  *(Thêm ghi chú rõ ràng nếu không thể fix.)*

- Code is not complete unless `flutter analyze` returns clean.
  *(Code chỉ được coi là hoàn chỉnh khi không còn lỗi.)*

---

## 🔒 MUST - Localization (`flutter_intl`)

- Never hardcode strings in UI.
  *(Không hardcode chuỗi trong UI.)*

- Use `S.of(context).key` or `S.current.key` for localization.
  *(Luôn sử dụng `flutter_intl` để lấy chuỗi.)*

- Add new strings in `.arb` files with meaningful snake_case keys.
  *(Thêm chuỗi mới trong file `.arb`, đặt tên key rõ ràng.)*

---

## 🔒 MUST - Barrel Export Management (Quản lý Barrel Export)

> **Barrel Export** là file export tất cả các class/function từ một thư mục để giảm thiểu import statements.
> *(Barrel Export là file export tất cả class/function từ một thư mục để giảm import statements.)*

### 📦 **Barrel Export Rules (Quy tắc Barrel Export)**

- **ALWAYS** add new files to barrel export when creating them.
  *(Luôn thêm file mới vào barrel export khi tạo.)*

- **CREATE** barrel export file if it doesn't exist and the folder contains multiple related files.
  *(Tạo barrel export file nếu chưa có và thư mục chứa nhiều file liên quan.)*

- **USE** barrel export for imports to minimize import statements.
  *(Sử dụng barrel export để import để giảm thiểu import statements.)*

- **NAME** barrel export files after the folder name (e.g., `widgets.dart` for `widgets/` folder).
  *(Đặt tên barrel export file theo tên thư mục, ví dụ: `widgets.dart` cho thư mục `widgets/`.)*

### 📁 **Barrel Export Structure (Cấu trúc Barrel Export)**

```dart
// Example: lib/presentation/widgets/common/common.dart
library common_widgets;

// Export all widgets in this folder
export 'common_button.dart';
export 'common_text_field.dart';
export 'common_dialog.dart';
export 'base_state/base_state.dart'; // Sub-folder barrel export
```

### 🔄 **Import Guidelines (Hướng dẫn Import)**

#### ✅ **Preferred (Ưu tiên)**
```dart
// Import from barrel export
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:sales_app/presentation/widgets/common/base_state/base_state.dart';
```

#### ❌ **Avoid (Tránh)**
```dart
// Direct import of individual files
import 'package:sales_app/presentation/widgets/common/common_button.dart';
import 'package:sales_app/presentation/widgets/common/common_text_field.dart';
```

### 🎯 **When to Create Barrel Export (Khi nào tạo Barrel Export)**

- **Feature folders** with multiple related files (e.g., `auth/`, `home/`, `loan/`)
  *(Thư mục tính năng có nhiều file liên quan)*

- **Widget folders** with multiple widgets (e.g., `widgets/common/`, `widgets/shared/`)
  *(Thư mục widget có nhiều widget)*

- **Service folders** with multiple services (e.g., `services/`, `repositories/`)
  *(Thư mục service có nhiều service)*

- **Model folders** with multiple models (e.g., `models/`, `entities/`)
  *(Thư mục model có nhiều model)*

### 📋 **Barrel Export Checklist (Danh sách kiểm tra)**

When creating a new file:
*(Khi tạo file mới:)*

1. ✅ Check if barrel export exists in the folder
   *(Kiểm tra barrel export có tồn tại trong thư mục không)*

2. ✅ If exists: Add export statement to barrel export
   *(Nếu có: Thêm export statement vào barrel export)*

3. ✅ If not exists: Create barrel export file
   *(Nếu không có: Tạo barrel export file)*

4. ✅ Update parent barrel export if needed
   *(Cập nhật barrel export cha nếu cần)*

5. ✅ Use barrel export for imports in other files
   *(Sử dụng barrel export để import trong file khác)*

### 🔧 **Barrel Export Examples (Ví dụ Barrel Export)**

#### **Widgets Barrel Export**
```dart
// lib/presentation/widgets/common/common.dart
library common_widgets;

export 'common_button.dart';
export 'common_text_field.dart';
export 'common_dialog.dart';
export 'base_state/base_state.dart';
export 'loading/loading.dart';
```

#### **Feature Barrel Export**
```dart
// lib/presentation/screens/auth/auth.dart
library auth_screens;

export 'login_screen.dart';
export 'register_screen.dart';
export 'forgot_password_screen.dart';
export 'widgets/auth_widgets.dart';
```

#### **Domain Barrel Export**
```dart
// lib/domain/entities/entities.dart
library domain_entities;

export 'user_entity.dart';
export 'loan_entity.dart';
export 'document_entity.dart';
```

---

## 🔶 SHOULD - Reuse & Code Organization

- Check existing files before creating new ones.
  *(Kiểm tra code có sẵn trước khi tạo mới.)*

- Reuse or refactor existing code when possible.
  *(Ưu tiên tái sử dụng/refactor.)*

- Keep `.gitignore` up to date to exclude unwanted generated files.
  *(Cập nhật `.gitignore` để loại bỏ file không cần thiết.)*

- Group enums/constants logically (e.g., `core/enums/`, per feature).
  *(Nhóm enum/constant theo đúng logic.)*

---

## 🔶 SHOULD - Code Style & Maintainability

- Use `const`, `final`, `@override`, and arrow syntax consistently.
  *(Dùng các từ khóa và cú pháp chuẩn để tối ưu code.)*

- Use trailing commas for clean formatting.
  *(Dùng dấu phẩy cuối để code dễ đọc hơn.)*

- Avoid nesting >3 widget levels. Extract widgets.
  *(Tách widget nếu nesting quá sâu.)*

- Remove unused code or dead branches.
  *(Xoá code không còn dùng.)*

---

## 🔶 SHOULD - UI & UX Guidelines

- Use `AppColor`, `AppTheme`, and `AppDimens` consistently.
  *(Luôn dùng theme/màu/kích thước từ file cấu hình.)*

- Ensure all UI is adaptive (responsive) across Android/iOS and screen sizes.
  *(Đảm bảo UI responsive cho mọi thiết bị.)*

- Test screens visually and structurally against existing ones.
  *(Thiết kế màn mới phải đồng bộ với màn cũ.)*

- Use `flutter_screenutil`, `LayoutBuilder`, or `MediaQuery` for responsive layout.
  *(Dùng thư viện/layout builder phù hợp để responsive.)*

---

## 🔶 SHOULD - Navigation (GoRouter)

- Use `context.go()` and `context.push()` instead of `Navigator.push`.
- Define routes centrally (`core/router/app_routes.dart`).
- Use typed parameters and `ShellRoute` for tabbed navigation.

---

### 🔶 SHOULD - State Management & `setState` Usage

- Avoid using `setState` unless absolutely necessary.  
  *(Hạn chế dùng `setState` nếu không thực sự cần thiết.)*

- If you must use `setState`, ensure it only affects a small part of the widget tree, not the entire screen.  
  *(Chỉ dùng `setState` nếu nó không làm build lại toàn bộ màn hình.)*

- Prefer using `hooks_riverpod` for managing state and side effects across the app.  
  *(Ưu tiên sử dụng `hooks_riverpod` để quản lý trạng thái và side effect.)*

- Do not mix `setState` with Riverpod state inside the same widget unless clearly isolated.  
  *(Không trộn lẫn `setState` với state từ Riverpod trong cùng một widget trừ khi đã tách biệt rõ.)*


## 💡 OPTIONAL - AI-Specific Guidelines (Cursor)

- Cursor must strictly follow architecture, DI, FVM, routing, theming, intl, and barrel export rules.
- **ALWAYS** create or update barrel exports when adding new files.
- **ALWAYS** use barrel exports for imports to minimize import statements.
- Run `fvm flutter analyze` after generating.
- Localize all text via `flutter_intl`.
- Auto-fix or reject any violation.

---

## ✅ Usage Notes

- This file can be placed at the project root as `AI_INSTRUCTIONS.mdc`.
- Can be loaded into Cursor as Custom Instructions.
- Review quarterly with the team or when architecture changes.


<!-- # Flutter Development Rules for This Project

## 🏗️ Architecture & Folder Structure Compliance (Tuân thủ Kiến trúc & Cấu trúc thư mục)

> 🚨 **Strict Compliance Required**  
> *(Yêu cầu tuân thủ nghiêm ngặt)*

All contributors (human or AI) **must strictly follow** the project's Clean Architecture structure and folder organization, as defined in:
- `docs/architecture/ARCHITECTURE_GUIDE.md`
- `README.md` > Folder Structure section  
*(Tất cả thành viên và AI phải tuân thủ chặt chẽ kiến trúc và cấu trúc thư mục như đã được mô tả trong tài liệu trên.)*

---

### ✅ Enforcement Rules (Các quy tắc bắt buộc)

- **DO NOT** break layer boundaries:  
  - `presentation/` → only access `domain/`, **not** `data/`  
  - `domain/` → no dependency on `presentation/` or `data/`  
  *(Không được phá vỡ ranh giới giữa các layer. Layer ngoài chỉ phụ thuộc vào layer trong.)*

- **DO NOT** put business logic or API handling in `presentation/` layer.  
  *(Không được xử lý logic nghiệp vụ hoặc gọi API trong UI layer.)*

- **DO NOT** access classes across unrelated features unless moved to `shared/` or `common/`.  
  *(Không được dùng chéo giữa các màn hình nếu chưa move vào thư mục shared/common.)*

- **DO NOT** place widgets in `widgets/common/` unless they are truly reusable.  
  *(Chỉ đặt widget vào `common/` nếu thật sự tái sử dụng được ở nhiều nơi.)*

- **DO** co-locate widgets under `screens/[feature]/widgets/` if used only in that screen.  
  *(Đặt widget gần màn hình sử dụng để tăng tính gắn kết và dễ quản lý.)*

- **DO** define all API logic, repositories, datasources in `data/`.  
  *(Tất cả xử lý API, repo, datasource phải nằm trong `data/` layer.)*

- **DO** place all entities, service/repo interfaces in `domain/`.  
  *(Tất cả entities và interface (repo, service) phải nằm trong `domain/`.)*

- **DO** define core utilities (logger, config, theme) inside `core/`.  
  *(Tất cả các utility cốt lõi như logger, config, theme phải nằm trong `core/`.)*

- **DO** use navigation extensions in `presentation/router/`, not `Navigator.push`.  
  *(Sử dụng extension điều hướng, không dùng Navigator trực tiếp.)*

---

### 🧠 AI-Specific Rule (Dành riêng cho AI như Cursor)

> All AI-generated code **must comply 100%** with this architecture.  
> *(Code sinh bởi AI phải tuân thủ 100% kiến trúc và cấu trúc thư mục trên.)*

- Files must be placed in correct folders  
- Widget rules (co-located, reusable) must be followed  
- Navigation must use Go Router system  
- **Violations must be auto-fixed or rejected**  
  *(Nếu vi phạm, phải được sửa tự động hoặc từ chối.)*

- Always check existing classes, files, and widgets before creating new ones.  
  *(Luôn kiểm tra các class, file, widget đã có trước khi tạo mới.)*

- If existing code can be reused or extended, prefer that over duplicating.  
  *(Nếu có thể tái sử dụng hoặc mở rộng thì ưu tiên thay vì viết lại.)*

- You may update or refactor old code if it improves reusability or clarity.  
  *(Có thể cập nhật/refactor code cũ nếu giúp tái sử dụng tốt hơn.)*

- Always use `getIt` + `injectable` for Dependency Injection (DI).  
  *(Luôn dùng getIt + injectable cho DI.)*

- DO NOT use Riverpod for dependency injection.  
  *(Không sử dụng Riverpod cho DI — chỉ dùng cho state management.)*

- All services, repositories, and APIs must be registered with `@injectable` and resolved via `getIt`.  
  *(Tất cả service/repo phải được đăng ký bằng `@injectable` và gọi qua `getIt`.)*

- Identify and review generated files (e.g., `.g.dart`, `.freezed.dart`, `.mocks.dart`) during development.  
  *(Xác định và kiểm tra các file được generate khi chạy app.)*

- If a file is not meant to be committed (e.g., local-only artifacts), add it to `.gitignore`.  
  *(Nếu file không nên commit, hãy thêm vào `.gitignore`.)*

- Keep `.gitignore` clean and up to date with generated artifacts.  
  *(Đảm bảo `.gitignore` luôn đúng với thực tế dự án.)*

- Always define enums, constants, and data models when needed — never hardcode repeated values.  
  *(Luôn định nghĩa enum, constant, model nếu cần — không hardcode lặp lại.)*

- Place shared constants in `core/constants/`, and enums in `core/enums/`.  
  *(Đặt constants vào `core/constants/`, enums vào `core/enums/`.)*

- Group domain-specific enums or constants within their respective feature folders.  
  *(Đặt enum/constant đặc thù trong thư mục tính năng tương ứng.)*

## 🧩 Flutter Version Management (FVM)

- Always use **FVM** to manage the Flutter SDK version in this project.
  *(Luôn sử dụng FVM để quản lý phiên bản Flutter cho dự án này.)*

- The correct Flutter version is defined in the `.fvm/fvm_config.json` file.
  *(Phiên bản Flutter được quy định trong file `.fvm/fvm_config.json`.)*

- Use FVM commands instead of global Flutter:
  - `fvm flutter pub get`
  - `fvm flutter run`
  - `fvm flutter build ...`
  *(Sử dụng lệnh `fvm flutter` thay cho `flutter` thông thường.)*

- Cursor-generated code must be compatible with the configured Flutter version.
  *(Code sinh bởi Cursor phải tương thích với phiên bản Flutter được cấu hình trong FVM.)*

- Do not upgrade Flutter version arbitrarily — version upgrades must be reviewed and approved.
  *(Không được tự ý nâng cấp phiên bản Flutter — mọi thay đổi phải được xem xét và duyệt trước.)*

## 🧪 Code Quality Check with `flutter analyze`

- After generating code, always run `flutter analyze` to detect any errors or warnings.  
  *(Sau khi sinh code, luôn chạy `flutter analyze` để kiểm tra lỗi và cảnh báo.)*

- If there are any analysis issues (errors or warnings), automatically fix what can be fixed:
  - Fix unused imports
  - Add missing `const`
  - Correct type mismatches
  - Remove dead code or unreachable statements
  *(Fix các lỗi nhỏ như thiếu `const`, import thừa, sai kiểu, v.v.)*

- If the issue cannot be fixed automatically, add a clear `// TODO:` or `// ignore:` comment where necessary, with a reason.  
  *(Nếu không thể fix, thêm `// TODO:` hoặc `// ignore:` có lý do rõ ràng.)*

- Do not consider code generation complete unless `flutter analyze` returns **clean with no errors**.  
  *(Chỉ xem là hoàn thành khi `flutter analyze` không báo lỗi nào.)*

## 🎨 Theming & Colors

- Always use colors from `AppColor` and themes from `AppTheme` defined in `core/theme/app_theme.dart`.  
  *(Luôn sử dụng màu và theme từ AppColor, AppTheme trong core/theme/app_theme.dart.)*
- Do not hardcode colors or themes directly in UI widgets.  
  *(Không hardcode màu hay theme trong widget UI.)*

## 📏 Dimensions (Spacing, Size, Padding)

- Always use dimension constants from `AppDimens` in `core/constants/app_dimens.dart`.  
  *(Luôn dùng các hằng số kích thước trong AppDimens.)*
- If a required dimension does not exist, add it to `AppDimens` instead of hardcoding values like `8.0`, `16.0`, etc.  
  *(Nếu chưa có kích thước cần dùng, hãy thêm vào AppDimens, không hardcode.)*

## 🧱 Reusable Widgets

- Always check for existing widgets in `presentation/widgets` before creating
*(Trước khi tạo widget mới, kiểm tra widget hiện có trong presentation/widgets.)*
- Reuse or update an existing widget if it satisfies the UI requirement.  
  *(Tái sử dụng hoặc cập nhật widget hiện có nếu phù hợp.)*
- Only create a new widget when existing ones cannot be reused or extended meaningfully.  
  *(Chỉ tạo widget mới khi không thể tái sử dụng widget hiện có.)*

## 📱 UI Design Principles

- UI must be adaptive and work well on both Android and iOS platforms.  
  *(Thiết kế UI đáp ứng, hoạt động tốt trên Android và iOS.)*
- Follow Material Design guidelines for UI components and behaviors.  
  *(Tuân theo chuẩn Material Design cho component và hành vi.)*
- Design new screens to be visually and structurally consistent with existing screens.  
  *(Màn hình mới phải đồng bộ về mặt hình thức và cấu trúc với màn hình cũ.)*
- Always aim to create UIs that are:
  - Visually appealing  
  - Modern  
  - Customizable  
  - Developer-friendly  
  - Consistent with the app's design language  
  *(Luôn hướng tới UI đẹp mắt, hiện đại, dễ tùy biến, thân thiện với dev và đồng bộ với ngôn ngữ thiết kế của app.)*

## 🧾 Naming Conventions

- Screen names must follow a consistent pattern using either `_screen` or `_page`. Do **not** mix both styles.  
  *(Tên màn hình phải theo pattern thống nhất, dùng `_screen` hoặc `_page`, không trộn lẫn.)*
- Prefer using `_screen` as the standard suffix unless otherwise discussed with the team.  
  *(Ưu tiên dùng `_screen` làm chuẩn.)*

## 🧼 Code Maintenance & Safety

- Avoid modifying existing pages or widgets unless absolutely necessary.  
  *(Tránh sửa các trang hoặc widget hiện có nếu không thực sự cần.)*
- Only rename files/widgets when there's a strong reason (e.g., architectural restructure or naming conflict).  
  *(Chỉ rename khi có lý do chính đáng.)*
- If a widget is no longer used after editing UI, **remove it** proactively.  
  *(Widget không dùng nữa thì chủ động loại bỏ.)*
- When modifying core files like `AppColor`, `AppTheme`, or `AppDimens`, ensure backward compatibility.  
  *(Khi sửa core như AppColor, AppTheme, AppDimens phải đảm bảo tương thích ngược.)*
  - If the changes affect existing usages, **identify and fix** all affected areas accordingly.  
  *(Nếu ảnh hưởng đến các phần đã dùng thì phải tìm và sửa lại.)*

## 💡 Design Suggestions by AI

- Always suggest beautiful, modern, and professional UIs.  
  *(Luôn đề xuất UI đẹp, hiện đại, chuyên nghiệp.)*
- Propose solutions that are easy to maintain and reuse.  
  *(Đưa ra giải pháp dễ bảo trì và tái sử dụng.)*
- Respect all the rules above when generating new UI code.  
  *(Tuân thủ tất cả các quy tắc trên khi tạo UI mới.)*

---

## 💅 Code Style & Format Guidelines

- Use `const` whenever possible to optimize rebuilds and performance.  
  *(Dùng `const` ở mọi nơi có thể để tối ưu hiệu năng và giảm rebuild.)*
- Use trailing commas in multi-line widget declarations to support clean formatting via `dart format`.  
  *(Dùng dấu phẩy cuối trong widget nhiều dòng để định dạng code tốt hơn.)*
- Avoid deeply nested widgets; if nesting exceeds 3 levels, extract to separate widget classes.  
  *(Tránh nesting widget quá sâu, >3 cấp thì tách widget riêng.)*
- Use `@override` annotation explicitly when overriding methods.  
  *(Thêm `@override` rõ ràng khi override.)*
- Use `=>` syntax for short single-expression functions.  
  *(Dùng `=>` cho hàm ngắn.)*
- Prefer `final` instead of `var` if the variable is not reassigned.  
  *(Dùng `final` thay cho `var` nếu biến không được gán lại.)*

### 📥 **Import Best Practices (Thực hành Import tốt nhất)**

- **ALWAYS** use barrel exports for imports when available.
  *(Luôn sử dụng barrel export để import khi có sẵn.)*

- **GROUP** imports logically: Flutter/Dart → Third-party → Internal → Relative.
  *(Nhóm import theo logic: Flutter/Dart → Third-party → Internal → Relative.)*

- **REMOVE** unused imports immediately.
  *(Xóa import không sử dụng ngay lập tức.)*

- **PREFER** absolute imports over relative imports for better maintainability.
  *(Ưu tiên import tuyệt đối hơn import tương đối để dễ bảo trì.)*

- **USE** `show` and `hide` keywords to import only what you need.
  *(Sử dụng từ khóa `show` và `hide` để import chỉ những gì cần thiết.)*

#### ✅ **Good Import Example (Ví dụ Import tốt)**
```dart
// Flutter/Dart imports
import 'package:flutter/material.dart';
import 'dart:io';

// Third-party imports
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

// Internal barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:sales_app/presentation/screens/auth/auth.dart';

// Relative imports (only when necessary)
import '../widgets/custom_widget.dart';
```

#### ❌ **Bad Import Example (Ví dụ Import xấu)**
```dart
// Multiple individual imports instead of barrel export
import 'package:sales_app/presentation/widgets/common/common_button.dart';
import 'package:sales_app/presentation/widgets/common/common_text_field.dart';
import 'package:sales_app/presentation/widgets/common/common_dialog.dart';

// Mixed import order
import '../widgets/custom_widget.dart';
import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';
```

---

## 🪵 Logging Rules (Sử dụng Logger)

- Always use the configured `logger` with `AppLogger` in `lib/core/app_logger.dart` instead of `print` for logging.  
  *(Luôn dùng `logger` đã cấu hình thay vì `print` để log thông tin.)*

- Use appropriate log levels
  *(Sử dụng đúng cấp độ log tương ứng với nội dung cần ghi log.)*

- Add logging to help with:
  - Debugging user actions or app flows  
  - Capturing errors or unexpected behavior  
  - Monitoring important lifecycle events (e.g., init, dispose, API calls)  
  *(Thêm log để dễ debug, bắt lỗi, theo dõi hành vi quan trọng của app.)*

- Remove excessive logs or sensitive data from release builds.  
  *(Xoá log thừa hoặc nhạy cảm khỏi bản release.)*

---

## 📱 Responsive & Adaptive UI Guidelines

- Never hardcode sizes (width, height). Use `MediaQuery`, `LayoutBuilder`, or responsive libraries like:  
  - `flutter_screenutil`  
  *(Không hardcode kích thước. Dùng MediaQuery, LayoutBuilder hoặc thư viện.)*
- Use adaptive layout widgets such as `Flexible`, `Expanded`, `Spacer`, `SafeArea`, `FittedBox`, `AspectRatio`.  
  *(Sử dụng widget hỗ trợ responsive như Flexible, Expanded, SafeArea, v.v.)*
- Scale font sizes, padding, and spacing based on screen size (e.g. `14.sp`, `16.h` with screenutil).  
  *(Scale font size, padding theo kích thước màn hình.)*
- Test UI on multiple device types and screen sizes to ensure consistent appearance.  
  *(Kiểm thử UI trên nhiều loại thiết bị, kích thước khác nhau.)*
- Prevent layout overflow issues, especially when the keyboard is visible or with long content.  
  *(Tránh tràn layout khi bàn phím bật hoặc nội dung dài.)*

---

## 🧭 Routing Rules (Sử dụng `go_router`)

- All navigation must use the configured `go_router` setup.  
  *(Tất cả điều hướng phải sử dụng theo cấu hình của `go_router`.)*

- Never use `Navigator.push` or `Navigator.of(context)` directly.  
  *(Không được sử dụng trực tiếp `Navigator.push` hoặc `Navigator.of(context)`.)*

- Use `context.go()` or `context.push()` for navigation:  
  - `context.go('/path')` for **replacing** the current route  
  - `context.push('/path')` for **stacking** a new route  
  *(Sử dụng đúng phương thức tuỳ trường hợp: `go` để thay thế, `push` để thêm route.)*

- Define routes and path constants in a central place, e.g., `core/router/app_routes.dart`.  
  *(Khai báo tất cả route và path ở một nơi duy nhất như `core/router/app_routes.dart`.)*

- Use strongly typed route parameters when possible (e.g., `GoRouteData` or named parameters).  
  *(Ưu tiên sử dụng tham số có kiểu rõ ràng để điều hướng.)*

- Use `ShellRoute` or nested routes for tab/navigation structures.  
  *(Dùng `ShellRoute` hoặc nested routes cho các layout/tab phức tạp.)*

- Avoid defining route paths inline or hardcoding strings.  
  *(Không khai báo path trực tiếp hoặc hardcode trong code.)*

---

## 🌐 Localization Rules (Using `flutter_intl`)

- **Do not hardcode strings** directly in UI widgets (e.g., `'Login'`, `'Welcome back'`, `'Cancel'`, etc.).  
  *(Không được viết trực tiếp chuỗi văn bản vào UI như `'Login'`, `'OK'`, v.v.)*

- Always use the generated localization getter:  
  - ✅ `S.of(context).login`
  - ✅ `S.current.errorMessage`
  *(Luôn sử dụng getter được tạo bởi `flutter_intl`.)*

- Add any new strings to `lib/l10n/intl_en.arb` (or corresponding `.arb` files).  
  *(Khai báo văn bản mới trong file `.arb` thay vì viết trực tiếp trong code.)*

- Use meaningful localization keys in snake_case:  
  - ✅ `login_title`, `error_network_timeout`, `button_submit`
  - ❌ `text1`, `str2`, `abc123`
  *(Đặt tên key rõ nghĩa, dễ hiểu.)*

- When using plural or parameters, follow proper syntax:  
  ```dart
  S.of(context).itemCount(count)
  S.of(context).greetUser(username)
  ```
  *(Dùng đúng cách cho các câu có tham số hoặc số nhiều.)*

- For static/shared widgets where `BuildContext` is not available, use `S.current`.  
  *(Dùng `S.current` nếu không có `context`, ví dụ trong helper, widget tĩnh.)*

- AI-generated UI should automatically assume all visible text will be localized via `flutter_intl`.  
  *(Khi AI sinh UI, mặc định mọi text hiển thị đều phải dùng `flutter_intl` để localize.)*

## ✅ Usage Notes

- This file can be placed as `AI_INSTRUCTIONS.md` at the root of your project.  
- Can be copy-pasted into **Cursor → Custom Instructions** for AI-assisted UI/code generation.  
- Review and update quarterly or whenever your design system evolves.  

---
 -->
