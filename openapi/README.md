# OpenAPI Integration Guide

> **🎯 <PERSON>ục tiêu**: S<PERSON> dụng JSON files được export từ Swagger để tự động generate API client code cho nhiều APIs

## 🎨 Current APIs

### 1. **Sales App API** (`sale-app-api-spec.json`)
- Authentication, user management, registration
- Base URL: `http://localhost:8888`
- Generated: `lib/generated/api/sales/`

### 2. **Media API** (`media-spec.json`)
- File upload/download, image processing  
- Base URL: `https://dev-ksapi.ssf.vn/media`
- Generated: `lib/generated/api/media/`

## ⚡ Quick Start

### 1. Chuẩn bị JSON files

**Export từ Swagger UI**:
- Mở Swagger documentation của backend APIs
- Click **Download** → **JSON**
- Lưu files với tên chính xác

**Đặt files vào thư mục**:
```bash
# Đặt files JSON vào đây:
openapi/json-specs/sale-app-api-spec.json
openapi/json-specs/media-spec.json
```

### 2. Generate API Clients

```bash
# Generate tất cả API clients
./scripts/generate_api.sh

# Hoặc generate riêng lẻ:
./scripts/generate_api.sh sales   # Chỉ Sales App API
./scripts/generate_api.sh media   # Chỉ Media API
```

### 3. Sử dụng Generated Clients

```dart
// Import generated clients
import 'package:sales_app/generated/api/sales/lib/sales_app_api.dart';
import 'package:sales_app/generated/api/media/lib/media_api.dart';

// Khởi tạo clients
final salesApi = SalesAppApi();
final mediaApi = MediaApi();

// Gọi API endpoints
final response = await salesApi.getUsersApi().getUsers();
final uploadResult = await mediaApi.getMinioControllerApi().uploadFile(file);
```

## 📁 Project Structure

```
openapi/
├── README.md                               # Guide này
└── json-specs/                             # JSON specifications
    ├── README.md                           # Chi tiết về JSON files
    ├── sale-app-api-spec.json             # Sales App API spec
    └── media-spec.json                    # Media API spec

lib/
├── data/network/
│   ├── openapi/                           # OpenAPI Generator configs
│   │   ├── README.md                      # Config documentation
│   │   ├── sales_api_config.dart          # Sales API config
│   │   └── media_api_config.dart          # Media API config
│   ├── api_client.dart                    # Base API client
│   ├── base_api_service.dart              # Base API service
│   ├── dio_builder.dart                   # Dio configuration
│   ├── openapi_client.dart                # OpenAPI client wrapper
│   └── interceptors/                      # HTTP interceptors
├── generated/api/
│   ├── sales/                             # Generated Sales API client
│   └── media/                             # Generated Media API client
└── scripts/
    └── generate_api.sh                    # Generation script
```

## ⚙️ Configuration

### Sales App API Config
```dart
// lib/data/network/openapi/sales_api_config.dart
@Openapi(
  additionalProperties: AdditionalProperties(
    pubName: 'sales_app_api',
    pubAuthor: 'KienlongBank Sales Team',
  ),
  inputSpec: InputSpec(path: 'openapi/json-specs/sale-app-api-spec.json'),
  generatorName: Generator.dio,
  outputDirectory: 'lib/generated/api/sales',
)
class SalesApiConfig {}
```

### Media API Config
```dart
// lib/data/network/openapi/media_api_config.dart
@Openapi(
  additionalProperties: AdditionalProperties(
    pubName: 'media_api',
    pubAuthor: 'KienlongBank Sales Team',
  ),
  inputSpec: InputSpec(path: 'openapi/json-specs/media-spec.json'),
  generatorName: Generator.dio,
  outputDirectory: 'lib/generated/api/media',
)
class MediaApiConfig {}
```

## 🔄 Workflow

### Khi backend APIs thay đổi:

1. **Export JSON mới từ Swagger UI**
2. **Thay thế files trong `openapi/json-specs/`**
3. **Generate lại API clients**: `./scripts/generate_api.sh`
4. **Update code nếu có breaking changes**
5. **Test integration**

## 🛠️ Troubleshooting

### Common Issues:

**Missing Dependencies**
```bash
# Thêm vào pubspec.yaml nếu cần:
dependencies:
  built_value: ^8.8.0
  built_collection: ^5.1.1
  
dev_dependencies:
  built_value_generator: ^8.8.0
  build_runner: ^2.4.8
```

**JSON Validation**
```bash
# Validate JSON online tại:
# https://editor.swagger.io/

# Hoặc sử dụng CLI nếu có:
openapi-generator-cli validate -i openapi/json-specs/sale-app-api-spec.json
```

**Build Errors**
```bash
# Clean và rebuild:
fvm flutter clean
fvm flutter pub get
./scripts/generate_api.sh
```

## 🔧 Advanced Usage

### Custom Templates
```bash
# Tạo custom templates trong:
openapi-generator/templates/

# Update config để sử dụng:
templateDirectory: 'openapi-generator/templates'
```

### Multiple Environments
```bash
# Tạo configs cho các environments khác nhau:
openapi/json-specs/
├── dev/
│   ├── sale-app-api-spec.json
│   └── media-spec.json
├── staging/
│   ├── sale-app-api-spec.json
│   └── media-spec.json
└── prod/
    ├── sale-app-api-spec.json
    └── media-spec.json
```

### CI/CD Integration
```yaml
# .github/workflows/api-generation.yml
name: Generate API Clients
on:
  push:
    paths:
      - 'openapi/json-specs/**'

jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.x'
      - run: ./scripts/generate_api.sh
      - run: fvm flutter analyze
```

## 📚 Documentation

- **Detailed JSON Specs Guide**: [openapi/json-specs/README.md](json-specs/README.md)
- **Network Layer Guide**: [docs/network/NETWORK_GUIDE.md](../docs/network/NETWORK_GUIDE.md)
- **Error Handling**: [docs/network/ERROR_HANDLING_GUIDE.md](../docs/network/ERROR_HANDLING_GUIDE.md)

## 💡 Best Practices

1. **Version Control**: Commit JSON specs để track API changes
2. **Validation**: Luôn validate JSON trước khi generate
3. **Testing**: Write integration tests cho generated clients
4. **Documentation**: Document breaking changes khi update APIs
5. **Dependencies**: Keep generated dependencies up-to-date

## 🔗 Resources

- [OpenAPI Generator Documentation](https://openapi-generator.tech/)
- [OpenAPI Specification](https://swagger.io/specification/)
- [Flutter OpenAPI Generator](https://pub.dev/packages/openapi_generator_annotations) 