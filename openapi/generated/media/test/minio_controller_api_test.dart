import 'package:test/test.dart';
import 'package:media_api/media_api.dart';

/// tests for MinioControllerApi
void main() {
  final instance = MediaApi().getMinioControllerApi();

  group(MinioControllerApi, () {
    // Delete file
    //
    // Use this endpoint to delete file
    //
    //Future delete(String bucketName, String fileType, String objectName) async
    test('test delete', () async {
      // TODO
    });

    // Download file
    //
    // Use this endpoint to download file
    //
    //Future<BuiltList<String>> downloadFile(String bucketName, String fileType, String objectName) async
    test('test downloadFile', () async {
      // TODO
    });

    // Preview file
    //
    // Use this endpoint to preview file
    //
    //Future previewImage(String bucketName, String fileType, String objectName) async
    test('test previewImage', () async {
      // TODO
    });

    // Preview file
    //
    // Use this endpoint to preview file
    //
    //Future previewPrivatePicture(String bucketName, String fileType, String objectName) async
    test('test previewPrivatePicture', () async {
      // TODO
    });

    // Upload file
    //
    // Use this endpoint to upload file
    //
    //Future<FileUploadResponse> privateUpload(String fileType, { String bucketName, MultipartFile file }) async
    test('test privateUpload', () async {
      // TODO
    });

    // Upload file
    //
    // Use this endpoint to upload file
    //
    //Future<FileUploadResponse> upload(String fileType, { String bucketName, MultipartFile file }) async
    test('test upload', () async {
      // TODO
    });

    // Upload file
    //
    // Use this endpoint to upload file
    //
    //Future<FileUploadResponseV1> uploadV1(String fileType, { String bucketName, MultipartFile file }) async
    test('test uploadV1', () async {
      // TODO
    });
  });
}
