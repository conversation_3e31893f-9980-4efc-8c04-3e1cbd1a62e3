import 'package:test/test.dart';
import 'package:media_api/media_api.dart';

// tests for FileUploadResponseV1
void main() {
  final instance = FileUploadResponseV1Builder();
  // TODO add properties to the builder and call build()

  group(FileUploadResponseV1, () {
    // String previewUrl
    test('to test the property `previewUrl`', () async {
      // TODO
    });

    // String privatePreviewUrl
    test('to test the property `privatePreviewUrl`', () async {
      // TODO
    });
  });
}
