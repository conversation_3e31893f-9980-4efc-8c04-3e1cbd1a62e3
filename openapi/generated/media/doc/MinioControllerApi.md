# media_api.api.MinioControllerApi

## Load the API package
```dart
import 'package:media_api/api.dart';
```

All URIs are relative to *https://dev-ksapi.ssf.vn/media*

Method | HTTP request | Description
------------- | ------------- | -------------
[**delete**](MinioControllerApi.md#delete) | **DELETE** /delete/{bucketName}/{fileType}/{objectName} | Delete file
[**downloadFile**](MinioControllerApi.md#downloadfile) | **GET** /download/{bucketName}/{fileType}/{objectName} | Download file
[**previewImage**](MinioControllerApi.md#previewimage) | **GET** /preview/{bucketName}/{fileType}/{objectName} | Preview file
[**previewPrivatePicture**](MinioControllerApi.md#previewprivatepicture) | **GET** /private/preview/{bucketName}/private/{fileType}/{objectName} | Preview file
[**privateUpload**](MinioControllerApi.md#privateupload) | **POST** /private/upload | Upload file
[**upload**](MinioControllerApi.md#upload) | **POST** /upload | Upload file
[**uploadV1**](MinioControllerApi.md#uploadv1) | **POST** /v1/upload | Upload file


# **delete**
> delete(bucketName, fileType, objectName)

Delete file

Use this endpoint to delete file

### Example
```dart
import 'package:media_api/api.dart';

final api = MediaApi().getMinioControllerApi();
final String bucketName = bucketName_example; // String | 
final String fileType = fileType_example; // String | 
final String objectName = objectName_example; // String | 

try {
    api.delete(bucketName, fileType, objectName);
} catch on DioException (e) {
    print('Exception when calling MinioControllerApi->delete: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bucketName** | **String**|  | 
 **fileType** | **String**|  | 
 **objectName** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **downloadFile**
> BuiltList<String> downloadFile(bucketName, fileType, objectName)

Download file

Use this endpoint to download file

### Example
```dart
import 'package:media_api/api.dart';

final api = MediaApi().getMinioControllerApi();
final String bucketName = bucketName_example; // String | 
final String fileType = fileType_example; // String | 
final String objectName = objectName_example; // String | 

try {
    final response = api.downloadFile(bucketName, fileType, objectName);
    print(response);
} catch on DioException (e) {
    print('Exception when calling MinioControllerApi->downloadFile: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bucketName** | **String**|  | 
 **fileType** | **String**|  | 
 **objectName** | **String**|  | 

### Return type

**BuiltList&lt;String&gt;**

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **previewImage**
> previewImage(bucketName, fileType, objectName)

Preview file

Use this endpoint to preview file

### Example
```dart
import 'package:media_api/api.dart';

final api = MediaApi().getMinioControllerApi();
final String bucketName = bucketName_example; // String | 
final String fileType = fileType_example; // String | 
final String objectName = objectName_example; // String | 

try {
    api.previewImage(bucketName, fileType, objectName);
} catch on DioException (e) {
    print('Exception when calling MinioControllerApi->previewImage: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bucketName** | **String**|  | 
 **fileType** | **String**|  | 
 **objectName** | **String**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **previewPrivatePicture**
> previewPrivatePicture(bucketName, fileType, objectName)

Preview file

Use this endpoint to preview file

### Example
```dart
import 'package:media_api/api.dart';

final api = MediaApi().getMinioControllerApi();
final String bucketName = bucketName_example; // String | 
final String fileType = fileType_example; // String | 
final String objectName = objectName_example; // String | 

try {
    api.previewPrivatePicture(bucketName, fileType, objectName);
} catch on DioException (e) {
    print('Exception when calling MinioControllerApi->previewPrivatePicture: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **bucketName** | **String**|  | 
 **fileType** | **String**|  | 
 **objectName** | **String**|  | 

### Return type

void (empty response body)

### Authorization

[Authorization](../README.md#Authorization)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **privateUpload**
> FileUploadResponse privateUpload(fileType, bucketName, file)

Upload file

Use this endpoint to upload file

### Example
```dart
import 'package:media_api/api.dart';

final api = MediaApi().getMinioControllerApi();
final String fileType = fileType_example; // String | 
final String bucketName = bucketName_example; // String | 
final MultipartFile file = BINARY_DATA_HERE; // MultipartFile | 

try {
    final response = api.privateUpload(fileType, bucketName, file);
    print(response);
} catch on DioException (e) {
    print('Exception when calling MinioControllerApi->privateUpload: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **fileType** | **String**|  | 
 **bucketName** | **String**|  | [optional] [default to 'salt']
 **file** | **MultipartFile**|  | [optional] 

### Return type

[**FileUploadResponse**](FileUploadResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **upload**
> FileUploadResponse upload(fileType, bucketName, file)

Upload file

Use this endpoint to upload file

### Example
```dart
import 'package:media_api/api.dart';

final api = MediaApi().getMinioControllerApi();
final String fileType = fileType_example; // String | 
final String bucketName = bucketName_example; // String | 
final MultipartFile file = BINARY_DATA_HERE; // MultipartFile | 

try {
    final response = api.upload(fileType, bucketName, file);
    print(response);
} catch on DioException (e) {
    print('Exception when calling MinioControllerApi->upload: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **fileType** | **String**|  | 
 **bucketName** | **String**|  | [optional] [default to 'salt']
 **file** | **MultipartFile**|  | [optional] 

### Return type

[**FileUploadResponse**](FileUploadResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **uploadV1**
> FileUploadResponseV1 uploadV1(fileType, bucketName, file)

Upload file

Use this endpoint to upload file

### Example
```dart
import 'package:media_api/api.dart';

final api = MediaApi().getMinioControllerApi();
final String fileType = fileType_example; // String | 
final String bucketName = bucketName_example; // String | 
final MultipartFile file = BINARY_DATA_HERE; // MultipartFile | 

try {
    final response = api.uploadV1(fileType, bucketName, file);
    print(response);
} catch on DioException (e) {
    print('Exception when calling MinioControllerApi->uploadV1: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **fileType** | **String**|  | 
 **bucketName** | **String**|  | [optional] [default to 'salt']
 **file** | **MultipartFile**|  | [optional] 

### Return type

[**FileUploadResponseV1**](FileUploadResponseV1.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

