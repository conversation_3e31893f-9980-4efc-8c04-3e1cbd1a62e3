// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file_upload_response_v1.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$FileUploadResponseV1 extends FileUploadResponseV1 {
  @override
  final String? previewUrl;
  @override
  final String? privatePreviewUrl;

  factory _$FileUploadResponseV1(
          [void Function(FileUploadResponseV1Builder)? updates]) =>
      (FileUploadResponseV1Builder()..update(updates))._build();

  _$FileUploadResponseV1._({this.previewUrl, this.privatePreviewUrl})
      : super._();
  @override
  FileUploadResponseV1 rebuild(
          void Function(FileUploadResponseV1Builder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  FileUploadResponseV1Builder toBuilder() =>
      FileUploadResponseV1Builder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is FileUploadResponseV1 &&
        previewUrl == other.previewUrl &&
        privatePreviewUrl == other.privatePreviewUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, previewUrl.hashCode);
    _$hash = $jc(_$hash, privatePreviewUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'FileUploadResponseV1')
          ..add('previewUrl', previewUrl)
          ..add('privatePreviewUrl', privatePreviewUrl))
        .toString();
  }
}

class FileUploadResponseV1Builder
    implements Builder<FileUploadResponseV1, FileUploadResponseV1Builder> {
  _$FileUploadResponseV1? _$v;

  String? _previewUrl;
  String? get previewUrl => _$this._previewUrl;
  set previewUrl(String? previewUrl) => _$this._previewUrl = previewUrl;

  String? _privatePreviewUrl;
  String? get privatePreviewUrl => _$this._privatePreviewUrl;
  set privatePreviewUrl(String? privatePreviewUrl) =>
      _$this._privatePreviewUrl = privatePreviewUrl;

  FileUploadResponseV1Builder() {
    FileUploadResponseV1._defaults(this);
  }

  FileUploadResponseV1Builder get _$this {
    final $v = _$v;
    if ($v != null) {
      _previewUrl = $v.previewUrl;
      _privatePreviewUrl = $v.privatePreviewUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(FileUploadResponseV1 other) {
    _$v = other as _$FileUploadResponseV1;
  }

  @override
  void update(void Function(FileUploadResponseV1Builder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  FileUploadResponseV1 build() => _build();

  _$FileUploadResponseV1 _build() {
    final _$result = _$v ??
        _$FileUploadResponseV1._(
          previewUrl: previewUrl,
          privatePreviewUrl: privatePreviewUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
