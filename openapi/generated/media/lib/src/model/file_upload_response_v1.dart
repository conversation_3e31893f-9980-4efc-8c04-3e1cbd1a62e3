//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'file_upload_response_v1.g.dart';

/// FileUploadResponseV1
///
/// Properties:
/// * [previewUrl]
/// * [privatePreviewUrl]
@BuiltValue()
abstract class FileUploadResponseV1
    implements Built<FileUploadResponseV1, FileUploadResponseV1Builder> {
  @BuiltValueField(wireName: r'previewUrl')
  String? get previewUrl;

  @BuiltValueField(wireName: r'privatePreviewUrl')
  String? get privatePreviewUrl;

  FileUploadResponseV1._();

  factory FileUploadResponseV1([void updates(FileUploadResponseV1Builder b)]) =
      _$FileUploadResponseV1;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(FileUploadResponseV1Builder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<FileUploadResponseV1> get serializer =>
      _$FileUploadResponseV1Serializer();
}

class _$FileUploadResponseV1Serializer
    implements PrimitiveSerializer<FileUploadResponseV1> {
  @override
  final Iterable<Type> types = const [
    FileUploadResponseV1,
    _$FileUploadResponseV1
  ];

  @override
  final String wireName = r'FileUploadResponseV1';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    FileUploadResponseV1 object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.previewUrl != null) {
      yield r'previewUrl';
      yield serializers.serialize(
        object.previewUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
    if (object.privatePreviewUrl != null) {
      yield r'privatePreviewUrl';
      yield serializers.serialize(
        object.privatePreviewUrl,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    FileUploadResponseV1 object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required FileUploadResponseV1Builder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'previewUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.previewUrl = valueDes;
          break;
        case r'privatePreviewUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.privatePreviewUrl = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  FileUploadResponseV1 deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = FileUploadResponseV1Builder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
