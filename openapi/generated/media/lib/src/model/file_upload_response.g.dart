// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file_upload_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$FileUploadResponse extends FileUploadResponse {
  @override
  final String? previewUrl;

  factory _$FileUploadResponse(
          [void Function(FileUploadResponseBuilder)? updates]) =>
      (FileUploadResponseBuilder()..update(updates))._build();

  _$FileUploadResponse._({this.previewUrl}) : super._();
  @override
  FileUploadResponse rebuild(
          void Function(FileUploadResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  FileUploadResponseBuilder toBuilder() =>
      FileUploadResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is FileUploadResponse && previewUrl == other.previewUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, previewUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'FileUploadResponse')
          ..add('previewUrl', previewUrl))
        .toString();
  }
}

class FileUploadResponseBuilder
    implements Builder<FileUploadResponse, FileUploadResponseBuilder> {
  _$FileUploadResponse? _$v;

  String? _previewUrl;
  String? get previewUrl => _$this._previewUrl;
  set previewUrl(String? previewUrl) => _$this._previewUrl = previewUrl;

  FileUploadResponseBuilder() {
    FileUploadResponse._defaults(this);
  }

  FileUploadResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _previewUrl = $v.previewUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(FileUploadResponse other) {
    _$v = other as _$FileUploadResponse;
  }

  @override
  void update(void Function(FileUploadResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  FileUploadResponse build() => _build();

  _$FileUploadResponse _build() {
    final _$result = _$v ??
        _$FileUploadResponse._(
          previewUrl: previewUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
