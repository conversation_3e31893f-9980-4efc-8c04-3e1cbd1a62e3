# See https://dart.dev/guides/libraries/private-files

# Files and directories created by pub
.dart_tool/
.buildlog
.packages
.project
.pub/
build/
**/packages/

# Files created by dart2js
# (Most Dart developers will use pub build to compile <PERSON><PERSON>, use/modify these
#  rules if you intend to use dart2js directly
#  Convention is to use extension '.dart.js' for Dart compiled to Javascript to
#  differentiate from explicit Javascript files)
*.dart.js
*.part.js
*.js.deps
*.js.map
*.info.json

# Directory created by dartdoc
doc/api/

# Don't commit pubspec lock file
# (Library packages only! Remove pattern if developing an application package)
pubspec.lock

# Don’t commit files and directories created by other development environments.
# For example, if your development environment creates any of the following files,
# consider putting them in a global ignore file:

# IntelliJ
*.iml
*.ipr
*.iws
.idea/

# Mac
.DS_Store
