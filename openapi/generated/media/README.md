# media_api (EXPERIMENTAL)
Documentation Media API v1.0

This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0
- Generator version: 7.9.0
- Build package: org.openapitools.codegen.languages.DartDioClientCodegen

## Requirements

* Dart 2.15.0+ or Flutter 2.8.0+
* Dio 5.0.0+ (https://pub.dev/packages/dio)

## Installation & Usage

### pub.dev
To use the package from [pub.dev](https://pub.dev), please include the following in pubspec.yaml
```yaml
dependencies:
  media_api: 1.0.0
```

### Github
If this Dart package is published to Github, please include the following in pubspec.yaml
```yaml
dependencies:
  media_api:
    git:
      url: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
      #ref: main
```

### Local development
To use the package from your local drive, please include the following in pubspec.yaml
```yaml
dependencies:
  media_api:
    path: /path/to/media_api
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:media_api/media_api.dart';


final api = MediaApi().getMinioControllerApi();
final String bucketName = bucketName_example; // String | 
final String fileType = fileType_example; // String | 
final String objectName = objectName_example; // String | 

try {
    api.delete(bucketName, fileType, objectName);
} catch on DioException (e) {
    print("Exception when calling MinioControllerApi->delete: $e\n");
}

```

## Documentation for API Endpoints

All URIs are relative to *https://dev-ksapi.ssf.vn/media*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
[*MinioControllerApi*](doc/MinioControllerApi.md) | [**delete**](doc/MinioControllerApi.md#delete) | **DELETE** /delete/{bucketName}/{fileType}/{objectName} | Delete file
[*MinioControllerApi*](doc/MinioControllerApi.md) | [**downloadFile**](doc/MinioControllerApi.md#downloadfile) | **GET** /download/{bucketName}/{fileType}/{objectName} | Download file
[*MinioControllerApi*](doc/MinioControllerApi.md) | [**previewImage**](doc/MinioControllerApi.md#previewimage) | **GET** /preview/{bucketName}/{fileType}/{objectName} | Preview file
[*MinioControllerApi*](doc/MinioControllerApi.md) | [**previewPrivatePicture**](doc/MinioControllerApi.md#previewprivatepicture) | **GET** /private/preview/{bucketName}/private/{fileType}/{objectName} | Preview file
[*MinioControllerApi*](doc/MinioControllerApi.md) | [**privateUpload**](doc/MinioControllerApi.md#privateupload) | **POST** /private/upload | Upload file
[*MinioControllerApi*](doc/MinioControllerApi.md) | [**upload**](doc/MinioControllerApi.md#upload) | **POST** /upload | Upload file
[*MinioControllerApi*](doc/MinioControllerApi.md) | [**uploadV1**](doc/MinioControllerApi.md#uploadv1) | **POST** /v1/upload | Upload file


## Documentation For Models

 - [FileUploadResponse](doc/FileUploadResponse.md)
 - [FileUploadResponseV1](doc/FileUploadResponseV1.md)
 - [NullType](doc/NullType.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### Authorization

- **Type**: HTTP Bearer Token authentication (Bearer [token])


## Author



