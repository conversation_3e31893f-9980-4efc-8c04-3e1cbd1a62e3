import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for PageableObject
void main() {
  final instance = PageableObjectBuilder();
  // TODO add properties to the builder and call build()

  group(PageableObject, () {
    // bool unpaged
    test('to test the property `unpaged`', () async {
      // TODO
    });

    // int pageNumber
    test('to test the property `pageNumber`', () async {
      // TODO
    });

    // bool paged
    test('to test the property `paged`', () async {
      // TODO
    });

    // int pageSize
    test('to test the property `pageSize`', () async {
      // TODO
    });

    // int offset
    test('to test the property `offset`', () async {
      // TODO
    });

    // SortObject sort
    test('to test the property `sort`', () async {
      // TODO
    });
  });
}
