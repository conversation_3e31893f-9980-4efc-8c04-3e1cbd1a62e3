import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

/// tests for UsersApi
void main() {
  final instance = SalesAppApi().getUsersApi();

  group(UsersApi, () {
    // Api lấy thông tin profile của user đang đăng nhập
    //
    // Api lấy thông tin profile của user đang đăng nhập
    //
    //Future<BaseResponseAppGetUserProfileResponse> getProfile() async
    test('test getProfile', () async {
      // TODO
    });

    // Api lấy thông tin người giới thiệu
    //
    // Api lấy thông tin người giới thiệu
    //
    //Future<BaseResponseAppGetReferrerByCodeResponse> getReferrerByCode(String referrerCode) async
    test('test getReferrerByCode', () async {
      // TODO
    });
  });
}
