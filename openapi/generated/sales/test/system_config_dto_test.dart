import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for SystemConfigDto
void main() {
  final instance = SystemConfigDtoBuilder();
  // TODO add properties to the builder and call build()

  group(SystemConfigDto, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // String label
    test('to test the property `label`', () async {
      // TODO
    });
  });
}
