import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for RoleDto
void main() {
  final instance = RoleDtoBuilder();
  // TODO add properties to the builder and call build()

  group(RoleDto, () {
    // Mã role
    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // Tên role
    // String name
    test('to test the property `name`', () async {
      // TODO
    });
  });
}
