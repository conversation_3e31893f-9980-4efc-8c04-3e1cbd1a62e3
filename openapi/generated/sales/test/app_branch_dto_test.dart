import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppBranchDto
void main() {
  final instance = AppBranchDtoBuilder();
  // TODO add properties to the builder and call build()

  group(AppBranchDto, () {
    // Id của chi nhánh
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // Mã chi nhánh
    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // Tên chi nhánh
    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // Địa chỉ chi nhánh
    // String address
    test('to test the property `address`', () async {
      // TODO
    });

    // ID của tỉnh thành phố
    // String provinceId
    test('to test the property `provinceId`', () async {
      // TODO
    });
  });
}
