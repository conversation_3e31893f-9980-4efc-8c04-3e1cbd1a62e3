import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

/// tests for AccountApi
void main() {
  final instance = SalesAppApi().getAccountApi();

  group(AccountApi, () {
    // API lây danh sách tài <PERSON> thanh toán theo số GTTT
    //
    //Future<BaseResponseAppGetListPaymentAccountResponse> getPaymentAccount(AppGetListPaymentAccountRequest appGetListPaymentAccountRequest) async
    test('test getPaymentAccount', () async {
      // TODO
    });
  });
}
