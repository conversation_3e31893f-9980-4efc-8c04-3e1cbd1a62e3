import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppGetListProvinceResponse
void main() {
  final instance = AppGetListProvinceResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppGetListProvinceResponse, () {
    // BuiltList<AppProvinceDto> provinces
    test('to test the property `provinces`', () async {
      // TODO
    });
  });
}
