import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminResetPasswordResponse
void main() {
  final instance = AdminResetPasswordResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AdminResetPasswordResponse, () {
    // String userId
    test('to test the property `userId`', () async {
      // TODO
    });

    // bool success
    test('to test the property `success`', () async {
      // TODO
    });
  });
}
