import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppGetSystemConfigsByGroupCodeResponse
void main() {
  final instance = AppGetSystemConfigsByGroupCodeResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppGetSystemConfigsByGroupCodeResponse, () {
    // String groupCode
    test('to test the property `groupCode`', () async {
      // TODO
    });

    // BuiltList<SystemConfigDto> configs
    test('to test the property `configs`', () async {
      // TODO
    });
  });
}
