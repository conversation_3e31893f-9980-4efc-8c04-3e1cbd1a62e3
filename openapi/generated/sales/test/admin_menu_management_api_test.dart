import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

/// tests for AdminMenuManagementApi
void main() {
  final instance = SalesAppApi().getAdminMenuManagementApi();

  group(AdminMenuManagementApi, () {
    // API tạo menu
    //
    // API tạo mới menu (APP/WEB)
    //
    //Future<BaseResponseAdminCreateMenuResponse> createMenu(AdminCreateMenuRequest adminCreateMenuRequest) async
    test('test createMenu', () async {
      // TODO
    });

    // API xóa menu
    //
    // Xóa menu (soft delete - chỉ cập nhật is_deleted = true)
    //
    //Future<BaseResponseAdminDeleteMenuResponse> deleteMenu(String id) async
    test('test deleteMenu', () async {
      // TODO
    });

    // API lấy danh sách action
    //
    // Lấy tất cả actions không phân trang
    //
    //Future<BaseResponseAdminGetActionsResponse> getActions() async
    test('test getActions', () async {
      // TODO
    });

    // API lấy chi tiết menu
    //
    // Lấy thông tin chi tiết menu theo ID
    //
    //Future<BaseResponseAdminGetMenuResponse> getMenu(String id) async
    test('test getMenu', () async {
      // TODO
    });

    // API lấy danh sách menu hierarchical
    //
    // Lấy danh sách menu và submenu với cấu trúc cha/con (không phân trang)
    //
    //Future<BaseResponseAdminListMenusResponse> listMenus() async
    test('test listMenus', () async {
      // TODO
    });

    // API lấy danh sách parent menu
    //
    // Lấy tất cả parent menu (menu không có parentId)
    //
    //Future<BaseResponseAdminListParentMenusResponse> listParentMenus({ String platform }) async
    test('test listParentMenus', () async {
      // TODO
    });

    // API cập nhật menu
    //
    // API cập nhật thông tin menu và actions
    //
    //Future<BaseResponseAdminUpdateMenuResponse> updateMenu(String id, AdminUpdateMenuRequest adminUpdateMenuRequest) async
    test('test updateMenu', () async {
      // TODO
    });
  });
}
