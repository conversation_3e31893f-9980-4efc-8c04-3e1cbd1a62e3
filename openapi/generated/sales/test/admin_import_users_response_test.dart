import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminImportUsersResponse
void main() {
  final instance = AdminImportUsersResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AdminImportUsersResponse, () {
    // int totalRequested
    test('to test the property `totalRequested`', () async {
      // TODO
    });

    // int totalImported
    test('to test the property `totalImported`', () async {
      // TODO
    });
  });
}
