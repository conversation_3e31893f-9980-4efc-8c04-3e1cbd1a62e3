import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for SortObject
void main() {
  final instance = SortObjectBuilder();
  // TODO add properties to the builder and call build()

  group(SortObject, () {
    // bool unsorted
    test('to test the property `unsorted`', () async {
      // TODO
    });

    // bool sorted
    test('to test the property `sorted`', () async {
      // TODO
    });

    // bool empty
    test('to test the property `empty`', () async {
      // TODO
    });
  });
}
