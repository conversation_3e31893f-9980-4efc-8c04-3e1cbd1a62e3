import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminListMenusResponse
void main() {
  final instance = AdminListMenusResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AdminListMenusResponse, () {
    // BuiltList<MenuDto> menuApp
    test('to test the property `menuApp`', () async {
      // TODO
    });

    // BuiltList<MenuDto> menuWeb
    test('to test the property `menuWeb`', () async {
      // TODO
    });
  });
}
