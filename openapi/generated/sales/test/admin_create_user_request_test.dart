import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminCreateUserRequest
void main() {
  final instance = AdminCreateUserRequestBuilder();
  // TODO add properties to the builder and call build()

  group(AdminCreateUserRequest, () {
    // String username
    test('to test the property `username`', () async {
      // TODO
    });

    // String password
    test('to test the property `password`', () async {
      // TODO
    });

    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // String phone
    test('to test the property `phone`', () async {
      // TODO
    });

    // String departmentId
    test('to test the property `departmentId`', () async {
      // TODO
    });
  });
}
