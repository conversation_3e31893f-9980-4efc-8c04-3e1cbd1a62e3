import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for BaseResponseAdminAssignRolesResponse
void main() {
  final instance = BaseResponseAdminAssignRolesResponseBuilder();
  // TODO add properties to the builder and call build()

  group(BaseResponseAdminAssignRolesResponse, () {
    // bool success
    test('to test the property `success`', () async {
      // TODO
    });

    // int code
    test('to test the property `code`', () async {
      // TODO
    });

    // String message
    test('to test the property `message`', () async {
      // TODO
    });

    // AdminAssignRolesResponse data
    test('to test the property `data`', () async {
      // TODO
    });
  });
}
