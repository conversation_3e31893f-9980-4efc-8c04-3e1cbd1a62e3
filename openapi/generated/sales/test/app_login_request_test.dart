import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppLoginRequest
void main() {
  final instance = AppLoginRequestBuilder();
  // TODO add properties to the builder and call build()

  group(AppLoginRequest, () {
    // Username for login
    // String username
    test('to test the property `username`', () async {
      // TODO
    });

    // Password for login
    // String password
    test('to test the property `password`', () async {
      // TODO
    });
  });
}
