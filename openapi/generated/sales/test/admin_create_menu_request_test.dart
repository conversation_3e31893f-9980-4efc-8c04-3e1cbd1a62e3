import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminCreateMenuRequest
void main() {
  final instance = AdminCreateMenuRequestBuilder();
  // TODO add properties to the builder and call build()

  group(AdminCreateMenuRequest, () {
    // Menu theo platform
    // String platform
    test('to test the property `platform`', () async {
      // TODO
    });

    // Mã menu
    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // Tên menu
    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // Mô tả menu
    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // Menu cha (nếu có)
    // String parentId
    test('to test the property `parentId`', () async {
      // TODO
    });

    // Sử dụng để order trên UI
    // int orderNo
    test('to test the property `orderNo`', () async {
      // TODO
    });

    // <PERSON>h sách ID của các action gắn với menu
    // BuiltList<String> actionIds
    test('to test the property `actionIds`', () async {
      // TODO
    });
  });
}
