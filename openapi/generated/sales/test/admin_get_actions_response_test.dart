import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminGetActionsResponse
void main() {
  final instance = AdminGetActionsResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AdminGetActionsResponse, () {
    // BuiltList<ActionDto> actions
    test('to test the property `actions`', () async {
      // TODO
    });
  });
}
