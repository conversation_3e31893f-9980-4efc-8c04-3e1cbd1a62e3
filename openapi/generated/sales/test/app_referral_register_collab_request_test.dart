import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppReferralRegisterCollabRequest
void main() {
  final instance = AppReferralRegisterCollabRequestBuilder();
  // TODO add properties to the builder and call build()

  group(AppReferralRegisterCollabRequest, () {
    // Họ tên
    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // Loại GTTT
    // String idCardType
    test('to test the property `idCardType`', () async {
      // TODO
    });

    // GTTT
    // String idCardNo
    test('to test the property `idCardNo`', () async {
      // TODO
    });

    // Số điện thoại
    // String phoneNumber
    test('to test the property `phoneNumber`', () async {
      // TODO
    });

    // Mã chi nhánh
    // String branchId
    test('to test the property `branchId`', () async {
      // TODO
    });

    // Ảnh mặt trước của giấy tờ
    // String frontCardUrl
    test('to test the property `frontCardUrl`', () async {
      // TODO
    });

    // Ảnh mặt sau của giấy tờ
    // String backCardUrl
    test('to test the property `backCardUrl`', () async {
      // TODO
    });

    // Địa chỉ thường trú
    // String permanentAddress
    test('to test the property `permanentAddress`', () async {
      // TODO
    });

    // Email
    // String email
    test('to test the property `email`', () async {
      // TODO
    });
  });
}
