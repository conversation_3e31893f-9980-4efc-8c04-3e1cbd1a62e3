import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

/// tests for AuthenticationApi
void main() {
  final instance = SalesAppApi().getAuthenticationApi();

  group(AuthenticationApi, () {
    // API login
    //
    // Login API for App Users
    //
    //Future<BaseResponseAppLoginResponse> login(AppLoginRequest appLoginRequest) async
    test('test login', () async {
      // TODO
    });

    // API logout
    //
    // Api logout for App Users
    //
    //Future<BaseResponseAppLogoutResponse> logout(AppLogoutRequest appLogoutRequest) async
    test('test logout', () async {
      // TODO
    });

    // API refresh token
    //
    // ApI for refreshing access token for App Users
    //
    //Future<BaseResponseAppRefreshTokenResponse> refreshToken(AppRefreshTokenRequest appRefreshTokenRequest) async
    test('test refreshToken', () async {
      // TODO
    });
  });
}
