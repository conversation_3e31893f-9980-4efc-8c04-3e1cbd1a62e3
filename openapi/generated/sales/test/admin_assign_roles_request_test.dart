import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminAssignRolesRequest
void main() {
  final instance = AdminAssignRolesRequestBuilder();
  // TODO add properties to the builder and call build()

  group(AdminAssignRolesRequest, () {
    // String userId
    test('to test the property `userId`', () async {
      // TODO
    });

    // BuiltList<String> roleIds
    test('to test the property `roleIds`', () async {
      // TODO
    });
  });
}
