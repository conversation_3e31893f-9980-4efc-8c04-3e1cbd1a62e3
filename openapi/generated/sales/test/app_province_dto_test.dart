import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppProvinceDto
void main() {
  final instance = AppProvinceDtoBuilder();
  // TODO add properties to the builder and call build()

  group(AppProvinceDto, () {
    // Id của tỉnh/thành phố
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // Mã GSO
    // String gsoCode
    test('to test the property `gsoCode`', () async {
      // TODO
    });

    // Tên tỉnh thành phố
    // String name
    test('to test the property `name`', () async {
      // TODO
    });
  });
}
