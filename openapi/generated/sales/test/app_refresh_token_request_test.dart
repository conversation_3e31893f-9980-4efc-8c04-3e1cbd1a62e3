import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppRefreshTokenRequest
void main() {
  final instance = AppRefreshTokenRequestBuilder();
  // TODO add properties to the builder and call build()

  group(AppRefreshTokenRequest, () {
    // The refresh token to be used for obtaining a new access token
    // String refreshToken
    test('to test the property `refreshToken`', () async {
      // TODO
    });
  });
}
