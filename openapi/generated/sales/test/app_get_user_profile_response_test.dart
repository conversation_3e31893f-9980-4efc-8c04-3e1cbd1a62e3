import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppGetUserProfileResponse
void main() {
  final instance = AppGetUserProfileResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppGetUserProfileResponse, () {
    // Mã nhân viên
    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // Unique identifier for the user
    // String cifNo
    test('to test the property `cifNo`', () async {
      // TODO
    });

    // Full name of the user
    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // Email of the user
    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // Phone number of the user
    // String phoneNumber
    test('to test the property `phoneNumber`', () async {
      // TODO
    });

    // <PERSON> <PERSON>h<PERSON>h người dùng
    // AppBranchDto branch
    test('to test the property `branch`', () async {
      // TODO
    });
  });
}
