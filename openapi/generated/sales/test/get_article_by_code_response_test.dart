import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for GetArticleByCodeResponse
void main() {
  final instance = GetArticleByCodeResponseBuilder();
  // TODO add properties to the builder and call build()

  group(GetArticleByCodeResponse, () {
    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // String title
    test('to test the property `title`', () async {
      // TODO
    });

    // String content
    test('to test the property `content`', () async {
      // TODO
    });
  });
}
