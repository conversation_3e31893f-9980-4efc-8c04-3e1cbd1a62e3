import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppGetListBranchResponse
void main() {
  final instance = AppGetListBranchResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppGetListBranchResponse, () {
    // BuiltList<AppBranchDto> branches
    test('to test the property `branches`', () async {
      // TODO
    });
  });
}
