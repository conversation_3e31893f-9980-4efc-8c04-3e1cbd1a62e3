import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppLoginResponse
void main() {
  final instance = AppLoginResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppLoginResponse, () {
    // Access token for the user
    // String accessToken
    test('to test the property `accessToken`', () async {
      // TODO
    });

    // Time in seconds until the access token expires
    // int expiresIn
    test('to test the property `expiresIn`', () async {
      // TODO
    });

    // Refresh token for obtaining a new access token
    // String refreshToken
    test('to test the property `refreshToken`', () async {
      // TODO
    });

    // Time in seconds until the refresh token expires
    // int refreshExpiresIn
    test('to test the property `refreshExpiresIn`', () async {
      // TODO
    });

    // Type of the token, typically 'Bearer'
    // String tokenType
    test('to test the property `tokenType`', () async {
      // TODO
    });

    // Mã nhân viên
    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // Unique identifier for the user
    // String cifNo
    test('to test the property `cifNo`', () async {
      // TODO
    });

    // Full name of the user
    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // Email of the user
    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // Phone number of the user
    // String phoneNumber
    test('to test the property `phoneNumber`', () async {
      // TODO
    });
  });
}
