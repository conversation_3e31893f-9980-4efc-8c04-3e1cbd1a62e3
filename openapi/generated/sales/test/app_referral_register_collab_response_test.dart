import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppReferralRegisterCollabResponse
void main() {
  final instance = AppReferralRegisterCollabResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppReferralRegisterCollabResponse, () {
    // Họ tên
    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // Tên chi nhánh
    // String branchName
    test('to test the property `branchName`', () async {
      // TODO
    });

    // Tên chức danh
    // String positionName
    test('to test the property `positionName`', () async {
      // TODO
    });

    // Mã người giới thiệu
    // String referrerCode
    test('to test the property `referrerCode`', () async {
      // TODO
    });

    // Tên người giới thiệu
    // String referrerName
    test('to test the property `referrerName`', () async {
      // TODO
    });
  });
}
