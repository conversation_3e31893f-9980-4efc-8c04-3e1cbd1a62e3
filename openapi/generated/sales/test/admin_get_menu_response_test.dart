import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminGetMenuResponse
void main() {
  final instance = AdminGetMenuResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AdminGetMenuResponse, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // String platform
    test('to test the property `platform`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // String parentId
    test('to test the property `parentId`', () async {
      // TODO
    });

    // int order
    test('to test the property `order`', () async {
      // TODO
    });

    // BuiltList<ActionDto> actions
    test('to test the property `actions`', () async {
      // TODO
    });
  });
}
