import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminCreateUserResponse
void main() {
  final instance = AdminCreateUserResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AdminCreateUserResponse, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String username
    test('to test the property `username`', () async {
      // TODO
    });

    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // String phone
    test('to test the property `phone`', () async {
      // TODO
    });

    // String departmentId
    test('to test the property `departmentId`', () async {
      // TODO
    });

    // bool enabled
    test('to test the property `enabled`', () async {
      // TODO
    });

    // DateTime createdAt
    test('to test the property `createdAt`', () async {
      // TODO
    });

    // String createdBy
    test('to test the property `createdBy`', () async {
      // TODO
    });
  });
}
