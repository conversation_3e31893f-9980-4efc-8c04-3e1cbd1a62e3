import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminGetUserRolePermissionResponse
void main() {
  final instance = AdminGetUserRolePermissionResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AdminGetUserRolePermissionResponse, () {
    // Danh sách role
    // BuiltList<RoleDto> roles
    test('to test the property `roles`', () async {
      // TODO
    });

    // Danh sách menu người dùng có thể truy cập
    // BuiltList<MenuDto> menus
    test('to test the property `menus`', () async {
      // TODO
    });
  });
}
