import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppGetListPaymentAccountResponse
void main() {
  final instance = AppGetListPaymentAccountResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppGetListPaymentAccountResponse, () {
    // Danh sách account
    // BuiltList<AccountDto> accounts
    test('to test the property `accounts`', () async {
      // TODO
    });
  });
}
