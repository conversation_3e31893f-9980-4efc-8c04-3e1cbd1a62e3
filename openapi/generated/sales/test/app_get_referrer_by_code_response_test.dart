import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppGetReferrerByCodeResponse
void main() {
  final instance = AppGetReferrerByCodeResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppGetReferrerByCodeResponse, () {
    // Mã người giới thiệu
    // String referrerCode
    test('to test the property `referrerCode`', () async {
      // TODO
    });

    // Tên người giới thiệu
    // String referrerName
    test('to test the property `referrerName`', () async {
      // TODO
    });
  });
}
