import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminGetUserByIdResponse
void main() {
  final instance = AdminGetUserByIdResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AdminGetUserByIdResponse, () {
    // bool success
    test('to test the property `success`', () async {
      // TODO
    });

    // int code
    test('to test the property `code`', () async {
      // TODO
    });

    // String message
    test('to test the property `message`', () async {
      // TODO
    });

    // JsonObject data
    test('to test the property `data`', () async {
      // TODO
    });

    // User user
    test('to test the property `user`', () async {
      // TODO
    });
  });
}
