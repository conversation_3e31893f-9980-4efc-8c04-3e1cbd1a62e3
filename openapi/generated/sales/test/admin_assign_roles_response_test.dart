import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminAssignRolesResponse
void main() {
  final instance = AdminAssignRolesResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AdminAssignRolesResponse, () {
    // String userId
    test('to test the property `userId`', () async {
      // TODO
    });

    // BuiltList<String> roleIds
    test('to test the property `roleIds`', () async {
      // TODO
    });

    // bool success
    test('to test the property `success`', () async {
      // TODO
    });
  });
}
