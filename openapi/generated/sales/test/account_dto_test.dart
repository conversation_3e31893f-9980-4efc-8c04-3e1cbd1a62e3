import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AccountDto
void main() {
  final instance = AccountDtoBuilder();
  // TODO add properties to the builder and call build()

  group(AccountDto, () {
    // String accountNo
    test('to test the property `accountNo`', () async {
      // TODO
    });

    // String accountName
    test('to test the property `accountName`', () async {
      // TODO
    });
  });
}
