import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppLogoutRequest
void main() {
  final instance = AppLogoutRequestBuilder();
  // TODO add properties to the builder and call build()

  group(AppLogoutRequest, () {
    // Access token to be blacklisted
    // String accessToken
    test('to test the property `accessToken`', () async {
      // TODO
    });

    // Refresh token to be revoked at Keycloak
    // String refreshToken
    test('to test the property `refreshToken`', () async {
      // TODO
    });
  });
}
