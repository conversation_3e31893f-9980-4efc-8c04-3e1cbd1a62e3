import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for PageUser
void main() {
  final instance = PageUserBuilder();
  // TODO add properties to the builder and call build()

  group(PageUser, () {
    // int totalPages
    test('to test the property `totalPages`', () async {
      // TODO
    });

    // int totalElements
    test('to test the property `totalElements`', () async {
      // TODO
    });

    // PageableObject pageable
    test('to test the property `pageable`', () async {
      // TODO
    });

    // int numberOfElements
    test('to test the property `numberOfElements`', () async {
      // TODO
    });

    // int size
    test('to test the property `size`', () async {
      // TODO
    });

    // BuiltList<User> content
    test('to test the property `content`', () async {
      // TODO
    });

    // int number
    test('to test the property `number`', () async {
      // TODO
    });

    // SortObject sort
    test('to test the property `sort`', () async {
      // TODO
    });

    // bool first
    test('to test the property `first`', () async {
      // TODO
    });

    // bool last
    test('to test the property `last`', () async {
      // TODO
    });

    // bool empty
    test('to test the property `empty`', () async {
      // TODO
    });
  });
}
