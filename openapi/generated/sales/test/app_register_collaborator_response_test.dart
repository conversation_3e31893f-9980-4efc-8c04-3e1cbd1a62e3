import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppRegisterCollaboratorResponse
void main() {
  final instance = AppRegisterCollaboratorResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppRegisterCollaboratorResponse, () {
    // Họ tên
    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // Tên chi nhánh
    // String branchName
    test('to test the property `branchName`', () async {
      // TODO
    });

    // Tên chức danh
    // String positionName
    test('to test the property `positionName`', () async {
      // TODO
    });
  });
}
