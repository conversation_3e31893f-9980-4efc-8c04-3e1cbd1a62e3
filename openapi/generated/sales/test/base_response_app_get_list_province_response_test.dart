import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for BaseResponseAppGetListProvinceResponse
void main() {
  final instance = BaseResponseAppGetListProvinceResponseBuilder();
  // TODO add properties to the builder and call build()

  group(BaseResponseAppGetListProvinceResponse, () {
    // bool success
    test('to test the property `success`', () async {
      // TODO
    });

    // int code
    test('to test the property `code`', () async {
      // TODO
    });

    // String message
    test('to test the property `message`', () async {
      // TODO
    });

    // AppGetListProvinceResponse data
    test('to test the property `data`', () async {
      // TODO
    });
  });
}
