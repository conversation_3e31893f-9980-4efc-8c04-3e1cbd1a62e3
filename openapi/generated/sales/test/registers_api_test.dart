import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

/// tests for RegistersApi
void main() {
  final instance = SalesAppApi().getRegistersApi();

  group(RegistersApi, () {
    // API đăng ký công tác viên trên app K+
    //
    // API đăng ký công tác viên trên app K+
    //
    //Future<BaseResponseAppKplusRegisterCollaboratorResponse> kplusRegister(AppKplusRegisterCollaboratorRequest appKplusRegisterCollaboratorRequest) async
    test('test kplusRegister', () async {
      // TODO
    });

    // API đăng ký công tác viên
    //
    // API đăng ký công tác viên
    //
    //Future<BaseResponseAppRegisterCollaboratorResponse> register(AppRegisterCollaboratorRequest appRegisterCollaboratorRequest) async
    test('test register', () async {
      // TODO
    });

    // API giới thiệu công tác viên
    //
    // API giới thiệu công tác viên
    //
    //Future<BaseResponseAppReferralRegisterCollabResponse> registerReferral(AppReferralRegisterCollabRequest appReferralRegisterCollabRequest) async
    test('test registerReferral', () async {
      // TODO
    });
  });
}
