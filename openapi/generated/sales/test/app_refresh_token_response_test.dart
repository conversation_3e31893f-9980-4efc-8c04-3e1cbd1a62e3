import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppRefreshTokenResponse
void main() {
  final instance = AppRefreshTokenResponseBuilder();
  // TODO add properties to the builder and call build()

  group(AppRefreshTokenResponse, () {
    // Access token for the user
    // String accessToken
    test('to test the property `accessToken`', () async {
      // TODO
    });

    // Time in seconds until the access token expires
    // int expiresIn
    test('to test the property `expiresIn`', () async {
      // TODO
    });

    // Refresh token for obtaining a new access token
    // String refreshToken
    test('to test the property `refreshToken`', () async {
      // TODO
    });

    // Time in seconds until the refresh token expires
    // int refreshExpiresIn
    test('to test the property `refreshExpiresIn`', () async {
      // TODO
    });

    // Type of the token, typically 'Bearer'
    // String tokenType
    test('to test the property `tokenType`', () async {
      // TODO
    });
  });
}
