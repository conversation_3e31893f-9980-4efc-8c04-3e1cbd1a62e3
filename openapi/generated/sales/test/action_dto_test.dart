import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for ActionDto
void main() {
  final instance = ActionDtoBuilder();
  // TODO add properties to the builder and call build()

  group(ActionDto, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // String description
    test('to test the property `description`', () async {
      // TODO
    });
  });
}
