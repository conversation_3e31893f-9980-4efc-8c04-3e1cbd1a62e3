import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AppWardDto
void main() {
  final instance = AppWardDtoBuilder();
  // TODO add properties to the builder and call build()

  group(AppWardDto, () {
    // Id của phường xã
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // Mã phường xã
    // String code
    test('to test the property `code`', () async {
      // TODO
    });

    // Tên phường xã
    // String name
    test('to test the property `name`', () async {
      // TODO
    });

    // ID của tỉnh thành phố
    // String provinceId
    test('to test the property `provinceId`', () async {
      // TODO
    });
  });
}
