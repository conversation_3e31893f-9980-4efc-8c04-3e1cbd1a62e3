import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for User
void main() {
  final instance = UserBuilder();
  // TODO add properties to the builder and call build()

  group(User, () {
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // DateTime createdAt
    test('to test the property `createdAt`', () async {
      // TODO
    });

    // DateTime updatedAt
    test('to test the property `updatedAt`', () async {
      // TODO
    });

    // String createdBy
    test('to test the property `createdBy`', () async {
      // TODO
    });

    // String updatedBy
    test('to test the property `updatedBy`', () async {
      // TODO
    });

    // bool isDeleted
    test('to test the property `isDeleted`', () async {
      // TODO
    });

    // String keycloakId
    test('to test the property `keycloakId`', () async {
      // TODO
    });

    // String username
    test('to test the property `username`', () async {
      // TODO
    });

    // String status
    test('to test the property `status`', () async {
      // TODO
    });
  });
}
