import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminUpdateUserRequest
void main() {
  final instance = AdminUpdateUserRequestBuilder();
  // TODO add properties to the builder and call build()

  group(AdminUpdateUserRequest, () {
    // String username
    test('to test the property `username`', () async {
      // TODO
    });

    // String fullName
    test('to test the property `fullName`', () async {
      // TODO
    });

    // String email
    test('to test the property `email`', () async {
      // TODO
    });

    // String phone
    test('to test the property `phone`', () async {
      // TODO
    });

    // String departmentId
    test('to test the property `departmentId`', () async {
      // TODO
    });

    // bool enabled
    test('to test the property `enabled`', () async {
      // TODO
    });
  });
}
