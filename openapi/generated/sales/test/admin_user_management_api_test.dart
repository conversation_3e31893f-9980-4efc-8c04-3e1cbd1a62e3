import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

/// tests for AdminUserManagementApi
void main() {
  final instance = SalesAppApi().getAdminUserManagementApi();

  group(AdminUserManagementApi, () {
    // Assign roles
    //
    // Admin can assign roles to users
    //
    //Future<BaseResponseAdminAssignRolesResponse> assignRoles(String id, AdminAssignRolesRequest adminAssignRolesRequest) async
    test('test assignRoles', () async {
      // TODO
    });

    // Create user
    //
    // Admin can create any user
    //
    //Future<BaseResponseAdminCreateUserResponse> createUser(AdminCreateUserRequest adminCreateUserRequest) async
    test('test createUser', () async {
      // TODO
    });

    // Delete user
    //
    // Admin can delete any user
    //
    //Future<BaseResponseAdminDeleteUserResponse> deleteUser(String id) async
    test('test deleteUser', () async {
      // TODO
    });

    // Get user by ID
    //
    // Admin can get any user
    //
    //Future<BaseResponseAdminGetUserByIdResponse> getUserById(String id) async
    test('test getUserById', () async {
      // TODO
    });

    // Get menus
    //
    // Lấy danh sách menu
    //
    //Future<BaseResponseAdminGetUserRolePermissionResponse> getUserRolePermissions() async
    test('test getUserRolePermissions', () async {
      // TODO
    });

    // Import users
    //
    // Admin can import multiple users
    //
    //Future<BaseResponseAdminImportUsersResponse> importUsers(AdminImportUsersRequest adminImportUsersRequest) async
    test('test importUsers', () async {
      // TODO
    });

    // Reset password
    //
    // Admin can reset user passwords
    //
    //Future<BaseResponseAdminResetPasswordResponse> resetPassword(String id) async
    test('test resetPassword', () async {
      // TODO
    });

    // Search users
    //
    // Admin can search all users
    //
    //Future<BaseResponseAdminSearchUsersResponse> search(AdminSearchUsersRequest arg0) async
    test('test search', () async {
      // TODO
    });

    // Update user
    //
    // Admin can update any user
    //
    //Future<BaseResponseAdminUpdateUserResponse> updateUser(String id, AdminUpdateUserRequest adminUpdateUserRequest) async
    test('test updateUser', () async {
      // TODO
    });
  });
}
