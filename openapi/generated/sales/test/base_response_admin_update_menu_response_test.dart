import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for BaseResponseAdminUpdateMenuResponse
void main() {
  final instance = BaseResponseAdminUpdateMenuResponseBuilder();
  // TODO add properties to the builder and call build()

  group(BaseResponseAdminUpdateMenuResponse, () {
    // bool success
    test('to test the property `success`', () async {
      // TODO
    });

    // int code
    test('to test the property `code`', () async {
      // TODO
    });

    // String message
    test('to test the property `message`', () async {
      // TODO
    });

    // AdminUpdateMenuResponse data
    test('to test the property `data`', () async {
      // TODO
    });
  });
}
