import 'package:test/test.dart';
import 'package:sales_app_api/sales_app_api.dart';

// tests for AdminSearchUsersRequest
void main() {
  final instance = AdminSearchUsersRequestBuilder();
  // TODO add properties to the builder and call build()

  group(AdminSearchUsersRequest, () {
    // int page
    test('to test the property `page`', () async {
      // TODO
    });

    // int size
    test('to test the property `size`', () async {
      // TODO
    });

    // String orderBy
    test('to test the property `orderBy`', () async {
      // TODO
    });

    // String orderDir
    test('to test the property `orderDir`', () async {
      // TODO
    });

    // String keyword
    test('to test the property `keyword`', () async {
      // TODO
    });

    // String departmentId
    test('to test the property `departmentId`', () async {
      // TODO
    });

    // bool enabled
    test('to test the property `enabled`', () async {
      // TODO
    });
  });
}
