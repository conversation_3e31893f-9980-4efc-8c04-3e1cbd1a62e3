# sales_app_api (EXPERIMENTAL)
Tài liệu API

This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0
- Generator version: 7.9.0
- Build package: org.openapitools.codegen.languages.DartDioClientCodegen

## Requirements

* Dart 2.15.0+ or Flutter 2.8.0+
* Dio 5.0.0+ (https://pub.dev/packages/dio)

## Installation & Usage

### pub.dev
To use the package from [pub.dev](https://pub.dev), please include the following in pubspec.yaml
```yaml
dependencies:
  sales_app_api: 1.0.0
```

### Github
If this Dart package is published to Github, please include the following in pubspec.yaml
```yaml
dependencies:
  sales_app_api:
    git:
      url: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
      #ref: main
```

### Local development
To use the package from your local drive, please include the following in pubspec.yaml
```yaml
dependencies:
  sales_app_api:
    path: /path/to/sales_app_api
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:sales_app_api/sales_app_api.dart';


final api = SalesAppApi().getAccountApi();
final AppGetListPaymentAccountRequest appGetListPaymentAccountRequest = ; // AppGetListPaymentAccountRequest | 

try {
    final response = await api.getPaymentAccount(appGetListPaymentAccountRequest);
    print(response);
} catch on DioException (e) {
    print("Exception when calling AccountApi->getPaymentAccount: $e\n");
}

```

## Documentation for API Endpoints

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
[*AccountApi*](doc/AccountApi.md) | [**getPaymentAccount**](doc/AccountApi.md#getpaymentaccount) | **POST** /system/api/v1/account/getPaymentAccount | API lây danh sách tài khoản thanh toán theo số GTTT
[*AdminMenuManagementApi*](doc/AdminMenuManagementApi.md) | [**createMenu**](doc/AdminMenuManagementApi.md#createmenu) | **POST** /system/admin/api/v1/menu | API tạo menu
[*AdminMenuManagementApi*](doc/AdminMenuManagementApi.md) | [**deleteMenu**](doc/AdminMenuManagementApi.md#deletemenu) | **DELETE** /system/admin/api/v1/menu/{id} | API xóa menu
[*AdminMenuManagementApi*](doc/AdminMenuManagementApi.md) | [**getActions**](doc/AdminMenuManagementApi.md#getactions) | **GET** /system/admin/api/v1/actions | API lấy danh sách action
[*AdminMenuManagementApi*](doc/AdminMenuManagementApi.md) | [**getMenu**](doc/AdminMenuManagementApi.md#getmenu) | **GET** /system/admin/api/v1/menu/{id} | API lấy chi tiết menu
[*AdminMenuManagementApi*](doc/AdminMenuManagementApi.md) | [**listMenus**](doc/AdminMenuManagementApi.md#listmenus) | **GET** /system/admin/api/v1/menus | API lấy danh sách menu hierarchical
[*AdminMenuManagementApi*](doc/AdminMenuManagementApi.md) | [**listParentMenus**](doc/AdminMenuManagementApi.md#listparentmenus) | **GET** /system/admin/api/v1/menus/parents | API lấy danh sách parent menu
[*AdminMenuManagementApi*](doc/AdminMenuManagementApi.md) | [**updateMenu**](doc/AdminMenuManagementApi.md#updatemenu) | **PUT** /system/admin/api/v1/menu/{id} | API cập nhật menu
[*AdminUserManagementApi*](doc/AdminUserManagementApi.md) | [**assignRoles**](doc/AdminUserManagementApi.md#assignroles) | **PUT** /system/admin/api/v1/users/{id}/roles | Assign roles
[*AdminUserManagementApi*](doc/AdminUserManagementApi.md) | [**createUser**](doc/AdminUserManagementApi.md#createuser) | **POST** /system/admin/api/v1/users | Create user
[*AdminUserManagementApi*](doc/AdminUserManagementApi.md) | [**deleteUser**](doc/AdminUserManagementApi.md#deleteuser) | **DELETE** /system/admin/api/v1/users/{id} | Delete user
[*AdminUserManagementApi*](doc/AdminUserManagementApi.md) | [**getUserById**](doc/AdminUserManagementApi.md#getuserbyid) | **GET** /system/admin/api/v1/users/{id} | Get user by ID
[*AdminUserManagementApi*](doc/AdminUserManagementApi.md) | [**getUserRolePermissions**](doc/AdminUserManagementApi.md#getuserrolepermissions) | **GET** /system/admin/api/v1/users/rolePermissions | Get menus
[*AdminUserManagementApi*](doc/AdminUserManagementApi.md) | [**importUsers**](doc/AdminUserManagementApi.md#importusers) | **POST** /system/admin/api/v1/users/import | Import users
[*AdminUserManagementApi*](doc/AdminUserManagementApi.md) | [**resetPassword**](doc/AdminUserManagementApi.md#resetpassword) | **POST** /system/admin/api/v1/users/{id}/reset-password | Reset password
[*AdminUserManagementApi*](doc/AdminUserManagementApi.md) | [**search**](doc/AdminUserManagementApi.md#search) | **GET** /system/admin/api/v1/users | Search users
[*AdminUserManagementApi*](doc/AdminUserManagementApi.md) | [**updateUser**](doc/AdminUserManagementApi.md#updateuser) | **PUT** /system/admin/api/v1/users/{id} | Update user
[*ArticleApi*](doc/ArticleApi.md) | [**getArticle**](doc/ArticleApi.md#getarticle) | **GET** /system/api/v1/article/getByCode | API lấy nội dung đã đc cấu hình
[*AuthenticationApi*](doc/AuthenticationApi.md) | [**login**](doc/AuthenticationApi.md#login) | **POST** /system/api/v1/auth/login | API login
[*AuthenticationApi*](doc/AuthenticationApi.md) | [**logout**](doc/AuthenticationApi.md#logout) | **POST** /system/api/v1/auth/logout | API logout
[*AuthenticationApi*](doc/AuthenticationApi.md) | [**refreshToken**](doc/AuthenticationApi.md#refreshtoken) | **POST** /system/api/v1/auth/refreshToken | API refresh token
[*BranchApi*](doc/BranchApi.md) | [**list2**](doc/BranchApi.md#list2) | **GET** /system/api/v1/branch | API lấy danh sách chi nhánh
[*ProvinceApi*](doc/ProvinceApi.md) | [**list1**](doc/ProvinceApi.md#list1) | **GET** /system/api/v1/province | API lấy danh sách tỉnh/thành phố
[*RegistersApi*](doc/RegistersApi.md) | [**kplusRegister**](doc/RegistersApi.md#kplusregister) | **POST** /system/api/v1/registers/kplus/collaborators | API đăng ký công tác viên trên app K+
[*RegistersApi*](doc/RegistersApi.md) | [**register**](doc/RegistersApi.md#register) | **POST** /system/api/v1/registers/collaborators | API đăng ký công tác viên
[*RegistersApi*](doc/RegistersApi.md) | [**registerReferral**](doc/RegistersApi.md#registerreferral) | **POST** /system/api/v1/registers/collaborators/referrals | API giới thiệu công tác viên
[*SystemConfigApi*](doc/SystemConfigApi.md) | [**getSystemConfigsByGroupCode**](doc/SystemConfigApi.md#getsystemconfigsbygroupcode) | **GET** /system/api/v1/system-configs/getByCode | API lấy danh sách cấu hình theo group code
[*UsersApi*](doc/UsersApi.md) | [**getProfile**](doc/UsersApi.md#getprofile) | **GET** /system/api/v1/users/profile | Api lấy thông tin profile của user đang đăng nhập
[*UsersApi*](doc/UsersApi.md) | [**getReferrerByCode**](doc/UsersApi.md#getreferrerbycode) | **GET** /system/api/v1/users/getReferrerByCode | Api lấy thông tin người giới thiệu
[*WardApi*](doc/WardApi.md) | [**list**](doc/WardApi.md#list) | **GET** /system/api/v1/ward | API lấy danh sách phường xã


## Documentation For Models

 - [AccountDto](doc/AccountDto.md)
 - [ActionDto](doc/ActionDto.md)
 - [AdminAssignRolesRequest](doc/AdminAssignRolesRequest.md)
 - [AdminAssignRolesResponse](doc/AdminAssignRolesResponse.md)
 - [AdminCreateMenuRequest](doc/AdminCreateMenuRequest.md)
 - [AdminCreateMenuResponse](doc/AdminCreateMenuResponse.md)
 - [AdminCreateUserRequest](doc/AdminCreateUserRequest.md)
 - [AdminCreateUserResponse](doc/AdminCreateUserResponse.md)
 - [AdminDeleteMenuResponse](doc/AdminDeleteMenuResponse.md)
 - [AdminDeleteUserResponse](doc/AdminDeleteUserResponse.md)
 - [AdminGetActionsResponse](doc/AdminGetActionsResponse.md)
 - [AdminGetMenuResponse](doc/AdminGetMenuResponse.md)
 - [AdminGetUserByIdResponse](doc/AdminGetUserByIdResponse.md)
 - [AdminGetUserRolePermissionResponse](doc/AdminGetUserRolePermissionResponse.md)
 - [AdminImportUsersRequest](doc/AdminImportUsersRequest.md)
 - [AdminImportUsersResponse](doc/AdminImportUsersResponse.md)
 - [AdminListMenusResponse](doc/AdminListMenusResponse.md)
 - [AdminListParentMenusResponse](doc/AdminListParentMenusResponse.md)
 - [AdminResetPasswordResponse](doc/AdminResetPasswordResponse.md)
 - [AdminSearchUsersRequest](doc/AdminSearchUsersRequest.md)
 - [AdminSearchUsersResponse](doc/AdminSearchUsersResponse.md)
 - [AdminUpdateMenuRequest](doc/AdminUpdateMenuRequest.md)
 - [AdminUpdateMenuResponse](doc/AdminUpdateMenuResponse.md)
 - [AdminUpdateUserRequest](doc/AdminUpdateUserRequest.md)
 - [AdminUpdateUserResponse](doc/AdminUpdateUserResponse.md)
 - [AppBranchDto](doc/AppBranchDto.md)
 - [AppGetListBranchResponse](doc/AppGetListBranchResponse.md)
 - [AppGetListPaymentAccountRequest](doc/AppGetListPaymentAccountRequest.md)
 - [AppGetListPaymentAccountResponse](doc/AppGetListPaymentAccountResponse.md)
 - [AppGetListProvinceResponse](doc/AppGetListProvinceResponse.md)
 - [AppGetListWardResponse](doc/AppGetListWardResponse.md)
 - [AppGetReferrerByCodeResponse](doc/AppGetReferrerByCodeResponse.md)
 - [AppGetSystemConfigsByGroupCodeResponse](doc/AppGetSystemConfigsByGroupCodeResponse.md)
 - [AppGetUserProfileResponse](doc/AppGetUserProfileResponse.md)
 - [AppKplusRegisterCollaboratorRequest](doc/AppKplusRegisterCollaboratorRequest.md)
 - [AppKplusRegisterCollaboratorResponse](doc/AppKplusRegisterCollaboratorResponse.md)
 - [AppLoginRequest](doc/AppLoginRequest.md)
 - [AppLoginResponse](doc/AppLoginResponse.md)
 - [AppLogoutRequest](doc/AppLogoutRequest.md)
 - [AppLogoutResponse](doc/AppLogoutResponse.md)
 - [AppProvinceDto](doc/AppProvinceDto.md)
 - [AppReferralRegisterCollabRequest](doc/AppReferralRegisterCollabRequest.md)
 - [AppReferralRegisterCollabResponse](doc/AppReferralRegisterCollabResponse.md)
 - [AppRefreshTokenRequest](doc/AppRefreshTokenRequest.md)
 - [AppRefreshTokenResponse](doc/AppRefreshTokenResponse.md)
 - [AppRegisterCollaboratorRequest](doc/AppRegisterCollaboratorRequest.md)
 - [AppRegisterCollaboratorResponse](doc/AppRegisterCollaboratorResponse.md)
 - [AppWardDto](doc/AppWardDto.md)
 - [BaseResponseAdminAssignRolesResponse](doc/BaseResponseAdminAssignRolesResponse.md)
 - [BaseResponseAdminCreateMenuResponse](doc/BaseResponseAdminCreateMenuResponse.md)
 - [BaseResponseAdminCreateUserResponse](doc/BaseResponseAdminCreateUserResponse.md)
 - [BaseResponseAdminDeleteMenuResponse](doc/BaseResponseAdminDeleteMenuResponse.md)
 - [BaseResponseAdminDeleteUserResponse](doc/BaseResponseAdminDeleteUserResponse.md)
 - [BaseResponseAdminGetActionsResponse](doc/BaseResponseAdminGetActionsResponse.md)
 - [BaseResponseAdminGetMenuResponse](doc/BaseResponseAdminGetMenuResponse.md)
 - [BaseResponseAdminGetUserByIdResponse](doc/BaseResponseAdminGetUserByIdResponse.md)
 - [BaseResponseAdminGetUserRolePermissionResponse](doc/BaseResponseAdminGetUserRolePermissionResponse.md)
 - [BaseResponseAdminImportUsersResponse](doc/BaseResponseAdminImportUsersResponse.md)
 - [BaseResponseAdminListMenusResponse](doc/BaseResponseAdminListMenusResponse.md)
 - [BaseResponseAdminListParentMenusResponse](doc/BaseResponseAdminListParentMenusResponse.md)
 - [BaseResponseAdminResetPasswordResponse](doc/BaseResponseAdminResetPasswordResponse.md)
 - [BaseResponseAdminSearchUsersResponse](doc/BaseResponseAdminSearchUsersResponse.md)
 - [BaseResponseAdminUpdateMenuResponse](doc/BaseResponseAdminUpdateMenuResponse.md)
 - [BaseResponseAdminUpdateUserResponse](doc/BaseResponseAdminUpdateUserResponse.md)
 - [BaseResponseAppGetListBranchResponse](doc/BaseResponseAppGetListBranchResponse.md)
 - [BaseResponseAppGetListPaymentAccountResponse](doc/BaseResponseAppGetListPaymentAccountResponse.md)
 - [BaseResponseAppGetListProvinceResponse](doc/BaseResponseAppGetListProvinceResponse.md)
 - [BaseResponseAppGetListWardResponse](doc/BaseResponseAppGetListWardResponse.md)
 - [BaseResponseAppGetReferrerByCodeResponse](doc/BaseResponseAppGetReferrerByCodeResponse.md)
 - [BaseResponseAppGetSystemConfigsByGroupCodeResponse](doc/BaseResponseAppGetSystemConfigsByGroupCodeResponse.md)
 - [BaseResponseAppGetUserProfileResponse](doc/BaseResponseAppGetUserProfileResponse.md)
 - [BaseResponseAppKplusRegisterCollaboratorResponse](doc/BaseResponseAppKplusRegisterCollaboratorResponse.md)
 - [BaseResponseAppLoginResponse](doc/BaseResponseAppLoginResponse.md)
 - [BaseResponseAppLogoutResponse](doc/BaseResponseAppLogoutResponse.md)
 - [BaseResponseAppReferralRegisterCollabResponse](doc/BaseResponseAppReferralRegisterCollabResponse.md)
 - [BaseResponseAppRefreshTokenResponse](doc/BaseResponseAppRefreshTokenResponse.md)
 - [BaseResponseAppRegisterCollaboratorResponse](doc/BaseResponseAppRegisterCollaboratorResponse.md)
 - [BaseResponseGetArticleByCodeResponse](doc/BaseResponseGetArticleByCodeResponse.md)
 - [GetArticleByCodeResponse](doc/GetArticleByCodeResponse.md)
 - [MenuDto](doc/MenuDto.md)
 - [PageUser](doc/PageUser.md)
 - [PageableObject](doc/PageableObject.md)
 - [RoleDto](doc/RoleDto.md)
 - [SortObject](doc/SortObject.md)
 - [SystemConfigDto](doc/SystemConfigDto.md)
 - [User](doc/User.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### bearerAuth

- **Type**: HTTP Bearer Token authentication (JWT)


## Author



