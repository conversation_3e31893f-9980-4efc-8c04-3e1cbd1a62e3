# sales_app_api.api.WardApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**list**](WardApi.md#list) | **GET** /system/api/v1/ward | API lấy danh sách phường xã


# **list**
> BaseResponseAppGetListWardResponse list(provinceId)

API lấy danh sách phường xã

API lấy danh sách phường xã

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getWardApi();
final String provinceId = 01; // String | Mã tỉnh thành phố

try {
    final response = api.list(provinceId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling WardApi->list: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **provinceId** | **String**| Mã tỉnh thành phố | [optional] 

### Return type

[**BaseResponseAppGetListWardResponse**](BaseResponseAppGetListWardResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

