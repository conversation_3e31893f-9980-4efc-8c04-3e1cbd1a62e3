# sales_app_api.model.PageableObject

## Load the model package
```dart
import 'package:sales_app_api/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**unpaged** | **bool** |  | [optional] 
**pageNumber** | **int** |  | [optional] 
**paged** | **bool** |  | [optional] 
**pageSize** | **int** |  | [optional] 
**offset** | **int** |  | [optional] 
**sort** | [**SortObject**](SortObject.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


