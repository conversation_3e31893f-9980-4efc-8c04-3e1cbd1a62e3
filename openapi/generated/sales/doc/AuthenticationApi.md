# sales_app_api.api.AuthenticationApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**login**](AuthenticationApi.md#login) | **POST** /system/api/v1/auth/login | API login
[**logout**](AuthenticationApi.md#logout) | **POST** /system/api/v1/auth/logout | API logout
[**refreshToken**](AuthenticationApi.md#refreshtoken) | **POST** /system/api/v1/auth/refreshToken | API refresh token


# **login**
> BaseResponseAppLoginResponse login(appLoginRequest)

API login

Login API for App Users

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAuthenticationApi();
final AppLoginRequest appLoginRequest = ; // AppLoginRequest | 

try {
    final response = api.login(appLoginRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AuthenticationApi->login: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **appLoginRequest** | [**AppLoginRequest**](AppLoginRequest.md)|  | 

### Return type

[**BaseResponseAppLoginResponse**](BaseResponseAppLoginResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **logout**
> BaseResponseAppLogoutResponse logout(appLogoutRequest)

API logout

Api logout for App Users

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAuthenticationApi();
final AppLogoutRequest appLogoutRequest = ; // AppLogoutRequest | 

try {
    final response = api.logout(appLogoutRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AuthenticationApi->logout: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **appLogoutRequest** | [**AppLogoutRequest**](AppLogoutRequest.md)|  | 

### Return type

[**BaseResponseAppLogoutResponse**](BaseResponseAppLogoutResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **refreshToken**
> BaseResponseAppRefreshTokenResponse refreshToken(appRefreshTokenRequest)

API refresh token

ApI for refreshing access token for App Users

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAuthenticationApi();
final AppRefreshTokenRequest appRefreshTokenRequest = ; // AppRefreshTokenRequest | 

try {
    final response = api.refreshToken(appRefreshTokenRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AuthenticationApi->refreshToken: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **appRefreshTokenRequest** | [**AppRefreshTokenRequest**](AppRefreshTokenRequest.md)|  | 

### Return type

[**BaseResponseAppRefreshTokenResponse**](BaseResponseAppRefreshTokenResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

