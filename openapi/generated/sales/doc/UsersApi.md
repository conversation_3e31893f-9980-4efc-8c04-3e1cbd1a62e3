# sales_app_api.api.UsersApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getProfile**](UsersApi.md#getprofile) | **GET** /system/api/v1/users/profile | Api lấy thông tin profile của user đang đăng nhập
[**getReferrerByCode**](UsersApi.md#getreferrerbycode) | **GET** /system/api/v1/users/getReferrerByCode | Api lấy thông tin người giới thiệu


# **getProfile**
> BaseResponseAppGetUserProfileResponse getProfile()

Api lấy thông tin profile của user đang đăng nhập

Api lấy thông tin profile của user đang đăng nhập

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getUsersApi();

try {
    final response = api.getProfile();
    print(response);
} catch on DioException (e) {
    print('Exception when calling UsersApi->getProfile: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BaseResponseAppGetUserProfileResponse**](BaseResponseAppGetUserProfileResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getReferrerByCode**
> BaseResponseAppGetReferrerByCodeResponse getReferrerByCode(referrerCode)

Api lấy thông tin người giới thiệu

Api lấy thông tin người giới thiệu

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getUsersApi();
final String referrerCode = referrerCode_example; // String | ID of user to get information

try {
    final response = api.getReferrerByCode(referrerCode);
    print(response);
} catch on DioException (e) {
    print('Exception when calling UsersApi->getReferrerByCode: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **referrerCode** | **String**| ID of user to get information | 

### Return type

[**BaseResponseAppGetReferrerByCodeResponse**](BaseResponseAppGetReferrerByCodeResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

