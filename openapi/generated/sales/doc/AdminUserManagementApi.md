# sales_app_api.api.AdminUserManagementApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**assignRoles**](AdminUserManagementApi.md#assignroles) | **PUT** /system/admin/api/v1/users/{id}/roles | Assign roles
[**createUser**](AdminUserManagementApi.md#createuser) | **POST** /system/admin/api/v1/users | Create user
[**deleteUser**](AdminUserManagementApi.md#deleteuser) | **DELETE** /system/admin/api/v1/users/{id} | Delete user
[**getUserById**](AdminUserManagementApi.md#getuserbyid) | **GET** /system/admin/api/v1/users/{id} | Get user by ID
[**getUserRolePermissions**](AdminUserManagementApi.md#getuserrolepermissions) | **GET** /system/admin/api/v1/users/rolePermissions | Get menus
[**importUsers**](AdminUserManagementApi.md#importusers) | **POST** /system/admin/api/v1/users/import | Import users
[**resetPassword**](AdminUserManagementApi.md#resetpassword) | **POST** /system/admin/api/v1/users/{id}/reset-password | Reset password
[**search**](AdminUserManagementApi.md#search) | **GET** /system/admin/api/v1/users | Search users
[**updateUser**](AdminUserManagementApi.md#updateuser) | **PUT** /system/admin/api/v1/users/{id} | Update user


# **assignRoles**
> BaseResponseAdminAssignRolesResponse assignRoles(id, adminAssignRolesRequest)

Assign roles

Admin can assign roles to users

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminUserManagementApi();
final String id = 38400000-8cf0-11bd-b23e-10b96e4ef00d; // String | 
final AdminAssignRolesRequest adminAssignRolesRequest = ; // AdminAssignRolesRequest | 

try {
    final response = api.assignRoles(id, adminAssignRolesRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminUserManagementApi->assignRoles: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **adminAssignRolesRequest** | [**AdminAssignRolesRequest**](AdminAssignRolesRequest.md)|  | 

### Return type

[**BaseResponseAdminAssignRolesResponse**](BaseResponseAdminAssignRolesResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **createUser**
> BaseResponseAdminCreateUserResponse createUser(adminCreateUserRequest)

Create user

Admin can create any user

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminUserManagementApi();
final AdminCreateUserRequest adminCreateUserRequest = ; // AdminCreateUserRequest | 

try {
    final response = api.createUser(adminCreateUserRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminUserManagementApi->createUser: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **adminCreateUserRequest** | [**AdminCreateUserRequest**](AdminCreateUserRequest.md)|  | 

### Return type

[**BaseResponseAdminCreateUserResponse**](BaseResponseAdminCreateUserResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteUser**
> BaseResponseAdminDeleteUserResponse deleteUser(id)

Delete user

Admin can delete any user

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminUserManagementApi();
final String id = 38400000-8cf0-11bd-b23e-10b96e4ef00d; // String | 

try {
    final response = api.deleteUser(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminUserManagementApi->deleteUser: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**BaseResponseAdminDeleteUserResponse**](BaseResponseAdminDeleteUserResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getUserById**
> BaseResponseAdminGetUserByIdResponse getUserById(id)

Get user by ID

Admin can get any user

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminUserManagementApi();
final String id = 38400000-8cf0-11bd-b23e-10b96e4ef00d; // String | 

try {
    final response = api.getUserById(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminUserManagementApi->getUserById: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**BaseResponseAdminGetUserByIdResponse**](BaseResponseAdminGetUserByIdResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getUserRolePermissions**
> BaseResponseAdminGetUserRolePermissionResponse getUserRolePermissions()

Get menus

Lấy danh sách menu

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminUserManagementApi();

try {
    final response = api.getUserRolePermissions();
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminUserManagementApi->getUserRolePermissions: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BaseResponseAdminGetUserRolePermissionResponse**](BaseResponseAdminGetUserRolePermissionResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **importUsers**
> BaseResponseAdminImportUsersResponse importUsers(adminImportUsersRequest)

Import users

Admin can import multiple users

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminUserManagementApi();
final AdminImportUsersRequest adminImportUsersRequest = ; // AdminImportUsersRequest | 

try {
    final response = api.importUsers(adminImportUsersRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminUserManagementApi->importUsers: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **adminImportUsersRequest** | [**AdminImportUsersRequest**](AdminImportUsersRequest.md)|  | 

### Return type

[**BaseResponseAdminImportUsersResponse**](BaseResponseAdminImportUsersResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **resetPassword**
> BaseResponseAdminResetPasswordResponse resetPassword(id)

Reset password

Admin can reset user passwords

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminUserManagementApi();
final String id = 38400000-8cf0-11bd-b23e-10b96e4ef00d; // String | 

try {
    final response = api.resetPassword(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminUserManagementApi->resetPassword: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**BaseResponseAdminResetPasswordResponse**](BaseResponseAdminResetPasswordResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **search**
> BaseResponseAdminSearchUsersResponse search(arg0)

Search users

Admin can search all users

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminUserManagementApi();
final AdminSearchUsersRequest arg0 = ; // AdminSearchUsersRequest | 

try {
    final response = api.search(arg0);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminUserManagementApi->search: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **arg0** | [**AdminSearchUsersRequest**](.md)|  | 

### Return type

[**BaseResponseAdminSearchUsersResponse**](BaseResponseAdminSearchUsersResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateUser**
> BaseResponseAdminUpdateUserResponse updateUser(id, adminUpdateUserRequest)

Update user

Admin can update any user

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminUserManagementApi();
final String id = 38400000-8cf0-11bd-b23e-10b96e4ef00d; // String | 
final AdminUpdateUserRequest adminUpdateUserRequest = ; // AdminUpdateUserRequest | 

try {
    final response = api.updateUser(id, adminUpdateUserRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminUserManagementApi->updateUser: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **adminUpdateUserRequest** | [**AdminUpdateUserRequest**](AdminUpdateUserRequest.md)|  | 

### Return type

[**BaseResponseAdminUpdateUserResponse**](BaseResponseAdminUpdateUserResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

