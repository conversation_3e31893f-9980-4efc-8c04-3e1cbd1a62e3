# sales_app_api.model.AppLoginResponse

## Load the model package
```dart
import 'package:sales_app_api/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**accessToken** | **String** | Access token for the user | [optional] 
**expiresIn** | **int** | Time in seconds until the access token expires | [optional] 
**refreshToken** | **String** | Refresh token for obtaining a new access token | [optional] 
**refreshExpiresIn** | **int** | Time in seconds until the refresh token expires | [optional] 
**tokenType** | **String** | Type of the token, typically 'Bearer' | [optional] 
**code** | **String** | Mã nhân viên | [optional] 
**cifNo** | **String** | Unique identifier for the user | [optional] 
**fullName** | **String** | Full name of the user | [optional] 
**email** | **String** | Email of the user | [optional] 
**phoneNumber** | **String** | Phone number of the user | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


