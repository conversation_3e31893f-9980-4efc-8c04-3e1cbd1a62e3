# sales_app_api.api.RegistersApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**kplusRegister**](RegistersApi.md#kplusregister) | **POST** /system/api/v1/registers/kplus/collaborators | API đăng ký công tác viên trên app K+
[**register**](RegistersApi.md#register) | **POST** /system/api/v1/registers/collaborators | API đăng ký công tác viên
[**registerReferral**](RegistersApi.md#registerreferral) | **POST** /system/api/v1/registers/collaborators/referrals | API giới thiệu công tác viên


# **kplusRegister**
> BaseResponseAppKplusRegisterCollaboratorResponse kplusRegister(appKplusRegisterCollaboratorRequest)

API đăng ký công tác viên trên app K+

API đăng ký công tác viên trên app K+

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getRegistersApi();
final AppKplusRegisterCollaboratorRequest appKplusRegisterCollaboratorRequest = ; // AppKplusRegisterCollaboratorRequest | 

try {
    final response = api.kplusRegister(appKplusRegisterCollaboratorRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling RegistersApi->kplusRegister: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **appKplusRegisterCollaboratorRequest** | [**AppKplusRegisterCollaboratorRequest**](AppKplusRegisterCollaboratorRequest.md)|  | 

### Return type

[**BaseResponseAppKplusRegisterCollaboratorResponse**](BaseResponseAppKplusRegisterCollaboratorResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **register**
> BaseResponseAppRegisterCollaboratorResponse register(appRegisterCollaboratorRequest)

API đăng ký công tác viên

API đăng ký công tác viên

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getRegistersApi();
final AppRegisterCollaboratorRequest appRegisterCollaboratorRequest = ; // AppRegisterCollaboratorRequest | 

try {
    final response = api.register(appRegisterCollaboratorRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling RegistersApi->register: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **appRegisterCollaboratorRequest** | [**AppRegisterCollaboratorRequest**](AppRegisterCollaboratorRequest.md)|  | 

### Return type

[**BaseResponseAppRegisterCollaboratorResponse**](BaseResponseAppRegisterCollaboratorResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **registerReferral**
> BaseResponseAppReferralRegisterCollabResponse registerReferral(appReferralRegisterCollabRequest)

API giới thiệu công tác viên

API giới thiệu công tác viên

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getRegistersApi();
final AppReferralRegisterCollabRequest appReferralRegisterCollabRequest = ; // AppReferralRegisterCollabRequest | 

try {
    final response = api.registerReferral(appReferralRegisterCollabRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling RegistersApi->registerReferral: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **appReferralRegisterCollabRequest** | [**AppReferralRegisterCollabRequest**](AppReferralRegisterCollabRequest.md)|  | 

### Return type

[**BaseResponseAppReferralRegisterCollabResponse**](BaseResponseAppReferralRegisterCollabResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

