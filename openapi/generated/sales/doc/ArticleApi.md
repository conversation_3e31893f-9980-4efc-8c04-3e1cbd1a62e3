# sales_app_api.api.ArticleApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getArticle**](ArticleApi.md#getarticle) | **GET** /system/api/v1/article/getByCode | API lấy nội dung đã đc cấu hình


# **getArticle**
> BaseResponseGetArticleByCodeResponse getArticle(code)

API lấy nội dung đã đc cấu hình

API lấy nội dung

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getArticleApi();
final String code = code_example; // String | 

try {
    final response = api.getArticle(code);
    print(response);
} catch on DioException (e) {
    print('Exception when calling ArticleApi->getArticle: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **code** | **String**|  | 

### Return type

[**BaseResponseGetArticleByCodeResponse**](BaseResponseGetArticleByCodeResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

