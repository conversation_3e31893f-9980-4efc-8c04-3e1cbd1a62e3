# sales_app_api.api.BranchApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**list2**](BranchApi.md#list2) | **GET** /system/api/v1/branch | API lấy danh sách chi nhánh


# **list2**
> BaseResponseAppGetListBranchResponse list2(provinceId)

API lấy danh sách chi nhánh

API lấy danh sách chi nhánh

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getBranchApi();
final String provinceId = 01; // String | Mã tỉnh thành phố

try {
    final response = api.list2(provinceId);
    print(response);
} catch on DioException (e) {
    print('Exception when calling BranchApi->list2: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **provinceId** | **String**| Mã tỉnh thành phố | [optional] 

### Return type

[**BaseResponseAppGetListBranchResponse**](BaseResponseAppGetListBranchResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

