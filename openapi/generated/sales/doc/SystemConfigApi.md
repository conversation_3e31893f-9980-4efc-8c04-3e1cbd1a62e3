# sales_app_api.api.SystemConfigApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getSystemConfigsByGroupCode**](SystemConfigApi.md#getsystemconfigsbygroupcode) | **GET** /system/api/v1/system-configs/getByCode | API lấy danh sách cấu hình theo group code


# **getSystemConfigsByGroupCode**
> BaseResponseAppGetSystemConfigsByGroupCodeResponse getSystemConfigsByGroupCode(groupCode)

API lấy danh sách cấu hình theo group code

Lấy tất cả cấu hình hệ thống theo group code

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getSystemConfigApi();
final String groupCode = groupCode_example; // String | Mã nhóm cấu hình

try {
    final response = api.getSystemConfigsByGroupCode(groupCode);
    print(response);
} catch on DioException (e) {
    print('Exception when calling SystemConfigApi->getSystemConfigsByGroupCode: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **groupCode** | **String**| Mã nhóm cấu hình | 

### Return type

[**BaseResponseAppGetSystemConfigsByGroupCodeResponse**](BaseResponseAppGetSystemConfigsByGroupCodeResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

