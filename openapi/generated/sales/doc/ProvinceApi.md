# sales_app_api.api.ProvinceApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**list1**](ProvinceApi.md#list1) | **GET** /system/api/v1/province | API lấy danh sách tỉnh/thành phố


# **list1**
> BaseResponseAppGetListProvinceResponse list1()

API lấy danh sách tỉnh/thành phố

API lấy danh sách tỉnh/thành phố

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getProvinceApi();

try {
    final response = api.list1();
    print(response);
} catch on DioException (e) {
    print('Exception when calling ProvinceApi->list1: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BaseResponseAppGetListProvinceResponse**](BaseResponseAppGetListProvinceResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

