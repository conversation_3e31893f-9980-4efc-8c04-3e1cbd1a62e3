# sales_app_api.api.AccountApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**getPaymentAccount**](AccountApi.md#getpaymentaccount) | **POST** /system/api/v1/account/getPaymentAccount | API lây danh sách tài khoản thanh toán theo số GTTT


# **getPaymentAccount**
> BaseResponseAppGetListPaymentAccountResponse getPaymentAccount(appGetListPaymentAccountRequest)

API lây danh sách tài khoản thanh toán theo số GTTT

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAccountApi();
final AppGetListPaymentAccountRequest appGetListPaymentAccountRequest = ; // AppGetListPaymentAccountRequest | 

try {
    final response = api.getPaymentAccount(appGetListPaymentAccountRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AccountApi->getPaymentAccount: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **appGetListPaymentAccountRequest** | [**AppGetListPaymentAccountRequest**](AppGetListPaymentAccountRequest.md)|  | 

### Return type

[**BaseResponseAppGetListPaymentAccountResponse**](BaseResponseAppGetListPaymentAccountResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

