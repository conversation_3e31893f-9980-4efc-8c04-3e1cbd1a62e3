# sales_app_api.api.AdminMenuManagementApi

## Load the API package
```dart
import 'package:sales_app_api/api.dart';
```

All URIs are relative to *https://api-internal-staging.kienlongbank.co*

Method | HTTP request | Description
------------- | ------------- | -------------
[**createMenu**](AdminMenuManagementApi.md#createmenu) | **POST** /system/admin/api/v1/menu | API tạo menu
[**deleteMenu**](AdminMenuManagementApi.md#deletemenu) | **DELETE** /system/admin/api/v1/menu/{id} | API xóa menu
[**getActions**](AdminMenuManagementApi.md#getactions) | **GET** /system/admin/api/v1/actions | API lấy danh sách action
[**getMenu**](AdminMenuManagementApi.md#getmenu) | **GET** /system/admin/api/v1/menu/{id} | API lấy chi tiết menu
[**listMenus**](AdminMenuManagementApi.md#listmenus) | **GET** /system/admin/api/v1/menus | API lấy danh sách menu hierarchical
[**listParentMenus**](AdminMenuManagementApi.md#listparentmenus) | **GET** /system/admin/api/v1/menus/parents | API lấy danh sách parent menu
[**updateMenu**](AdminMenuManagementApi.md#updatemenu) | **PUT** /system/admin/api/v1/menu/{id} | API cập nhật menu


# **createMenu**
> BaseResponseAdminCreateMenuResponse createMenu(adminCreateMenuRequest)

API tạo menu

API tạo mới menu (APP/WEB)

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminMenuManagementApi();
final AdminCreateMenuRequest adminCreateMenuRequest = ; // AdminCreateMenuRequest | 

try {
    final response = api.createMenu(adminCreateMenuRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminMenuManagementApi->createMenu: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **adminCreateMenuRequest** | [**AdminCreateMenuRequest**](AdminCreateMenuRequest.md)|  | 

### Return type

[**BaseResponseAdminCreateMenuResponse**](BaseResponseAdminCreateMenuResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **deleteMenu**
> BaseResponseAdminDeleteMenuResponse deleteMenu(id)

API xóa menu

Xóa menu (soft delete - chỉ cập nhật is_deleted = true)

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminMenuManagementApi();
final String id = 38400000-8cf0-11bd-b23e-10b96e4ef00d; // String | 

try {
    final response = api.deleteMenu(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminMenuManagementApi->deleteMenu: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**BaseResponseAdminDeleteMenuResponse**](BaseResponseAdminDeleteMenuResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getActions**
> BaseResponseAdminGetActionsResponse getActions()

API lấy danh sách action

Lấy tất cả actions không phân trang

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminMenuManagementApi();

try {
    final response = api.getActions();
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminMenuManagementApi->getActions: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BaseResponseAdminGetActionsResponse**](BaseResponseAdminGetActionsResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **getMenu**
> BaseResponseAdminGetMenuResponse getMenu(id)

API lấy chi tiết menu

Lấy thông tin chi tiết menu theo ID

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminMenuManagementApi();
final String id = 38400000-8cf0-11bd-b23e-10b96e4ef00d; // String | 

try {
    final response = api.getMenu(id);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminMenuManagementApi->getMenu: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 

### Return type

[**BaseResponseAdminGetMenuResponse**](BaseResponseAdminGetMenuResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listMenus**
> BaseResponseAdminListMenusResponse listMenus()

API lấy danh sách menu hierarchical

Lấy danh sách menu và submenu với cấu trúc cha/con (không phân trang)

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminMenuManagementApi();

try {
    final response = api.listMenus();
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminMenuManagementApi->listMenus: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**BaseResponseAdminListMenusResponse**](BaseResponseAdminListMenusResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **listParentMenus**
> BaseResponseAdminListParentMenusResponse listParentMenus(platform)

API lấy danh sách parent menu

Lấy tất cả parent menu (menu không có parentId)

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminMenuManagementApi();
final String platform = platform_example; // String | Platform filter (APP/WEB)

try {
    final response = api.listParentMenus(platform);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminMenuManagementApi->listParentMenus: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **platform** | **String**| Platform filter (APP/WEB) | [optional] 

### Return type

[**BaseResponseAdminListParentMenusResponse**](BaseResponseAdminListParentMenusResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **updateMenu**
> BaseResponseAdminUpdateMenuResponse updateMenu(id, adminUpdateMenuRequest)

API cập nhật menu

API cập nhật thông tin menu và actions

### Example
```dart
import 'package:sales_app_api/api.dart';

final api = SalesAppApi().getAdminMenuManagementApi();
final String id = 38400000-8cf0-11bd-b23e-10b96e4ef00d; // String | 
final AdminUpdateMenuRequest adminUpdateMenuRequest = ; // AdminUpdateMenuRequest | 

try {
    final response = api.updateMenu(id, adminUpdateMenuRequest);
    print(response);
} catch on DioException (e) {
    print('Exception when calling AdminMenuManagementApi->updateMenu: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **String**|  | 
 **adminUpdateMenuRequest** | [**AdminUpdateMenuRequest**](AdminUpdateMenuRequest.md)|  | 

### Return type

[**BaseResponseAdminUpdateMenuResponse**](BaseResponseAdminUpdateMenuResponse.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: */*

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

