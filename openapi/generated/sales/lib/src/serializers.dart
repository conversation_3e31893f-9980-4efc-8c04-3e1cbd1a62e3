//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_import

import 'package:one_of_serializer/any_of_serializer.dart';
import 'package:one_of_serializer/one_of_serializer.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:built_value/standard_json_plugin.dart';
import 'package:built_value/iso_8601_date_time_serializer.dart';
import 'package:sales_app_api/src/date_serializer.dart';
import 'package:sales_app_api/src/model/date.dart';

import 'package:sales_app_api/src/model/account_dto.dart';
import 'package:sales_app_api/src/model/action_dto.dart';
import 'package:sales_app_api/src/model/admin_assign_roles_request.dart';
import 'package:sales_app_api/src/model/admin_assign_roles_response.dart';
import 'package:sales_app_api/src/model/admin_create_menu_request.dart';
import 'package:sales_app_api/src/model/admin_create_menu_response.dart';
import 'package:sales_app_api/src/model/admin_create_user_request.dart';
import 'package:sales_app_api/src/model/admin_create_user_response.dart';
import 'package:sales_app_api/src/model/admin_delete_menu_response.dart';
import 'package:sales_app_api/src/model/admin_delete_user_response.dart';
import 'package:sales_app_api/src/model/admin_get_actions_response.dart';
import 'package:sales_app_api/src/model/admin_get_menu_response.dart';
import 'package:sales_app_api/src/model/admin_get_user_by_id_response.dart';
import 'package:sales_app_api/src/model/admin_get_user_role_permission_response.dart';
import 'package:sales_app_api/src/model/admin_import_users_request.dart';
import 'package:sales_app_api/src/model/admin_import_users_response.dart';
import 'package:sales_app_api/src/model/admin_list_menus_response.dart';
import 'package:sales_app_api/src/model/admin_list_parent_menus_response.dart';
import 'package:sales_app_api/src/model/admin_reset_password_response.dart';
import 'package:sales_app_api/src/model/admin_search_users_request.dart';
import 'package:sales_app_api/src/model/admin_search_users_response.dart';
import 'package:sales_app_api/src/model/admin_update_menu_request.dart';
import 'package:sales_app_api/src/model/admin_update_menu_response.dart';
import 'package:sales_app_api/src/model/admin_update_user_request.dart';
import 'package:sales_app_api/src/model/admin_update_user_response.dart';
import 'package:sales_app_api/src/model/app_branch_dto.dart';
import 'package:sales_app_api/src/model/app_get_list_branch_response.dart';
import 'package:sales_app_api/src/model/app_get_list_payment_account_request.dart';
import 'package:sales_app_api/src/model/app_get_list_payment_account_response.dart';
import 'package:sales_app_api/src/model/app_get_list_province_response.dart';
import 'package:sales_app_api/src/model/app_get_list_ward_response.dart';
import 'package:sales_app_api/src/model/app_get_referrer_by_code_response.dart';
import 'package:sales_app_api/src/model/app_get_system_configs_by_group_code_response.dart';
import 'package:sales_app_api/src/model/app_get_user_profile_response.dart';
import 'package:sales_app_api/src/model/app_kplus_register_collaborator_request.dart';
import 'package:sales_app_api/src/model/app_kplus_register_collaborator_response.dart';
import 'package:sales_app_api/src/model/app_login_request.dart';
import 'package:sales_app_api/src/model/app_login_response.dart';
import 'package:sales_app_api/src/model/app_logout_request.dart';
import 'package:sales_app_api/src/model/app_logout_response.dart';
import 'package:sales_app_api/src/model/app_province_dto.dart';
import 'package:sales_app_api/src/model/app_referral_register_collab_request.dart';
import 'package:sales_app_api/src/model/app_referral_register_collab_response.dart';
import 'package:sales_app_api/src/model/app_refresh_token_request.dart';
import 'package:sales_app_api/src/model/app_refresh_token_response.dart';
import 'package:sales_app_api/src/model/app_register_collaborator_request.dart';
import 'package:sales_app_api/src/model/app_register_collaborator_response.dart';
import 'package:sales_app_api/src/model/app_ward_dto.dart';
import 'package:sales_app_api/src/model/base_response_admin_assign_roles_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_create_menu_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_create_user_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_delete_menu_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_delete_user_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_get_actions_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_get_menu_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_get_user_by_id_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_get_user_role_permission_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_import_users_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_list_menus_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_list_parent_menus_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_reset_password_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_search_users_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_update_menu_response.dart';
import 'package:sales_app_api/src/model/base_response_admin_update_user_response.dart';
import 'package:sales_app_api/src/model/base_response_app_get_list_branch_response.dart';
import 'package:sales_app_api/src/model/base_response_app_get_list_payment_account_response.dart';
import 'package:sales_app_api/src/model/base_response_app_get_list_province_response.dart';
import 'package:sales_app_api/src/model/base_response_app_get_list_ward_response.dart';
import 'package:sales_app_api/src/model/base_response_app_get_referrer_by_code_response.dart';
import 'package:sales_app_api/src/model/base_response_app_get_system_configs_by_group_code_response.dart';
import 'package:sales_app_api/src/model/base_response_app_get_user_profile_response.dart';
import 'package:sales_app_api/src/model/base_response_app_kplus_register_collaborator_response.dart';
import 'package:sales_app_api/src/model/base_response_app_login_response.dart';
import 'package:sales_app_api/src/model/base_response_app_logout_response.dart';
import 'package:sales_app_api/src/model/base_response_app_referral_register_collab_response.dart';
import 'package:sales_app_api/src/model/base_response_app_refresh_token_response.dart';
import 'package:sales_app_api/src/model/base_response_app_register_collaborator_response.dart';
import 'package:sales_app_api/src/model/base_response_get_article_by_code_response.dart';
import 'package:sales_app_api/src/model/get_article_by_code_response.dart';
import 'package:sales_app_api/src/model/menu_dto.dart';
import 'package:sales_app_api/src/model/page_user.dart';
import 'package:sales_app_api/src/model/pageable_object.dart';
import 'package:sales_app_api/src/model/role_dto.dart';
import 'package:sales_app_api/src/model/sort_object.dart';
import 'package:sales_app_api/src/model/system_config_dto.dart';
import 'package:sales_app_api/src/model/user.dart';

part 'serializers.g.dart';

@SerializersFor([
  AccountDto,
  ActionDto,
  AdminAssignRolesRequest,
  AdminAssignRolesResponse,
  AdminCreateMenuRequest,
  AdminCreateMenuResponse,
  AdminCreateUserRequest,
  AdminCreateUserResponse,
  AdminDeleteMenuResponse,
  AdminDeleteUserResponse,
  AdminGetActionsResponse,
  AdminGetMenuResponse,
  AdminGetUserByIdResponse,
  AdminGetUserRolePermissionResponse,
  AdminImportUsersRequest,
  AdminImportUsersResponse,
  AdminListMenusResponse,
  AdminListParentMenusResponse,
  AdminResetPasswordResponse,
  AdminSearchUsersRequest,
  AdminSearchUsersResponse,
  AdminUpdateMenuRequest,
  AdminUpdateMenuResponse,
  AdminUpdateUserRequest,
  AdminUpdateUserResponse,
  AppBranchDto,
  AppGetListBranchResponse,
  AppGetListPaymentAccountRequest,
  AppGetListPaymentAccountResponse,
  AppGetListProvinceResponse,
  AppGetListWardResponse,
  AppGetReferrerByCodeResponse,
  AppGetSystemConfigsByGroupCodeResponse,
  AppGetUserProfileResponse,
  AppKplusRegisterCollaboratorRequest,
  AppKplusRegisterCollaboratorResponse,
  AppLoginRequest,
  AppLoginResponse,
  AppLogoutRequest,
  AppLogoutResponse,
  AppProvinceDto,
  AppReferralRegisterCollabRequest,
  AppReferralRegisterCollabResponse,
  AppRefreshTokenRequest,
  AppRefreshTokenResponse,
  AppRegisterCollaboratorRequest,
  AppRegisterCollaboratorResponse,
  AppWardDto,
  BaseResponseAdminAssignRolesResponse,
  BaseResponseAdminCreateMenuResponse,
  BaseResponseAdminCreateUserResponse,
  BaseResponseAdminDeleteMenuResponse,
  BaseResponseAdminDeleteUserResponse,
  BaseResponseAdminGetActionsResponse,
  BaseResponseAdminGetMenuResponse,
  BaseResponseAdminGetUserByIdResponse,
  BaseResponseAdminGetUserRolePermissionResponse,
  BaseResponseAdminImportUsersResponse,
  BaseResponseAdminListMenusResponse,
  BaseResponseAdminListParentMenusResponse,
  BaseResponseAdminResetPasswordResponse,
  BaseResponseAdminSearchUsersResponse,
  BaseResponseAdminUpdateMenuResponse,
  BaseResponseAdminUpdateUserResponse,
  BaseResponseAppGetListBranchResponse,
  BaseResponseAppGetListPaymentAccountResponse,
  BaseResponseAppGetListProvinceResponse,
  BaseResponseAppGetListWardResponse,
  BaseResponseAppGetReferrerByCodeResponse,
  BaseResponseAppGetSystemConfigsByGroupCodeResponse,
  BaseResponseAppGetUserProfileResponse,
  BaseResponseAppKplusRegisterCollaboratorResponse,
  BaseResponseAppLoginResponse,
  BaseResponseAppLogoutResponse,
  BaseResponseAppReferralRegisterCollabResponse,
  BaseResponseAppRefreshTokenResponse,
  BaseResponseAppRegisterCollaboratorResponse,
  BaseResponseGetArticleByCodeResponse,
  GetArticleByCodeResponse,
  MenuDto,
  PageUser,
  PageableObject,
  RoleDto,
  SortObject,
  SystemConfigDto,
  User,
])
Serializers serializers = (_$serializers.toBuilder()
      ..add(const OneOfSerializer())
      ..add(const AnyOfSerializer())
      ..add(const DateSerializer())
      ..add(Iso8601DateTimeSerializer()))
    .build();

Serializers standardSerializers =
    (serializers.toBuilder()..addPlugin(StandardJsonPlugin())).build();
