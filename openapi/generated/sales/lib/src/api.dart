//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'package:dio/dio.dart';
import 'package:built_value/serializer.dart';
import 'package:sales_app_api/src/serializers.dart';
import 'package:sales_app_api/src/auth/api_key_auth.dart';
import 'package:sales_app_api/src/auth/basic_auth.dart';
import 'package:sales_app_api/src/auth/bearer_auth.dart';
import 'package:sales_app_api/src/auth/oauth.dart';
import 'package:sales_app_api/src/api/account_api.dart';
import 'package:sales_app_api/src/api/admin_menu_management_api.dart';
import 'package:sales_app_api/src/api/admin_user_management_api.dart';
import 'package:sales_app_api/src/api/article_api.dart';
import 'package:sales_app_api/src/api/authentication_api.dart';
import 'package:sales_app_api/src/api/branch_api.dart';
import 'package:sales_app_api/src/api/province_api.dart';
import 'package:sales_app_api/src/api/registers_api.dart';
import 'package:sales_app_api/src/api/system_config_api.dart';
import 'package:sales_app_api/src/api/users_api.dart';
import 'package:sales_app_api/src/api/ward_api.dart';

class SalesAppApi {
  static const String basePath =
      r'https://api-internal-staging.kienlongbank.co';

  final Dio dio;
  final Serializers serializers;

  SalesAppApi({
    Dio? dio,
    Serializers? serializers,
    String? basePathOverride,
    List<Interceptor>? interceptors,
  })  : this.serializers = serializers ?? standardSerializers,
        this.dio = dio ??
            Dio(BaseOptions(
              baseUrl: basePathOverride ?? basePath,
              connectTimeout: const Duration(milliseconds: 5000),
              receiveTimeout: const Duration(milliseconds: 3000),
            )) {
    if (interceptors == null) {
      this.dio.interceptors.addAll([
        OAuthInterceptor(),
        BasicAuthInterceptor(),
        BearerAuthInterceptor(),
        ApiKeyAuthInterceptor(),
      ]);
    } else {
      this.dio.interceptors.addAll(interceptors);
    }
  }

  void setOAuthToken(String name, String token) {
    if (this.dio.interceptors.any((i) => i is OAuthInterceptor)) {
      (this.dio.interceptors.firstWhere((i) => i is OAuthInterceptor)
              as OAuthInterceptor)
          .tokens[name] = token;
    }
  }

  void setBearerAuth(String name, String token) {
    if (this.dio.interceptors.any((i) => i is BearerAuthInterceptor)) {
      (this.dio.interceptors.firstWhere((i) => i is BearerAuthInterceptor)
              as BearerAuthInterceptor)
          .tokens[name] = token;
    }
  }

  void setBasicAuth(String name, String username, String password) {
    if (this.dio.interceptors.any((i) => i is BasicAuthInterceptor)) {
      (this.dio.interceptors.firstWhere((i) => i is BasicAuthInterceptor)
              as BasicAuthInterceptor)
          .authInfo[name] = BasicAuthInfo(username, password);
    }
  }

  void setApiKey(String name, String apiKey) {
    if (this.dio.interceptors.any((i) => i is ApiKeyAuthInterceptor)) {
      (this
                  .dio
                  .interceptors
                  .firstWhere((element) => element is ApiKeyAuthInterceptor)
              as ApiKeyAuthInterceptor)
          .apiKeys[name] = apiKey;
    }
  }

  /// Get AccountApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  AccountApi getAccountApi() {
    return AccountApi(dio, serializers);
  }

  /// Get AdminMenuManagementApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  AdminMenuManagementApi getAdminMenuManagementApi() {
    return AdminMenuManagementApi(dio, serializers);
  }

  /// Get AdminUserManagementApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  AdminUserManagementApi getAdminUserManagementApi() {
    return AdminUserManagementApi(dio, serializers);
  }

  /// Get ArticleApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  ArticleApi getArticleApi() {
    return ArticleApi(dio, serializers);
  }

  /// Get AuthenticationApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  AuthenticationApi getAuthenticationApi() {
    return AuthenticationApi(dio, serializers);
  }

  /// Get BranchApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  BranchApi getBranchApi() {
    return BranchApi(dio, serializers);
  }

  /// Get ProvinceApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  ProvinceApi getProvinceApi() {
    return ProvinceApi(dio, serializers);
  }

  /// Get RegistersApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  RegistersApi getRegistersApi() {
    return RegistersApi(dio, serializers);
  }

  /// Get SystemConfigApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  SystemConfigApi getSystemConfigApi() {
    return SystemConfigApi(dio, serializers);
  }

  /// Get UsersApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  UsersApi getUsersApi() {
    return UsersApi(dio, serializers);
  }

  /// Get WardApi instance, base route and serializer can be overridden by a given but be careful,
  /// by doing that all interceptors will not be executed
  WardApi getWardApi() {
    return WardApi(dio, serializers);
  }
}
