// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_login_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppLoginRequest extends AppLoginRequest {
  @override
  final String username;
  @override
  final String password;

  factory _$AppLoginRequest([void Function(AppLoginRequestBuilder)? updates]) =>
      (AppLoginRequestBuilder()..update(updates))._build();

  _$AppLoginRequest._({required this.username, required this.password})
      : super._();
  @override
  AppLoginRequest rebuild(void Function(AppLoginRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppLoginRequestBuilder toBuilder() => AppLoginRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppLoginRequest &&
        username == other.username &&
        password == other.password;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, username.hashCode);
    _$hash = $jc(_$hash, password.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppLoginRequest')
          ..add('username', username)
          ..add('password', password))
        .toString();
  }
}

class AppLoginRequestBuilder
    implements Builder<AppLoginRequest, AppLoginRequestBuilder> {
  _$AppLoginRequest? _$v;

  String? _username;
  String? get username => _$this._username;
  set username(String? username) => _$this._username = username;

  String? _password;
  String? get password => _$this._password;
  set password(String? password) => _$this._password = password;

  AppLoginRequestBuilder() {
    AppLoginRequest._defaults(this);
  }

  AppLoginRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _username = $v.username;
      _password = $v.password;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppLoginRequest other) {
    _$v = other as _$AppLoginRequest;
  }

  @override
  void update(void Function(AppLoginRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppLoginRequest build() => _build();

  _$AppLoginRequest _build() {
    final _$result = _$v ??
        _$AppLoginRequest._(
          username: BuiltValueNullFieldError.checkNotNull(
              username, r'AppLoginRequest', 'username'),
          password: BuiltValueNullFieldError.checkNotNull(
              password, r'AppLoginRequest', 'password'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
