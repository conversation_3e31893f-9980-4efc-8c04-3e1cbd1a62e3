//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:sales_app_api/src/model/menu_dto.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_list_parent_menus_response.g.dart';

/// AdminListParentMenusResponse
///
/// Properties:
/// * [menus]
@BuiltValue()
abstract class AdminListParentMenusResponse
    implements
        Built<AdminListParentMenusResponse,
            AdminListParentMenusResponseBuilder> {
  @BuiltValueField(wireName: r'menus')
  BuiltList<MenuDto>? get menus;

  AdminListParentMenusResponse._();

  factory AdminListParentMenusResponse(
          [void updates(AdminListParentMenusResponseBuilder b)]) =
      _$AdminListParentMenusResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminListParentMenusResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminListParentMenusResponse> get serializer =>
      _$AdminListParentMenusResponseSerializer();
}

class _$AdminListParentMenusResponseSerializer
    implements PrimitiveSerializer<AdminListParentMenusResponse> {
  @override
  final Iterable<Type> types = const [
    AdminListParentMenusResponse,
    _$AdminListParentMenusResponse
  ];

  @override
  final String wireName = r'AdminListParentMenusResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminListParentMenusResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.menus != null) {
      yield r'menus';
      yield serializers.serialize(
        object.menus,
        specifiedType: const FullType(BuiltList, [FullType(MenuDto)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminListParentMenusResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminListParentMenusResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'menus':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(MenuDto)]),
          ) as BuiltList<MenuDto>;
          result.menus.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminListParentMenusResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminListParentMenusResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
