// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_delete_user_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminDeleteUserResponse
    extends BaseResponseAdminDeleteUserResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminDeleteUserResponse? data;

  factory _$BaseResponseAdminDeleteUserResponse(
          [void Function(BaseResponseAdminDeleteUserResponseBuilder)?
              updates]) =>
      (BaseResponseAdminDeleteUserResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminDeleteUserResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminDeleteUserResponse rebuild(
          void Function(BaseResponseAdminDeleteUserResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminDeleteUserResponseBuilder toBuilder() =>
      BaseResponseAdminDeleteUserResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminDeleteUserResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminDeleteUserResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminDeleteUserResponseBuilder
    implements
        Builder<BaseResponseAdminDeleteUserResponse,
            BaseResponseAdminDeleteUserResponseBuilder> {
  _$BaseResponseAdminDeleteUserResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminDeleteUserResponseBuilder? _data;
  AdminDeleteUserResponseBuilder get data =>
      _$this._data ??= AdminDeleteUserResponseBuilder();
  set data(AdminDeleteUserResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminDeleteUserResponseBuilder() {
    BaseResponseAdminDeleteUserResponse._defaults(this);
  }

  BaseResponseAdminDeleteUserResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminDeleteUserResponse other) {
    _$v = other as _$BaseResponseAdminDeleteUserResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminDeleteUserResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminDeleteUserResponse build() => _build();

  _$BaseResponseAdminDeleteUserResponse _build() {
    _$BaseResponseAdminDeleteUserResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminDeleteUserResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(r'BaseResponseAdminDeleteUserResponse',
            _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
