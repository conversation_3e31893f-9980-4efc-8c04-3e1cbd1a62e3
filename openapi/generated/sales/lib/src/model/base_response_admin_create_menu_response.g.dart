// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_create_menu_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminCreateMenuResponse
    extends BaseResponseAdminCreateMenuResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminCreateMenuResponse? data;

  factory _$BaseResponseAdminCreateMenuResponse(
          [void Function(BaseResponseAdminCreateMenuResponseBuilder)?
              updates]) =>
      (BaseResponseAdminCreateMenuResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminCreateMenuResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminCreateMenuResponse rebuild(
          void Function(BaseResponseAdminCreateMenuResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminCreateMenuResponseBuilder toBuilder() =>
      BaseResponseAdminCreateMenuResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminCreateMenuResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminCreateMenuResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminCreateMenuResponseBuilder
    implements
        Builder<BaseResponseAdminCreateMenuResponse,
            BaseResponseAdminCreateMenuResponseBuilder> {
  _$BaseResponseAdminCreateMenuResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminCreateMenuResponseBuilder? _data;
  AdminCreateMenuResponseBuilder get data =>
      _$this._data ??= AdminCreateMenuResponseBuilder();
  set data(AdminCreateMenuResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminCreateMenuResponseBuilder() {
    BaseResponseAdminCreateMenuResponse._defaults(this);
  }

  BaseResponseAdminCreateMenuResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminCreateMenuResponse other) {
    _$v = other as _$BaseResponseAdminCreateMenuResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminCreateMenuResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminCreateMenuResponse build() => _build();

  _$BaseResponseAdminCreateMenuResponse _build() {
    _$BaseResponseAdminCreateMenuResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminCreateMenuResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(r'BaseResponseAdminCreateMenuResponse',
            _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
