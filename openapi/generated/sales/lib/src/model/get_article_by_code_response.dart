//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'get_article_by_code_response.g.dart';

/// GetArticleByCodeResponse
///
/// Properties:
/// * [code]
/// * [title]
/// * [content]
@BuiltValue()
abstract class GetArticleByCodeResponse
    implements
        Built<GetArticleByCodeResponse, GetArticleByCodeResponseBuilder> {
  @BuiltValueField(wireName: r'code')
  String? get code;

  @BuiltValueField(wireName: r'title')
  String? get title;

  @BuiltValueField(wireName: r'content')
  String? get content;

  GetArticleByCodeResponse._();

  factory GetArticleByCodeResponse(
          [void updates(GetArticleByCodeResponseBuilder b)]) =
      _$GetArticleByCodeResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(GetArticleByCodeResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<GetArticleByCodeResponse> get serializer =>
      _$GetArticleByCodeResponseSerializer();
}

class _$GetArticleByCodeResponseSerializer
    implements PrimitiveSerializer<GetArticleByCodeResponse> {
  @override
  final Iterable<Type> types = const [
    GetArticleByCodeResponse,
    _$GetArticleByCodeResponse
  ];

  @override
  final String wireName = r'GetArticleByCodeResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    GetArticleByCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(String),
      );
    }
    if (object.title != null) {
      yield r'title';
      yield serializers.serialize(
        object.title,
        specifiedType: const FullType(String),
      );
    }
    if (object.content != null) {
      yield r'content';
      yield serializers.serialize(
        object.content,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    GetArticleByCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required GetArticleByCodeResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.title = valueDes;
          break;
        case r'content':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.content = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  GetArticleByCodeResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = GetArticleByCodeResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
