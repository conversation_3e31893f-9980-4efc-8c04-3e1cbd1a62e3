//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_register_collaborator_request.g.dart';

/// AppRegisterCollaboratorRequest
///
/// Properties:
/// * [fullName] - Họ tên
/// * [idCardType] - Loại GTTT
/// * [idCardNo] - GTTT
/// * [phoneNumber] - Số điện thoại
/// * [branchId] - <PERSON><PERSON> chi nhánh
/// * [frontCardUrl] - Ảnh mặt trước của giấy tờ
/// * [backCardUrl] - Ảnh mặt sau của giấy tờ
/// * [permanentAddress] - Địa chỉ thường trú
/// * [referrerCode] - Mã người giới thiệu. Mã cif
/// * [email] - Email
@BuiltValue()
abstract class AppRegisterCollaboratorRequest
    implements
        Built<AppRegisterCollaboratorRequest,
            AppRegisterCollaboratorRequestBuilder> {
  /// Họ tên
  @BuiltValueField(wireName: r'fullName')
  String get fullName;

  /// Loại GTTT
  @BuiltValueField(wireName: r'idCardType')
  AppRegisterCollaboratorRequestIdCardTypeEnum get idCardType;
  // enum idCardTypeEnum {  CHIP_ID,  PASSPORT,  };

  /// GTTT
  @BuiltValueField(wireName: r'idCardNo')
  String get idCardNo;

  /// Số điện thoại
  @BuiltValueField(wireName: r'phoneNumber')
  String get phoneNumber;

  /// Mã chi nhánh
  @BuiltValueField(wireName: r'branchId')
  String get branchId;

  /// Ảnh mặt trước của giấy tờ
  @BuiltValueField(wireName: r'frontCardUrl')
  String get frontCardUrl;

  /// Ảnh mặt sau của giấy tờ
  @BuiltValueField(wireName: r'backCardUrl')
  String get backCardUrl;

  /// Địa chỉ thường trú
  @BuiltValueField(wireName: r'permanentAddress')
  String? get permanentAddress;

  /// Mã người giới thiệu. Mã cif
  @BuiltValueField(wireName: r'referrerCode')
  String? get referrerCode;

  /// Email
  @BuiltValueField(wireName: r'email')
  String? get email;

  AppRegisterCollaboratorRequest._();

  factory AppRegisterCollaboratorRequest(
          [void updates(AppRegisterCollaboratorRequestBuilder b)]) =
      _$AppRegisterCollaboratorRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppRegisterCollaboratorRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppRegisterCollaboratorRequest> get serializer =>
      _$AppRegisterCollaboratorRequestSerializer();
}

class _$AppRegisterCollaboratorRequestSerializer
    implements PrimitiveSerializer<AppRegisterCollaboratorRequest> {
  @override
  final Iterable<Type> types = const [
    AppRegisterCollaboratorRequest,
    _$AppRegisterCollaboratorRequest
  ];

  @override
  final String wireName = r'AppRegisterCollaboratorRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppRegisterCollaboratorRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'fullName';
    yield serializers.serialize(
      object.fullName,
      specifiedType: const FullType(String),
    );
    yield r'idCardType';
    yield serializers.serialize(
      object.idCardType,
      specifiedType:
          const FullType(AppRegisterCollaboratorRequestIdCardTypeEnum),
    );
    yield r'idCardNo';
    yield serializers.serialize(
      object.idCardNo,
      specifiedType: const FullType(String),
    );
    yield r'phoneNumber';
    yield serializers.serialize(
      object.phoneNumber,
      specifiedType: const FullType(String),
    );
    yield r'branchId';
    yield serializers.serialize(
      object.branchId,
      specifiedType: const FullType(String),
    );
    yield r'frontCardUrl';
    yield serializers.serialize(
      object.frontCardUrl,
      specifiedType: const FullType(String),
    );
    yield r'backCardUrl';
    yield serializers.serialize(
      object.backCardUrl,
      specifiedType: const FullType(String),
    );
    if (object.permanentAddress != null) {
      yield r'permanentAddress';
      yield serializers.serialize(
        object.permanentAddress,
        specifiedType: const FullType(String),
      );
    }
    if (object.referrerCode != null) {
      yield r'referrerCode';
      yield serializers.serialize(
        object.referrerCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.email != null) {
      yield r'email';
      yield serializers.serialize(
        object.email,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppRegisterCollaboratorRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppRegisterCollaboratorRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'fullName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.fullName = valueDes;
          break;
        case r'idCardType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType(AppRegisterCollaboratorRequestIdCardTypeEnum),
          ) as AppRegisterCollaboratorRequestIdCardTypeEnum;
          result.idCardType = valueDes;
          break;
        case r'idCardNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.idCardNo = valueDes;
          break;
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.phoneNumber = valueDes;
          break;
        case r'branchId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.branchId = valueDes;
          break;
        case r'frontCardUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.frontCardUrl = valueDes;
          break;
        case r'backCardUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.backCardUrl = valueDes;
          break;
        case r'permanentAddress':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.permanentAddress = valueDes;
          break;
        case r'referrerCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.referrerCode = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppRegisterCollaboratorRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppRegisterCollaboratorRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class AppRegisterCollaboratorRequestIdCardTypeEnum extends EnumClass {
  /// Loại GTTT
  @BuiltValueEnumConst(wireName: r'CHIP_ID')
  static const AppRegisterCollaboratorRequestIdCardTypeEnum CHIP_ID =
      _$appRegisterCollaboratorRequestIdCardTypeEnum_CHIP_ID;

  /// Loại GTTT
  @BuiltValueEnumConst(wireName: r'PASSPORT')
  static const AppRegisterCollaboratorRequestIdCardTypeEnum PASSPORT =
      _$appRegisterCollaboratorRequestIdCardTypeEnum_PASSPORT;

  static Serializer<AppRegisterCollaboratorRequestIdCardTypeEnum>
      get serializer =>
          _$appRegisterCollaboratorRequestIdCardTypeEnumSerializer;

  const AppRegisterCollaboratorRequestIdCardTypeEnum._(String name)
      : super(name);

  static BuiltSet<AppRegisterCollaboratorRequestIdCardTypeEnum> get values =>
      _$appRegisterCollaboratorRequestIdCardTypeEnumValues;
  static AppRegisterCollaboratorRequestIdCardTypeEnum valueOf(String name) =>
      _$appRegisterCollaboratorRequestIdCardTypeEnumValueOf(name);
}
