// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_delete_menu_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminDeleteMenuResponse
    extends BaseResponseAdminDeleteMenuResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminDeleteMenuResponse? data;

  factory _$BaseResponseAdminDeleteMenuResponse(
          [void Function(BaseResponseAdminDeleteMenuResponseBuilder)?
              updates]) =>
      (BaseResponseAdminDeleteMenuResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminDeleteMenuResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminDeleteMenuResponse rebuild(
          void Function(BaseResponseAdminDeleteMenuResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminDeleteMenuResponseBuilder toBuilder() =>
      BaseResponseAdminDeleteMenuResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminDeleteMenuResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminDeleteMenuResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminDeleteMenuResponseBuilder
    implements
        Builder<BaseResponseAdminDeleteMenuResponse,
            BaseResponseAdminDeleteMenuResponseBuilder> {
  _$BaseResponseAdminDeleteMenuResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminDeleteMenuResponseBuilder? _data;
  AdminDeleteMenuResponseBuilder get data =>
      _$this._data ??= AdminDeleteMenuResponseBuilder();
  set data(AdminDeleteMenuResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminDeleteMenuResponseBuilder() {
    BaseResponseAdminDeleteMenuResponse._defaults(this);
  }

  BaseResponseAdminDeleteMenuResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminDeleteMenuResponse other) {
    _$v = other as _$BaseResponseAdminDeleteMenuResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminDeleteMenuResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminDeleteMenuResponse build() => _build();

  _$BaseResponseAdminDeleteMenuResponse _build() {
    _$BaseResponseAdminDeleteMenuResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminDeleteMenuResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(r'BaseResponseAdminDeleteMenuResponse',
            _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
