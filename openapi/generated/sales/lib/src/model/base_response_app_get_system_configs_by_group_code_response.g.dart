// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_get_system_configs_by_group_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppGetSystemConfigsByGroupCodeResponse
    extends BaseResponseAppGetSystemConfigsByGroupCodeResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppGetSystemConfigsByGroupCodeResponse? data;

  factory _$BaseResponseAppGetSystemConfigsByGroupCodeResponse(
          [void Function(
                  BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder)?
              updates]) =>
      (BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder()
            ..update(updates))
          ._build();

  _$BaseResponseAppGetSystemConfigsByGroupCodeResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppGetSystemConfigsByGroupCodeResponse rebuild(
          void Function(
                  BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder toBuilder() =>
      BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder()
        ..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppGetSystemConfigsByGroupCodeResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAppGetSystemConfigsByGroupCodeResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder
    implements
        Builder<BaseResponseAppGetSystemConfigsByGroupCodeResponse,
            BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder> {
  _$BaseResponseAppGetSystemConfigsByGroupCodeResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppGetSystemConfigsByGroupCodeResponseBuilder? _data;
  AppGetSystemConfigsByGroupCodeResponseBuilder get data =>
      _$this._data ??= AppGetSystemConfigsByGroupCodeResponseBuilder();
  set data(AppGetSystemConfigsByGroupCodeResponseBuilder? data) =>
      _$this._data = data;

  BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder() {
    BaseResponseAppGetSystemConfigsByGroupCodeResponse._defaults(this);
  }

  BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppGetSystemConfigsByGroupCodeResponse other) {
    _$v = other as _$BaseResponseAppGetSystemConfigsByGroupCodeResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppGetSystemConfigsByGroupCodeResponse build() => _build();

  _$BaseResponseAppGetSystemConfigsByGroupCodeResponse _build() {
    _$BaseResponseAppGetSystemConfigsByGroupCodeResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppGetSystemConfigsByGroupCodeResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppGetSystemConfigsByGroupCodeResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
