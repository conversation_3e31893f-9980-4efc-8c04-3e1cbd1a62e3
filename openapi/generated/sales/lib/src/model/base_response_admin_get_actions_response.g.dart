// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_get_actions_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminGetActionsResponse
    extends BaseResponseAdminGetActionsResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminGetActionsResponse? data;

  factory _$BaseResponseAdminGetActionsResponse(
          [void Function(BaseResponseAdminGetActionsResponseBuilder)?
              updates]) =>
      (BaseResponseAdminGetActionsResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminGetActionsResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminGetActionsResponse rebuild(
          void Function(BaseResponseAdminGetActionsResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminGetActionsResponseBuilder toBuilder() =>
      BaseResponseAdminGetActionsResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminGetActionsResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminGetActionsResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminGetActionsResponseBuilder
    implements
        Builder<BaseResponseAdminGetActionsResponse,
            BaseResponseAdminGetActionsResponseBuilder> {
  _$BaseResponseAdminGetActionsResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminGetActionsResponseBuilder? _data;
  AdminGetActionsResponseBuilder get data =>
      _$this._data ??= AdminGetActionsResponseBuilder();
  set data(AdminGetActionsResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminGetActionsResponseBuilder() {
    BaseResponseAdminGetActionsResponse._defaults(this);
  }

  BaseResponseAdminGetActionsResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminGetActionsResponse other) {
    _$v = other as _$BaseResponseAdminGetActionsResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminGetActionsResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminGetActionsResponse build() => _build();

  _$BaseResponseAdminGetActionsResponse _build() {
    _$BaseResponseAdminGetActionsResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminGetActionsResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(r'BaseResponseAdminGetActionsResponse',
            _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
