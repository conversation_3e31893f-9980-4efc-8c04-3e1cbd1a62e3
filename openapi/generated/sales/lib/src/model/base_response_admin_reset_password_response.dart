//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:sales_app_api/src/model/admin_reset_password_response.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'base_response_admin_reset_password_response.g.dart';

/// BaseResponseAdminResetPasswordResponse
///
/// Properties:
/// * [success]
/// * [code]
/// * [message]
/// * [data]
@BuiltValue()
abstract class BaseResponseAdminResetPasswordResponse
    implements
        Built<BaseResponseAdminResetPasswordResponse,
            BaseResponseAdminResetPasswordResponseBuilder> {
  @BuiltValueField(wireName: r'success')
  bool? get success;

  @BuiltValueField(wireName: r'code')
  int? get code;

  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  AdminResetPasswordResponse? get data;

  BaseResponseAdminResetPasswordResponse._();

  factory BaseResponseAdminResetPasswordResponse(
          [void updates(BaseResponseAdminResetPasswordResponseBuilder b)]) =
      _$BaseResponseAdminResetPasswordResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BaseResponseAdminResetPasswordResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BaseResponseAdminResetPasswordResponse> get serializer =>
      _$BaseResponseAdminResetPasswordResponseSerializer();
}

class _$BaseResponseAdminResetPasswordResponseSerializer
    implements PrimitiveSerializer<BaseResponseAdminResetPasswordResponse> {
  @override
  final Iterable<Type> types = const [
    BaseResponseAdminResetPasswordResponse,
    _$BaseResponseAdminResetPasswordResponse
  ];

  @override
  final String wireName = r'BaseResponseAdminResetPasswordResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BaseResponseAdminResetPasswordResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.success != null) {
      yield r'success';
      yield serializers.serialize(
        object.success,
        specifiedType: const FullType(bool),
      );
    }
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(int),
      );
    }
    if (object.message != null) {
      yield r'message';
      yield serializers.serialize(
        object.message,
        specifiedType: const FullType(String),
      );
    }
    if (object.data != null) {
      yield r'data';
      yield serializers.serialize(
        object.data,
        specifiedType: const FullType(AdminResetPasswordResponse),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    BaseResponseAdminResetPasswordResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BaseResponseAdminResetPasswordResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.success = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.code = valueDes;
          break;
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(AdminResetPasswordResponse),
          ) as AdminResetPasswordResponse;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BaseResponseAdminResetPasswordResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BaseResponseAdminResetPasswordResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
