// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_article_by_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$GetArticleByCodeResponse extends GetArticleByCodeResponse {
  @override
  final String? code;
  @override
  final String? title;
  @override
  final String? content;

  factory _$GetArticleByCodeResponse(
          [void Function(GetArticleByCodeResponseBuilder)? updates]) =>
      (GetArticleByCodeResponseBuilder()..update(updates))._build();

  _$GetArticleByCodeResponse._({this.code, this.title, this.content})
      : super._();
  @override
  GetArticleByCodeResponse rebuild(
          void Function(GetArticleByCodeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  GetArticleByCodeResponseBuilder toBuilder() =>
      GetArticleByCodeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is GetArticleByCodeResponse &&
        code == other.code &&
        title == other.title &&
        content == other.content;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, title.hashCode);
    _$hash = $jc(_$hash, content.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'GetArticleByCodeResponse')
          ..add('code', code)
          ..add('title', title)
          ..add('content', content))
        .toString();
  }
}

class GetArticleByCodeResponseBuilder
    implements
        Builder<GetArticleByCodeResponse, GetArticleByCodeResponseBuilder> {
  _$GetArticleByCodeResponse? _$v;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _title;
  String? get title => _$this._title;
  set title(String? title) => _$this._title = title;

  String? _content;
  String? get content => _$this._content;
  set content(String? content) => _$this._content = content;

  GetArticleByCodeResponseBuilder() {
    GetArticleByCodeResponse._defaults(this);
  }

  GetArticleByCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _code = $v.code;
      _title = $v.title;
      _content = $v.content;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(GetArticleByCodeResponse other) {
    _$v = other as _$GetArticleByCodeResponse;
  }

  @override
  void update(void Function(GetArticleByCodeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  GetArticleByCodeResponse build() => _build();

  _$GetArticleByCodeResponse _build() {
    final _$result = _$v ??
        _$GetArticleByCodeResponse._(
          code: code,
          title: title,
          content: content,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
