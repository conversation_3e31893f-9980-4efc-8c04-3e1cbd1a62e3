// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_get_referrer_by_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppGetReferrerByCodeResponse extends AppGetReferrerByCodeResponse {
  @override
  final String? referrerCode;
  @override
  final String? referrerName;

  factory _$AppGetReferrerByCodeResponse(
          [void Function(AppGetReferrerByCodeResponseBuilder)? updates]) =>
      (AppGetReferrerByCodeResponseBuilder()..update(updates))._build();

  _$AppGetReferrerByCodeResponse._({this.referrerCode, this.referrerName})
      : super._();
  @override
  AppGetReferrerByCodeResponse rebuild(
          void Function(AppGetReferrerByCodeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppGetReferrerByCodeResponseBuilder toBuilder() =>
      AppGetReferrerByCodeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppGetReferrerByCodeResponse &&
        referrerCode == other.referrerCode &&
        referrerName == other.referrerName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, referrerCode.hashCode);
    _$hash = $jc(_$hash, referrerName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppGetReferrerByCodeResponse')
          ..add('referrerCode', referrerCode)
          ..add('referrerName', referrerName))
        .toString();
  }
}

class AppGetReferrerByCodeResponseBuilder
    implements
        Builder<AppGetReferrerByCodeResponse,
            AppGetReferrerByCodeResponseBuilder> {
  _$AppGetReferrerByCodeResponse? _$v;

  String? _referrerCode;
  String? get referrerCode => _$this._referrerCode;
  set referrerCode(String? referrerCode) => _$this._referrerCode = referrerCode;

  String? _referrerName;
  String? get referrerName => _$this._referrerName;
  set referrerName(String? referrerName) => _$this._referrerName = referrerName;

  AppGetReferrerByCodeResponseBuilder() {
    AppGetReferrerByCodeResponse._defaults(this);
  }

  AppGetReferrerByCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _referrerCode = $v.referrerCode;
      _referrerName = $v.referrerName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppGetReferrerByCodeResponse other) {
    _$v = other as _$AppGetReferrerByCodeResponse;
  }

  @override
  void update(void Function(AppGetReferrerByCodeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppGetReferrerByCodeResponse build() => _build();

  _$AppGetReferrerByCodeResponse _build() {
    final _$result = _$v ??
        _$AppGetReferrerByCodeResponse._(
          referrerCode: referrerCode,
          referrerName: referrerName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
