// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_search_users_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminSearchUsersResponse extends AdminSearchUsersResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final JsonObject? data;
  @override
  final PageUser? users;

  factory _$AdminSearchUsersResponse(
          [void Function(AdminSearchUsersResponseBuilder)? updates]) =>
      (AdminSearchUsersResponseBuilder()..update(updates))._build();

  _$AdminSearchUsersResponse._(
      {this.success, this.code, this.message, this.data, this.users})
      : super._();
  @override
  AdminSearchUsersResponse rebuild(
          void Function(AdminSearchUsersResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminSearchUsersResponseBuilder toBuilder() =>
      AdminSearchUsersResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminSearchUsersResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data &&
        users == other.users;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, users.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminSearchUsersResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data)
          ..add('users', users))
        .toString();
  }
}

class AdminSearchUsersResponseBuilder
    implements
        Builder<AdminSearchUsersResponse, AdminSearchUsersResponseBuilder> {
  _$AdminSearchUsersResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  JsonObject? _data;
  JsonObject? get data => _$this._data;
  set data(JsonObject? data) => _$this._data = data;

  PageUserBuilder? _users;
  PageUserBuilder get users => _$this._users ??= PageUserBuilder();
  set users(PageUserBuilder? users) => _$this._users = users;

  AdminSearchUsersResponseBuilder() {
    AdminSearchUsersResponse._defaults(this);
  }

  AdminSearchUsersResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data;
      _users = $v.users?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminSearchUsersResponse other) {
    _$v = other as _$AdminSearchUsersResponse;
  }

  @override
  void update(void Function(AdminSearchUsersResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminSearchUsersResponse build() => _build();

  _$AdminSearchUsersResponse _build() {
    _$AdminSearchUsersResponse _$result;
    try {
      _$result = _$v ??
          _$AdminSearchUsersResponse._(
            success: success,
            code: code,
            message: message,
            data: data,
            users: _users?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'users';
        _users?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminSearchUsersResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
