//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_refresh_token_request.g.dart';

/// AppRefreshTokenRequest
///
/// Properties:
/// * [refreshToken] - The refresh token to be used for obtaining a new access token
@BuiltValue()
abstract class AppRefreshTokenRequest
    implements Built<AppRefreshTokenRequest, AppRefreshTokenRequestBuilder> {
  /// The refresh token to be used for obtaining a new access token
  @BuiltValueField(wireName: r'refreshToken')
  String get refreshToken;

  AppRefreshTokenRequest._();

  factory AppRefreshTokenRequest(
          [void updates(AppRefreshTokenRequestBuilder b)]) =
      _$AppRefreshTokenRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppRefreshTokenRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppRefreshTokenRequest> get serializer =>
      _$AppRefreshTokenRequestSerializer();
}

class _$AppRefreshTokenRequestSerializer
    implements PrimitiveSerializer<AppRefreshTokenRequest> {
  @override
  final Iterable<Type> types = const [
    AppRefreshTokenRequest,
    _$AppRefreshTokenRequest
  ];

  @override
  final String wireName = r'AppRefreshTokenRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppRefreshTokenRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'refreshToken';
    yield serializers.serialize(
      object.refreshToken,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AppRefreshTokenRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppRefreshTokenRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'refreshToken':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.refreshToken = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppRefreshTokenRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppRefreshTokenRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
