// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_create_menu_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const AdminCreateMenuRequestPlatformEnum
    _$adminCreateMenuRequestPlatformEnum_APP =
    const AdminCreateMenuRequestPlatformEnum._('APP');
const AdminCreateMenuRequestPlatformEnum
    _$adminCreateMenuRequestPlatformEnum_WEB =
    const AdminCreateMenuRequestPlatformEnum._('WEB');

AdminCreateMenuRequestPlatformEnum _$adminCreateMenuRequestPlatformEnumValueOf(
    String name) {
  switch (name) {
    case 'APP':
      return _$adminCreateMenuRequestPlatformEnum_APP;
    case 'WEB':
      return _$adminCreateMenuRequestPlatformEnum_WEB;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<AdminCreateMenuRequestPlatformEnum>
    _$adminCreateMenuRequestPlatformEnumValues = BuiltSet<
        AdminCreateMenuRequestPlatformEnum>(const <AdminCreateMenuRequestPlatformEnum>[
  _$adminCreateMenuRequestPlatformEnum_APP,
  _$adminCreateMenuRequestPlatformEnum_WEB,
]);

Serializer<AdminCreateMenuRequestPlatformEnum>
    _$adminCreateMenuRequestPlatformEnumSerializer =
    _$AdminCreateMenuRequestPlatformEnumSerializer();

class _$AdminCreateMenuRequestPlatformEnumSerializer
    implements PrimitiveSerializer<AdminCreateMenuRequestPlatformEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'APP': 'APP',
    'WEB': 'WEB',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'APP': 'APP',
    'WEB': 'WEB',
  };

  @override
  final Iterable<Type> types = const <Type>[AdminCreateMenuRequestPlatformEnum];
  @override
  final String wireName = 'AdminCreateMenuRequestPlatformEnum';

  @override
  Object serialize(
          Serializers serializers, AdminCreateMenuRequestPlatformEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  AdminCreateMenuRequestPlatformEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      AdminCreateMenuRequestPlatformEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$AdminCreateMenuRequest extends AdminCreateMenuRequest {
  @override
  final AdminCreateMenuRequestPlatformEnum platform;
  @override
  final String? code;
  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? parentId;
  @override
  final int? orderNo;
  @override
  final BuiltList<String>? actionIds;

  factory _$AdminCreateMenuRequest(
          [void Function(AdminCreateMenuRequestBuilder)? updates]) =>
      (AdminCreateMenuRequestBuilder()..update(updates))._build();

  _$AdminCreateMenuRequest._(
      {required this.platform,
      this.code,
      this.name,
      this.description,
      this.parentId,
      this.orderNo,
      this.actionIds})
      : super._();
  @override
  AdminCreateMenuRequest rebuild(
          void Function(AdminCreateMenuRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminCreateMenuRequestBuilder toBuilder() =>
      AdminCreateMenuRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminCreateMenuRequest &&
        platform == other.platform &&
        code == other.code &&
        name == other.name &&
        description == other.description &&
        parentId == other.parentId &&
        orderNo == other.orderNo &&
        actionIds == other.actionIds;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, platform.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, parentId.hashCode);
    _$hash = $jc(_$hash, orderNo.hashCode);
    _$hash = $jc(_$hash, actionIds.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminCreateMenuRequest')
          ..add('platform', platform)
          ..add('code', code)
          ..add('name', name)
          ..add('description', description)
          ..add('parentId', parentId)
          ..add('orderNo', orderNo)
          ..add('actionIds', actionIds))
        .toString();
  }
}

class AdminCreateMenuRequestBuilder
    implements Builder<AdminCreateMenuRequest, AdminCreateMenuRequestBuilder> {
  _$AdminCreateMenuRequest? _$v;

  AdminCreateMenuRequestPlatformEnum? _platform;
  AdminCreateMenuRequestPlatformEnum? get platform => _$this._platform;
  set platform(AdminCreateMenuRequestPlatformEnum? platform) =>
      _$this._platform = platform;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _parentId;
  String? get parentId => _$this._parentId;
  set parentId(String? parentId) => _$this._parentId = parentId;

  int? _orderNo;
  int? get orderNo => _$this._orderNo;
  set orderNo(int? orderNo) => _$this._orderNo = orderNo;

  ListBuilder<String>? _actionIds;
  ListBuilder<String> get actionIds =>
      _$this._actionIds ??= ListBuilder<String>();
  set actionIds(ListBuilder<String>? actionIds) =>
      _$this._actionIds = actionIds;

  AdminCreateMenuRequestBuilder() {
    AdminCreateMenuRequest._defaults(this);
  }

  AdminCreateMenuRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _platform = $v.platform;
      _code = $v.code;
      _name = $v.name;
      _description = $v.description;
      _parentId = $v.parentId;
      _orderNo = $v.orderNo;
      _actionIds = $v.actionIds?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminCreateMenuRequest other) {
    _$v = other as _$AdminCreateMenuRequest;
  }

  @override
  void update(void Function(AdminCreateMenuRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminCreateMenuRequest build() => _build();

  _$AdminCreateMenuRequest _build() {
    _$AdminCreateMenuRequest _$result;
    try {
      _$result = _$v ??
          _$AdminCreateMenuRequest._(
            platform: BuiltValueNullFieldError.checkNotNull(
                platform, r'AdminCreateMenuRequest', 'platform'),
            code: code,
            name: name,
            description: description,
            parentId: parentId,
            orderNo: orderNo,
            actionIds: _actionIds?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'actionIds';
        _actionIds?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminCreateMenuRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
