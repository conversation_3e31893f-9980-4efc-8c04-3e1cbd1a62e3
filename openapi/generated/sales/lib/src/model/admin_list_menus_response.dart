//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:sales_app_api/src/model/menu_dto.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_list_menus_response.g.dart';

/// AdminListMenusResponse
///
/// Properties:
/// * [menuApp]
/// * [menuWeb]
@BuiltValue()
abstract class AdminListMenusResponse
    implements Built<AdminListMenusResponse, AdminListMenusResponseBuilder> {
  @BuiltValueField(wireName: r'menuApp')
  BuiltList<MenuDto>? get menuApp;

  @BuiltValueField(wireName: r'menuWeb')
  BuiltList<MenuDto>? get menuWeb;

  AdminListMenusResponse._();

  factory AdminListMenusResponse(
          [void updates(AdminListMenusResponseBuilder b)]) =
      _$AdminListMenusResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminListMenusResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminListMenusResponse> get serializer =>
      _$AdminListMenusResponseSerializer();
}

class _$AdminListMenusResponseSerializer
    implements PrimitiveSerializer<AdminListMenusResponse> {
  @override
  final Iterable<Type> types = const [
    AdminListMenusResponse,
    _$AdminListMenusResponse
  ];

  @override
  final String wireName = r'AdminListMenusResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminListMenusResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.menuApp != null) {
      yield r'menuApp';
      yield serializers.serialize(
        object.menuApp,
        specifiedType: const FullType(BuiltList, [FullType(MenuDto)]),
      );
    }
    if (object.menuWeb != null) {
      yield r'menuWeb';
      yield serializers.serialize(
        object.menuWeb,
        specifiedType: const FullType(BuiltList, [FullType(MenuDto)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminListMenusResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminListMenusResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'menuApp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(MenuDto)]),
          ) as BuiltList<MenuDto>;
          result.menuApp.replace(valueDes);
          break;
        case r'menuWeb':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(MenuDto)]),
          ) as BuiltList<MenuDto>;
          result.menuWeb.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminListMenusResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminListMenusResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
