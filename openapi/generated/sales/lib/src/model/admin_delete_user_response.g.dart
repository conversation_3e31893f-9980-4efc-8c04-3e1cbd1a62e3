// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_delete_user_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminDeleteUserResponse extends AdminDeleteUserResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final JsonObject? data;
  @override
  final String? userId;

  factory _$AdminDeleteUserResponse(
          [void Function(AdminDeleteUserResponseBuilder)? updates]) =>
      (AdminDeleteUserResponseBuilder()..update(updates))._build();

  _$AdminDeleteUserResponse._(
      {this.success, this.code, this.message, this.data, this.userId})
      : super._();
  @override
  AdminDeleteUserResponse rebuild(
          void Function(AdminDeleteUserResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminDeleteUserResponseBuilder toBuilder() =>
      AdminDeleteUserResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminDeleteUserResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data &&
        userId == other.userId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminDeleteUserResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data)
          ..add('userId', userId))
        .toString();
  }
}

class AdminDeleteUserResponseBuilder
    implements
        Builder<AdminDeleteUserResponse, AdminDeleteUserResponseBuilder> {
  _$AdminDeleteUserResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  JsonObject? _data;
  JsonObject? get data => _$this._data;
  set data(JsonObject? data) => _$this._data = data;

  String? _userId;
  String? get userId => _$this._userId;
  set userId(String? userId) => _$this._userId = userId;

  AdminDeleteUserResponseBuilder() {
    AdminDeleteUserResponse._defaults(this);
  }

  AdminDeleteUserResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data;
      _userId = $v.userId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminDeleteUserResponse other) {
    _$v = other as _$AdminDeleteUserResponse;
  }

  @override
  void update(void Function(AdminDeleteUserResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminDeleteUserResponse build() => _build();

  _$AdminDeleteUserResponse _build() {
    final _$result = _$v ??
        _$AdminDeleteUserResponse._(
          success: success,
          code: code,
          message: message,
          data: data,
          userId: userId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
