// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_logout_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppLogoutResponse extends BaseResponseAppLogoutResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppLogoutResponse? data;

  factory _$BaseResponseAppLogoutResponse(
          [void Function(BaseResponseAppLogoutResponseBuilder)? updates]) =>
      (BaseResponseAppLogoutResponseBuilder()..update(updates))._build();

  _$BaseResponseAppLogoutResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppLogoutResponse rebuild(
          void Function(BaseResponseAppLogoutResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppLogoutResponseBuilder toBuilder() =>
      BaseResponseAppLogoutResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppLogoutResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAppLogoutResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppLogoutResponseBuilder
    implements
        Builder<BaseResponseAppLogoutResponse,
            BaseResponseAppLogoutResponseBuilder> {
  _$BaseResponseAppLogoutResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppLogoutResponseBuilder? _data;
  AppLogoutResponseBuilder get data =>
      _$this._data ??= AppLogoutResponseBuilder();
  set data(AppLogoutResponseBuilder? data) => _$this._data = data;

  BaseResponseAppLogoutResponseBuilder() {
    BaseResponseAppLogoutResponse._defaults(this);
  }

  BaseResponseAppLogoutResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppLogoutResponse other) {
    _$v = other as _$BaseResponseAppLogoutResponse;
  }

  @override
  void update(void Function(BaseResponseAppLogoutResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppLogoutResponse build() => _build();

  _$BaseResponseAppLogoutResponse _build() {
    _$BaseResponseAppLogoutResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppLogoutResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppLogoutResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
