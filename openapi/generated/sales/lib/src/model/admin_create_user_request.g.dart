// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_create_user_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminCreateUserRequest extends AdminCreateUserRequest {
  @override
  final String? username;
  @override
  final String? password;
  @override
  final String? fullName;
  @override
  final String? email;
  @override
  final String? phone;
  @override
  final String? departmentId;

  factory _$AdminCreateUserRequest(
          [void Function(AdminCreateUserRequestBuilder)? updates]) =>
      (AdminCreateUserRequestBuilder()..update(updates))._build();

  _$AdminCreateUserRequest._(
      {this.username,
      this.password,
      this.fullName,
      this.email,
      this.phone,
      this.departmentId})
      : super._();
  @override
  AdminCreateUserRequest rebuild(
          void Function(AdminCreateUserRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminCreateUserRequestBuilder toBuilder() =>
      AdminCreateUserRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminCreateUserRequest &&
        username == other.username &&
        password == other.password &&
        fullName == other.fullName &&
        email == other.email &&
        phone == other.phone &&
        departmentId == other.departmentId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, username.hashCode);
    _$hash = $jc(_$hash, password.hashCode);
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, phone.hashCode);
    _$hash = $jc(_$hash, departmentId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminCreateUserRequest')
          ..add('username', username)
          ..add('password', password)
          ..add('fullName', fullName)
          ..add('email', email)
          ..add('phone', phone)
          ..add('departmentId', departmentId))
        .toString();
  }
}

class AdminCreateUserRequestBuilder
    implements Builder<AdminCreateUserRequest, AdminCreateUserRequestBuilder> {
  _$AdminCreateUserRequest? _$v;

  String? _username;
  String? get username => _$this._username;
  set username(String? username) => _$this._username = username;

  String? _password;
  String? get password => _$this._password;
  set password(String? password) => _$this._password = password;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _phone;
  String? get phone => _$this._phone;
  set phone(String? phone) => _$this._phone = phone;

  String? _departmentId;
  String? get departmentId => _$this._departmentId;
  set departmentId(String? departmentId) => _$this._departmentId = departmentId;

  AdminCreateUserRequestBuilder() {
    AdminCreateUserRequest._defaults(this);
  }

  AdminCreateUserRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _username = $v.username;
      _password = $v.password;
      _fullName = $v.fullName;
      _email = $v.email;
      _phone = $v.phone;
      _departmentId = $v.departmentId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminCreateUserRequest other) {
    _$v = other as _$AdminCreateUserRequest;
  }

  @override
  void update(void Function(AdminCreateUserRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminCreateUserRequest build() => _build();

  _$AdminCreateUserRequest _build() {
    final _$result = _$v ??
        _$AdminCreateUserRequest._(
          username: username,
          password: password,
          fullName: fullName,
          email: email,
          phone: phone,
          departmentId: departmentId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
