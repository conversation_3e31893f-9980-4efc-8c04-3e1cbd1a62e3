//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_logout_response.g.dart';

/// AppLogoutResponse
///
/// Properties:
/// * [logoutAt]
@BuiltValue()
abstract class AppLogoutResponse
    implements Built<AppLogoutResponse, AppLogoutResponseBuilder> {
  @BuiltValueField(wireName: r'logoutAt')
  DateTime? get logoutAt;

  AppLogoutResponse._();

  factory AppLogoutResponse([void updates(AppLogoutResponseBuilder b)]) =
      _$AppLogoutResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppLogoutResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppLogoutResponse> get serializer =>
      _$AppLogoutResponseSerializer();
}

class _$AppLogoutResponseSerializer
    implements PrimitiveSerializer<AppLogoutResponse> {
  @override
  final Iterable<Type> types = const [AppLogoutResponse, _$AppLogoutResponse];

  @override
  final String wireName = r'AppLogoutResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppLogoutResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.logoutAt != null) {
      yield r'logoutAt';
      yield serializers.serialize(
        object.logoutAt,
        specifiedType: const FullType(DateTime),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppLogoutResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppLogoutResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'logoutAt':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.logoutAt = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppLogoutResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppLogoutResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
