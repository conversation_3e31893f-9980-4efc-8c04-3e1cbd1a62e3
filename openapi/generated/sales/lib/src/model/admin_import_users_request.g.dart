// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_import_users_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminImportUsersRequest extends AdminImportUsersRequest {
  @override
  final BuiltList<AdminCreateUserRequest>? users;

  factory _$AdminImportUsersRequest(
          [void Function(AdminImportUsersRequestBuilder)? updates]) =>
      (AdminImportUsersRequestBuilder()..update(updates))._build();

  _$AdminImportUsersRequest._({this.users}) : super._();
  @override
  AdminImportUsersRequest rebuild(
          void Function(AdminImportUsersRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminImportUsersRequestBuilder toBuilder() =>
      AdminImportUsersRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminImportUsersRequest && users == other.users;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, users.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminImportUsersRequest')
          ..add('users', users))
        .toString();
  }
}

class AdminImportUsersRequestBuilder
    implements
        Builder<AdminImportUsersRequest, AdminImportUsersRequestBuilder> {
  _$AdminImportUsersRequest? _$v;

  ListBuilder<AdminCreateUserRequest>? _users;
  ListBuilder<AdminCreateUserRequest> get users =>
      _$this._users ??= ListBuilder<AdminCreateUserRequest>();
  set users(ListBuilder<AdminCreateUserRequest>? users) =>
      _$this._users = users;

  AdminImportUsersRequestBuilder() {
    AdminImportUsersRequest._defaults(this);
  }

  AdminImportUsersRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _users = $v.users?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminImportUsersRequest other) {
    _$v = other as _$AdminImportUsersRequest;
  }

  @override
  void update(void Function(AdminImportUsersRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminImportUsersRequest build() => _build();

  _$AdminImportUsersRequest _build() {
    _$AdminImportUsersRequest _$result;
    try {
      _$result = _$v ??
          _$AdminImportUsersRequest._(
            users: _users?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'users';
        _users?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminImportUsersRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
