// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'system_config_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$SystemConfigDto extends SystemConfigDto {
  @override
  final String? id;
  @override
  final String? code;
  @override
  final String? label;

  factory _$SystemConfigDto([void Function(SystemConfigDtoBuilder)? updates]) =>
      (SystemConfigDtoBuilder()..update(updates))._build();

  _$SystemConfigDto._({this.id, this.code, this.label}) : super._();
  @override
  SystemConfigDto rebuild(void Function(SystemConfigDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  SystemConfigDtoBuilder toBuilder() => SystemConfigDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is SystemConfigDto &&
        id == other.id &&
        code == other.code &&
        label == other.label;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, label.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'SystemConfigDto')
          ..add('id', id)
          ..add('code', code)
          ..add('label', label))
        .toString();
  }
}

class SystemConfigDtoBuilder
    implements Builder<SystemConfigDto, SystemConfigDtoBuilder> {
  _$SystemConfigDto? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _label;
  String? get label => _$this._label;
  set label(String? label) => _$this._label = label;

  SystemConfigDtoBuilder() {
    SystemConfigDto._defaults(this);
  }

  SystemConfigDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _code = $v.code;
      _label = $v.label;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(SystemConfigDto other) {
    _$v = other as _$SystemConfigDto;
  }

  @override
  void update(void Function(SystemConfigDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  SystemConfigDto build() => _build();

  _$SystemConfigDto _build() {
    final _$result = _$v ??
        _$SystemConfigDto._(
          id: id,
          code: code,
          label: label,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
