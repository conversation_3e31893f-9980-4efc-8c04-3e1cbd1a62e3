// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_referral_register_collab_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppReferralRegisterCollabResponse
    extends BaseResponseAppReferralRegisterCollabResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppReferralRegisterCollabResponse? data;

  factory _$BaseResponseAppReferralRegisterCollabResponse(
          [void Function(BaseResponseAppReferralRegisterCollabResponseBuilder)?
              updates]) =>
      (BaseResponseAppReferralRegisterCollabResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseAppReferralRegisterCollabResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppReferralRegisterCollabResponse rebuild(
          void Function(BaseResponseAppReferralRegisterCollabResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppReferralRegisterCollabResponseBuilder toBuilder() =>
      BaseResponseAppReferralRegisterCollabResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppReferralRegisterCollabResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAppReferralRegisterCollabResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppReferralRegisterCollabResponseBuilder
    implements
        Builder<BaseResponseAppReferralRegisterCollabResponse,
            BaseResponseAppReferralRegisterCollabResponseBuilder> {
  _$BaseResponseAppReferralRegisterCollabResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppReferralRegisterCollabResponseBuilder? _data;
  AppReferralRegisterCollabResponseBuilder get data =>
      _$this._data ??= AppReferralRegisterCollabResponseBuilder();
  set data(AppReferralRegisterCollabResponseBuilder? data) =>
      _$this._data = data;

  BaseResponseAppReferralRegisterCollabResponseBuilder() {
    BaseResponseAppReferralRegisterCollabResponse._defaults(this);
  }

  BaseResponseAppReferralRegisterCollabResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppReferralRegisterCollabResponse other) {
    _$v = other as _$BaseResponseAppReferralRegisterCollabResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppReferralRegisterCollabResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppReferralRegisterCollabResponse build() => _build();

  _$BaseResponseAppReferralRegisterCollabResponse _build() {
    _$BaseResponseAppReferralRegisterCollabResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppReferralRegisterCollabResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppReferralRegisterCollabResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
