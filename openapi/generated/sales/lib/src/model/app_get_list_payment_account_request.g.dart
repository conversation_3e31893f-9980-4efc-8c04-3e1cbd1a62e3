// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_get_list_payment_account_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppGetListPaymentAccountRequest
    extends AppGetListPaymentAccountRequest {
  @override
  final String idCardNo;

  factory _$AppGetListPaymentAccountRequest(
          [void Function(AppGetListPaymentAccountRequestBuilder)? updates]) =>
      (AppGetListPaymentAccountRequestBuilder()..update(updates))._build();

  _$AppGetListPaymentAccountRequest._({required this.idCardNo}) : super._();
  @override
  AppGetListPaymentAccountRequest rebuild(
          void Function(AppGetListPaymentAccountRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppGetListPaymentAccountRequestBuilder toBuilder() =>
      AppGetListPaymentAccountRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppGetListPaymentAccountRequest &&
        idCardNo == other.idCardNo;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, idCardNo.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppGetListPaymentAccountRequest')
          ..add('idCardNo', idCardNo))
        .toString();
  }
}

class AppGetListPaymentAccountRequestBuilder
    implements
        Builder<AppGetListPaymentAccountRequest,
            AppGetListPaymentAccountRequestBuilder> {
  _$AppGetListPaymentAccountRequest? _$v;

  String? _idCardNo;
  String? get idCardNo => _$this._idCardNo;
  set idCardNo(String? idCardNo) => _$this._idCardNo = idCardNo;

  AppGetListPaymentAccountRequestBuilder() {
    AppGetListPaymentAccountRequest._defaults(this);
  }

  AppGetListPaymentAccountRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _idCardNo = $v.idCardNo;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppGetListPaymentAccountRequest other) {
    _$v = other as _$AppGetListPaymentAccountRequest;
  }

  @override
  void update(void Function(AppGetListPaymentAccountRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppGetListPaymentAccountRequest build() => _build();

  _$AppGetListPaymentAccountRequest _build() {
    final _$result = _$v ??
        _$AppGetListPaymentAccountRequest._(
          idCardNo: BuiltValueNullFieldError.checkNotNull(
              idCardNo, r'AppGetListPaymentAccountRequest', 'idCardNo'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
