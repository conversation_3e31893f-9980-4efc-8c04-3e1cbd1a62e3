// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_refresh_token_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppRefreshTokenResponse extends AppRefreshTokenResponse {
  @override
  final String? accessToken;
  @override
  final int? expiresIn;
  @override
  final String? refreshToken;
  @override
  final int? refreshExpiresIn;
  @override
  final String? tokenType;

  factory _$AppRefreshTokenResponse(
          [void Function(AppRefreshTokenResponseBuilder)? updates]) =>
      (AppRefreshTokenResponseBuilder()..update(updates))._build();

  _$AppRefreshTokenResponse._(
      {this.accessToken,
      this.expiresIn,
      this.refreshToken,
      this.refreshExpiresIn,
      this.tokenType})
      : super._();
  @override
  AppRefreshTokenResponse rebuild(
          void Function(AppRefreshTokenResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppRefreshTokenResponseBuilder toBuilder() =>
      AppRefreshTokenResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppRefreshTokenResponse &&
        accessToken == other.accessToken &&
        expiresIn == other.expiresIn &&
        refreshToken == other.refreshToken &&
        refreshExpiresIn == other.refreshExpiresIn &&
        tokenType == other.tokenType;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accessToken.hashCode);
    _$hash = $jc(_$hash, expiresIn.hashCode);
    _$hash = $jc(_$hash, refreshToken.hashCode);
    _$hash = $jc(_$hash, refreshExpiresIn.hashCode);
    _$hash = $jc(_$hash, tokenType.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppRefreshTokenResponse')
          ..add('accessToken', accessToken)
          ..add('expiresIn', expiresIn)
          ..add('refreshToken', refreshToken)
          ..add('refreshExpiresIn', refreshExpiresIn)
          ..add('tokenType', tokenType))
        .toString();
  }
}

class AppRefreshTokenResponseBuilder
    implements
        Builder<AppRefreshTokenResponse, AppRefreshTokenResponseBuilder> {
  _$AppRefreshTokenResponse? _$v;

  String? _accessToken;
  String? get accessToken => _$this._accessToken;
  set accessToken(String? accessToken) => _$this._accessToken = accessToken;

  int? _expiresIn;
  int? get expiresIn => _$this._expiresIn;
  set expiresIn(int? expiresIn) => _$this._expiresIn = expiresIn;

  String? _refreshToken;
  String? get refreshToken => _$this._refreshToken;
  set refreshToken(String? refreshToken) => _$this._refreshToken = refreshToken;

  int? _refreshExpiresIn;
  int? get refreshExpiresIn => _$this._refreshExpiresIn;
  set refreshExpiresIn(int? refreshExpiresIn) =>
      _$this._refreshExpiresIn = refreshExpiresIn;

  String? _tokenType;
  String? get tokenType => _$this._tokenType;
  set tokenType(String? tokenType) => _$this._tokenType = tokenType;

  AppRefreshTokenResponseBuilder() {
    AppRefreshTokenResponse._defaults(this);
  }

  AppRefreshTokenResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accessToken = $v.accessToken;
      _expiresIn = $v.expiresIn;
      _refreshToken = $v.refreshToken;
      _refreshExpiresIn = $v.refreshExpiresIn;
      _tokenType = $v.tokenType;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppRefreshTokenResponse other) {
    _$v = other as _$AppRefreshTokenResponse;
  }

  @override
  void update(void Function(AppRefreshTokenResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppRefreshTokenResponse build() => _build();

  _$AppRefreshTokenResponse _build() {
    final _$result = _$v ??
        _$AppRefreshTokenResponse._(
          accessToken: accessToken,
          expiresIn: expiresIn,
          refreshToken: refreshToken,
          refreshExpiresIn: refreshExpiresIn,
          tokenType: tokenType,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
