// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_get_list_province_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppGetListProvinceResponse extends AppGetListProvinceResponse {
  @override
  final BuiltList<AppProvinceDto>? provinces;

  factory _$AppGetListProvinceResponse(
          [void Function(AppGetListProvinceResponseBuilder)? updates]) =>
      (AppGetListProvinceResponseBuilder()..update(updates))._build();

  _$AppGetListProvinceResponse._({this.provinces}) : super._();
  @override
  AppGetListProvinceResponse rebuild(
          void Function(AppGetListProvinceResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppGetListProvinceResponseBuilder toBuilder() =>
      AppGetListProvinceResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppGetListProvinceResponse && provinces == other.provinces;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, provinces.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppGetListProvinceResponse')
          ..add('provinces', provinces))
        .toString();
  }
}

class AppGetListProvinceResponseBuilder
    implements
        Builder<AppGetListProvinceResponse, AppGetListProvinceResponseBuilder> {
  _$AppGetListProvinceResponse? _$v;

  ListBuilder<AppProvinceDto>? _provinces;
  ListBuilder<AppProvinceDto> get provinces =>
      _$this._provinces ??= ListBuilder<AppProvinceDto>();
  set provinces(ListBuilder<AppProvinceDto>? provinces) =>
      _$this._provinces = provinces;

  AppGetListProvinceResponseBuilder() {
    AppGetListProvinceResponse._defaults(this);
  }

  AppGetListProvinceResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _provinces = $v.provinces?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppGetListProvinceResponse other) {
    _$v = other as _$AppGetListProvinceResponse;
  }

  @override
  void update(void Function(AppGetListProvinceResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppGetListProvinceResponse build() => _build();

  _$AppGetListProvinceResponse _build() {
    _$AppGetListProvinceResponse _$result;
    try {
      _$result = _$v ??
          _$AppGetListProvinceResponse._(
            provinces: _provinces?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'provinces';
        _provinces?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AppGetListProvinceResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
