//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'role_dto.g.dart';

/// RoleDto
///
/// Properties:
/// * [code] - <PERSON><PERSON> role
/// * [name] - Tên role
@BuiltValue()
abstract class RoleDto implements Built<RoleDto, RoleDtoBuilder> {
  /// Mã role
  @BuiltValueField(wireName: r'code')
  String? get code;

  /// Tên role
  @BuiltValueField(wireName: r'name')
  String? get name;

  RoleDto._();

  factory RoleDto([void updates(RoleDtoBuilder b)]) = _$RoleDto;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(RoleDtoBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<RoleDto> get serializer => _$RoleDtoSerializer();
}

class _$RoleDtoSerializer implements PrimitiveSerializer<RoleDto> {
  @override
  final Iterable<Type> types = const [RoleDto, _$RoleDto];

  @override
  final String wireName = r'RoleDto';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    RoleDto object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(String),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    RoleDto object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required RoleDtoBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  RoleDto deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = RoleDtoBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
