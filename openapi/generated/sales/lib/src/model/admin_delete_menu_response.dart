//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_delete_menu_response.g.dart';

/// AdminDeleteMenuResponse
///
/// Properties:
/// * [id]
@BuiltValue()
abstract class AdminDeleteMenuResponse
    implements Built<AdminDeleteMenuResponse, AdminDeleteMenuResponseBuilder> {
  @BuiltValueField(wireName: r'id')
  String? get id;

  AdminDeleteMenuResponse._();

  factory AdminDeleteMenuResponse(
          [void updates(AdminDeleteMenuResponseBuilder b)]) =
      _$AdminDeleteMenuResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminDeleteMenuResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminDeleteMenuResponse> get serializer =>
      _$AdminDeleteMenuResponseSerializer();
}

class _$AdminDeleteMenuResponseSerializer
    implements PrimitiveSerializer<AdminDeleteMenuResponse> {
  @override
  final Iterable<Type> types = const [
    AdminDeleteMenuResponse,
    _$AdminDeleteMenuResponse
  ];

  @override
  final String wireName = r'AdminDeleteMenuResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminDeleteMenuResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminDeleteMenuResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminDeleteMenuResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminDeleteMenuResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminDeleteMenuResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
