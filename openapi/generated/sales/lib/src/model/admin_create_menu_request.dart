//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_create_menu_request.g.dart';

/// AdminCreateMenuRequest
///
/// Properties:
/// * [platform] - Menu theo platform
/// * [code] - Mã menu
/// * [name] - Tên menu
/// * [description] - <PERSON><PERSON> tả menu
/// * [parentId] - <PERSON><PERSON> cha (nếu có)
/// * [orderNo] - Sử dụng để order trên UI
/// * [actionIds] - <PERSON>h sách ID của các action gắn với menu
@BuiltValue()
abstract class AdminCreateMenuRequest
    implements Built<AdminCreateMenuRequest, AdminCreateMenuRequestBuilder> {
  /// Menu theo platform
  @BuiltValueField(wireName: r'platform')
  AdminCreateMenuRequestPlatformEnum get platform;
  // enum platformEnum {  APP,  WEB,  };

  /// Mã menu
  @BuiltValueField(wireName: r'code')
  String? get code;

  /// Tên menu
  @BuiltValueField(wireName: r'name')
  String? get name;

  /// Mô tả menu
  @BuiltValueField(wireName: r'description')
  String? get description;

  /// Menu cha (nếu có)
  @BuiltValueField(wireName: r'parentId')
  String? get parentId;

  /// Sử dụng để order trên UI
  @BuiltValueField(wireName: r'orderNo')
  int? get orderNo;

  /// Danh sách ID của các action gắn với menu
  @BuiltValueField(wireName: r'actionIds')
  BuiltList<String>? get actionIds;

  AdminCreateMenuRequest._();

  factory AdminCreateMenuRequest(
          [void updates(AdminCreateMenuRequestBuilder b)]) =
      _$AdminCreateMenuRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminCreateMenuRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminCreateMenuRequest> get serializer =>
      _$AdminCreateMenuRequestSerializer();
}

class _$AdminCreateMenuRequestSerializer
    implements PrimitiveSerializer<AdminCreateMenuRequest> {
  @override
  final Iterable<Type> types = const [
    AdminCreateMenuRequest,
    _$AdminCreateMenuRequest
  ];

  @override
  final String wireName = r'AdminCreateMenuRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminCreateMenuRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'platform';
    yield serializers.serialize(
      object.platform,
      specifiedType: const FullType(AdminCreateMenuRequestPlatformEnum),
    );
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(String),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType(String),
      );
    }
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType(String),
      );
    }
    if (object.parentId != null) {
      yield r'parentId';
      yield serializers.serialize(
        object.parentId,
        specifiedType: const FullType(String),
      );
    }
    if (object.orderNo != null) {
      yield r'orderNo';
      yield serializers.serialize(
        object.orderNo,
        specifiedType: const FullType(int),
      );
    }
    if (object.actionIds != null) {
      yield r'actionIds';
      yield serializers.serialize(
        object.actionIds,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminCreateMenuRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminCreateMenuRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'platform':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(AdminCreateMenuRequestPlatformEnum),
          ) as AdminCreateMenuRequestPlatformEnum;
          result.platform = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.description = valueDes;
          break;
        case r'parentId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.parentId = valueDes;
          break;
        case r'orderNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.orderNo = valueDes;
          break;
        case r'actionIds':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.actionIds.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminCreateMenuRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminCreateMenuRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class AdminCreateMenuRequestPlatformEnum extends EnumClass {
  /// Menu theo platform
  @BuiltValueEnumConst(wireName: r'APP')
  static const AdminCreateMenuRequestPlatformEnum APP =
      _$adminCreateMenuRequestPlatformEnum_APP;

  /// Menu theo platform
  @BuiltValueEnumConst(wireName: r'WEB')
  static const AdminCreateMenuRequestPlatformEnum WEB =
      _$adminCreateMenuRequestPlatformEnum_WEB;

  static Serializer<AdminCreateMenuRequestPlatformEnum> get serializer =>
      _$adminCreateMenuRequestPlatformEnumSerializer;

  const AdminCreateMenuRequestPlatformEnum._(String name) : super(name);

  static BuiltSet<AdminCreateMenuRequestPlatformEnum> get values =>
      _$adminCreateMenuRequestPlatformEnumValues;
  static AdminCreateMenuRequestPlatformEnum valueOf(String name) =>
      _$adminCreateMenuRequestPlatformEnumValueOf(name);
}
