// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_logout_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppLogoutRequest extends AppLogoutRequest {
  @override
  final String accessToken;
  @override
  final String refreshToken;

  factory _$AppLogoutRequest(
          [void Function(AppLogoutRequestBuilder)? updates]) =>
      (AppLogoutRequestBuilder()..update(updates))._build();

  _$AppLogoutRequest._({required this.accessToken, required this.refreshToken})
      : super._();
  @override
  AppLogoutRequest rebuild(void Function(AppLogoutRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppLogoutRequestBuilder toBuilder() =>
      AppLogoutRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppLogoutRequest &&
        accessToken == other.accessToken &&
        refreshToken == other.refreshToken;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accessToken.hashCode);
    _$hash = $jc(_$hash, refreshToken.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppLogoutRequest')
          ..add('accessToken', accessToken)
          ..add('refreshToken', refreshToken))
        .toString();
  }
}

class AppLogoutRequestBuilder
    implements Builder<AppLogoutRequest, AppLogoutRequestBuilder> {
  _$AppLogoutRequest? _$v;

  String? _accessToken;
  String? get accessToken => _$this._accessToken;
  set accessToken(String? accessToken) => _$this._accessToken = accessToken;

  String? _refreshToken;
  String? get refreshToken => _$this._refreshToken;
  set refreshToken(String? refreshToken) => _$this._refreshToken = refreshToken;

  AppLogoutRequestBuilder() {
    AppLogoutRequest._defaults(this);
  }

  AppLogoutRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accessToken = $v.accessToken;
      _refreshToken = $v.refreshToken;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppLogoutRequest other) {
    _$v = other as _$AppLogoutRequest;
  }

  @override
  void update(void Function(AppLogoutRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppLogoutRequest build() => _build();

  _$AppLogoutRequest _build() {
    final _$result = _$v ??
        _$AppLogoutRequest._(
          accessToken: BuiltValueNullFieldError.checkNotNull(
              accessToken, r'AppLogoutRequest', 'accessToken'),
          refreshToken: BuiltValueNullFieldError.checkNotNull(
              refreshToken, r'AppLogoutRequest', 'refreshToken'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
