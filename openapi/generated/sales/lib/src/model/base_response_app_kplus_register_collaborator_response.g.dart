// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_kplus_register_collaborator_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppKplusRegisterCollaboratorResponse
    extends BaseResponseAppKplusRegisterCollaboratorResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppKplusRegisterCollaboratorResponse? data;

  factory _$BaseResponseAppKplusRegisterCollaboratorResponse(
          [void Function(
                  BaseResponseAppKplusRegisterCollaboratorResponseBuilder)?
              updates]) =>
      (BaseResponseAppKplusRegisterCollaboratorResponseBuilder()
            ..update(updates))
          ._build();

  _$BaseResponseAppKplusRegisterCollaboratorResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppKplusRegisterCollaboratorResponse rebuild(
          void Function(BaseResponseAppKplusRegisterCollaboratorResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppKplusRegisterCollaboratorResponseBuilder toBuilder() =>
      BaseResponseAppKplusRegisterCollaboratorResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppKplusRegisterCollaboratorResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAppKplusRegisterCollaboratorResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppKplusRegisterCollaboratorResponseBuilder
    implements
        Builder<BaseResponseAppKplusRegisterCollaboratorResponse,
            BaseResponseAppKplusRegisterCollaboratorResponseBuilder> {
  _$BaseResponseAppKplusRegisterCollaboratorResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppKplusRegisterCollaboratorResponseBuilder? _data;
  AppKplusRegisterCollaboratorResponseBuilder get data =>
      _$this._data ??= AppKplusRegisterCollaboratorResponseBuilder();
  set data(AppKplusRegisterCollaboratorResponseBuilder? data) =>
      _$this._data = data;

  BaseResponseAppKplusRegisterCollaboratorResponseBuilder() {
    BaseResponseAppKplusRegisterCollaboratorResponse._defaults(this);
  }

  BaseResponseAppKplusRegisterCollaboratorResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppKplusRegisterCollaboratorResponse other) {
    _$v = other as _$BaseResponseAppKplusRegisterCollaboratorResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppKplusRegisterCollaboratorResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppKplusRegisterCollaboratorResponse build() => _build();

  _$BaseResponseAppKplusRegisterCollaboratorResponse _build() {
    _$BaseResponseAppKplusRegisterCollaboratorResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppKplusRegisterCollaboratorResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppKplusRegisterCollaboratorResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
