// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_referral_register_collab_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const AppReferralRegisterCollabRequestIdCardTypeEnum
    _$appReferralRegisterCollabRequestIdCardTypeEnum_CHIP_ID =
    const AppReferralRegisterCollabRequestIdCardTypeEnum._('CHIP_ID');
const AppReferralRegisterCollabRequestIdCardTypeEnum
    _$appReferralRegisterCollabRequestIdCardTypeEnum_PASSPORT =
    const AppReferralRegisterCollabRequestIdCardTypeEnum._('PASSPORT');

AppReferralRegisterCollabRequestIdCardTypeEnum
    _$appReferralRegisterCollabRequestIdCardTypeEnumValueOf(String name) {
  switch (name) {
    case 'CHIP_ID':
      return _$appReferralRegisterCollabRequestIdCardTypeEnum_CHIP_ID;
    case 'PASSPORT':
      return _$appReferralRegisterCollabRequestIdCardTypeEnum_PASSPORT;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<AppReferralRegisterCollabRequestIdCardTypeEnum>
    _$appReferralRegisterCollabRequestIdCardTypeEnumValues = BuiltSet<
        AppReferralRegisterCollabRequestIdCardTypeEnum>(const <AppReferralRegisterCollabRequestIdCardTypeEnum>[
  _$appReferralRegisterCollabRequestIdCardTypeEnum_CHIP_ID,
  _$appReferralRegisterCollabRequestIdCardTypeEnum_PASSPORT,
]);

Serializer<AppReferralRegisterCollabRequestIdCardTypeEnum>
    _$appReferralRegisterCollabRequestIdCardTypeEnumSerializer =
    _$AppReferralRegisterCollabRequestIdCardTypeEnumSerializer();

class _$AppReferralRegisterCollabRequestIdCardTypeEnumSerializer
    implements
        PrimitiveSerializer<AppReferralRegisterCollabRequestIdCardTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'CHIP_ID': 'CHIP_ID',
    'PASSPORT': 'PASSPORT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'CHIP_ID': 'CHIP_ID',
    'PASSPORT': 'PASSPORT',
  };

  @override
  final Iterable<Type> types = const <Type>[
    AppReferralRegisterCollabRequestIdCardTypeEnum
  ];
  @override
  final String wireName = 'AppReferralRegisterCollabRequestIdCardTypeEnum';

  @override
  Object serialize(Serializers serializers,
          AppReferralRegisterCollabRequestIdCardTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  AppReferralRegisterCollabRequestIdCardTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      AppReferralRegisterCollabRequestIdCardTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$AppReferralRegisterCollabRequest
    extends AppReferralRegisterCollabRequest {
  @override
  final String fullName;
  @override
  final AppReferralRegisterCollabRequestIdCardTypeEnum idCardType;
  @override
  final String idCardNo;
  @override
  final String phoneNumber;
  @override
  final String branchId;
  @override
  final String frontCardUrl;
  @override
  final String backCardUrl;
  @override
  final String? permanentAddress;
  @override
  final String? email;

  factory _$AppReferralRegisterCollabRequest(
          [void Function(AppReferralRegisterCollabRequestBuilder)? updates]) =>
      (AppReferralRegisterCollabRequestBuilder()..update(updates))._build();

  _$AppReferralRegisterCollabRequest._(
      {required this.fullName,
      required this.idCardType,
      required this.idCardNo,
      required this.phoneNumber,
      required this.branchId,
      required this.frontCardUrl,
      required this.backCardUrl,
      this.permanentAddress,
      this.email})
      : super._();
  @override
  AppReferralRegisterCollabRequest rebuild(
          void Function(AppReferralRegisterCollabRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppReferralRegisterCollabRequestBuilder toBuilder() =>
      AppReferralRegisterCollabRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppReferralRegisterCollabRequest &&
        fullName == other.fullName &&
        idCardType == other.idCardType &&
        idCardNo == other.idCardNo &&
        phoneNumber == other.phoneNumber &&
        branchId == other.branchId &&
        frontCardUrl == other.frontCardUrl &&
        backCardUrl == other.backCardUrl &&
        permanentAddress == other.permanentAddress &&
        email == other.email;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, idCardType.hashCode);
    _$hash = $jc(_$hash, idCardNo.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, branchId.hashCode);
    _$hash = $jc(_$hash, frontCardUrl.hashCode);
    _$hash = $jc(_$hash, backCardUrl.hashCode);
    _$hash = $jc(_$hash, permanentAddress.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppReferralRegisterCollabRequest')
          ..add('fullName', fullName)
          ..add('idCardType', idCardType)
          ..add('idCardNo', idCardNo)
          ..add('phoneNumber', phoneNumber)
          ..add('branchId', branchId)
          ..add('frontCardUrl', frontCardUrl)
          ..add('backCardUrl', backCardUrl)
          ..add('permanentAddress', permanentAddress)
          ..add('email', email))
        .toString();
  }
}

class AppReferralRegisterCollabRequestBuilder
    implements
        Builder<AppReferralRegisterCollabRequest,
            AppReferralRegisterCollabRequestBuilder> {
  _$AppReferralRegisterCollabRequest? _$v;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  AppReferralRegisterCollabRequestIdCardTypeEnum? _idCardType;
  AppReferralRegisterCollabRequestIdCardTypeEnum? get idCardType =>
      _$this._idCardType;
  set idCardType(AppReferralRegisterCollabRequestIdCardTypeEnum? idCardType) =>
      _$this._idCardType = idCardType;

  String? _idCardNo;
  String? get idCardNo => _$this._idCardNo;
  set idCardNo(String? idCardNo) => _$this._idCardNo = idCardNo;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  String? _branchId;
  String? get branchId => _$this._branchId;
  set branchId(String? branchId) => _$this._branchId = branchId;

  String? _frontCardUrl;
  String? get frontCardUrl => _$this._frontCardUrl;
  set frontCardUrl(String? frontCardUrl) => _$this._frontCardUrl = frontCardUrl;

  String? _backCardUrl;
  String? get backCardUrl => _$this._backCardUrl;
  set backCardUrl(String? backCardUrl) => _$this._backCardUrl = backCardUrl;

  String? _permanentAddress;
  String? get permanentAddress => _$this._permanentAddress;
  set permanentAddress(String? permanentAddress) =>
      _$this._permanentAddress = permanentAddress;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  AppReferralRegisterCollabRequestBuilder() {
    AppReferralRegisterCollabRequest._defaults(this);
  }

  AppReferralRegisterCollabRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _fullName = $v.fullName;
      _idCardType = $v.idCardType;
      _idCardNo = $v.idCardNo;
      _phoneNumber = $v.phoneNumber;
      _branchId = $v.branchId;
      _frontCardUrl = $v.frontCardUrl;
      _backCardUrl = $v.backCardUrl;
      _permanentAddress = $v.permanentAddress;
      _email = $v.email;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppReferralRegisterCollabRequest other) {
    _$v = other as _$AppReferralRegisterCollabRequest;
  }

  @override
  void update(void Function(AppReferralRegisterCollabRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppReferralRegisterCollabRequest build() => _build();

  _$AppReferralRegisterCollabRequest _build() {
    final _$result = _$v ??
        _$AppReferralRegisterCollabRequest._(
          fullName: BuiltValueNullFieldError.checkNotNull(
              fullName, r'AppReferralRegisterCollabRequest', 'fullName'),
          idCardType: BuiltValueNullFieldError.checkNotNull(
              idCardType, r'AppReferralRegisterCollabRequest', 'idCardType'),
          idCardNo: BuiltValueNullFieldError.checkNotNull(
              idCardNo, r'AppReferralRegisterCollabRequest', 'idCardNo'),
          phoneNumber: BuiltValueNullFieldError.checkNotNull(
              phoneNumber, r'AppReferralRegisterCollabRequest', 'phoneNumber'),
          branchId: BuiltValueNullFieldError.checkNotNull(
              branchId, r'AppReferralRegisterCollabRequest', 'branchId'),
          frontCardUrl: BuiltValueNullFieldError.checkNotNull(frontCardUrl,
              r'AppReferralRegisterCollabRequest', 'frontCardUrl'),
          backCardUrl: BuiltValueNullFieldError.checkNotNull(
              backCardUrl, r'AppReferralRegisterCollabRequest', 'backCardUrl'),
          permanentAddress: permanentAddress,
          email: email,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
