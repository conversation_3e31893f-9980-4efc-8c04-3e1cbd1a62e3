// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_get_user_profile_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppGetUserProfileResponse extends AppGetUserProfileResponse {
  @override
  final String? code;
  @override
  final String? cifNo;
  @override
  final String? fullName;
  @override
  final String? email;
  @override
  final String? phoneNumber;
  @override
  final AppBranchDto? branch;

  factory _$AppGetUserProfileResponse(
          [void Function(AppGetUserProfileResponseBuilder)? updates]) =>
      (AppGetUserProfileResponseBuilder()..update(updates))._build();

  _$AppGetUserProfileResponse._(
      {this.code,
      this.cifNo,
      this.fullName,
      this.email,
      this.phoneNumber,
      this.branch})
      : super._();
  @override
  AppGetUserProfileResponse rebuild(
          void Function(AppGetUserProfileResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppGetUserProfileResponseBuilder toBuilder() =>
      AppGetUserProfileResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppGetUserProfileResponse &&
        code == other.code &&
        cifNo == other.cifNo &&
        fullName == other.fullName &&
        email == other.email &&
        phoneNumber == other.phoneNumber &&
        branch == other.branch;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, branch.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppGetUserProfileResponse')
          ..add('code', code)
          ..add('cifNo', cifNo)
          ..add('fullName', fullName)
          ..add('email', email)
          ..add('phoneNumber', phoneNumber)
          ..add('branch', branch))
        .toString();
  }
}

class AppGetUserProfileResponseBuilder
    implements
        Builder<AppGetUserProfileResponse, AppGetUserProfileResponseBuilder> {
  _$AppGetUserProfileResponse? _$v;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  AppBranchDtoBuilder? _branch;
  AppBranchDtoBuilder get branch => _$this._branch ??= AppBranchDtoBuilder();
  set branch(AppBranchDtoBuilder? branch) => _$this._branch = branch;

  AppGetUserProfileResponseBuilder() {
    AppGetUserProfileResponse._defaults(this);
  }

  AppGetUserProfileResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _code = $v.code;
      _cifNo = $v.cifNo;
      _fullName = $v.fullName;
      _email = $v.email;
      _phoneNumber = $v.phoneNumber;
      _branch = $v.branch?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppGetUserProfileResponse other) {
    _$v = other as _$AppGetUserProfileResponse;
  }

  @override
  void update(void Function(AppGetUserProfileResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppGetUserProfileResponse build() => _build();

  _$AppGetUserProfileResponse _build() {
    _$AppGetUserProfileResponse _$result;
    try {
      _$result = _$v ??
          _$AppGetUserProfileResponse._(
            code: code,
            cifNo: cifNo,
            fullName: fullName,
            email: email,
            phoneNumber: phoneNumber,
            branch: _branch?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'branch';
        _branch?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AppGetUserProfileResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
