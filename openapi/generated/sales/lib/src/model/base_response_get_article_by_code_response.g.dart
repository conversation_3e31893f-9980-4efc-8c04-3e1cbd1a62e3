// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_get_article_by_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseGetArticleByCodeResponse
    extends BaseResponseGetArticleByCodeResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final GetArticleByCodeResponse? data;

  factory _$BaseResponseGetArticleByCodeResponse(
          [void Function(BaseResponseGetArticleByCodeResponseBuilder)?
              updates]) =>
      (BaseResponseGetArticleByCodeResponseBuilder()..update(updates))._build();

  _$BaseResponseGetArticleByCodeResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseGetArticleByCodeResponse rebuild(
          void Function(BaseResponseGetArticleByCodeResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseGetArticleByCodeResponseBuilder toBuilder() =>
      BaseResponseGetArticleByCodeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseGetArticleByCodeResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseGetArticleByCodeResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseGetArticleByCodeResponseBuilder
    implements
        Builder<BaseResponseGetArticleByCodeResponse,
            BaseResponseGetArticleByCodeResponseBuilder> {
  _$BaseResponseGetArticleByCodeResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  GetArticleByCodeResponseBuilder? _data;
  GetArticleByCodeResponseBuilder get data =>
      _$this._data ??= GetArticleByCodeResponseBuilder();
  set data(GetArticleByCodeResponseBuilder? data) => _$this._data = data;

  BaseResponseGetArticleByCodeResponseBuilder() {
    BaseResponseGetArticleByCodeResponse._defaults(this);
  }

  BaseResponseGetArticleByCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseGetArticleByCodeResponse other) {
    _$v = other as _$BaseResponseGetArticleByCodeResponse;
  }

  @override
  void update(
      void Function(BaseResponseGetArticleByCodeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseGetArticleByCodeResponse build() => _build();

  _$BaseResponseGetArticleByCodeResponse _build() {
    _$BaseResponseGetArticleByCodeResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseGetArticleByCodeResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseGetArticleByCodeResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
