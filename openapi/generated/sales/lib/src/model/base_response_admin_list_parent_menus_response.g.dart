// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_list_parent_menus_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminListParentMenusResponse
    extends BaseResponseAdminListParentMenusResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminListParentMenusResponse? data;

  factory _$BaseResponseAdminListParentMenusResponse(
          [void Function(BaseResponseAdminListParentMenusResponseBuilder)?
              updates]) =>
      (BaseResponseAdminListParentMenusResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseAdminListParentMenusResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminListParentMenusResponse rebuild(
          void Function(BaseResponseAdminListParentMenusResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminListParentMenusResponseBuilder toBuilder() =>
      BaseResponseAdminListParentMenusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminListParentMenusResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAdminListParentMenusResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminListParentMenusResponseBuilder
    implements
        Builder<BaseResponseAdminListParentMenusResponse,
            BaseResponseAdminListParentMenusResponseBuilder> {
  _$BaseResponseAdminListParentMenusResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminListParentMenusResponseBuilder? _data;
  AdminListParentMenusResponseBuilder get data =>
      _$this._data ??= AdminListParentMenusResponseBuilder();
  set data(AdminListParentMenusResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminListParentMenusResponseBuilder() {
    BaseResponseAdminListParentMenusResponse._defaults(this);
  }

  BaseResponseAdminListParentMenusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminListParentMenusResponse other) {
    _$v = other as _$BaseResponseAdminListParentMenusResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminListParentMenusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminListParentMenusResponse build() => _build();

  _$BaseResponseAdminListParentMenusResponse _build() {
    _$BaseResponseAdminListParentMenusResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminListParentMenusResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAdminListParentMenusResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
