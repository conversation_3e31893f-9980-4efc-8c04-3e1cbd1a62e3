// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_update_menu_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const AdminUpdateMenuRequestPlatformEnum
    _$adminUpdateMenuRequestPlatformEnum_APP =
    const AdminUpdateMenuRequestPlatformEnum._('APP');
const AdminUpdateMenuRequestPlatformEnum
    _$adminUpdateMenuRequestPlatformEnum_WEB =
    const AdminUpdateMenuRequestPlatformEnum._('WEB');

AdminUpdateMenuRequestPlatformEnum _$adminUpdateMenuRequestPlatformEnumValueOf(
    String name) {
  switch (name) {
    case 'APP':
      return _$adminUpdateMenuRequestPlatformEnum_APP;
    case 'WEB':
      return _$adminUpdateMenuRequestPlatformEnum_WEB;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<AdminUpdateMenuRequestPlatformEnum>
    _$adminUpdateMenuRequestPlatformEnumValues = BuiltSet<
        AdminUpdateMenuRequestPlatformEnum>(const <AdminUpdateMenuRequestPlatformEnum>[
  _$adminUpdateMenuRequestPlatformEnum_APP,
  _$adminUpdateMenuRequestPlatformEnum_WEB,
]);

Serializer<AdminUpdateMenuRequestPlatformEnum>
    _$adminUpdateMenuRequestPlatformEnumSerializer =
    _$AdminUpdateMenuRequestPlatformEnumSerializer();

class _$AdminUpdateMenuRequestPlatformEnumSerializer
    implements PrimitiveSerializer<AdminUpdateMenuRequestPlatformEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'APP': 'APP',
    'WEB': 'WEB',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'APP': 'APP',
    'WEB': 'WEB',
  };

  @override
  final Iterable<Type> types = const <Type>[AdminUpdateMenuRequestPlatformEnum];
  @override
  final String wireName = 'AdminUpdateMenuRequestPlatformEnum';

  @override
  Object serialize(
          Serializers serializers, AdminUpdateMenuRequestPlatformEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  AdminUpdateMenuRequestPlatformEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      AdminUpdateMenuRequestPlatformEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$AdminUpdateMenuRequest extends AdminUpdateMenuRequest {
  @override
  final AdminUpdateMenuRequestPlatformEnum platform;
  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? parentId;
  @override
  final int? order;
  @override
  final BuiltList<String>? actionIds;

  factory _$AdminUpdateMenuRequest(
          [void Function(AdminUpdateMenuRequestBuilder)? updates]) =>
      (AdminUpdateMenuRequestBuilder()..update(updates))._build();

  _$AdminUpdateMenuRequest._(
      {required this.platform,
      this.name,
      this.description,
      this.parentId,
      this.order,
      this.actionIds})
      : super._();
  @override
  AdminUpdateMenuRequest rebuild(
          void Function(AdminUpdateMenuRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminUpdateMenuRequestBuilder toBuilder() =>
      AdminUpdateMenuRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminUpdateMenuRequest &&
        platform == other.platform &&
        name == other.name &&
        description == other.description &&
        parentId == other.parentId &&
        order == other.order &&
        actionIds == other.actionIds;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, platform.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, parentId.hashCode);
    _$hash = $jc(_$hash, order.hashCode);
    _$hash = $jc(_$hash, actionIds.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminUpdateMenuRequest')
          ..add('platform', platform)
          ..add('name', name)
          ..add('description', description)
          ..add('parentId', parentId)
          ..add('order', order)
          ..add('actionIds', actionIds))
        .toString();
  }
}

class AdminUpdateMenuRequestBuilder
    implements Builder<AdminUpdateMenuRequest, AdminUpdateMenuRequestBuilder> {
  _$AdminUpdateMenuRequest? _$v;

  AdminUpdateMenuRequestPlatformEnum? _platform;
  AdminUpdateMenuRequestPlatformEnum? get platform => _$this._platform;
  set platform(AdminUpdateMenuRequestPlatformEnum? platform) =>
      _$this._platform = platform;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _parentId;
  String? get parentId => _$this._parentId;
  set parentId(String? parentId) => _$this._parentId = parentId;

  int? _order;
  int? get order => _$this._order;
  set order(int? order) => _$this._order = order;

  ListBuilder<String>? _actionIds;
  ListBuilder<String> get actionIds =>
      _$this._actionIds ??= ListBuilder<String>();
  set actionIds(ListBuilder<String>? actionIds) =>
      _$this._actionIds = actionIds;

  AdminUpdateMenuRequestBuilder() {
    AdminUpdateMenuRequest._defaults(this);
  }

  AdminUpdateMenuRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _platform = $v.platform;
      _name = $v.name;
      _description = $v.description;
      _parentId = $v.parentId;
      _order = $v.order;
      _actionIds = $v.actionIds?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminUpdateMenuRequest other) {
    _$v = other as _$AdminUpdateMenuRequest;
  }

  @override
  void update(void Function(AdminUpdateMenuRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminUpdateMenuRequest build() => _build();

  _$AdminUpdateMenuRequest _build() {
    _$AdminUpdateMenuRequest _$result;
    try {
      _$result = _$v ??
          _$AdminUpdateMenuRequest._(
            platform: BuiltValueNullFieldError.checkNotNull(
                platform, r'AdminUpdateMenuRequest', 'platform'),
            name: name,
            description: description,
            parentId: parentId,
            order: order,
            actionIds: _actionIds?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'actionIds';
        _actionIds?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminUpdateMenuRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
