//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:sales_app_api/src/model/get_article_by_code_response.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'base_response_get_article_by_code_response.g.dart';

/// BaseResponseGetArticleByCodeResponse
///
/// Properties:
/// * [success]
/// * [code]
/// * [message]
/// * [data]
@BuiltValue()
abstract class BaseResponseGetArticleByCodeResponse
    implements
        Built<BaseResponseGetArticleByCodeResponse,
            BaseResponseGetArticleByCodeResponseBuilder> {
  @BuiltValueField(wireName: r'success')
  bool? get success;

  @BuiltValueField(wireName: r'code')
  int? get code;

  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  GetArticleByCodeResponse? get data;

  BaseResponseGetArticleByCodeResponse._();

  factory BaseResponseGetArticleByCodeResponse(
          [void updates(BaseResponseGetArticleByCodeResponseBuilder b)]) =
      _$BaseResponseGetArticleByCodeResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(BaseResponseGetArticleByCodeResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BaseResponseGetArticleByCodeResponse> get serializer =>
      _$BaseResponseGetArticleByCodeResponseSerializer();
}

class _$BaseResponseGetArticleByCodeResponseSerializer
    implements PrimitiveSerializer<BaseResponseGetArticleByCodeResponse> {
  @override
  final Iterable<Type> types = const [
    BaseResponseGetArticleByCodeResponse,
    _$BaseResponseGetArticleByCodeResponse
  ];

  @override
  final String wireName = r'BaseResponseGetArticleByCodeResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BaseResponseGetArticleByCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.success != null) {
      yield r'success';
      yield serializers.serialize(
        object.success,
        specifiedType: const FullType(bool),
      );
    }
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(int),
      );
    }
    if (object.message != null) {
      yield r'message';
      yield serializers.serialize(
        object.message,
        specifiedType: const FullType(String),
      );
    }
    if (object.data != null) {
      yield r'data';
      yield serializers.serialize(
        object.data,
        specifiedType: const FullType(GetArticleByCodeResponse),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    BaseResponseGetArticleByCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BaseResponseGetArticleByCodeResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.success = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.code = valueDes;
          break;
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(GetArticleByCodeResponse),
          ) as GetArticleByCodeResponse;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BaseResponseGetArticleByCodeResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BaseResponseGetArticleByCodeResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
