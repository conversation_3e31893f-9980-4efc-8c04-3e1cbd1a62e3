//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_assign_roles_response.g.dart';

/// AdminAssignRolesResponse
///
/// Properties:
/// * [userId]
/// * [roleIds]
/// * [success]
@BuiltValue()
abstract class AdminAssignRolesResponse
    implements
        Built<AdminAssignRolesResponse, AdminAssignRolesResponseBuilder> {
  @BuiltValueField(wireName: r'userId')
  String? get userId;

  @BuiltValueField(wireName: r'roleIds')
  BuiltList<String>? get roleIds;

  @BuiltValueField(wireName: r'success')
  bool? get success;

  AdminAssignRolesResponse._();

  factory AdminAssignRolesResponse(
          [void updates(AdminAssignRolesResponseBuilder b)]) =
      _$AdminAssignRolesResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminAssignRolesResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminAssignRolesResponse> get serializer =>
      _$AdminAssignRolesResponseSerializer();
}

class _$AdminAssignRolesResponseSerializer
    implements PrimitiveSerializer<AdminAssignRolesResponse> {
  @override
  final Iterable<Type> types = const [
    AdminAssignRolesResponse,
    _$AdminAssignRolesResponse
  ];

  @override
  final String wireName = r'AdminAssignRolesResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminAssignRolesResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.userId != null) {
      yield r'userId';
      yield serializers.serialize(
        object.userId,
        specifiedType: const FullType(String),
      );
    }
    if (object.roleIds != null) {
      yield r'roleIds';
      yield serializers.serialize(
        object.roleIds,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
    if (object.success != null) {
      yield r'success';
      yield serializers.serialize(
        object.success,
        specifiedType: const FullType(bool),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminAssignRolesResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminAssignRolesResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'userId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.userId = valueDes;
          break;
        case r'roleIds':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.roleIds.replace(valueDes);
          break;
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.success = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminAssignRolesResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminAssignRolesResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
