// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_get_user_profile_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppGetUserProfileResponse
    extends BaseResponseAppGetUserProfileResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppGetUserProfileResponse? data;

  factory _$BaseResponseAppGetUserProfileResponse(
          [void Function(BaseResponseAppGetUserProfileResponseBuilder)?
              updates]) =>
      (BaseResponseAppGetUserProfileResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseAppGetUserProfileResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppGetUserProfileResponse rebuild(
          void Function(BaseResponseAppGetUserProfileResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppGetUserProfileResponseBuilder toBuilder() =>
      BaseResponseAppGetUserProfileResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppGetUserProfileResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAppGetUserProfileResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppGetUserProfileResponseBuilder
    implements
        Builder<BaseResponseAppGetUserProfileResponse,
            BaseResponseAppGetUserProfileResponseBuilder> {
  _$BaseResponseAppGetUserProfileResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppGetUserProfileResponseBuilder? _data;
  AppGetUserProfileResponseBuilder get data =>
      _$this._data ??= AppGetUserProfileResponseBuilder();
  set data(AppGetUserProfileResponseBuilder? data) => _$this._data = data;

  BaseResponseAppGetUserProfileResponseBuilder() {
    BaseResponseAppGetUserProfileResponse._defaults(this);
  }

  BaseResponseAppGetUserProfileResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppGetUserProfileResponse other) {
    _$v = other as _$BaseResponseAppGetUserProfileResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppGetUserProfileResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppGetUserProfileResponse build() => _build();

  _$BaseResponseAppGetUserProfileResponse _build() {
    _$BaseResponseAppGetUserProfileResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppGetUserProfileResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppGetUserProfileResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
