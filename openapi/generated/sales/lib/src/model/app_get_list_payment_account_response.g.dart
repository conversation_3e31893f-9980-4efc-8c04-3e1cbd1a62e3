// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_get_list_payment_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppGetListPaymentAccountResponse
    extends AppGetListPaymentAccountResponse {
  @override
  final BuiltList<AccountDto>? accounts;

  factory _$AppGetListPaymentAccountResponse(
          [void Function(AppGetListPaymentAccountResponseBuilder)? updates]) =>
      (AppGetListPaymentAccountResponseBuilder()..update(updates))._build();

  _$AppGetListPaymentAccountResponse._({this.accounts}) : super._();
  @override
  AppGetListPaymentAccountResponse rebuild(
          void Function(AppGetListPaymentAccountResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppGetListPaymentAccountResponseBuilder toBuilder() =>
      AppGetListPaymentAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppGetListPaymentAccountResponse &&
        accounts == other.accounts;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accounts.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppGetListPaymentAccountResponse')
          ..add('accounts', accounts))
        .toString();
  }
}

class AppGetListPaymentAccountResponseBuilder
    implements
        Builder<AppGetListPaymentAccountResponse,
            AppGetListPaymentAccountResponseBuilder> {
  _$AppGetListPaymentAccountResponse? _$v;

  ListBuilder<AccountDto>? _accounts;
  ListBuilder<AccountDto> get accounts =>
      _$this._accounts ??= ListBuilder<AccountDto>();
  set accounts(ListBuilder<AccountDto>? accounts) =>
      _$this._accounts = accounts;

  AppGetListPaymentAccountResponseBuilder() {
    AppGetListPaymentAccountResponse._defaults(this);
  }

  AppGetListPaymentAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accounts = $v.accounts?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppGetListPaymentAccountResponse other) {
    _$v = other as _$AppGetListPaymentAccountResponse;
  }

  @override
  void update(void Function(AppGetListPaymentAccountResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppGetListPaymentAccountResponse build() => _build();

  _$AppGetListPaymentAccountResponse _build() {
    _$AppGetListPaymentAccountResponse _$result;
    try {
      _$result = _$v ??
          _$AppGetListPaymentAccountResponse._(
            accounts: _accounts?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'accounts';
        _accounts?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AppGetListPaymentAccountResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
