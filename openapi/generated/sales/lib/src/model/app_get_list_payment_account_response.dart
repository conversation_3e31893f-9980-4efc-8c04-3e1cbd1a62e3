//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:sales_app_api/src/model/account_dto.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_get_list_payment_account_response.g.dart';

/// AppGetListPaymentAccountResponse
///
/// Properties:
/// * [accounts] - Danh sách account
@BuiltValue()
abstract class AppGetListPaymentAccountResponse
    implements
        Built<AppGetListPaymentAccountResponse,
            AppGetListPaymentAccountResponseBuilder> {
  /// Danh sách account
  @BuiltValueField(wireName: r'accounts')
  BuiltList<AccountDto>? get accounts;

  AppGetListPaymentAccountResponse._();

  factory AppGetListPaymentAccountResponse(
          [void updates(AppGetListPaymentAccountResponseBuilder b)]) =
      _$AppGetListPaymentAccountResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppGetListPaymentAccountResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppGetListPaymentAccountResponse> get serializer =>
      _$AppGetListPaymentAccountResponseSerializer();
}

class _$AppGetListPaymentAccountResponseSerializer
    implements PrimitiveSerializer<AppGetListPaymentAccountResponse> {
  @override
  final Iterable<Type> types = const [
    AppGetListPaymentAccountResponse,
    _$AppGetListPaymentAccountResponse
  ];

  @override
  final String wireName = r'AppGetListPaymentAccountResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppGetListPaymentAccountResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.accounts != null) {
      yield r'accounts';
      yield serializers.serialize(
        object.accounts,
        specifiedType: const FullType(BuiltList, [FullType(AccountDto)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppGetListPaymentAccountResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppGetListPaymentAccountResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'accounts':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(AccountDto)]),
          ) as BuiltList<AccountDto>;
          result.accounts.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppGetListPaymentAccountResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppGetListPaymentAccountResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
