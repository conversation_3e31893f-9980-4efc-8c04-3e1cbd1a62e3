// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_get_system_configs_by_group_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppGetSystemConfigsByGroupCodeResponse
    extends AppGetSystemConfigsByGroupCodeResponse {
  @override
  final String? groupCode;
  @override
  final BuiltList<SystemConfigDto>? configs;

  factory _$AppGetSystemConfigsByGroupCodeResponse(
          [void Function(AppGetSystemConfigsByGroupCodeResponseBuilder)?
              updates]) =>
      (AppGetSystemConfigsByGroupCodeResponseBuilder()..update(updates))
          ._build();

  _$AppGetSystemConfigsByGroupCodeResponse._({this.groupCode, this.configs})
      : super._();
  @override
  AppGetSystemConfigsByGroupCodeResponse rebuild(
          void Function(AppGetSystemConfigsByGroupCodeResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppGetSystemConfigsByGroupCodeResponseBuilder toBuilder() =>
      AppGetSystemConfigsByGroupCodeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppGetSystemConfigsByGroupCodeResponse &&
        groupCode == other.groupCode &&
        configs == other.configs;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, groupCode.hashCode);
    _$hash = $jc(_$hash, configs.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'AppGetSystemConfigsByGroupCodeResponse')
          ..add('groupCode', groupCode)
          ..add('configs', configs))
        .toString();
  }
}

class AppGetSystemConfigsByGroupCodeResponseBuilder
    implements
        Builder<AppGetSystemConfigsByGroupCodeResponse,
            AppGetSystemConfigsByGroupCodeResponseBuilder> {
  _$AppGetSystemConfigsByGroupCodeResponse? _$v;

  String? _groupCode;
  String? get groupCode => _$this._groupCode;
  set groupCode(String? groupCode) => _$this._groupCode = groupCode;

  ListBuilder<SystemConfigDto>? _configs;
  ListBuilder<SystemConfigDto> get configs =>
      _$this._configs ??= ListBuilder<SystemConfigDto>();
  set configs(ListBuilder<SystemConfigDto>? configs) =>
      _$this._configs = configs;

  AppGetSystemConfigsByGroupCodeResponseBuilder() {
    AppGetSystemConfigsByGroupCodeResponse._defaults(this);
  }

  AppGetSystemConfigsByGroupCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _groupCode = $v.groupCode;
      _configs = $v.configs?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppGetSystemConfigsByGroupCodeResponse other) {
    _$v = other as _$AppGetSystemConfigsByGroupCodeResponse;
  }

  @override
  void update(
      void Function(AppGetSystemConfigsByGroupCodeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppGetSystemConfigsByGroupCodeResponse build() => _build();

  _$AppGetSystemConfigsByGroupCodeResponse _build() {
    _$AppGetSystemConfigsByGroupCodeResponse _$result;
    try {
      _$result = _$v ??
          _$AppGetSystemConfigsByGroupCodeResponse._(
            groupCode: groupCode,
            configs: _configs?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'configs';
        _configs?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AppGetSystemConfigsByGroupCodeResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
