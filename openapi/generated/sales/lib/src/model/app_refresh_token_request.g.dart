// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_refresh_token_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppRefreshTokenRequest extends AppRefreshTokenRequest {
  @override
  final String refreshToken;

  factory _$AppRefreshTokenRequest(
          [void Function(AppRefreshTokenRequestBuilder)? updates]) =>
      (AppRefreshTokenRequestBuilder()..update(updates))._build();

  _$AppRefreshTokenRequest._({required this.refreshToken}) : super._();
  @override
  AppRefreshTokenRequest rebuild(
          void Function(AppRefreshTokenRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppRefreshTokenRequestBuilder toBuilder() =>
      AppRefreshTokenRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppRefreshTokenRequest &&
        refreshToken == other.refreshToken;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, refreshToken.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppRefreshTokenRequest')
          ..add('refreshToken', refreshToken))
        .toString();
  }
}

class AppRefreshTokenRequestBuilder
    implements Builder<AppRefreshTokenRequest, AppRefreshTokenRequestBuilder> {
  _$AppRefreshTokenRequest? _$v;

  String? _refreshToken;
  String? get refreshToken => _$this._refreshToken;
  set refreshToken(String? refreshToken) => _$this._refreshToken = refreshToken;

  AppRefreshTokenRequestBuilder() {
    AppRefreshTokenRequest._defaults(this);
  }

  AppRefreshTokenRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _refreshToken = $v.refreshToken;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppRefreshTokenRequest other) {
    _$v = other as _$AppRefreshTokenRequest;
  }

  @override
  void update(void Function(AppRefreshTokenRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppRefreshTokenRequest build() => _build();

  _$AppRefreshTokenRequest _build() {
    final _$result = _$v ??
        _$AppRefreshTokenRequest._(
          refreshToken: BuiltValueNullFieldError.checkNotNull(
              refreshToken, r'AppRefreshTokenRequest', 'refreshToken'),
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
