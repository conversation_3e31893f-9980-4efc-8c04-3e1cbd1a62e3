//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:sales_app_api/src/model/menu_dto.dart';
import 'package:sales_app_api/src/model/role_dto.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_get_user_role_permission_response.g.dart';

/// AdminGetUserRolePermissionResponse
///
/// Properties:
/// * [roles] - Danh sách role
/// * [menus] - Danh sách menu người dùng có thể truy cập
@BuiltValue()
abstract class AdminGetUserRolePermissionResponse
    implements
        Built<AdminGetUserRolePermissionResponse,
            AdminGetUserRolePermissionResponseBuilder> {
  /// Danh sách role
  @BuiltValueField(wireName: r'roles')
  BuiltList<RoleDto>? get roles;

  /// Danh sách menu người dùng có thể truy cập
  @BuiltValueField(wireName: r'menus')
  BuiltList<MenuDto>? get menus;

  AdminGetUserRolePermissionResponse._();

  factory AdminGetUserRolePermissionResponse(
          [void updates(AdminGetUserRolePermissionResponseBuilder b)]) =
      _$AdminGetUserRolePermissionResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminGetUserRolePermissionResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminGetUserRolePermissionResponse> get serializer =>
      _$AdminGetUserRolePermissionResponseSerializer();
}

class _$AdminGetUserRolePermissionResponseSerializer
    implements PrimitiveSerializer<AdminGetUserRolePermissionResponse> {
  @override
  final Iterable<Type> types = const [
    AdminGetUserRolePermissionResponse,
    _$AdminGetUserRolePermissionResponse
  ];

  @override
  final String wireName = r'AdminGetUserRolePermissionResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminGetUserRolePermissionResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.roles != null) {
      yield r'roles';
      yield serializers.serialize(
        object.roles,
        specifiedType: const FullType(BuiltList, [FullType(RoleDto)]),
      );
    }
    if (object.menus != null) {
      yield r'menus';
      yield serializers.serialize(
        object.menus,
        specifiedType: const FullType(BuiltList, [FullType(MenuDto)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminGetUserRolePermissionResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminGetUserRolePermissionResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'roles':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(RoleDto)]),
          ) as BuiltList<RoleDto>;
          result.roles.replace(valueDes);
          break;
        case r'menus':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(MenuDto)]),
          ) as BuiltList<MenuDto>;
          result.menus.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminGetUserRolePermissionResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminGetUserRolePermissionResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
