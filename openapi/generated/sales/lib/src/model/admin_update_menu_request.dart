//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_update_menu_request.g.dart';

/// AdminUpdateMenuRequest
///
/// Properties:
/// * [platform] - Menu theo platform
/// * [name] - Tên menu
/// * [description] - <PERSON><PERSON> tả menu
/// * [parentId] - <PERSON>u cha (nếu có)
/// * [order] - Sử dụng để order trên UI
/// * [actionIds] - Danh sách ID của các action gắn với menu
@BuiltValue()
abstract class AdminUpdateMenuRequest
    implements Built<AdminUpdateMenuRequest, AdminUpdateMenuRequestBuilder> {
  /// Menu theo platform
  @BuiltValueField(wireName: r'platform')
  AdminUpdateMenuRequestPlatformEnum get platform;
  // enum platformEnum {  APP,  WEB,  };

  /// Tên menu
  @BuiltValueField(wireName: r'name')
  String? get name;

  /// Mô tả menu
  @BuiltValueField(wireName: r'description')
  String? get description;

  /// Menu cha (nếu có)
  @BuiltValueField(wireName: r'parentId')
  String? get parentId;

  /// Sử dụng để order trên UI
  @BuiltValueField(wireName: r'order')
  int? get order;

  /// Danh sách ID của các action gắn với menu
  @BuiltValueField(wireName: r'actionIds')
  BuiltList<String>? get actionIds;

  AdminUpdateMenuRequest._();

  factory AdminUpdateMenuRequest(
          [void updates(AdminUpdateMenuRequestBuilder b)]) =
      _$AdminUpdateMenuRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminUpdateMenuRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminUpdateMenuRequest> get serializer =>
      _$AdminUpdateMenuRequestSerializer();
}

class _$AdminUpdateMenuRequestSerializer
    implements PrimitiveSerializer<AdminUpdateMenuRequest> {
  @override
  final Iterable<Type> types = const [
    AdminUpdateMenuRequest,
    _$AdminUpdateMenuRequest
  ];

  @override
  final String wireName = r'AdminUpdateMenuRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminUpdateMenuRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'platform';
    yield serializers.serialize(
      object.platform,
      specifiedType: const FullType(AdminUpdateMenuRequestPlatformEnum),
    );
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType(String),
      );
    }
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType(String),
      );
    }
    if (object.parentId != null) {
      yield r'parentId';
      yield serializers.serialize(
        object.parentId,
        specifiedType: const FullType(String),
      );
    }
    if (object.order != null) {
      yield r'order';
      yield serializers.serialize(
        object.order,
        specifiedType: const FullType(int),
      );
    }
    if (object.actionIds != null) {
      yield r'actionIds';
      yield serializers.serialize(
        object.actionIds,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminUpdateMenuRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminUpdateMenuRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'platform':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(AdminUpdateMenuRequestPlatformEnum),
          ) as AdminUpdateMenuRequestPlatformEnum;
          result.platform = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.description = valueDes;
          break;
        case r'parentId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.parentId = valueDes;
          break;
        case r'order':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.order = valueDes;
          break;
        case r'actionIds':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.actionIds.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminUpdateMenuRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminUpdateMenuRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class AdminUpdateMenuRequestPlatformEnum extends EnumClass {
  /// Menu theo platform
  @BuiltValueEnumConst(wireName: r'APP')
  static const AdminUpdateMenuRequestPlatformEnum APP =
      _$adminUpdateMenuRequestPlatformEnum_APP;

  /// Menu theo platform
  @BuiltValueEnumConst(wireName: r'WEB')
  static const AdminUpdateMenuRequestPlatformEnum WEB =
      _$adminUpdateMenuRequestPlatformEnum_WEB;

  static Serializer<AdminUpdateMenuRequestPlatformEnum> get serializer =>
      _$adminUpdateMenuRequestPlatformEnumSerializer;

  const AdminUpdateMenuRequestPlatformEnum._(String name) : super(name);

  static BuiltSet<AdminUpdateMenuRequestPlatformEnum> get values =>
      _$adminUpdateMenuRequestPlatformEnumValues;
  static AdminUpdateMenuRequestPlatformEnum valueOf(String name) =>
      _$adminUpdateMenuRequestPlatformEnumValueOf(name);
}
