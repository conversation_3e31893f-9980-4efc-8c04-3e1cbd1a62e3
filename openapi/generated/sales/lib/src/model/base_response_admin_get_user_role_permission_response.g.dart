// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_get_user_role_permission_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminGetUserRolePermissionResponse
    extends BaseResponseAdminGetUserRolePermissionResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminGetUserRolePermissionResponse? data;

  factory _$BaseResponseAdminGetUserRolePermissionResponse(
          [void Function(BaseResponseAdminGetUserRolePermissionResponseBuilder)?
              updates]) =>
      (BaseResponseAdminGetUserRolePermissionResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseAdminGetUserRolePermissionResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminGetUserRolePermissionResponse rebuild(
          void Function(BaseResponseAdminGetUserRolePermissionResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminGetUserRolePermissionResponseBuilder toBuilder() =>
      BaseResponseAdminGetUserRolePermissionResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminGetUserRolePermissionResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAdminGetUserRolePermissionResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminGetUserRolePermissionResponseBuilder
    implements
        Builder<BaseResponseAdminGetUserRolePermissionResponse,
            BaseResponseAdminGetUserRolePermissionResponseBuilder> {
  _$BaseResponseAdminGetUserRolePermissionResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminGetUserRolePermissionResponseBuilder? _data;
  AdminGetUserRolePermissionResponseBuilder get data =>
      _$this._data ??= AdminGetUserRolePermissionResponseBuilder();
  set data(AdminGetUserRolePermissionResponseBuilder? data) =>
      _$this._data = data;

  BaseResponseAdminGetUserRolePermissionResponseBuilder() {
    BaseResponseAdminGetUserRolePermissionResponse._defaults(this);
  }

  BaseResponseAdminGetUserRolePermissionResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminGetUserRolePermissionResponse other) {
    _$v = other as _$BaseResponseAdminGetUserRolePermissionResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminGetUserRolePermissionResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminGetUserRolePermissionResponse build() => _build();

  _$BaseResponseAdminGetUserRolePermissionResponse _build() {
    _$BaseResponseAdminGetUserRolePermissionResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminGetUserRolePermissionResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAdminGetUserRolePermissionResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
