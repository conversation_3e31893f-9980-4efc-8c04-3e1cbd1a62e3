// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_import_users_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminImportUsersResponse
    extends BaseResponseAdminImportUsersResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminImportUsersResponse? data;

  factory _$BaseResponseAdminImportUsersResponse(
          [void Function(BaseResponseAdminImportUsersResponseBuilder)?
              updates]) =>
      (BaseResponseAdminImportUsersResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminImportUsersResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminImportUsersResponse rebuild(
          void Function(BaseResponseAdminImportUsersResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminImportUsersResponseBuilder toBuilder() =>
      BaseResponseAdminImportUsersResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminImportUsersResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminImportUsersResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminImportUsersResponseBuilder
    implements
        Builder<BaseResponseAdminImportUsersResponse,
            BaseResponseAdminImportUsersResponseBuilder> {
  _$BaseResponseAdminImportUsersResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminImportUsersResponseBuilder? _data;
  AdminImportUsersResponseBuilder get data =>
      _$this._data ??= AdminImportUsersResponseBuilder();
  set data(AdminImportUsersResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminImportUsersResponseBuilder() {
    BaseResponseAdminImportUsersResponse._defaults(this);
  }

  BaseResponseAdminImportUsersResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminImportUsersResponse other) {
    _$v = other as _$BaseResponseAdminImportUsersResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminImportUsersResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminImportUsersResponse build() => _build();

  _$BaseResponseAdminImportUsersResponse _build() {
    _$BaseResponseAdminImportUsersResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminImportUsersResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAdminImportUsersResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
