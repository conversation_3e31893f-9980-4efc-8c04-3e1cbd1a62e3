//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_login_request.g.dart';

/// AppLoginRequest
///
/// Properties:
/// * [username] - Username for login
/// * [password] - Password for login
@BuiltValue()
abstract class AppLoginRequest
    implements Built<AppLoginRequest, AppLoginRequestBuilder> {
  /// Username for login
  @BuiltValueField(wireName: r'username')
  String get username;

  /// Password for login
  @BuiltValueField(wireName: r'password')
  String get password;

  AppLoginRequest._();

  factory AppLoginRequest([void updates(AppLoginRequestBuilder b)]) =
      _$AppLoginRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppLoginRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppLoginRequest> get serializer =>
      _$AppLoginRequestSerializer();
}

class _$AppLoginRequestSerializer
    implements PrimitiveSerializer<AppLoginRequest> {
  @override
  final Iterable<Type> types = const [AppLoginRequest, _$AppLoginRequest];

  @override
  final String wireName = r'AppLoginRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppLoginRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'username';
    yield serializers.serialize(
      object.username,
      specifiedType: const FullType(String),
    );
    yield r'password';
    yield serializers.serialize(
      object.password,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AppLoginRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppLoginRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'username':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.username = valueDes;
          break;
        case r'password':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.password = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppLoginRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppLoginRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
