// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_register_collaborator_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppRegisterCollaboratorResponse
    extends BaseResponseAppRegisterCollaboratorResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppRegisterCollaboratorResponse? data;

  factory _$BaseResponseAppRegisterCollaboratorResponse(
          [void Function(BaseResponseAppRegisterCollaboratorResponseBuilder)?
              updates]) =>
      (BaseResponseAppRegisterCollaboratorResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseAppRegisterCollaboratorResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppRegisterCollaboratorResponse rebuild(
          void Function(BaseResponseAppRegisterCollaboratorResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppRegisterCollaboratorResponseBuilder toBuilder() =>
      BaseResponseAppRegisterCollaboratorResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppRegisterCollaboratorResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAppRegisterCollaboratorResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppRegisterCollaboratorResponseBuilder
    implements
        Builder<BaseResponseAppRegisterCollaboratorResponse,
            BaseResponseAppRegisterCollaboratorResponseBuilder> {
  _$BaseResponseAppRegisterCollaboratorResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppRegisterCollaboratorResponseBuilder? _data;
  AppRegisterCollaboratorResponseBuilder get data =>
      _$this._data ??= AppRegisterCollaboratorResponseBuilder();
  set data(AppRegisterCollaboratorResponseBuilder? data) => _$this._data = data;

  BaseResponseAppRegisterCollaboratorResponseBuilder() {
    BaseResponseAppRegisterCollaboratorResponse._defaults(this);
  }

  BaseResponseAppRegisterCollaboratorResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppRegisterCollaboratorResponse other) {
    _$v = other as _$BaseResponseAppRegisterCollaboratorResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppRegisterCollaboratorResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppRegisterCollaboratorResponse build() => _build();

  _$BaseResponseAppRegisterCollaboratorResponse _build() {
    _$BaseResponseAppRegisterCollaboratorResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppRegisterCollaboratorResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppRegisterCollaboratorResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
