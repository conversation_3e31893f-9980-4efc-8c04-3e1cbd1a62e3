// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_kplus_register_collaborator_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const AppKplusRegisterCollaboratorRequestIdCardTypeEnum
    _$appKplusRegisterCollaboratorRequestIdCardTypeEnum_CHIP_ID =
    const AppKplusRegisterCollaboratorRequestIdCardTypeEnum._('CHIP_ID');
const AppKplusRegisterCollaboratorRequestIdCardTypeEnum
    _$appKplusRegisterCollaboratorRequestIdCardTypeEnum_PASSPORT =
    const AppKplusRegisterCollaboratorRequestIdCardTypeEnum._('PASSPORT');

AppKplusRegisterCollaboratorRequestIdCardTypeEnum
    _$appKplusRegisterCollaboratorRequestIdCardTypeEnumValueOf(String name) {
  switch (name) {
    case 'CHIP_ID':
      return _$appKplusRegisterCollaboratorRequestIdCardTypeEnum_CHIP_ID;
    case 'PASSPORT':
      return _$appKplusRegisterCollaboratorRequestIdCardTypeEnum_PASSPORT;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<AppKplusRegisterCollaboratorRequestIdCardTypeEnum>
    _$appKplusRegisterCollaboratorRequestIdCardTypeEnumValues = BuiltSet<
        AppKplusRegisterCollaboratorRequestIdCardTypeEnum>(const <AppKplusRegisterCollaboratorRequestIdCardTypeEnum>[
  _$appKplusRegisterCollaboratorRequestIdCardTypeEnum_CHIP_ID,
  _$appKplusRegisterCollaboratorRequestIdCardTypeEnum_PASSPORT,
]);

Serializer<AppKplusRegisterCollaboratorRequestIdCardTypeEnum>
    _$appKplusRegisterCollaboratorRequestIdCardTypeEnumSerializer =
    _$AppKplusRegisterCollaboratorRequestIdCardTypeEnumSerializer();

class _$AppKplusRegisterCollaboratorRequestIdCardTypeEnumSerializer
    implements
        PrimitiveSerializer<AppKplusRegisterCollaboratorRequestIdCardTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'CHIP_ID': 'CHIP_ID',
    'PASSPORT': 'PASSPORT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'CHIP_ID': 'CHIP_ID',
    'PASSPORT': 'PASSPORT',
  };

  @override
  final Iterable<Type> types = const <Type>[
    AppKplusRegisterCollaboratorRequestIdCardTypeEnum
  ];
  @override
  final String wireName = 'AppKplusRegisterCollaboratorRequestIdCardTypeEnum';

  @override
  Object serialize(Serializers serializers,
          AppKplusRegisterCollaboratorRequestIdCardTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  AppKplusRegisterCollaboratorRequestIdCardTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      AppKplusRegisterCollaboratorRequestIdCardTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$AppKplusRegisterCollaboratorRequest
    extends AppKplusRegisterCollaboratorRequest {
  @override
  final String fullName;
  @override
  final AppKplusRegisterCollaboratorRequestIdCardTypeEnum idCardType;
  @override
  final String idCardNo;
  @override
  final String phoneNumber;
  @override
  final String branchCode;
  @override
  final String? permanentAddress;
  @override
  final String? referrerCode;
  @override
  final String? email;
  @override
  final String? frontCardUrl;
  @override
  final String? backCardUrl;

  factory _$AppKplusRegisterCollaboratorRequest(
          [void Function(AppKplusRegisterCollaboratorRequestBuilder)?
              updates]) =>
      (AppKplusRegisterCollaboratorRequestBuilder()..update(updates))._build();

  _$AppKplusRegisterCollaboratorRequest._(
      {required this.fullName,
      required this.idCardType,
      required this.idCardNo,
      required this.phoneNumber,
      required this.branchCode,
      this.permanentAddress,
      this.referrerCode,
      this.email,
      this.frontCardUrl,
      this.backCardUrl})
      : super._();
  @override
  AppKplusRegisterCollaboratorRequest rebuild(
          void Function(AppKplusRegisterCollaboratorRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppKplusRegisterCollaboratorRequestBuilder toBuilder() =>
      AppKplusRegisterCollaboratorRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppKplusRegisterCollaboratorRequest &&
        fullName == other.fullName &&
        idCardType == other.idCardType &&
        idCardNo == other.idCardNo &&
        phoneNumber == other.phoneNumber &&
        branchCode == other.branchCode &&
        permanentAddress == other.permanentAddress &&
        referrerCode == other.referrerCode &&
        email == other.email &&
        frontCardUrl == other.frontCardUrl &&
        backCardUrl == other.backCardUrl;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, idCardType.hashCode);
    _$hash = $jc(_$hash, idCardNo.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, branchCode.hashCode);
    _$hash = $jc(_$hash, permanentAddress.hashCode);
    _$hash = $jc(_$hash, referrerCode.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, frontCardUrl.hashCode);
    _$hash = $jc(_$hash, backCardUrl.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppKplusRegisterCollaboratorRequest')
          ..add('fullName', fullName)
          ..add('idCardType', idCardType)
          ..add('idCardNo', idCardNo)
          ..add('phoneNumber', phoneNumber)
          ..add('branchCode', branchCode)
          ..add('permanentAddress', permanentAddress)
          ..add('referrerCode', referrerCode)
          ..add('email', email)
          ..add('frontCardUrl', frontCardUrl)
          ..add('backCardUrl', backCardUrl))
        .toString();
  }
}

class AppKplusRegisterCollaboratorRequestBuilder
    implements
        Builder<AppKplusRegisterCollaboratorRequest,
            AppKplusRegisterCollaboratorRequestBuilder> {
  _$AppKplusRegisterCollaboratorRequest? _$v;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  AppKplusRegisterCollaboratorRequestIdCardTypeEnum? _idCardType;
  AppKplusRegisterCollaboratorRequestIdCardTypeEnum? get idCardType =>
      _$this._idCardType;
  set idCardType(
          AppKplusRegisterCollaboratorRequestIdCardTypeEnum? idCardType) =>
      _$this._idCardType = idCardType;

  String? _idCardNo;
  String? get idCardNo => _$this._idCardNo;
  set idCardNo(String? idCardNo) => _$this._idCardNo = idCardNo;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  String? _branchCode;
  String? get branchCode => _$this._branchCode;
  set branchCode(String? branchCode) => _$this._branchCode = branchCode;

  String? _permanentAddress;
  String? get permanentAddress => _$this._permanentAddress;
  set permanentAddress(String? permanentAddress) =>
      _$this._permanentAddress = permanentAddress;

  String? _referrerCode;
  String? get referrerCode => _$this._referrerCode;
  set referrerCode(String? referrerCode) => _$this._referrerCode = referrerCode;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _frontCardUrl;
  String? get frontCardUrl => _$this._frontCardUrl;
  set frontCardUrl(String? frontCardUrl) => _$this._frontCardUrl = frontCardUrl;

  String? _backCardUrl;
  String? get backCardUrl => _$this._backCardUrl;
  set backCardUrl(String? backCardUrl) => _$this._backCardUrl = backCardUrl;

  AppKplusRegisterCollaboratorRequestBuilder() {
    AppKplusRegisterCollaboratorRequest._defaults(this);
  }

  AppKplusRegisterCollaboratorRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _fullName = $v.fullName;
      _idCardType = $v.idCardType;
      _idCardNo = $v.idCardNo;
      _phoneNumber = $v.phoneNumber;
      _branchCode = $v.branchCode;
      _permanentAddress = $v.permanentAddress;
      _referrerCode = $v.referrerCode;
      _email = $v.email;
      _frontCardUrl = $v.frontCardUrl;
      _backCardUrl = $v.backCardUrl;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppKplusRegisterCollaboratorRequest other) {
    _$v = other as _$AppKplusRegisterCollaboratorRequest;
  }

  @override
  void update(
      void Function(AppKplusRegisterCollaboratorRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppKplusRegisterCollaboratorRequest build() => _build();

  _$AppKplusRegisterCollaboratorRequest _build() {
    final _$result = _$v ??
        _$AppKplusRegisterCollaboratorRequest._(
          fullName: BuiltValueNullFieldError.checkNotNull(
              fullName, r'AppKplusRegisterCollaboratorRequest', 'fullName'),
          idCardType: BuiltValueNullFieldError.checkNotNull(
              idCardType, r'AppKplusRegisterCollaboratorRequest', 'idCardType'),
          idCardNo: BuiltValueNullFieldError.checkNotNull(
              idCardNo, r'AppKplusRegisterCollaboratorRequest', 'idCardNo'),
          phoneNumber: BuiltValueNullFieldError.checkNotNull(phoneNumber,
              r'AppKplusRegisterCollaboratorRequest', 'phoneNumber'),
          branchCode: BuiltValueNullFieldError.checkNotNull(
              branchCode, r'AppKplusRegisterCollaboratorRequest', 'branchCode'),
          permanentAddress: permanentAddress,
          referrerCode: referrerCode,
          email: email,
          frontCardUrl: frontCardUrl,
          backCardUrl: backCardUrl,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
