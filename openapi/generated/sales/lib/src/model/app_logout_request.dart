//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_logout_request.g.dart';

/// AppLogoutRequest
///
/// Properties:
/// * [accessToken] - Access token to be blacklisted
/// * [refreshToken] - Refresh token to be revoked at Keycloak
@BuiltValue()
abstract class AppLogoutRequest
    implements Built<AppLogoutRequest, AppLogoutRequestBuilder> {
  /// Access token to be blacklisted
  @BuiltValueField(wireName: r'accessToken')
  String get accessToken;

  /// Refresh token to be revoked at Keycloak
  @BuiltValueField(wireName: r'refreshToken')
  String get refreshToken;

  AppLogoutRequest._();

  factory AppLogoutRequest([void updates(AppLogoutRequestBuilder b)]) =
      _$AppLogoutRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppLogoutRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppLogoutRequest> get serializer =>
      _$AppLogoutRequestSerializer();
}

class _$AppLogoutRequestSerializer
    implements PrimitiveSerializer<AppLogoutRequest> {
  @override
  final Iterable<Type> types = const [AppLogoutRequest, _$AppLogoutRequest];

  @override
  final String wireName = r'AppLogoutRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppLogoutRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'accessToken';
    yield serializers.serialize(
      object.accessToken,
      specifiedType: const FullType(String),
    );
    yield r'refreshToken';
    yield serializers.serialize(
      object.refreshToken,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AppLogoutRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppLogoutRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'accessToken':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.accessToken = valueDes;
          break;
        case r'refreshToken':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.refreshToken = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppLogoutRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppLogoutRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
