// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_get_list_province_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppGetListProvinceResponse
    extends BaseResponseAppGetListProvinceResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppGetListProvinceResponse? data;

  factory _$BaseResponseAppGetListProvinceResponse(
          [void Function(BaseResponseAppGetListProvinceResponseBuilder)?
              updates]) =>
      (BaseResponseAppGetListProvinceResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseAppGetListProvinceResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppGetListProvinceResponse rebuild(
          void Function(BaseResponseAppGetListProvinceResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppGetListProvinceResponseBuilder toBuilder() =>
      BaseResponseAppGetListProvinceResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppGetListProvinceResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAppGetListProvinceResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppGetListProvinceResponseBuilder
    implements
        Builder<BaseResponseAppGetListProvinceResponse,
            BaseResponseAppGetListProvinceResponseBuilder> {
  _$BaseResponseAppGetListProvinceResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppGetListProvinceResponseBuilder? _data;
  AppGetListProvinceResponseBuilder get data =>
      _$this._data ??= AppGetListProvinceResponseBuilder();
  set data(AppGetListProvinceResponseBuilder? data) => _$this._data = data;

  BaseResponseAppGetListProvinceResponseBuilder() {
    BaseResponseAppGetListProvinceResponse._defaults(this);
  }

  BaseResponseAppGetListProvinceResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppGetListProvinceResponse other) {
    _$v = other as _$BaseResponseAppGetListProvinceResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppGetListProvinceResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppGetListProvinceResponse build() => _build();

  _$BaseResponseAppGetListProvinceResponse _build() {
    _$BaseResponseAppGetListProvinceResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppGetListProvinceResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppGetListProvinceResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
