// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_create_user_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminCreateUserResponse extends AdminCreateUserResponse {
  @override
  final String? id;
  @override
  final String? username;
  @override
  final String? fullName;
  @override
  final String? email;
  @override
  final String? phone;
  @override
  final String? departmentId;
  @override
  final bool? enabled;
  @override
  final DateTime? createdAt;
  @override
  final String? createdBy;

  factory _$AdminCreateUserResponse(
          [void Function(AdminCreateUserResponseBuilder)? updates]) =>
      (AdminCreateUserResponseBuilder()..update(updates))._build();

  _$AdminCreateUserResponse._(
      {this.id,
      this.username,
      this.fullName,
      this.email,
      this.phone,
      this.departmentId,
      this.enabled,
      this.createdAt,
      this.createdBy})
      : super._();
  @override
  AdminCreateUserResponse rebuild(
          void Function(AdminCreateUserResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminCreateUserResponseBuilder toBuilder() =>
      AdminCreateUserResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminCreateUserResponse &&
        id == other.id &&
        username == other.username &&
        fullName == other.fullName &&
        email == other.email &&
        phone == other.phone &&
        departmentId == other.departmentId &&
        enabled == other.enabled &&
        createdAt == other.createdAt &&
        createdBy == other.createdBy;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, username.hashCode);
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, phone.hashCode);
    _$hash = $jc(_$hash, departmentId.hashCode);
    _$hash = $jc(_$hash, enabled.hashCode);
    _$hash = $jc(_$hash, createdAt.hashCode);
    _$hash = $jc(_$hash, createdBy.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminCreateUserResponse')
          ..add('id', id)
          ..add('username', username)
          ..add('fullName', fullName)
          ..add('email', email)
          ..add('phone', phone)
          ..add('departmentId', departmentId)
          ..add('enabled', enabled)
          ..add('createdAt', createdAt)
          ..add('createdBy', createdBy))
        .toString();
  }
}

class AdminCreateUserResponseBuilder
    implements
        Builder<AdminCreateUserResponse, AdminCreateUserResponseBuilder> {
  _$AdminCreateUserResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _username;
  String? get username => _$this._username;
  set username(String? username) => _$this._username = username;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _phone;
  String? get phone => _$this._phone;
  set phone(String? phone) => _$this._phone = phone;

  String? _departmentId;
  String? get departmentId => _$this._departmentId;
  set departmentId(String? departmentId) => _$this._departmentId = departmentId;

  bool? _enabled;
  bool? get enabled => _$this._enabled;
  set enabled(bool? enabled) => _$this._enabled = enabled;

  DateTime? _createdAt;
  DateTime? get createdAt => _$this._createdAt;
  set createdAt(DateTime? createdAt) => _$this._createdAt = createdAt;

  String? _createdBy;
  String? get createdBy => _$this._createdBy;
  set createdBy(String? createdBy) => _$this._createdBy = createdBy;

  AdminCreateUserResponseBuilder() {
    AdminCreateUserResponse._defaults(this);
  }

  AdminCreateUserResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _username = $v.username;
      _fullName = $v.fullName;
      _email = $v.email;
      _phone = $v.phone;
      _departmentId = $v.departmentId;
      _enabled = $v.enabled;
      _createdAt = $v.createdAt;
      _createdBy = $v.createdBy;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminCreateUserResponse other) {
    _$v = other as _$AdminCreateUserResponse;
  }

  @override
  void update(void Function(AdminCreateUserResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminCreateUserResponse build() => _build();

  _$AdminCreateUserResponse _build() {
    final _$result = _$v ??
        _$AdminCreateUserResponse._(
          id: id,
          username: username,
          fullName: fullName,
          email: email,
          phone: phone,
          departmentId: departmentId,
          enabled: enabled,
          createdAt: createdAt,
          createdBy: createdBy,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
