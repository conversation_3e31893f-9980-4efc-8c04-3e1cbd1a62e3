//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:sales_app_api/src/model/action_dto.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_get_actions_response.g.dart';

/// AdminGetActionsResponse
///
/// Properties:
/// * [actions]
@BuiltValue()
abstract class AdminGetActionsResponse
    implements Built<AdminGetActionsResponse, AdminGetActionsResponseBuilder> {
  @BuiltValueField(wireName: r'actions')
  BuiltList<ActionDto>? get actions;

  AdminGetActionsResponse._();

  factory AdminGetActionsResponse(
          [void updates(AdminGetActionsResponseBuilder b)]) =
      _$AdminGetActionsResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminGetActionsResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminGetActionsResponse> get serializer =>
      _$AdminGetActionsResponseSerializer();
}

class _$AdminGetActionsResponseSerializer
    implements PrimitiveSerializer<AdminGetActionsResponse> {
  @override
  final Iterable<Type> types = const [
    AdminGetActionsResponse,
    _$AdminGetActionsResponse
  ];

  @override
  final String wireName = r'AdminGetActionsResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminGetActionsResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.actions != null) {
      yield r'actions';
      yield serializers.serialize(
        object.actions,
        specifiedType: const FullType(BuiltList, [FullType(ActionDto)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminGetActionsResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminGetActionsResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'actions':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(ActionDto)]),
          ) as BuiltList<ActionDto>;
          result.actions.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminGetActionsResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminGetActionsResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
