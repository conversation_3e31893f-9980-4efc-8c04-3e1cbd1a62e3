//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_get_list_payment_account_request.g.dart';

/// AppGetListPaymentAccountRequest
///
/// Properties:
/// * [idCardNo] - Số giấy tờ
@BuiltValue()
abstract class AppGetListPaymentAccountRequest
    implements
        Built<AppGetListPaymentAccountRequest,
            AppGetListPaymentAccountRequestBuilder> {
  /// Số giấy tờ
  @BuiltValueField(wireName: r'idCardNo')
  String get idCardNo;

  AppGetListPaymentAccountRequest._();

  factory AppGetListPaymentAccountRequest(
          [void updates(AppGetListPaymentAccountRequestBuilder b)]) =
      _$AppGetListPaymentAccountRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppGetListPaymentAccountRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppGetListPaymentAccountRequest> get serializer =>
      _$AppGetListPaymentAccountRequestSerializer();
}

class _$AppGetListPaymentAccountRequestSerializer
    implements PrimitiveSerializer<AppGetListPaymentAccountRequest> {
  @override
  final Iterable<Type> types = const [
    AppGetListPaymentAccountRequest,
    _$AppGetListPaymentAccountRequest
  ];

  @override
  final String wireName = r'AppGetListPaymentAccountRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppGetListPaymentAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'idCardNo';
    yield serializers.serialize(
      object.idCardNo,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AppGetListPaymentAccountRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppGetListPaymentAccountRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'idCardNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.idCardNo = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppGetListPaymentAccountRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppGetListPaymentAccountRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
