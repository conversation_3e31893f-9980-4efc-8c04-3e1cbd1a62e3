// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_get_menu_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminGetMenuResponse
    extends BaseResponseAdminGetMenuResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminGetMenuResponse? data;

  factory _$BaseResponseAdminGetMenuResponse(
          [void Function(BaseResponseAdminGetMenuResponseBuilder)? updates]) =>
      (BaseResponseAdminGetMenuResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminGetMenuResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminGetMenuResponse rebuild(
          void Function(BaseResponseAdminGetMenuResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminGetMenuResponseBuilder toBuilder() =>
      BaseResponseAdminGetMenuResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminGetMenuResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminGetMenuResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminGetMenuResponseBuilder
    implements
        Builder<BaseResponseAdminGetMenuResponse,
            BaseResponseAdminGetMenuResponseBuilder> {
  _$BaseResponseAdminGetMenuResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminGetMenuResponseBuilder? _data;
  AdminGetMenuResponseBuilder get data =>
      _$this._data ??= AdminGetMenuResponseBuilder();
  set data(AdminGetMenuResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminGetMenuResponseBuilder() {
    BaseResponseAdminGetMenuResponse._defaults(this);
  }

  BaseResponseAdminGetMenuResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminGetMenuResponse other) {
    _$v = other as _$BaseResponseAdminGetMenuResponse;
  }

  @override
  void update(void Function(BaseResponseAdminGetMenuResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminGetMenuResponse build() => _build();

  _$BaseResponseAdminGetMenuResponse _build() {
    _$BaseResponseAdminGetMenuResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminGetMenuResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAdminGetMenuResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
