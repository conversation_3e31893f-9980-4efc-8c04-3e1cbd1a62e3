//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_assign_roles_request.g.dart';

/// AdminAssignRolesRequest
///
/// Properties:
/// * [userId]
/// * [roleIds]
@BuiltValue()
abstract class AdminAssignRolesRequest
    implements Built<AdminAssignRolesRequest, AdminAssignRolesRequestBuilder> {
  @BuiltValueField(wireName: r'userId')
  String? get userId;

  @BuiltValueField(wireName: r'roleIds')
  BuiltList<String>? get roleIds;

  AdminAssignRolesRequest._();

  factory AdminAssignRolesRequest(
          [void updates(AdminAssignRolesRequestBuilder b)]) =
      _$AdminAssignRolesRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminAssignRolesRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminAssignRolesRequest> get serializer =>
      _$AdminAssignRolesRequestSerializer();
}

class _$AdminAssignRolesRequestSerializer
    implements PrimitiveSerializer<AdminAssignRolesRequest> {
  @override
  final Iterable<Type> types = const [
    AdminAssignRolesRequest,
    _$AdminAssignRolesRequest
  ];

  @override
  final String wireName = r'AdminAssignRolesRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminAssignRolesRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.userId != null) {
      yield r'userId';
      yield serializers.serialize(
        object.userId,
        specifiedType: const FullType(String),
      );
    }
    if (object.roleIds != null) {
      yield r'roleIds';
      yield serializers.serialize(
        object.roleIds,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminAssignRolesRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminAssignRolesRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'userId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.userId = valueDes;
          break;
        case r'roleIds':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.roleIds.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminAssignRolesRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminAssignRolesRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
