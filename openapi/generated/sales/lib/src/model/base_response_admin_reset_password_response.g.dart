// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_reset_password_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminResetPasswordResponse
    extends BaseResponseAdminResetPasswordResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminResetPasswordResponse? data;

  factory _$BaseResponseAdminResetPasswordResponse(
          [void Function(BaseResponseAdminResetPasswordResponseBuilder)?
              updates]) =>
      (BaseResponseAdminResetPasswordResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseAdminResetPasswordResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminResetPasswordResponse rebuild(
          void Function(BaseResponseAdminResetPasswordResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminResetPasswordResponseBuilder toBuilder() =>
      BaseResponseAdminResetPasswordResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminResetPasswordResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAdminResetPasswordResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminResetPasswordResponseBuilder
    implements
        Builder<BaseResponseAdminResetPasswordResponse,
            BaseResponseAdminResetPasswordResponseBuilder> {
  _$BaseResponseAdminResetPasswordResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminResetPasswordResponseBuilder? _data;
  AdminResetPasswordResponseBuilder get data =>
      _$this._data ??= AdminResetPasswordResponseBuilder();
  set data(AdminResetPasswordResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminResetPasswordResponseBuilder() {
    BaseResponseAdminResetPasswordResponse._defaults(this);
  }

  BaseResponseAdminResetPasswordResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminResetPasswordResponse other) {
    _$v = other as _$BaseResponseAdminResetPasswordResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminResetPasswordResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminResetPasswordResponse build() => _build();

  _$BaseResponseAdminResetPasswordResponse _build() {
    _$BaseResponseAdminResetPasswordResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminResetPasswordResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAdminResetPasswordResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
