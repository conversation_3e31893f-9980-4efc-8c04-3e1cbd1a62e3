// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_get_user_role_permission_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminGetUserRolePermissionResponse
    extends AdminGetUserRolePermissionResponse {
  @override
  final BuiltList<RoleDto>? roles;
  @override
  final BuiltList<MenuDto>? menus;

  factory _$AdminGetUserRolePermissionResponse(
          [void Function(AdminGetUserRolePermissionResponseBuilder)?
              updates]) =>
      (AdminGetUserRolePermissionResponseBuilder()..update(updates))._build();

  _$AdminGetUserRolePermissionResponse._({this.roles, this.menus}) : super._();
  @override
  AdminGetUserRolePermissionResponse rebuild(
          void Function(AdminGetUserRolePermissionResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminGetUserRolePermissionResponseBuilder toBuilder() =>
      AdminGetUserRolePermissionResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminGetUserRolePermissionResponse &&
        roles == other.roles &&
        menus == other.menus;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, roles.hashCode);
    _$hash = $jc(_$hash, menus.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminGetUserRolePermissionResponse')
          ..add('roles', roles)
          ..add('menus', menus))
        .toString();
  }
}

class AdminGetUserRolePermissionResponseBuilder
    implements
        Builder<AdminGetUserRolePermissionResponse,
            AdminGetUserRolePermissionResponseBuilder> {
  _$AdminGetUserRolePermissionResponse? _$v;

  ListBuilder<RoleDto>? _roles;
  ListBuilder<RoleDto> get roles => _$this._roles ??= ListBuilder<RoleDto>();
  set roles(ListBuilder<RoleDto>? roles) => _$this._roles = roles;

  ListBuilder<MenuDto>? _menus;
  ListBuilder<MenuDto> get menus => _$this._menus ??= ListBuilder<MenuDto>();
  set menus(ListBuilder<MenuDto>? menus) => _$this._menus = menus;

  AdminGetUserRolePermissionResponseBuilder() {
    AdminGetUserRolePermissionResponse._defaults(this);
  }

  AdminGetUserRolePermissionResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _roles = $v.roles?.toBuilder();
      _menus = $v.menus?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminGetUserRolePermissionResponse other) {
    _$v = other as _$AdminGetUserRolePermissionResponse;
  }

  @override
  void update(
      void Function(AdminGetUserRolePermissionResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminGetUserRolePermissionResponse build() => _build();

  _$AdminGetUserRolePermissionResponse _build() {
    _$AdminGetUserRolePermissionResponse _$result;
    try {
      _$result = _$v ??
          _$AdminGetUserRolePermissionResponse._(
            roles: _roles?.build(),
            menus: _menus?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'roles';
        _roles?.build();
        _$failedField = 'menus';
        _menus?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminGetUserRolePermissionResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
