// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_assign_roles_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminAssignRolesRequest extends AdminAssignRolesRequest {
  @override
  final String? userId;
  @override
  final BuiltList<String>? roleIds;

  factory _$AdminAssignRolesRequest(
          [void Function(AdminAssignRolesRequestBuilder)? updates]) =>
      (AdminAssignRolesRequestBuilder()..update(updates))._build();

  _$AdminAssignRolesRequest._({this.userId, this.roleIds}) : super._();
  @override
  AdminAssignRolesRequest rebuild(
          void Function(AdminAssignRolesRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminAssignRolesRequestBuilder toBuilder() =>
      AdminAssignRolesRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminAssignRolesRequest &&
        userId == other.userId &&
        roleIds == other.roleIds;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, roleIds.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminAssignRolesRequest')
          ..add('userId', userId)
          ..add('roleIds', roleIds))
        .toString();
  }
}

class AdminAssignRolesRequestBuilder
    implements
        Builder<AdminAssignRolesRequest, AdminAssignRolesRequestBuilder> {
  _$AdminAssignRolesRequest? _$v;

  String? _userId;
  String? get userId => _$this._userId;
  set userId(String? userId) => _$this._userId = userId;

  ListBuilder<String>? _roleIds;
  ListBuilder<String> get roleIds => _$this._roleIds ??= ListBuilder<String>();
  set roleIds(ListBuilder<String>? roleIds) => _$this._roleIds = roleIds;

  AdminAssignRolesRequestBuilder() {
    AdminAssignRolesRequest._defaults(this);
  }

  AdminAssignRolesRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userId = $v.userId;
      _roleIds = $v.roleIds?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminAssignRolesRequest other) {
    _$v = other as _$AdminAssignRolesRequest;
  }

  @override
  void update(void Function(AdminAssignRolesRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminAssignRolesRequest build() => _build();

  _$AdminAssignRolesRequest _build() {
    _$AdminAssignRolesRequest _$result;
    try {
      _$result = _$v ??
          _$AdminAssignRolesRequest._(
            userId: userId,
            roleIds: _roleIds?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'roleIds';
        _roleIds?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminAssignRolesRequest', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
