//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:sales_app_api/src/model/system_config_dto.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_get_system_configs_by_group_code_response.g.dart';

/// AppGetSystemConfigsByGroupCodeResponse
///
/// Properties:
/// * [groupCode]
/// * [configs]
@BuiltValue()
abstract class AppGetSystemConfigsByGroupCodeResponse
    implements
        Built<AppGetSystemConfigsByGroupCodeResponse,
            AppGetSystemConfigsByGroupCodeResponseBuilder> {
  @BuiltValueField(wireName: r'groupCode')
  String? get groupCode;

  @BuiltValueField(wireName: r'configs')
  BuiltList<SystemConfigDto>? get configs;

  AppGetSystemConfigsByGroupCodeResponse._();

  factory AppGetSystemConfigsByGroupCodeResponse(
          [void updates(AppGetSystemConfigsByGroupCodeResponseBuilder b)]) =
      _$AppGetSystemConfigsByGroupCodeResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppGetSystemConfigsByGroupCodeResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppGetSystemConfigsByGroupCodeResponse> get serializer =>
      _$AppGetSystemConfigsByGroupCodeResponseSerializer();
}

class _$AppGetSystemConfigsByGroupCodeResponseSerializer
    implements PrimitiveSerializer<AppGetSystemConfigsByGroupCodeResponse> {
  @override
  final Iterable<Type> types = const [
    AppGetSystemConfigsByGroupCodeResponse,
    _$AppGetSystemConfigsByGroupCodeResponse
  ];

  @override
  final String wireName = r'AppGetSystemConfigsByGroupCodeResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppGetSystemConfigsByGroupCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.groupCode != null) {
      yield r'groupCode';
      yield serializers.serialize(
        object.groupCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.configs != null) {
      yield r'configs';
      yield serializers.serialize(
        object.configs,
        specifiedType: const FullType(BuiltList, [FullType(SystemConfigDto)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppGetSystemConfigsByGroupCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppGetSystemConfigsByGroupCodeResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'groupCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.groupCode = valueDes;
          break;
        case r'configs':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType(BuiltList, [FullType(SystemConfigDto)]),
          ) as BuiltList<SystemConfigDto>;
          result.configs.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppGetSystemConfigsByGroupCodeResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppGetSystemConfigsByGroupCodeResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
