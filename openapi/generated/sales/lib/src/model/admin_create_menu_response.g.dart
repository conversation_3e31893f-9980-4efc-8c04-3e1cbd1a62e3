// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_create_menu_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminCreateMenuResponse extends AdminCreateMenuResponse {
  @override
  final String? id;
  @override
  final String? code;
  @override
  final String? platform;
  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? parentId;
  @override
  final int? order;
  @override
  final BuiltList<String>? actionIds;

  factory _$AdminCreateMenuResponse(
          [void Function(AdminCreateMenuResponseBuilder)? updates]) =>
      (AdminCreateMenuResponseBuilder()..update(updates))._build();

  _$AdminCreateMenuResponse._(
      {this.id,
      this.code,
      this.platform,
      this.name,
      this.description,
      this.parentId,
      this.order,
      this.actionIds})
      : super._();
  @override
  AdminCreateMenuResponse rebuild(
          void Function(AdminCreateMenuResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminCreateMenuResponseBuilder toBuilder() =>
      AdminCreateMenuResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminCreateMenuResponse &&
        id == other.id &&
        code == other.code &&
        platform == other.platform &&
        name == other.name &&
        description == other.description &&
        parentId == other.parentId &&
        order == other.order &&
        actionIds == other.actionIds;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, platform.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jc(_$hash, parentId.hashCode);
    _$hash = $jc(_$hash, order.hashCode);
    _$hash = $jc(_$hash, actionIds.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminCreateMenuResponse')
          ..add('id', id)
          ..add('code', code)
          ..add('platform', platform)
          ..add('name', name)
          ..add('description', description)
          ..add('parentId', parentId)
          ..add('order', order)
          ..add('actionIds', actionIds))
        .toString();
  }
}

class AdminCreateMenuResponseBuilder
    implements
        Builder<AdminCreateMenuResponse, AdminCreateMenuResponseBuilder> {
  _$AdminCreateMenuResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _platform;
  String? get platform => _$this._platform;
  set platform(String? platform) => _$this._platform = platform;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  String? _parentId;
  String? get parentId => _$this._parentId;
  set parentId(String? parentId) => _$this._parentId = parentId;

  int? _order;
  int? get order => _$this._order;
  set order(int? order) => _$this._order = order;

  ListBuilder<String>? _actionIds;
  ListBuilder<String> get actionIds =>
      _$this._actionIds ??= ListBuilder<String>();
  set actionIds(ListBuilder<String>? actionIds) =>
      _$this._actionIds = actionIds;

  AdminCreateMenuResponseBuilder() {
    AdminCreateMenuResponse._defaults(this);
  }

  AdminCreateMenuResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _code = $v.code;
      _platform = $v.platform;
      _name = $v.name;
      _description = $v.description;
      _parentId = $v.parentId;
      _order = $v.order;
      _actionIds = $v.actionIds?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminCreateMenuResponse other) {
    _$v = other as _$AdminCreateMenuResponse;
  }

  @override
  void update(void Function(AdminCreateMenuResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminCreateMenuResponse build() => _build();

  _$AdminCreateMenuResponse _build() {
    _$AdminCreateMenuResponse _$result;
    try {
      _$result = _$v ??
          _$AdminCreateMenuResponse._(
            id: id,
            code: code,
            platform: platform,
            name: name,
            description: description,
            parentId: parentId,
            order: order,
            actionIds: _actionIds?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'actionIds';
        _actionIds?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminCreateMenuResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
