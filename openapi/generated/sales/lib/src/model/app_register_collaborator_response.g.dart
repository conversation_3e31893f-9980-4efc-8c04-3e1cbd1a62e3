// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_register_collaborator_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppRegisterCollaboratorResponse
    extends AppRegisterCollaboratorResponse {
  @override
  final String? fullName;
  @override
  final String? branchName;
  @override
  final String? positionName;

  factory _$AppRegisterCollaboratorResponse(
          [void Function(AppRegisterCollaboratorResponseBuilder)? updates]) =>
      (AppRegisterCollaboratorResponseBuilder()..update(updates))._build();

  _$AppRegisterCollaboratorResponse._(
      {this.fullName, this.branchName, this.positionName})
      : super._();
  @override
  AppRegisterCollaboratorResponse rebuild(
          void Function(AppRegisterCollaboratorResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppRegisterCollaboratorResponseBuilder toBuilder() =>
      AppRegisterCollaboratorResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppRegisterCollaboratorResponse &&
        fullName == other.fullName &&
        branchName == other.branchName &&
        positionName == other.positionName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, branchName.hashCode);
    _$hash = $jc(_$hash, positionName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppRegisterCollaboratorResponse')
          ..add('fullName', fullName)
          ..add('branchName', branchName)
          ..add('positionName', positionName))
        .toString();
  }
}

class AppRegisterCollaboratorResponseBuilder
    implements
        Builder<AppRegisterCollaboratorResponse,
            AppRegisterCollaboratorResponseBuilder> {
  _$AppRegisterCollaboratorResponse? _$v;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _branchName;
  String? get branchName => _$this._branchName;
  set branchName(String? branchName) => _$this._branchName = branchName;

  String? _positionName;
  String? get positionName => _$this._positionName;
  set positionName(String? positionName) => _$this._positionName = positionName;

  AppRegisterCollaboratorResponseBuilder() {
    AppRegisterCollaboratorResponse._defaults(this);
  }

  AppRegisterCollaboratorResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _fullName = $v.fullName;
      _branchName = $v.branchName;
      _positionName = $v.positionName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppRegisterCollaboratorResponse other) {
    _$v = other as _$AppRegisterCollaboratorResponse;
  }

  @override
  void update(void Function(AppRegisterCollaboratorResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppRegisterCollaboratorResponse build() => _build();

  _$AppRegisterCollaboratorResponse _build() {
    final _$result = _$v ??
        _$AppRegisterCollaboratorResponse._(
          fullName: fullName,
          branchName: branchName,
          positionName: positionName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
