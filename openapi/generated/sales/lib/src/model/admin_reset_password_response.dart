//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_reset_password_response.g.dart';

/// AdminResetPasswordResponse
///
/// Properties:
/// * [userId]
/// * [success]
@BuiltValue()
abstract class AdminResetPasswordResponse
    implements
        Built<AdminResetPasswordResponse, AdminResetPasswordResponseBuilder> {
  @BuiltValueField(wireName: r'userId')
  String? get userId;

  @BuiltValueField(wireName: r'success')
  bool? get success;

  AdminResetPasswordResponse._();

  factory AdminResetPasswordResponse(
          [void updates(AdminResetPasswordResponseBuilder b)]) =
      _$AdminResetPasswordResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminResetPasswordResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminResetPasswordResponse> get serializer =>
      _$AdminResetPasswordResponseSerializer();
}

class _$AdminResetPasswordResponseSerializer
    implements PrimitiveSerializer<AdminResetPasswordResponse> {
  @override
  final Iterable<Type> types = const [
    AdminResetPasswordResponse,
    _$AdminResetPasswordResponse
  ];

  @override
  final String wireName = r'AdminResetPasswordResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminResetPasswordResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.userId != null) {
      yield r'userId';
      yield serializers.serialize(
        object.userId,
        specifiedType: const FullType(String),
      );
    }
    if (object.success != null) {
      yield r'success';
      yield serializers.serialize(
        object.success,
        specifiedType: const FullType(bool),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminResetPasswordResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminResetPasswordResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'userId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.userId = valueDes;
          break;
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.success = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminResetPasswordResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminResetPasswordResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
