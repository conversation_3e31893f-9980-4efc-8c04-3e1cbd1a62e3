//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_kplus_register_collaborator_response.g.dart';

/// AppKplusRegisterCollaboratorResponse
///
/// Properties:
/// * [fullName] - Họ tên
/// * [branchName] - Tên chi nhánh
/// * [positionName] - Tên chức danh
@BuiltValue()
abstract class AppKplusRegisterCollaboratorResponse
    implements
        Built<AppKplusRegisterCollaboratorResponse,
            AppKplusRegisterCollaboratorResponseBuilder> {
  /// Họ tên
  @BuiltValueField(wireName: r'fullName')
  String? get fullName;

  /// Tên chi nhánh
  @BuiltValueField(wireName: r'branchName')
  String? get branchName;

  /// Tên chức danh
  @BuiltValueField(wireName: r'positionName')
  String? get positionName;

  AppKplusRegisterCollaboratorResponse._();

  factory AppKplusRegisterCollaboratorResponse(
          [void updates(AppKplusRegisterCollaboratorResponseBuilder b)]) =
      _$AppKplusRegisterCollaboratorResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppKplusRegisterCollaboratorResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppKplusRegisterCollaboratorResponse> get serializer =>
      _$AppKplusRegisterCollaboratorResponseSerializer();
}

class _$AppKplusRegisterCollaboratorResponseSerializer
    implements PrimitiveSerializer<AppKplusRegisterCollaboratorResponse> {
  @override
  final Iterable<Type> types = const [
    AppKplusRegisterCollaboratorResponse,
    _$AppKplusRegisterCollaboratorResponse
  ];

  @override
  final String wireName = r'AppKplusRegisterCollaboratorResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppKplusRegisterCollaboratorResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.fullName != null) {
      yield r'fullName';
      yield serializers.serialize(
        object.fullName,
        specifiedType: const FullType(String),
      );
    }
    if (object.branchName != null) {
      yield r'branchName';
      yield serializers.serialize(
        object.branchName,
        specifiedType: const FullType(String),
      );
    }
    if (object.positionName != null) {
      yield r'positionName';
      yield serializers.serialize(
        object.positionName,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppKplusRegisterCollaboratorResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppKplusRegisterCollaboratorResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'fullName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.fullName = valueDes;
          break;
        case r'branchName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.branchName = valueDes;
          break;
        case r'positionName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.positionName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppKplusRegisterCollaboratorResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppKplusRegisterCollaboratorResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
