// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_branch_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppBranchDto extends AppBranchDto {
  @override
  final String? id;
  @override
  final String? code;
  @override
  final String? name;
  @override
  final String? address;
  @override
  final String? provinceId;

  factory _$AppBranchDto([void Function(AppBranchDtoBuilder)? updates]) =>
      (AppBranchDtoBuilder()..update(updates))._build();

  _$AppBranchDto._(
      {this.id, this.code, this.name, this.address, this.provinceId})
      : super._();
  @override
  AppBranchDto rebuild(void Function(AppBranchDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppBranchDtoBuilder toBuilder() => AppBranchDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppBranchDto &&
        id == other.id &&
        code == other.code &&
        name == other.name &&
        address == other.address &&
        provinceId == other.provinceId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, address.hashCode);
    _$hash = $jc(_$hash, provinceId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppBranchDto')
          ..add('id', id)
          ..add('code', code)
          ..add('name', name)
          ..add('address', address)
          ..add('provinceId', provinceId))
        .toString();
  }
}

class AppBranchDtoBuilder
    implements Builder<AppBranchDto, AppBranchDtoBuilder> {
  _$AppBranchDto? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _address;
  String? get address => _$this._address;
  set address(String? address) => _$this._address = address;

  String? _provinceId;
  String? get provinceId => _$this._provinceId;
  set provinceId(String? provinceId) => _$this._provinceId = provinceId;

  AppBranchDtoBuilder() {
    AppBranchDto._defaults(this);
  }

  AppBranchDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _code = $v.code;
      _name = $v.name;
      _address = $v.address;
      _provinceId = $v.provinceId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppBranchDto other) {
    _$v = other as _$AppBranchDto;
  }

  @override
  void update(void Function(AppBranchDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppBranchDto build() => _build();

  _$AppBranchDto _build() {
    final _$result = _$v ??
        _$AppBranchDto._(
          id: id,
          code: code,
          name: name,
          address: address,
          provinceId: provinceId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
