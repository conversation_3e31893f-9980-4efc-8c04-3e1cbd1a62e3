//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_refresh_token_response.g.dart';

/// AppRefreshTokenResponse
///
/// Properties:
/// * [accessToken] - Access token for the user
/// * [expiresIn] - Time in seconds until the access token expires
/// * [refreshToken] - Refresh token for obtaining a new access token
/// * [refreshExpiresIn] - Time in seconds until the refresh token expires
/// * [tokenType] - Type of the token, typically 'Bearer'
@BuiltValue()
abstract class AppRefreshTokenResponse
    implements Built<AppRefreshTokenResponse, AppRefreshTokenResponseBuilder> {
  /// Access token for the user
  @BuiltValueField(wireName: r'accessToken')
  String? get accessToken;

  /// Time in seconds until the access token expires
  @BuiltValueField(wireName: r'expiresIn')
  int? get expiresIn;

  /// Refresh token for obtaining a new access token
  @BuiltValueField(wireName: r'refreshToken')
  String? get refreshToken;

  /// Time in seconds until the refresh token expires
  @BuiltValueField(wireName: r'refreshExpiresIn')
  int? get refreshExpiresIn;

  /// Type of the token, typically 'Bearer'
  @BuiltValueField(wireName: r'tokenType')
  String? get tokenType;

  AppRefreshTokenResponse._();

  factory AppRefreshTokenResponse(
          [void updates(AppRefreshTokenResponseBuilder b)]) =
      _$AppRefreshTokenResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppRefreshTokenResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppRefreshTokenResponse> get serializer =>
      _$AppRefreshTokenResponseSerializer();
}

class _$AppRefreshTokenResponseSerializer
    implements PrimitiveSerializer<AppRefreshTokenResponse> {
  @override
  final Iterable<Type> types = const [
    AppRefreshTokenResponse,
    _$AppRefreshTokenResponse
  ];

  @override
  final String wireName = r'AppRefreshTokenResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppRefreshTokenResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.accessToken != null) {
      yield r'accessToken';
      yield serializers.serialize(
        object.accessToken,
        specifiedType: const FullType(String),
      );
    }
    if (object.expiresIn != null) {
      yield r'expiresIn';
      yield serializers.serialize(
        object.expiresIn,
        specifiedType: const FullType(int),
      );
    }
    if (object.refreshToken != null) {
      yield r'refreshToken';
      yield serializers.serialize(
        object.refreshToken,
        specifiedType: const FullType(String),
      );
    }
    if (object.refreshExpiresIn != null) {
      yield r'refreshExpiresIn';
      yield serializers.serialize(
        object.refreshExpiresIn,
        specifiedType: const FullType(int),
      );
    }
    if (object.tokenType != null) {
      yield r'tokenType';
      yield serializers.serialize(
        object.tokenType,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppRefreshTokenResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppRefreshTokenResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'accessToken':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.accessToken = valueDes;
          break;
        case r'expiresIn':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.expiresIn = valueDes;
          break;
        case r'refreshToken':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.refreshToken = valueDes;
          break;
        case r'refreshExpiresIn':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.refreshExpiresIn = valueDes;
          break;
        case r'tokenType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.tokenType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppRefreshTokenResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppRefreshTokenResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
