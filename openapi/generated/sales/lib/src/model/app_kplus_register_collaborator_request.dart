//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_kplus_register_collaborator_request.g.dart';

/// AppKplusRegisterCollaboratorRequest
///
/// Properties:
/// * [fullName] - Họ tên
/// * [idCardType] - Loại GTTT
/// * [idCardNo] - GTTT
/// * [phoneNumber] - Số điện thoại
/// * [branchCode] - Mã chi nhánh
/// * [permanentAddress] - Địa chỉ thường trú
/// * [referrerCode] - Mã người giới thiệu. Mã cif
/// * [email] - Email
/// * [frontCardUrl] - Ảnh mặt trước của giấy tờ
/// * [backCardUrl] - Ảnh mặt sau của giấy tờ
@BuiltValue()
abstract class AppKplusRegisterCollaboratorRequest
    implements
        Built<AppKplusRegisterCollaboratorRequest,
            AppKplusRegisterCollaboratorRequestBuilder> {
  /// Họ tên
  @BuiltValueField(wireName: r'fullName')
  String get fullName;

  /// Loại GTTT
  @BuiltValueField(wireName: r'idCardType')
  AppKplusRegisterCollaboratorRequestIdCardTypeEnum get idCardType;
  // enum idCardTypeEnum {  CHIP_ID,  PASSPORT,  };

  /// GTTT
  @BuiltValueField(wireName: r'idCardNo')
  String get idCardNo;

  /// Số điện thoại
  @BuiltValueField(wireName: r'phoneNumber')
  String get phoneNumber;

  /// Mã chi nhánh
  @BuiltValueField(wireName: r'branchCode')
  String get branchCode;

  /// Địa chỉ thường trú
  @BuiltValueField(wireName: r'permanentAddress')
  String? get permanentAddress;

  /// Mã người giới thiệu. Mã cif
  @BuiltValueField(wireName: r'referrerCode')
  String? get referrerCode;

  /// Email
  @BuiltValueField(wireName: r'email')
  String? get email;

  /// Ảnh mặt trước của giấy tờ
  @BuiltValueField(wireName: r'frontCardUrl')
  String? get frontCardUrl;

  /// Ảnh mặt sau của giấy tờ
  @BuiltValueField(wireName: r'backCardUrl')
  String? get backCardUrl;

  AppKplusRegisterCollaboratorRequest._();

  factory AppKplusRegisterCollaboratorRequest(
          [void updates(AppKplusRegisterCollaboratorRequestBuilder b)]) =
      _$AppKplusRegisterCollaboratorRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppKplusRegisterCollaboratorRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppKplusRegisterCollaboratorRequest> get serializer =>
      _$AppKplusRegisterCollaboratorRequestSerializer();
}

class _$AppKplusRegisterCollaboratorRequestSerializer
    implements PrimitiveSerializer<AppKplusRegisterCollaboratorRequest> {
  @override
  final Iterable<Type> types = const [
    AppKplusRegisterCollaboratorRequest,
    _$AppKplusRegisterCollaboratorRequest
  ];

  @override
  final String wireName = r'AppKplusRegisterCollaboratorRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppKplusRegisterCollaboratorRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'fullName';
    yield serializers.serialize(
      object.fullName,
      specifiedType: const FullType(String),
    );
    yield r'idCardType';
    yield serializers.serialize(
      object.idCardType,
      specifiedType:
          const FullType(AppKplusRegisterCollaboratorRequestIdCardTypeEnum),
    );
    yield r'idCardNo';
    yield serializers.serialize(
      object.idCardNo,
      specifiedType: const FullType(String),
    );
    yield r'phoneNumber';
    yield serializers.serialize(
      object.phoneNumber,
      specifiedType: const FullType(String),
    );
    yield r'branchCode';
    yield serializers.serialize(
      object.branchCode,
      specifiedType: const FullType(String),
    );
    if (object.permanentAddress != null) {
      yield r'permanentAddress';
      yield serializers.serialize(
        object.permanentAddress,
        specifiedType: const FullType(String),
      );
    }
    if (object.referrerCode != null) {
      yield r'referrerCode';
      yield serializers.serialize(
        object.referrerCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.email != null) {
      yield r'email';
      yield serializers.serialize(
        object.email,
        specifiedType: const FullType(String),
      );
    }
    if (object.frontCardUrl != null) {
      yield r'frontCardUrl';
      yield serializers.serialize(
        object.frontCardUrl,
        specifiedType: const FullType(String),
      );
    }
    if (object.backCardUrl != null) {
      yield r'backCardUrl';
      yield serializers.serialize(
        object.backCardUrl,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppKplusRegisterCollaboratorRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppKplusRegisterCollaboratorRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'fullName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.fullName = valueDes;
          break;
        case r'idCardType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(
                AppKplusRegisterCollaboratorRequestIdCardTypeEnum),
          ) as AppKplusRegisterCollaboratorRequestIdCardTypeEnum;
          result.idCardType = valueDes;
          break;
        case r'idCardNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.idCardNo = valueDes;
          break;
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.phoneNumber = valueDes;
          break;
        case r'branchCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.branchCode = valueDes;
          break;
        case r'permanentAddress':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.permanentAddress = valueDes;
          break;
        case r'referrerCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.referrerCode = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        case r'frontCardUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.frontCardUrl = valueDes;
          break;
        case r'backCardUrl':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.backCardUrl = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppKplusRegisterCollaboratorRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppKplusRegisterCollaboratorRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class AppKplusRegisterCollaboratorRequestIdCardTypeEnum extends EnumClass {
  /// Loại GTTT
  @BuiltValueEnumConst(wireName: r'CHIP_ID')
  static const AppKplusRegisterCollaboratorRequestIdCardTypeEnum CHIP_ID =
      _$appKplusRegisterCollaboratorRequestIdCardTypeEnum_CHIP_ID;

  /// Loại GTTT
  @BuiltValueEnumConst(wireName: r'PASSPORT')
  static const AppKplusRegisterCollaboratorRequestIdCardTypeEnum PASSPORT =
      _$appKplusRegisterCollaboratorRequestIdCardTypeEnum_PASSPORT;

  static Serializer<AppKplusRegisterCollaboratorRequestIdCardTypeEnum>
      get serializer =>
          _$appKplusRegisterCollaboratorRequestIdCardTypeEnumSerializer;

  const AppKplusRegisterCollaboratorRequestIdCardTypeEnum._(String name)
      : super(name);

  static BuiltSet<AppKplusRegisterCollaboratorRequestIdCardTypeEnum>
      get values => _$appKplusRegisterCollaboratorRequestIdCardTypeEnumValues;
  static AppKplusRegisterCollaboratorRequestIdCardTypeEnum valueOf(
          String name) =>
      _$appKplusRegisterCollaboratorRequestIdCardTypeEnumValueOf(name);
}
