// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_kplus_register_collaborator_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppKplusRegisterCollaboratorResponse
    extends AppKplusRegisterCollaboratorResponse {
  @override
  final String? fullName;
  @override
  final String? branchName;
  @override
  final String? positionName;

  factory _$AppKplusRegisterCollaboratorResponse(
          [void Function(AppKplusRegisterCollaboratorResponseBuilder)?
              updates]) =>
      (AppKplusRegisterCollaboratorResponseBuilder()..update(updates))._build();

  _$AppKplusRegisterCollaboratorResponse._(
      {this.fullName, this.branchName, this.positionName})
      : super._();
  @override
  AppKplusRegisterCollaboratorResponse rebuild(
          void Function(AppKplusRegisterCollaboratorResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppKplusRegisterCollaboratorResponseBuilder toBuilder() =>
      AppKplusRegisterCollaboratorResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppKplusRegisterCollaboratorResponse &&
        fullName == other.fullName &&
        branchName == other.branchName &&
        positionName == other.positionName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, branchName.hashCode);
    _$hash = $jc(_$hash, positionName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppKplusRegisterCollaboratorResponse')
          ..add('fullName', fullName)
          ..add('branchName', branchName)
          ..add('positionName', positionName))
        .toString();
  }
}

class AppKplusRegisterCollaboratorResponseBuilder
    implements
        Builder<AppKplusRegisterCollaboratorResponse,
            AppKplusRegisterCollaboratorResponseBuilder> {
  _$AppKplusRegisterCollaboratorResponse? _$v;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _branchName;
  String? get branchName => _$this._branchName;
  set branchName(String? branchName) => _$this._branchName = branchName;

  String? _positionName;
  String? get positionName => _$this._positionName;
  set positionName(String? positionName) => _$this._positionName = positionName;

  AppKplusRegisterCollaboratorResponseBuilder() {
    AppKplusRegisterCollaboratorResponse._defaults(this);
  }

  AppKplusRegisterCollaboratorResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _fullName = $v.fullName;
      _branchName = $v.branchName;
      _positionName = $v.positionName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppKplusRegisterCollaboratorResponse other) {
    _$v = other as _$AppKplusRegisterCollaboratorResponse;
  }

  @override
  void update(
      void Function(AppKplusRegisterCollaboratorResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppKplusRegisterCollaboratorResponse build() => _build();

  _$AppKplusRegisterCollaboratorResponse _build() {
    final _$result = _$v ??
        _$AppKplusRegisterCollaboratorResponse._(
          fullName: fullName,
          branchName: branchName,
          positionName: positionName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
