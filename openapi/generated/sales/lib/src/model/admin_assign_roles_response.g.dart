// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_assign_roles_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminAssignRolesResponse extends AdminAssignRolesResponse {
  @override
  final String? userId;
  @override
  final BuiltList<String>? roleIds;
  @override
  final bool? success;

  factory _$AdminAssignRolesResponse(
          [void Function(AdminAssignRolesResponseBuilder)? updates]) =>
      (AdminAssignRolesResponseBuilder()..update(updates))._build();

  _$AdminAssignRolesResponse._({this.userId, this.roleIds, this.success})
      : super._();
  @override
  AdminAssignRolesResponse rebuild(
          void Function(AdminAssignRolesResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminAssignRolesResponseBuilder toBuilder() =>
      AdminAssignRolesResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminAssignRolesResponse &&
        userId == other.userId &&
        roleIds == other.roleIds &&
        success == other.success;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, roleIds.hashCode);
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminAssignRolesResponse')
          ..add('userId', userId)
          ..add('roleIds', roleIds)
          ..add('success', success))
        .toString();
  }
}

class AdminAssignRolesResponseBuilder
    implements
        Builder<AdminAssignRolesResponse, AdminAssignRolesResponseBuilder> {
  _$AdminAssignRolesResponse? _$v;

  String? _userId;
  String? get userId => _$this._userId;
  set userId(String? userId) => _$this._userId = userId;

  ListBuilder<String>? _roleIds;
  ListBuilder<String> get roleIds => _$this._roleIds ??= ListBuilder<String>();
  set roleIds(ListBuilder<String>? roleIds) => _$this._roleIds = roleIds;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  AdminAssignRolesResponseBuilder() {
    AdminAssignRolesResponse._defaults(this);
  }

  AdminAssignRolesResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userId = $v.userId;
      _roleIds = $v.roleIds?.toBuilder();
      _success = $v.success;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminAssignRolesResponse other) {
    _$v = other as _$AdminAssignRolesResponse;
  }

  @override
  void update(void Function(AdminAssignRolesResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminAssignRolesResponse build() => _build();

  _$AdminAssignRolesResponse _build() {
    _$AdminAssignRolesResponse _$result;
    try {
      _$result = _$v ??
          _$AdminAssignRolesResponse._(
            userId: userId,
            roleIds: _roleIds?.build(),
            success: success,
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'roleIds';
        _roleIds?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminAssignRolesResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
