//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:sales_app_api/src/model/app_branch_dto.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_get_user_profile_response.g.dart';

/// AppGetUserProfileResponse
///
/// Properties:
/// * [code] - Mã nhân viên
/// * [cifNo] - Unique identifier for the user
/// * [fullName] - Full name of the user
/// * [email] - Email of the user
/// * [phoneNumber] - Phone number of the user
/// * [branch] - Chi nhánh người dùng
@BuiltValue()
abstract class AppGetUserProfileResponse
    implements
        Built<AppGetUserProfileResponse, AppGetUserProfileResponseBuilder> {
  /// Mã nhân viên
  @BuiltValueField(wireName: r'code')
  String? get code;

  /// Unique identifier for the user
  @BuiltValueField(wireName: r'cifNo')
  String? get cifNo;

  /// Full name of the user
  @BuiltValueField(wireName: r'fullName')
  String? get fullName;

  /// Email of the user
  @BuiltValueField(wireName: r'email')
  String? get email;

  /// Phone number of the user
  @BuiltValueField(wireName: r'phoneNumber')
  String? get phoneNumber;

  /// Chi nhánh người dùng
  @BuiltValueField(wireName: r'branch')
  AppBranchDto? get branch;

  AppGetUserProfileResponse._();

  factory AppGetUserProfileResponse(
          [void updates(AppGetUserProfileResponseBuilder b)]) =
      _$AppGetUserProfileResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppGetUserProfileResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppGetUserProfileResponse> get serializer =>
      _$AppGetUserProfileResponseSerializer();
}

class _$AppGetUserProfileResponseSerializer
    implements PrimitiveSerializer<AppGetUserProfileResponse> {
  @override
  final Iterable<Type> types = const [
    AppGetUserProfileResponse,
    _$AppGetUserProfileResponse
  ];

  @override
  final String wireName = r'AppGetUserProfileResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppGetUserProfileResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(String),
      );
    }
    if (object.cifNo != null) {
      yield r'cifNo';
      yield serializers.serialize(
        object.cifNo,
        specifiedType: const FullType(String),
      );
    }
    if (object.fullName != null) {
      yield r'fullName';
      yield serializers.serialize(
        object.fullName,
        specifiedType: const FullType(String),
      );
    }
    if (object.email != null) {
      yield r'email';
      yield serializers.serialize(
        object.email,
        specifiedType: const FullType(String),
      );
    }
    if (object.phoneNumber != null) {
      yield r'phoneNumber';
      yield serializers.serialize(
        object.phoneNumber,
        specifiedType: const FullType(String),
      );
    }
    if (object.branch != null) {
      yield r'branch';
      yield serializers.serialize(
        object.branch,
        specifiedType: const FullType(AppBranchDto),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppGetUserProfileResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppGetUserProfileResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        case r'cifNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.cifNo = valueDes;
          break;
        case r'fullName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.fullName = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.phoneNumber = valueDes;
          break;
        case r'branch':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(AppBranchDto),
          ) as AppBranchDto;
          result.branch.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppGetUserProfileResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppGetUserProfileResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
