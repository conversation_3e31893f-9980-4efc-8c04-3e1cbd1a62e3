// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_get_user_by_id_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminGetUserByIdResponse extends AdminGetUserByIdResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final JsonObject? data;
  @override
  final User? user;

  factory _$AdminGetUserByIdResponse(
          [void Function(AdminGetUserByIdResponseBuilder)? updates]) =>
      (AdminGetUserByIdResponseBuilder()..update(updates))._build();

  _$AdminGetUserByIdResponse._(
      {this.success, this.code, this.message, this.data, this.user})
      : super._();
  @override
  AdminGetUserByIdResponse rebuild(
          void Function(AdminGetUserByIdResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminGetUserByIdResponseBuilder toBuilder() =>
      AdminGetUserByIdResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminGetUserByIdResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data &&
        user == other.user;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jc(_$hash, user.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminGetUserByIdResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data)
          ..add('user', user))
        .toString();
  }
}

class AdminGetUserByIdResponseBuilder
    implements
        Builder<AdminGetUserByIdResponse, AdminGetUserByIdResponseBuilder> {
  _$AdminGetUserByIdResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  JsonObject? _data;
  JsonObject? get data => _$this._data;
  set data(JsonObject? data) => _$this._data = data;

  UserBuilder? _user;
  UserBuilder get user => _$this._user ??= UserBuilder();
  set user(UserBuilder? user) => _$this._user = user;

  AdminGetUserByIdResponseBuilder() {
    AdminGetUserByIdResponse._defaults(this);
  }

  AdminGetUserByIdResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data;
      _user = $v.user?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminGetUserByIdResponse other) {
    _$v = other as _$AdminGetUserByIdResponse;
  }

  @override
  void update(void Function(AdminGetUserByIdResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminGetUserByIdResponse build() => _build();

  _$AdminGetUserByIdResponse _build() {
    _$AdminGetUserByIdResponse _$result;
    try {
      _$result = _$v ??
          _$AdminGetUserByIdResponse._(
            success: success,
            code: code,
            message: message,
            data: data,
            user: _user?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'user';
        _user?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminGetUserByIdResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
