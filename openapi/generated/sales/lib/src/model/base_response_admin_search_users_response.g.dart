// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_search_users_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminSearchUsersResponse
    extends BaseResponseAdminSearchUsersResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminSearchUsersResponse? data;

  factory _$BaseResponseAdminSearchUsersResponse(
          [void Function(BaseResponseAdminSearchUsersResponseBuilder)?
              updates]) =>
      (BaseResponseAdminSearchUsersResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminSearchUsersResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminSearchUsersResponse rebuild(
          void Function(BaseResponseAdminSearchUsersResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminSearchUsersResponseBuilder toBuilder() =>
      BaseResponseAdminSearchUsersResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminSearchUsersResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminSearchUsersResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminSearchUsersResponseBuilder
    implements
        Builder<BaseResponseAdminSearchUsersResponse,
            BaseResponseAdminSearchUsersResponseBuilder> {
  _$BaseResponseAdminSearchUsersResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminSearchUsersResponseBuilder? _data;
  AdminSearchUsersResponseBuilder get data =>
      _$this._data ??= AdminSearchUsersResponseBuilder();
  set data(AdminSearchUsersResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminSearchUsersResponseBuilder() {
    BaseResponseAdminSearchUsersResponse._defaults(this);
  }

  BaseResponseAdminSearchUsersResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminSearchUsersResponse other) {
    _$v = other as _$BaseResponseAdminSearchUsersResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminSearchUsersResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminSearchUsersResponse build() => _build();

  _$BaseResponseAdminSearchUsersResponse _build() {
    _$BaseResponseAdminSearchUsersResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminSearchUsersResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAdminSearchUsersResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
