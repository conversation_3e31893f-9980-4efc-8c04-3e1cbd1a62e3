// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_register_collaborator_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

const AppRegisterCollaboratorRequestIdCardTypeEnum
    _$appRegisterCollaboratorRequestIdCardTypeEnum_CHIP_ID =
    const AppRegisterCollaboratorRequestIdCardTypeEnum._('CHIP_ID');
const AppRegisterCollaboratorRequestIdCardTypeEnum
    _$appRegisterCollaboratorRequestIdCardTypeEnum_PASSPORT =
    const AppRegisterCollaboratorRequestIdCardTypeEnum._('PASSPORT');

AppRegisterCollaboratorRequestIdCardTypeEnum
    _$appRegisterCollaboratorRequestIdCardTypeEnumValueOf(String name) {
  switch (name) {
    case 'CHIP_ID':
      return _$appRegisterCollaboratorRequestIdCardTypeEnum_CHIP_ID;
    case 'PASSPORT':
      return _$appRegisterCollaboratorRequestIdCardTypeEnum_PASSPORT;
    default:
      throw ArgumentError(name);
  }
}

final BuiltSet<AppRegisterCollaboratorRequestIdCardTypeEnum>
    _$appRegisterCollaboratorRequestIdCardTypeEnumValues = BuiltSet<
        AppRegisterCollaboratorRequestIdCardTypeEnum>(const <AppRegisterCollaboratorRequestIdCardTypeEnum>[
  _$appRegisterCollaboratorRequestIdCardTypeEnum_CHIP_ID,
  _$appRegisterCollaboratorRequestIdCardTypeEnum_PASSPORT,
]);

Serializer<AppRegisterCollaboratorRequestIdCardTypeEnum>
    _$appRegisterCollaboratorRequestIdCardTypeEnumSerializer =
    _$AppRegisterCollaboratorRequestIdCardTypeEnumSerializer();

class _$AppRegisterCollaboratorRequestIdCardTypeEnumSerializer
    implements
        PrimitiveSerializer<AppRegisterCollaboratorRequestIdCardTypeEnum> {
  static const Map<String, Object> _toWire = const <String, Object>{
    'CHIP_ID': 'CHIP_ID',
    'PASSPORT': 'PASSPORT',
  };
  static const Map<Object, String> _fromWire = const <Object, String>{
    'CHIP_ID': 'CHIP_ID',
    'PASSPORT': 'PASSPORT',
  };

  @override
  final Iterable<Type> types = const <Type>[
    AppRegisterCollaboratorRequestIdCardTypeEnum
  ];
  @override
  final String wireName = 'AppRegisterCollaboratorRequestIdCardTypeEnum';

  @override
  Object serialize(Serializers serializers,
          AppRegisterCollaboratorRequestIdCardTypeEnum object,
          {FullType specifiedType = FullType.unspecified}) =>
      _toWire[object.name] ?? object.name;

  @override
  AppRegisterCollaboratorRequestIdCardTypeEnum deserialize(
          Serializers serializers, Object serialized,
          {FullType specifiedType = FullType.unspecified}) =>
      AppRegisterCollaboratorRequestIdCardTypeEnum.valueOf(
          _fromWire[serialized] ?? (serialized is String ? serialized : ''));
}

class _$AppRegisterCollaboratorRequest extends AppRegisterCollaboratorRequest {
  @override
  final String fullName;
  @override
  final AppRegisterCollaboratorRequestIdCardTypeEnum idCardType;
  @override
  final String idCardNo;
  @override
  final String phoneNumber;
  @override
  final String branchId;
  @override
  final String frontCardUrl;
  @override
  final String backCardUrl;
  @override
  final String? permanentAddress;
  @override
  final String? referrerCode;
  @override
  final String? email;

  factory _$AppRegisterCollaboratorRequest(
          [void Function(AppRegisterCollaboratorRequestBuilder)? updates]) =>
      (AppRegisterCollaboratorRequestBuilder()..update(updates))._build();

  _$AppRegisterCollaboratorRequest._(
      {required this.fullName,
      required this.idCardType,
      required this.idCardNo,
      required this.phoneNumber,
      required this.branchId,
      required this.frontCardUrl,
      required this.backCardUrl,
      this.permanentAddress,
      this.referrerCode,
      this.email})
      : super._();
  @override
  AppRegisterCollaboratorRequest rebuild(
          void Function(AppRegisterCollaboratorRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppRegisterCollaboratorRequestBuilder toBuilder() =>
      AppRegisterCollaboratorRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppRegisterCollaboratorRequest &&
        fullName == other.fullName &&
        idCardType == other.idCardType &&
        idCardNo == other.idCardNo &&
        phoneNumber == other.phoneNumber &&
        branchId == other.branchId &&
        frontCardUrl == other.frontCardUrl &&
        backCardUrl == other.backCardUrl &&
        permanentAddress == other.permanentAddress &&
        referrerCode == other.referrerCode &&
        email == other.email;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, idCardType.hashCode);
    _$hash = $jc(_$hash, idCardNo.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jc(_$hash, branchId.hashCode);
    _$hash = $jc(_$hash, frontCardUrl.hashCode);
    _$hash = $jc(_$hash, backCardUrl.hashCode);
    _$hash = $jc(_$hash, permanentAddress.hashCode);
    _$hash = $jc(_$hash, referrerCode.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppRegisterCollaboratorRequest')
          ..add('fullName', fullName)
          ..add('idCardType', idCardType)
          ..add('idCardNo', idCardNo)
          ..add('phoneNumber', phoneNumber)
          ..add('branchId', branchId)
          ..add('frontCardUrl', frontCardUrl)
          ..add('backCardUrl', backCardUrl)
          ..add('permanentAddress', permanentAddress)
          ..add('referrerCode', referrerCode)
          ..add('email', email))
        .toString();
  }
}

class AppRegisterCollaboratorRequestBuilder
    implements
        Builder<AppRegisterCollaboratorRequest,
            AppRegisterCollaboratorRequestBuilder> {
  _$AppRegisterCollaboratorRequest? _$v;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  AppRegisterCollaboratorRequestIdCardTypeEnum? _idCardType;
  AppRegisterCollaboratorRequestIdCardTypeEnum? get idCardType =>
      _$this._idCardType;
  set idCardType(AppRegisterCollaboratorRequestIdCardTypeEnum? idCardType) =>
      _$this._idCardType = idCardType;

  String? _idCardNo;
  String? get idCardNo => _$this._idCardNo;
  set idCardNo(String? idCardNo) => _$this._idCardNo = idCardNo;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  String? _branchId;
  String? get branchId => _$this._branchId;
  set branchId(String? branchId) => _$this._branchId = branchId;

  String? _frontCardUrl;
  String? get frontCardUrl => _$this._frontCardUrl;
  set frontCardUrl(String? frontCardUrl) => _$this._frontCardUrl = frontCardUrl;

  String? _backCardUrl;
  String? get backCardUrl => _$this._backCardUrl;
  set backCardUrl(String? backCardUrl) => _$this._backCardUrl = backCardUrl;

  String? _permanentAddress;
  String? get permanentAddress => _$this._permanentAddress;
  set permanentAddress(String? permanentAddress) =>
      _$this._permanentAddress = permanentAddress;

  String? _referrerCode;
  String? get referrerCode => _$this._referrerCode;
  set referrerCode(String? referrerCode) => _$this._referrerCode = referrerCode;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  AppRegisterCollaboratorRequestBuilder() {
    AppRegisterCollaboratorRequest._defaults(this);
  }

  AppRegisterCollaboratorRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _fullName = $v.fullName;
      _idCardType = $v.idCardType;
      _idCardNo = $v.idCardNo;
      _phoneNumber = $v.phoneNumber;
      _branchId = $v.branchId;
      _frontCardUrl = $v.frontCardUrl;
      _backCardUrl = $v.backCardUrl;
      _permanentAddress = $v.permanentAddress;
      _referrerCode = $v.referrerCode;
      _email = $v.email;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppRegisterCollaboratorRequest other) {
    _$v = other as _$AppRegisterCollaboratorRequest;
  }

  @override
  void update(void Function(AppRegisterCollaboratorRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppRegisterCollaboratorRequest build() => _build();

  _$AppRegisterCollaboratorRequest _build() {
    final _$result = _$v ??
        _$AppRegisterCollaboratorRequest._(
          fullName: BuiltValueNullFieldError.checkNotNull(
              fullName, r'AppRegisterCollaboratorRequest', 'fullName'),
          idCardType: BuiltValueNullFieldError.checkNotNull(
              idCardType, r'AppRegisterCollaboratorRequest', 'idCardType'),
          idCardNo: BuiltValueNullFieldError.checkNotNull(
              idCardNo, r'AppRegisterCollaboratorRequest', 'idCardNo'),
          phoneNumber: BuiltValueNullFieldError.checkNotNull(
              phoneNumber, r'AppRegisterCollaboratorRequest', 'phoneNumber'),
          branchId: BuiltValueNullFieldError.checkNotNull(
              branchId, r'AppRegisterCollaboratorRequest', 'branchId'),
          frontCardUrl: BuiltValueNullFieldError.checkNotNull(
              frontCardUrl, r'AppRegisterCollaboratorRequest', 'frontCardUrl'),
          backCardUrl: BuiltValueNullFieldError.checkNotNull(
              backCardUrl, r'AppRegisterCollaboratorRequest', 'backCardUrl'),
          permanentAddress: permanentAddress,
          referrerCode: referrerCode,
          email: email,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
