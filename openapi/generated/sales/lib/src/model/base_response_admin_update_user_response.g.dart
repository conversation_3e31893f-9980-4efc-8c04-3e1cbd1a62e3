// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_update_user_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminUpdateUserResponse
    extends BaseResponseAdminUpdateUserResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminUpdateUserResponse? data;

  factory _$BaseResponseAdminUpdateUserResponse(
          [void Function(BaseResponseAdminUpdateUserResponseBuilder)?
              updates]) =>
      (BaseResponseAdminUpdateUserResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminUpdateUserResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminUpdateUserResponse rebuild(
          void Function(BaseResponseAdminUpdateUserResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminUpdateUserResponseBuilder toBuilder() =>
      BaseResponseAdminUpdateUserResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminUpdateUserResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminUpdateUserResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminUpdateUserResponseBuilder
    implements
        Builder<BaseResponseAdminUpdateUserResponse,
            BaseResponseAdminUpdateUserResponseBuilder> {
  _$BaseResponseAdminUpdateUserResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminUpdateUserResponseBuilder? _data;
  AdminUpdateUserResponseBuilder get data =>
      _$this._data ??= AdminUpdateUserResponseBuilder();
  set data(AdminUpdateUserResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminUpdateUserResponseBuilder() {
    BaseResponseAdminUpdateUserResponse._defaults(this);
  }

  BaseResponseAdminUpdateUserResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminUpdateUserResponse other) {
    _$v = other as _$BaseResponseAdminUpdateUserResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminUpdateUserResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminUpdateUserResponse build() => _build();

  _$BaseResponseAdminUpdateUserResponse _build() {
    _$BaseResponseAdminUpdateUserResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminUpdateUserResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(r'BaseResponseAdminUpdateUserResponse',
            _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
