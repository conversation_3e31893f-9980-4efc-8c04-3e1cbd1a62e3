//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_import_users_response.g.dart';

/// AdminImportUsersResponse
///
/// Properties:
/// * [totalRequested]
/// * [totalImported]
@BuiltValue()
abstract class AdminImportUsersResponse
    implements
        Built<AdminImportUsersResponse, AdminImportUsersResponseBuilder> {
  @BuiltValueField(wireName: r'totalRequested')
  int? get totalRequested;

  @BuiltValueField(wireName: r'totalImported')
  int? get totalImported;

  AdminImportUsersResponse._();

  factory AdminImportUsersResponse(
          [void updates(AdminImportUsersResponseBuilder b)]) =
      _$AdminImportUsersResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminImportUsersResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminImportUsersResponse> get serializer =>
      _$AdminImportUsersResponseSerializer();
}

class _$AdminImportUsersResponseSerializer
    implements PrimitiveSerializer<AdminImportUsersResponse> {
  @override
  final Iterable<Type> types = const [
    AdminImportUsersResponse,
    _$AdminImportUsersResponse
  ];

  @override
  final String wireName = r'AdminImportUsersResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminImportUsersResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.totalRequested != null) {
      yield r'totalRequested';
      yield serializers.serialize(
        object.totalRequested,
        specifiedType: const FullType(int),
      );
    }
    if (object.totalImported != null) {
      yield r'totalImported';
      yield serializers.serialize(
        object.totalImported,
        specifiedType: const FullType(int),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminImportUsersResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminImportUsersResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'totalRequested':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalRequested = valueDes;
          break;
        case r'totalImported':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.totalImported = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminImportUsersResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminImportUsersResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
