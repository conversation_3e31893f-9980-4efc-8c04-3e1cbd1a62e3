// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_ward_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppWardDto extends AppWardDto {
  @override
  final String? id;
  @override
  final String? code;
  @override
  final String? name;
  @override
  final String? provinceId;

  factory _$AppWardDto([void Function(AppWardDtoBuilder)? updates]) =>
      (AppWardDtoBuilder()..update(updates))._build();

  _$AppWardDto._({this.id, this.code, this.name, this.provinceId}) : super._();
  @override
  AppWardDto rebuild(void Function(AppWardDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppWardDtoBuilder toBuilder() => AppWardDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppWardDto &&
        id == other.id &&
        code == other.code &&
        name == other.name &&
        provinceId == other.provinceId;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, provinceId.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppWardDto')
          ..add('id', id)
          ..add('code', code)
          ..add('name', name)
          ..add('provinceId', provinceId))
        .toString();
  }
}

class AppWardDtoBuilder implements Builder<AppWardDto, AppWardDtoBuilder> {
  _$AppWardDto? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _provinceId;
  String? get provinceId => _$this._provinceId;
  set provinceId(String? provinceId) => _$this._provinceId = provinceId;

  AppWardDtoBuilder() {
    AppWardDto._defaults(this);
  }

  AppWardDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _code = $v.code;
      _name = $v.name;
      _provinceId = $v.provinceId;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppWardDto other) {
    _$v = other as _$AppWardDto;
  }

  @override
  void update(void Function(AppWardDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppWardDto build() => _build();

  _$AppWardDto _build() {
    final _$result = _$v ??
        _$AppWardDto._(
          id: id,
          code: code,
          name: name,
          provinceId: provinceId,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
