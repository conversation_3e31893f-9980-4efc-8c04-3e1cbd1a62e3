// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_get_list_ward_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppGetListWardResponse extends AppGetListWardResponse {
  @override
  final BuiltList<AppWardDto>? wards;

  factory _$AppGetListWardResponse(
          [void Function(AppGetListWardResponseBuilder)? updates]) =>
      (AppGetListWardResponseBuilder()..update(updates))._build();

  _$AppGetListWardResponse._({this.wards}) : super._();
  @override
  AppGetListWardResponse rebuild(
          void Function(AppGetListWardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppGetListWardResponseBuilder toBuilder() =>
      AppGetListWardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppGetListWardResponse && wards == other.wards;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, wards.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppGetListWardResponse')
          ..add('wards', wards))
        .toString();
  }
}

class AppGetListWardResponseBuilder
    implements Builder<AppGetListWardResponse, AppGetListWardResponseBuilder> {
  _$AppGetListWardResponse? _$v;

  ListBuilder<AppWardDto>? _wards;
  ListBuilder<AppWardDto> get wards =>
      _$this._wards ??= ListBuilder<AppWardDto>();
  set wards(ListBuilder<AppWardDto>? wards) => _$this._wards = wards;

  AppGetListWardResponseBuilder() {
    AppGetListWardResponse._defaults(this);
  }

  AppGetListWardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _wards = $v.wards?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppGetListWardResponse other) {
    _$v = other as _$AppGetListWardResponse;
  }

  @override
  void update(void Function(AppGetListWardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppGetListWardResponse build() => _build();

  _$AppGetListWardResponse _build() {
    _$AppGetListWardResponse _$result;
    try {
      _$result = _$v ??
          _$AppGetListWardResponse._(
            wards: _wards?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'wards';
        _wards?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AppGetListWardResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
