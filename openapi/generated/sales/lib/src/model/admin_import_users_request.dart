//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:sales_app_api/src/model/admin_create_user_request.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'admin_import_users_request.g.dart';

/// AdminImportUsersRequest
///
/// Properties:
/// * [users]
@BuiltValue()
abstract class AdminImportUsersRequest
    implements Built<AdminImportUsersRequest, AdminImportUsersRequestBuilder> {
  @BuiltValueField(wireName: r'users')
  BuiltList<AdminCreateUserRequest>? get users;

  AdminImportUsersRequest._();

  factory AdminImportUsersRequest(
          [void updates(AdminImportUsersRequestBuilder b)]) =
      _$AdminImportUsersRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AdminImportUsersRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AdminImportUsersRequest> get serializer =>
      _$AdminImportUsersRequestSerializer();
}

class _$AdminImportUsersRequestSerializer
    implements PrimitiveSerializer<AdminImportUsersRequest> {
  @override
  final Iterable<Type> types = const [
    AdminImportUsersRequest,
    _$AdminImportUsersRequest
  ];

  @override
  final String wireName = r'AdminImportUsersRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AdminImportUsersRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.users != null) {
      yield r'users';
      yield serializers.serialize(
        object.users,
        specifiedType:
            const FullType(BuiltList, [FullType(AdminCreateUserRequest)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AdminImportUsersRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AdminImportUsersRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'users':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType(BuiltList, [FullType(AdminCreateUserRequest)]),
          ) as BuiltList<AdminCreateUserRequest>;
          result.users.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AdminImportUsersRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AdminImportUsersRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
