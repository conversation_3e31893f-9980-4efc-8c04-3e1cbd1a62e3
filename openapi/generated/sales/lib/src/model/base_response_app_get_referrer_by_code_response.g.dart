// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_get_referrer_by_code_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppGetReferrerByCodeResponse
    extends BaseResponseAppGetReferrerByCodeResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppGetReferrerByCodeResponse? data;

  factory _$BaseResponseAppGetReferrerByCodeResponse(
          [void Function(BaseResponseAppGetReferrerByCodeResponseBuilder)?
              updates]) =>
      (BaseResponseAppGetReferrerByCodeResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseAppGetReferrerByCodeResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppGetReferrerByCodeResponse rebuild(
          void Function(BaseResponseAppGetReferrerByCodeResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppGetReferrerByCodeResponseBuilder toBuilder() =>
      BaseResponseAppGetReferrerByCodeResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppGetReferrerByCodeResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAppGetReferrerByCodeResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppGetReferrerByCodeResponseBuilder
    implements
        Builder<BaseResponseAppGetReferrerByCodeResponse,
            BaseResponseAppGetReferrerByCodeResponseBuilder> {
  _$BaseResponseAppGetReferrerByCodeResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppGetReferrerByCodeResponseBuilder? _data;
  AppGetReferrerByCodeResponseBuilder get data =>
      _$this._data ??= AppGetReferrerByCodeResponseBuilder();
  set data(AppGetReferrerByCodeResponseBuilder? data) => _$this._data = data;

  BaseResponseAppGetReferrerByCodeResponseBuilder() {
    BaseResponseAppGetReferrerByCodeResponse._defaults(this);
  }

  BaseResponseAppGetReferrerByCodeResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppGetReferrerByCodeResponse other) {
    _$v = other as _$BaseResponseAppGetReferrerByCodeResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppGetReferrerByCodeResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppGetReferrerByCodeResponse build() => _build();

  _$BaseResponseAppGetReferrerByCodeResponse _build() {
    _$BaseResponseAppGetReferrerByCodeResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppGetReferrerByCodeResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppGetReferrerByCodeResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
