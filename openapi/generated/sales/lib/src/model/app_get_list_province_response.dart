//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:sales_app_api/src/model/app_province_dto.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_get_list_province_response.g.dart';

/// AppGetListProvinceResponse
///
/// Properties:
/// * [provinces]
@BuiltValue()
abstract class AppGetListProvinceResponse
    implements
        Built<AppGetListProvinceResponse, AppGetListProvinceResponseBuilder> {
  @BuiltValueField(wireName: r'provinces')
  BuiltList<AppProvinceDto>? get provinces;

  AppGetListProvinceResponse._();

  factory AppGetListProvinceResponse(
          [void updates(AppGetListProvinceResponseBuilder b)]) =
      _$AppGetListProvinceResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppGetListProvinceResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppGetListProvinceResponse> get serializer =>
      _$AppGetListProvinceResponseSerializer();
}

class _$AppGetListProvinceResponseSerializer
    implements PrimitiveSerializer<AppGetListProvinceResponse> {
  @override
  final Iterable<Type> types = const [
    AppGetListProvinceResponse,
    _$AppGetListProvinceResponse
  ];

  @override
  final String wireName = r'AppGetListProvinceResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppGetListProvinceResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.provinces != null) {
      yield r'provinces';
      yield serializers.serialize(
        object.provinces,
        specifiedType: const FullType(BuiltList, [FullType(AppProvinceDto)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppGetListProvinceResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppGetListProvinceResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'provinces':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType(BuiltList, [FullType(AppProvinceDto)]),
          ) as BuiltList<AppProvinceDto>;
          result.provinces.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppGetListProvinceResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppGetListProvinceResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
