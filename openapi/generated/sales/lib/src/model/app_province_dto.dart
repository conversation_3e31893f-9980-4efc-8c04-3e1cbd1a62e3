//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_province_dto.g.dart';

/// AppProvinceDto
///
/// Properties:
/// * [id] - Id của tỉnh/thành phố
/// * [gsoCode] - Mã GSO
/// * [name] - Tên tỉnh thành phố
@BuiltValue()
abstract class AppProvinceDto
    implements Built<AppProvinceDto, AppProvinceDtoBuilder> {
  /// Id của tỉnh/thành phố
  @BuiltValueField(wireName: r'id')
  String? get id;

  /// Mã GSO
  @BuiltValueField(wireName: r'gsoCode')
  String? get gsoCode;

  /// Tên tỉnh thành phố
  @BuiltValueField(wireName: r'name')
  String? get name;

  AppProvinceDto._();

  factory AppProvinceDto([void updates(AppProvinceDtoBuilder b)]) =
      _$AppProvinceDto;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppProvinceDtoBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppProvinceDto> get serializer =>
      _$AppProvinceDtoSerializer();
}

class _$AppProvinceDtoSerializer
    implements PrimitiveSerializer<AppProvinceDto> {
  @override
  final Iterable<Type> types = const [AppProvinceDto, _$AppProvinceDto];

  @override
  final String wireName = r'AppProvinceDto';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppProvinceDto object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType(String),
      );
    }
    if (object.gsoCode != null) {
      yield r'gsoCode';
      yield serializers.serialize(
        object.gsoCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppProvinceDto object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppProvinceDtoBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'gsoCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.gsoCode = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppProvinceDto deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppProvinceDtoBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
