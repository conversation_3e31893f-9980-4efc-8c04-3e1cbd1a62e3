// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_delete_menu_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminDeleteMenuResponse extends AdminDeleteMenuResponse {
  @override
  final String? id;

  factory _$AdminDeleteMenuResponse(
          [void Function(AdminDeleteMenuResponseBuilder)? updates]) =>
      (AdminDeleteMenuResponseBuilder()..update(updates))._build();

  _$AdminDeleteMenuResponse._({this.id}) : super._();
  @override
  AdminDeleteMenuResponse rebuild(
          void Function(AdminDeleteMenuResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminDeleteMenuResponseBuilder toBuilder() =>
      AdminDeleteMenuResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminDeleteMenuResponse && id == other.id;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminDeleteMenuResponse')
          ..add('id', id))
        .toString();
  }
}

class AdminDeleteMenuResponseBuilder
    implements
        Builder<AdminDeleteMenuResponse, AdminDeleteMenuResponseBuilder> {
  _$AdminDeleteMenuResponse? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  AdminDeleteMenuResponseBuilder() {
    AdminDeleteMenuResponse._defaults(this);
  }

  AdminDeleteMenuResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminDeleteMenuResponse other) {
    _$v = other as _$AdminDeleteMenuResponse;
  }

  @override
  void update(void Function(AdminDeleteMenuResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminDeleteMenuResponse build() => _build();

  _$AdminDeleteMenuResponse _build() {
    final _$result = _$v ??
        _$AdminDeleteMenuResponse._(
          id: id,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
