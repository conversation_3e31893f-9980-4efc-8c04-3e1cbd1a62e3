// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_get_user_by_id_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminGetUserByIdResponse
    extends BaseResponseAdminGetUserByIdResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminGetUserByIdResponse? data;

  factory _$BaseResponseAdminGetUserByIdResponse(
          [void Function(BaseResponseAdminGetUserByIdResponseBuilder)?
              updates]) =>
      (BaseResponseAdminGetUserByIdResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminGetUserByIdResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminGetUserByIdResponse rebuild(
          void Function(BaseResponseAdminGetUserByIdResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminGetUserByIdResponseBuilder toBuilder() =>
      BaseResponseAdminGetUserByIdResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminGetUserByIdResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminGetUserByIdResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminGetUserByIdResponseBuilder
    implements
        Builder<BaseResponseAdminGetUserByIdResponse,
            BaseResponseAdminGetUserByIdResponseBuilder> {
  _$BaseResponseAdminGetUserByIdResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminGetUserByIdResponseBuilder? _data;
  AdminGetUserByIdResponseBuilder get data =>
      _$this._data ??= AdminGetUserByIdResponseBuilder();
  set data(AdminGetUserByIdResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminGetUserByIdResponseBuilder() {
    BaseResponseAdminGetUserByIdResponse._defaults(this);
  }

  BaseResponseAdminGetUserByIdResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminGetUserByIdResponse other) {
    _$v = other as _$BaseResponseAdminGetUserByIdResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminGetUserByIdResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminGetUserByIdResponse build() => _build();

  _$BaseResponseAdminGetUserByIdResponse _build() {
    _$BaseResponseAdminGetUserByIdResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminGetUserByIdResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAdminGetUserByIdResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
