// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_get_list_payment_account_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppGetListPaymentAccountResponse
    extends BaseResponseAppGetListPaymentAccountResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppGetListPaymentAccountResponse? data;

  factory _$BaseResponseAppGetListPaymentAccountResponse(
          [void Function(BaseResponseAppGetListPaymentAccountResponseBuilder)?
              updates]) =>
      (BaseResponseAppGetListPaymentAccountResponseBuilder()..update(updates))
          ._build();

  _$BaseResponseAppGetListPaymentAccountResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppGetListPaymentAccountResponse rebuild(
          void Function(BaseResponseAppGetListPaymentAccountResponseBuilder)
              updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppGetListPaymentAccountResponseBuilder toBuilder() =>
      BaseResponseAppGetListPaymentAccountResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppGetListPaymentAccountResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(
            r'BaseResponseAppGetListPaymentAccountResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppGetListPaymentAccountResponseBuilder
    implements
        Builder<BaseResponseAppGetListPaymentAccountResponse,
            BaseResponseAppGetListPaymentAccountResponseBuilder> {
  _$BaseResponseAppGetListPaymentAccountResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppGetListPaymentAccountResponseBuilder? _data;
  AppGetListPaymentAccountResponseBuilder get data =>
      _$this._data ??= AppGetListPaymentAccountResponseBuilder();
  set data(AppGetListPaymentAccountResponseBuilder? data) =>
      _$this._data = data;

  BaseResponseAppGetListPaymentAccountResponseBuilder() {
    BaseResponseAppGetListPaymentAccountResponse._defaults(this);
  }

  BaseResponseAppGetListPaymentAccountResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppGetListPaymentAccountResponse other) {
    _$v = other as _$BaseResponseAppGetListPaymentAccountResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppGetListPaymentAccountResponseBuilder)?
          updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppGetListPaymentAccountResponse build() => _build();

  _$BaseResponseAppGetListPaymentAccountResponse _build() {
    _$BaseResponseAppGetListPaymentAccountResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppGetListPaymentAccountResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppGetListPaymentAccountResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
