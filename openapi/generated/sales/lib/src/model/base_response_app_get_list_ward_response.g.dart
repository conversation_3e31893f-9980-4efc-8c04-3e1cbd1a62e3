// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_get_list_ward_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppGetListWardResponse
    extends BaseResponseAppGetListWardResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppGetListWardResponse? data;

  factory _$BaseResponseAppGetListWardResponse(
          [void Function(BaseResponseAppGetListWardResponseBuilder)?
              updates]) =>
      (BaseResponseAppGetListWardResponseBuilder()..update(updates))._build();

  _$BaseResponseAppGetListWardResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppGetListWardResponse rebuild(
          void Function(BaseResponseAppGetListWardResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppGetListWardResponseBuilder toBuilder() =>
      BaseResponseAppGetListWardResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppGetListWardResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAppGetListWardResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppGetListWardResponseBuilder
    implements
        Builder<BaseResponseAppGetListWardResponse,
            BaseResponseAppGetListWardResponseBuilder> {
  _$BaseResponseAppGetListWardResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppGetListWardResponseBuilder? _data;
  AppGetListWardResponseBuilder get data =>
      _$this._data ??= AppGetListWardResponseBuilder();
  set data(AppGetListWardResponseBuilder? data) => _$this._data = data;

  BaseResponseAppGetListWardResponseBuilder() {
    BaseResponseAppGetListWardResponse._defaults(this);
  }

  BaseResponseAppGetListWardResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppGetListWardResponse other) {
    _$v = other as _$BaseResponseAppGetListWardResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppGetListWardResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppGetListWardResponse build() => _build();

  _$BaseResponseAppGetListWardResponse _build() {
    _$BaseResponseAppGetListWardResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppGetListWardResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppGetListWardResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
