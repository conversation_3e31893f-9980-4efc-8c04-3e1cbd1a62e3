// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_login_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppLoginResponse extends AppLoginResponse {
  @override
  final String? accessToken;
  @override
  final int? expiresIn;
  @override
  final String? refreshToken;
  @override
  final int? refreshExpiresIn;
  @override
  final String? tokenType;
  @override
  final String? code;
  @override
  final String? cifNo;
  @override
  final String? fullName;
  @override
  final String? email;
  @override
  final String? phoneNumber;

  factory _$AppLoginResponse(
          [void Function(AppLoginResponseBuilder)? updates]) =>
      (AppLoginResponseBuilder()..update(updates))._build();

  _$AppLoginResponse._(
      {this.accessToken,
      this.expiresIn,
      this.refreshToken,
      this.refreshExpiresIn,
      this.tokenType,
      this.code,
      this.cifNo,
      this.fullName,
      this.email,
      this.phoneNumber})
      : super._();
  @override
  AppLoginResponse rebuild(void Function(AppLoginResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppLoginResponseBuilder toBuilder() =>
      AppLoginResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppLoginResponse &&
        accessToken == other.accessToken &&
        expiresIn == other.expiresIn &&
        refreshToken == other.refreshToken &&
        refreshExpiresIn == other.refreshExpiresIn &&
        tokenType == other.tokenType &&
        code == other.code &&
        cifNo == other.cifNo &&
        fullName == other.fullName &&
        email == other.email &&
        phoneNumber == other.phoneNumber;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, accessToken.hashCode);
    _$hash = $jc(_$hash, expiresIn.hashCode);
    _$hash = $jc(_$hash, refreshToken.hashCode);
    _$hash = $jc(_$hash, refreshExpiresIn.hashCode);
    _$hash = $jc(_$hash, tokenType.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, cifNo.hashCode);
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, phoneNumber.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppLoginResponse')
          ..add('accessToken', accessToken)
          ..add('expiresIn', expiresIn)
          ..add('refreshToken', refreshToken)
          ..add('refreshExpiresIn', refreshExpiresIn)
          ..add('tokenType', tokenType)
          ..add('code', code)
          ..add('cifNo', cifNo)
          ..add('fullName', fullName)
          ..add('email', email)
          ..add('phoneNumber', phoneNumber))
        .toString();
  }
}

class AppLoginResponseBuilder
    implements Builder<AppLoginResponse, AppLoginResponseBuilder> {
  _$AppLoginResponse? _$v;

  String? _accessToken;
  String? get accessToken => _$this._accessToken;
  set accessToken(String? accessToken) => _$this._accessToken = accessToken;

  int? _expiresIn;
  int? get expiresIn => _$this._expiresIn;
  set expiresIn(int? expiresIn) => _$this._expiresIn = expiresIn;

  String? _refreshToken;
  String? get refreshToken => _$this._refreshToken;
  set refreshToken(String? refreshToken) => _$this._refreshToken = refreshToken;

  int? _refreshExpiresIn;
  int? get refreshExpiresIn => _$this._refreshExpiresIn;
  set refreshExpiresIn(int? refreshExpiresIn) =>
      _$this._refreshExpiresIn = refreshExpiresIn;

  String? _tokenType;
  String? get tokenType => _$this._tokenType;
  set tokenType(String? tokenType) => _$this._tokenType = tokenType;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _cifNo;
  String? get cifNo => _$this._cifNo;
  set cifNo(String? cifNo) => _$this._cifNo = cifNo;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _phoneNumber;
  String? get phoneNumber => _$this._phoneNumber;
  set phoneNumber(String? phoneNumber) => _$this._phoneNumber = phoneNumber;

  AppLoginResponseBuilder() {
    AppLoginResponse._defaults(this);
  }

  AppLoginResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _accessToken = $v.accessToken;
      _expiresIn = $v.expiresIn;
      _refreshToken = $v.refreshToken;
      _refreshExpiresIn = $v.refreshExpiresIn;
      _tokenType = $v.tokenType;
      _code = $v.code;
      _cifNo = $v.cifNo;
      _fullName = $v.fullName;
      _email = $v.email;
      _phoneNumber = $v.phoneNumber;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppLoginResponse other) {
    _$v = other as _$AppLoginResponse;
  }

  @override
  void update(void Function(AppLoginResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppLoginResponse build() => _build();

  _$AppLoginResponse _build() {
    final _$result = _$v ??
        _$AppLoginResponse._(
          accessToken: accessToken,
          expiresIn: expiresIn,
          refreshToken: refreshToken,
          refreshExpiresIn: refreshExpiresIn,
          tokenType: tokenType,
          code: code,
          cifNo: cifNo,
          fullName: fullName,
          email: email,
          phoneNumber: phoneNumber,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
