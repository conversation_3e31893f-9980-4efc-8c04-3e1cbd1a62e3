// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_import_users_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminImportUsersResponse extends AdminImportUsersResponse {
  @override
  final int? totalRequested;
  @override
  final int? totalImported;

  factory _$AdminImportUsersResponse(
          [void Function(AdminImportUsersResponseBuilder)? updates]) =>
      (AdminImportUsersResponseBuilder()..update(updates))._build();

  _$AdminImportUsersResponse._({this.totalRequested, this.totalImported})
      : super._();
  @override
  AdminImportUsersResponse rebuild(
          void Function(AdminImportUsersResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminImportUsersResponseBuilder toBuilder() =>
      AdminImportUsersResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminImportUsersResponse &&
        totalRequested == other.totalRequested &&
        totalImported == other.totalImported;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, totalRequested.hashCode);
    _$hash = $jc(_$hash, totalImported.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminImportUsersResponse')
          ..add('totalRequested', totalRequested)
          ..add('totalImported', totalImported))
        .toString();
  }
}

class AdminImportUsersResponseBuilder
    implements
        Builder<AdminImportUsersResponse, AdminImportUsersResponseBuilder> {
  _$AdminImportUsersResponse? _$v;

  int? _totalRequested;
  int? get totalRequested => _$this._totalRequested;
  set totalRequested(int? totalRequested) =>
      _$this._totalRequested = totalRequested;

  int? _totalImported;
  int? get totalImported => _$this._totalImported;
  set totalImported(int? totalImported) =>
      _$this._totalImported = totalImported;

  AdminImportUsersResponseBuilder() {
    AdminImportUsersResponse._defaults(this);
  }

  AdminImportUsersResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _totalRequested = $v.totalRequested;
      _totalImported = $v.totalImported;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminImportUsersResponse other) {
    _$v = other as _$AdminImportUsersResponse;
  }

  @override
  void update(void Function(AdminImportUsersResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminImportUsersResponse build() => _build();

  _$AdminImportUsersResponse _build() {
    final _$result = _$v ??
        _$AdminImportUsersResponse._(
          totalRequested: totalRequested,
          totalImported: totalImported,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
