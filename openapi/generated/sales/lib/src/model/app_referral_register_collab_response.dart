//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_referral_register_collab_response.g.dart';

/// AppReferralRegisterCollabResponse
///
/// Properties:
/// * [fullName] - Họ tên
/// * [branchName] - Tên chi nhánh
/// * [positionName] - Tên chức danh
/// * [referrerCode] - Mã người giới thiệu
/// * [referrerName] - Tên người giới thiệu
@BuiltValue()
abstract class AppReferralRegisterCollabResponse
    implements
        Built<AppReferralRegisterCollabResponse,
            AppReferralRegisterCollabResponseBuilder> {
  /// Họ tên
  @BuiltValueField(wireName: r'fullName')
  String? get fullName;

  /// Tên chi nh<PERSON>
  @BuiltValueField(wireName: r'branchName')
  String? get branchName;

  /// Tên chức danh
  @BuiltValueField(wireName: r'positionName')
  String? get positionName;

  /// Mã người giới thiệu
  @BuiltValueField(wireName: r'referrerCode')
  String? get referrerCode;

  /// Tên người giới thiệu
  @BuiltValueField(wireName: r'referrerName')
  String? get referrerName;

  AppReferralRegisterCollabResponse._();

  factory AppReferralRegisterCollabResponse(
          [void updates(AppReferralRegisterCollabResponseBuilder b)]) =
      _$AppReferralRegisterCollabResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppReferralRegisterCollabResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppReferralRegisterCollabResponse> get serializer =>
      _$AppReferralRegisterCollabResponseSerializer();
}

class _$AppReferralRegisterCollabResponseSerializer
    implements PrimitiveSerializer<AppReferralRegisterCollabResponse> {
  @override
  final Iterable<Type> types = const [
    AppReferralRegisterCollabResponse,
    _$AppReferralRegisterCollabResponse
  ];

  @override
  final String wireName = r'AppReferralRegisterCollabResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppReferralRegisterCollabResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.fullName != null) {
      yield r'fullName';
      yield serializers.serialize(
        object.fullName,
        specifiedType: const FullType(String),
      );
    }
    if (object.branchName != null) {
      yield r'branchName';
      yield serializers.serialize(
        object.branchName,
        specifiedType: const FullType(String),
      );
    }
    if (object.positionName != null) {
      yield r'positionName';
      yield serializers.serialize(
        object.positionName,
        specifiedType: const FullType(String),
      );
    }
    if (object.referrerCode != null) {
      yield r'referrerCode';
      yield serializers.serialize(
        object.referrerCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.referrerName != null) {
      yield r'referrerName';
      yield serializers.serialize(
        object.referrerName,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppReferralRegisterCollabResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppReferralRegisterCollabResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'fullName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.fullName = valueDes;
          break;
        case r'branchName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.branchName = valueDes;
          break;
        case r'positionName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.positionName = valueDes;
          break;
        case r'referrerCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.referrerCode = valueDes;
          break;
        case r'referrerName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.referrerName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppReferralRegisterCollabResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppReferralRegisterCollabResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
