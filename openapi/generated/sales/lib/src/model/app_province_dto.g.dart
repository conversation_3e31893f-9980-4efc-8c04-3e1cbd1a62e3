// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_province_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppProvinceDto extends AppProvinceDto {
  @override
  final String? id;
  @override
  final String? gsoCode;
  @override
  final String? name;

  factory _$AppProvinceDto([void Function(AppProvinceDtoBuilder)? updates]) =>
      (AppProvinceDtoBuilder()..update(updates))._build();

  _$AppProvinceDto._({this.id, this.gsoCode, this.name}) : super._();
  @override
  AppProvinceDto rebuild(void Function(AppProvinceDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppProvinceDtoBuilder toBuilder() => AppProvinceDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppProvinceDto &&
        id == other.id &&
        gsoCode == other.gsoCode &&
        name == other.name;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, gsoCode.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppProvinceDto')
          ..add('id', id)
          ..add('gsoCode', gsoCode)
          ..add('name', name))
        .toString();
  }
}

class AppProvinceDtoBuilder
    implements Builder<AppProvinceDto, AppProvinceDtoBuilder> {
  _$AppProvinceDto? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _gsoCode;
  String? get gsoCode => _$this._gsoCode;
  set gsoCode(String? gsoCode) => _$this._gsoCode = gsoCode;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  AppProvinceDtoBuilder() {
    AppProvinceDto._defaults(this);
  }

  AppProvinceDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _gsoCode = $v.gsoCode;
      _name = $v.name;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppProvinceDto other) {
    _$v = other as _$AppProvinceDto;
  }

  @override
  void update(void Function(AppProvinceDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppProvinceDto build() => _build();

  _$AppProvinceDto _build() {
    final _$result = _$v ??
        _$AppProvinceDto._(
          id: id,
          gsoCode: gsoCode,
          name: name,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
