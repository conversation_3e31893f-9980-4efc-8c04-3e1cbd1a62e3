// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_get_list_branch_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppGetListBranchResponse extends AppGetListBranchResponse {
  @override
  final BuiltList<AppBranchDto>? branches;

  factory _$AppGetListBranchResponse(
          [void Function(AppGetListBranchResponseBuilder)? updates]) =>
      (AppGetListBranchResponseBuilder()..update(updates))._build();

  _$AppGetListBranchResponse._({this.branches}) : super._();
  @override
  AppGetListBranchResponse rebuild(
          void Function(AppGetListBranchResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppGetListBranchResponseBuilder toBuilder() =>
      AppGetListBranchResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppGetListBranchResponse && branches == other.branches;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, branches.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppGetListBranchResponse')
          ..add('branches', branches))
        .toString();
  }
}

class AppGetListBranchResponseBuilder
    implements
        Builder<AppGetListBranchResponse, AppGetListBranchResponseBuilder> {
  _$AppGetListBranchResponse? _$v;

  ListBuilder<AppBranchDto>? _branches;
  ListBuilder<AppBranchDto> get branches =>
      _$this._branches ??= ListBuilder<AppBranchDto>();
  set branches(ListBuilder<AppBranchDto>? branches) =>
      _$this._branches = branches;

  AppGetListBranchResponseBuilder() {
    AppGetListBranchResponse._defaults(this);
  }

  AppGetListBranchResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _branches = $v.branches?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppGetListBranchResponse other) {
    _$v = other as _$AppGetListBranchResponse;
  }

  @override
  void update(void Function(AppGetListBranchResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppGetListBranchResponse build() => _build();

  _$AppGetListBranchResponse _build() {
    _$AppGetListBranchResponse _$result;
    try {
      _$result = _$v ??
          _$AppGetListBranchResponse._(
            branches: _branches?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'branches';
        _branches?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AppGetListBranchResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
