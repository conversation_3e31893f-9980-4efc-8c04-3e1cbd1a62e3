//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_get_referrer_by_code_response.g.dart';

/// AppGetReferrerByCodeResponse
///
/// Properties:
/// * [referrerCode] - Mã người giới thiệu
/// * [referrerName] - Tên người giới thiệu
@BuiltValue()
abstract class AppGetReferrerByCodeResponse
    implements
        Built<AppGetReferrerByCodeResponse,
            AppGetReferrerByCodeResponseBuilder> {
  /// Mã người giới thiệu
  @BuiltValueField(wireName: r'referrerCode')
  String? get referrerCode;

  /// Tên người giới thiệu
  @BuiltValueField(wireName: r'referrerName')
  String? get referrerName;

  AppGetReferrerByCodeResponse._();

  factory AppGetReferrerByCodeResponse(
          [void updates(AppGetReferrerByCodeResponseBuilder b)]) =
      _$AppGetReferrerByCodeResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppGetReferrerByCodeResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppGetReferrerByCodeResponse> get serializer =>
      _$AppGetReferrerByCodeResponseSerializer();
}

class _$AppGetReferrerByCodeResponseSerializer
    implements PrimitiveSerializer<AppGetReferrerByCodeResponse> {
  @override
  final Iterable<Type> types = const [
    AppGetReferrerByCodeResponse,
    _$AppGetReferrerByCodeResponse
  ];

  @override
  final String wireName = r'AppGetReferrerByCodeResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppGetReferrerByCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.referrerCode != null) {
      yield r'referrerCode';
      yield serializers.serialize(
        object.referrerCode,
        specifiedType: const FullType(String),
      );
    }
    if (object.referrerName != null) {
      yield r'referrerName';
      yield serializers.serialize(
        object.referrerName,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppGetReferrerByCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppGetReferrerByCodeResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'referrerCode':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.referrerCode = valueDes;
          break;
        case r'referrerName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.referrerName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppGetReferrerByCodeResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppGetReferrerByCodeResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
