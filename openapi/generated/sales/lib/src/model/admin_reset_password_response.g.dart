// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_reset_password_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminResetPasswordResponse extends AdminResetPasswordResponse {
  @override
  final String? userId;
  @override
  final bool? success;

  factory _$AdminResetPasswordResponse(
          [void Function(AdminResetPasswordResponseBuilder)? updates]) =>
      (AdminResetPasswordResponseBuilder()..update(updates))._build();

  _$AdminResetPasswordResponse._({this.userId, this.success}) : super._();
  @override
  AdminResetPasswordResponse rebuild(
          void Function(AdminResetPasswordResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminResetPasswordResponseBuilder toBuilder() =>
      AdminResetPasswordResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminResetPasswordResponse &&
        userId == other.userId &&
        success == other.success;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, userId.hashCode);
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminResetPasswordResponse')
          ..add('userId', userId)
          ..add('success', success))
        .toString();
  }
}

class AdminResetPasswordResponseBuilder
    implements
        Builder<AdminResetPasswordResponse, AdminResetPasswordResponseBuilder> {
  _$AdminResetPasswordResponse? _$v;

  String? _userId;
  String? get userId => _$this._userId;
  set userId(String? userId) => _$this._userId = userId;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  AdminResetPasswordResponseBuilder() {
    AdminResetPasswordResponse._defaults(this);
  }

  AdminResetPasswordResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _userId = $v.userId;
      _success = $v.success;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminResetPasswordResponse other) {
    _$v = other as _$AdminResetPasswordResponse;
  }

  @override
  void update(void Function(AdminResetPasswordResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminResetPasswordResponse build() => _build();

  _$AdminResetPasswordResponse _build() {
    final _$result = _$v ??
        _$AdminResetPasswordResponse._(
          userId: userId,
          success: success,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
