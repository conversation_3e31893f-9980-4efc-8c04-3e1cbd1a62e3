//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:sales_app_api/src/model/app_branch_dto.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_get_list_branch_response.g.dart';

/// AppGetListBranchResponse
///
/// Properties:
/// * [branches]
@BuiltValue()
abstract class AppGetListBranchResponse
    implements
        Built<AppGetListBranchResponse, AppGetListBranchResponseBuilder> {
  @BuiltValueField(wireName: r'branches')
  BuiltList<AppBranchDto>? get branches;

  AppGetListBranchResponse._();

  factory AppGetListBranchResponse(
          [void updates(AppGetListBranchResponseBuilder b)]) =
      _$AppGetListBranchResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppGetListBranchResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppGetListBranchResponse> get serializer =>
      _$AppGetListBranchResponseSerializer();
}

class _$AppGetListBranchResponseSerializer
    implements PrimitiveSerializer<AppGetListBranchResponse> {
  @override
  final Iterable<Type> types = const [
    AppGetListBranchResponse,
    _$AppGetListBranchResponse
  ];

  @override
  final String wireName = r'AppGetListBranchResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppGetListBranchResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.branches != null) {
      yield r'branches';
      yield serializers.serialize(
        object.branches,
        specifiedType: const FullType(BuiltList, [FullType(AppBranchDto)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppGetListBranchResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppGetListBranchResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'branches':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(AppBranchDto)]),
          ) as BuiltList<AppBranchDto>;
          result.branches.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppGetListBranchResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppGetListBranchResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
