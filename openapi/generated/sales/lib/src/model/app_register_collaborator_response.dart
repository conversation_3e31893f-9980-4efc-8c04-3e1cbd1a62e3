//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_register_collaborator_response.g.dart';

/// AppRegisterCollaboratorResponse
///
/// Properties:
/// * [fullName] - Họ tên
/// * [branchName] - Tên chi nhánh
/// * [positionName] - Tê<PERSON> chứ<PERSON> danh
@BuiltValue()
abstract class AppRegisterCollaboratorResponse
    implements
        Built<AppRegisterCollaboratorResponse,
            AppRegisterCollaboratorResponseBuilder> {
  /// Họ tên
  @BuiltValueField(wireName: r'fullName')
  String? get fullName;

  /// Tên chi nhánh
  @BuiltValueField(wireName: r'branchName')
  String? get branchName;

  /// Tên chức danh
  @BuiltValueField(wireName: r'positionName')
  String? get positionName;

  AppRegisterCollaboratorResponse._();

  factory AppRegisterCollaboratorResponse(
          [void updates(AppRegisterCollaboratorResponseBuilder b)]) =
      _$AppRegisterCollaboratorResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppRegisterCollaboratorResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppRegisterCollaboratorResponse> get serializer =>
      _$AppRegisterCollaboratorResponseSerializer();
}

class _$AppRegisterCollaboratorResponseSerializer
    implements PrimitiveSerializer<AppRegisterCollaboratorResponse> {
  @override
  final Iterable<Type> types = const [
    AppRegisterCollaboratorResponse,
    _$AppRegisterCollaboratorResponse
  ];

  @override
  final String wireName = r'AppRegisterCollaboratorResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppRegisterCollaboratorResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.fullName != null) {
      yield r'fullName';
      yield serializers.serialize(
        object.fullName,
        specifiedType: const FullType(String),
      );
    }
    if (object.branchName != null) {
      yield r'branchName';
      yield serializers.serialize(
        object.branchName,
        specifiedType: const FullType(String),
      );
    }
    if (object.positionName != null) {
      yield r'positionName';
      yield serializers.serialize(
        object.positionName,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppRegisterCollaboratorResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppRegisterCollaboratorResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'fullName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.fullName = valueDes;
          break;
        case r'branchName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.branchName = valueDes;
          break;
        case r'positionName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.positionName = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppRegisterCollaboratorResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppRegisterCollaboratorResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
