// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_update_user_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminUpdateUserRequest extends AdminUpdateUserRequest {
  @override
  final String? username;
  @override
  final String? fullName;
  @override
  final String? email;
  @override
  final String? phone;
  @override
  final String? departmentId;
  @override
  final bool? enabled;

  factory _$AdminUpdateUserRequest(
          [void Function(AdminUpdateUserRequestBuilder)? updates]) =>
      (AdminUpdateUserRequestBuilder()..update(updates))._build();

  _$AdminUpdateUserRequest._(
      {this.username,
      this.fullName,
      this.email,
      this.phone,
      this.departmentId,
      this.enabled})
      : super._();
  @override
  AdminUpdateUserRequest rebuild(
          void Function(AdminUpdateUserRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminUpdateUserRequestBuilder toBuilder() =>
      AdminUpdateUserRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminUpdateUserRequest &&
        username == other.username &&
        fullName == other.fullName &&
        email == other.email &&
        phone == other.phone &&
        departmentId == other.departmentId &&
        enabled == other.enabled;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, username.hashCode);
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, email.hashCode);
    _$hash = $jc(_$hash, phone.hashCode);
    _$hash = $jc(_$hash, departmentId.hashCode);
    _$hash = $jc(_$hash, enabled.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminUpdateUserRequest')
          ..add('username', username)
          ..add('fullName', fullName)
          ..add('email', email)
          ..add('phone', phone)
          ..add('departmentId', departmentId)
          ..add('enabled', enabled))
        .toString();
  }
}

class AdminUpdateUserRequestBuilder
    implements Builder<AdminUpdateUserRequest, AdminUpdateUserRequestBuilder> {
  _$AdminUpdateUserRequest? _$v;

  String? _username;
  String? get username => _$this._username;
  set username(String? username) => _$this._username = username;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _email;
  String? get email => _$this._email;
  set email(String? email) => _$this._email = email;

  String? _phone;
  String? get phone => _$this._phone;
  set phone(String? phone) => _$this._phone = phone;

  String? _departmentId;
  String? get departmentId => _$this._departmentId;
  set departmentId(String? departmentId) => _$this._departmentId = departmentId;

  bool? _enabled;
  bool? get enabled => _$this._enabled;
  set enabled(bool? enabled) => _$this._enabled = enabled;

  AdminUpdateUserRequestBuilder() {
    AdminUpdateUserRequest._defaults(this);
  }

  AdminUpdateUserRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _username = $v.username;
      _fullName = $v.fullName;
      _email = $v.email;
      _phone = $v.phone;
      _departmentId = $v.departmentId;
      _enabled = $v.enabled;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminUpdateUserRequest other) {
    _$v = other as _$AdminUpdateUserRequest;
  }

  @override
  void update(void Function(AdminUpdateUserRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminUpdateUserRequest build() => _build();

  _$AdminUpdateUserRequest _build() {
    final _$result = _$v ??
        _$AdminUpdateUserRequest._(
          username: username,
          fullName: fullName,
          email: email,
          phone: phone,
          departmentId: departmentId,
          enabled: enabled,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
