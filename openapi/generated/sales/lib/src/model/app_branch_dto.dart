//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_branch_dto.g.dart';

/// AppBranchDto
///
/// Properties:
/// * [id] - Id của chi nhánh
/// * [code] - Mã chi nhánh
/// * [name] - Tên chi nhánh
/// * [address] - Địa chỉ chi nhánh
/// * [provinceId] - ID của tỉnh thành phố
@BuiltValue()
abstract class AppBranchDto
    implements Built<AppBranchDto, AppBranchDtoBuilder> {
  /// Id của chi nhánh
  @BuiltValueField(wireName: r'id')
  String? get id;

  /// Mã chi nhánh
  @BuiltValueField(wireName: r'code')
  String? get code;

  /// Tên chi nhánh
  @BuiltValueField(wireName: r'name')
  String? get name;

  /// Đ<PERSON>a chỉ chi nhánh
  @BuiltValueField(wireName: r'address')
  String? get address;

  /// ID của tỉnh thành phố
  @BuiltValueField(wireName: r'provinceId')
  String? get provinceId;

  AppBranchDto._();

  factory AppBranchDto([void updates(AppBranchDtoBuilder b)]) = _$AppBranchDto;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppBranchDtoBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppBranchDto> get serializer => _$AppBranchDtoSerializer();
}

class _$AppBranchDtoSerializer implements PrimitiveSerializer<AppBranchDto> {
  @override
  final Iterable<Type> types = const [AppBranchDto, _$AppBranchDto];

  @override
  final String wireName = r'AppBranchDto';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppBranchDto object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType(String),
      );
    }
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(String),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType(String),
      );
    }
    if (object.address != null) {
      yield r'address';
      yield serializers.serialize(
        object.address,
        specifiedType: const FullType(String),
      );
    }
    if (object.provinceId != null) {
      yield r'provinceId';
      yield serializers.serialize(
        object.provinceId,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppBranchDto object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppBranchDtoBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'address':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.address = valueDes;
          break;
        case r'provinceId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.provinceId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppBranchDto deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppBranchDtoBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
