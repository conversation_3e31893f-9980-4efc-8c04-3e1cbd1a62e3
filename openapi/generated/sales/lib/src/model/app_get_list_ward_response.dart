//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:sales_app_api/src/model/app_ward_dto.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_get_list_ward_response.g.dart';

/// AppGetListWardResponse
///
/// Properties:
/// * [wards] - Danh sách phường xã
@BuiltValue()
abstract class AppGetListWardResponse
    implements Built<AppGetListWardResponse, AppGetListWardResponseBuilder> {
  /// Danh sách phường xã
  @BuiltValueField(wireName: r'wards')
  BuiltList<AppWardDto>? get wards;

  AppGetListWardResponse._();

  factory AppGetListWardResponse(
          [void updates(AppGetListWardResponseBuilder b)]) =
      _$AppGetListWardResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppGetListWardResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppGetListWardResponse> get serializer =>
      _$AppGetListWardResponseSerializer();
}

class _$AppGetListWardResponseSerializer
    implements PrimitiveSerializer<AppGetListWardResponse> {
  @override
  final Iterable<Type> types = const [
    AppGetListWardResponse,
    _$AppGetListWardResponse
  ];

  @override
  final String wireName = r'AppGetListWardResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppGetListWardResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.wards != null) {
      yield r'wards';
      yield serializers.serialize(
        object.wards,
        specifiedType: const FullType(BuiltList, [FullType(AppWardDto)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppGetListWardResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppGetListWardResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'wards':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(AppWardDto)]),
          ) as BuiltList<AppWardDto>;
          result.wards.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppGetListWardResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppGetListWardResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
