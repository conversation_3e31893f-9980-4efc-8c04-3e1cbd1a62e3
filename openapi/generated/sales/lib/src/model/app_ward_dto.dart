//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_ward_dto.g.dart';

/// AppWardDto
///
/// Properties:
/// * [id] - Id của phường xã
/// * [code] - Mã phường xã
/// * [name] - Tên phường xã
/// * [provinceId] - ID của tỉnh thành phố
@BuiltValue()
abstract class AppWardDto implements Built<AppWardDto, AppWardDtoBuilder> {
  /// Id của phường xã
  @BuiltValueField(wireName: r'id')
  String? get id;

  /// Mã phường xã
  @BuiltValueField(wireName: r'code')
  String? get code;

  /// Tên phường xã
  @BuiltValueField(wireName: r'name')
  String? get name;

  /// ID của tỉnh thành phố
  @BuiltValueField(wireName: r'provinceId')
  String? get provinceId;

  AppWardDto._();

  factory AppWardDto([void updates(AppWardDtoBuilder b)]) = _$AppWardDto;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppWardDtoBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppWardDto> get serializer => _$AppWardDtoSerializer();
}

class _$AppWardDtoSerializer implements PrimitiveSerializer<AppWardDto> {
  @override
  final Iterable<Type> types = const [AppWardDto, _$AppWardDto];

  @override
  final String wireName = r'AppWardDto';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppWardDto object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.id != null) {
      yield r'id';
      yield serializers.serialize(
        object.id,
        specifiedType: const FullType(String),
      );
    }
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(String),
      );
    }
    if (object.name != null) {
      yield r'name';
      yield serializers.serialize(
        object.name,
        specifiedType: const FullType(String),
      );
    }
    if (object.provinceId != null) {
      yield r'provinceId';
      yield serializers.serialize(
        object.provinceId,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppWardDto object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppWardDtoBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.name = valueDes;
          break;
        case r'provinceId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.provinceId = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppWardDto deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppWardDtoBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
