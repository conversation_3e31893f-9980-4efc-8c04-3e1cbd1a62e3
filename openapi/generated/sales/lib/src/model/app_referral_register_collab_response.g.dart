// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_referral_register_collab_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppReferralRegisterCollabResponse
    extends AppReferralRegisterCollabResponse {
  @override
  final String? fullName;
  @override
  final String? branchName;
  @override
  final String? positionName;
  @override
  final String? referrerCode;
  @override
  final String? referrerName;

  factory _$AppReferralRegisterCollabResponse(
          [void Function(AppReferralRegisterCollabResponseBuilder)? updates]) =>
      (AppReferralRegisterCollabResponseBuilder()..update(updates))._build();

  _$AppReferralRegisterCollabResponse._(
      {this.fullName,
      this.branchName,
      this.positionName,
      this.referrerCode,
      this.referrerName})
      : super._();
  @override
  AppReferralRegisterCollabResponse rebuild(
          void Function(AppReferralRegisterCollabResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppReferralRegisterCollabResponseBuilder toBuilder() =>
      AppReferralRegisterCollabResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppReferralRegisterCollabResponse &&
        fullName == other.fullName &&
        branchName == other.branchName &&
        positionName == other.positionName &&
        referrerCode == other.referrerCode &&
        referrerName == other.referrerName;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, fullName.hashCode);
    _$hash = $jc(_$hash, branchName.hashCode);
    _$hash = $jc(_$hash, positionName.hashCode);
    _$hash = $jc(_$hash, referrerCode.hashCode);
    _$hash = $jc(_$hash, referrerName.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppReferralRegisterCollabResponse')
          ..add('fullName', fullName)
          ..add('branchName', branchName)
          ..add('positionName', positionName)
          ..add('referrerCode', referrerCode)
          ..add('referrerName', referrerName))
        .toString();
  }
}

class AppReferralRegisterCollabResponseBuilder
    implements
        Builder<AppReferralRegisterCollabResponse,
            AppReferralRegisterCollabResponseBuilder> {
  _$AppReferralRegisterCollabResponse? _$v;

  String? _fullName;
  String? get fullName => _$this._fullName;
  set fullName(String? fullName) => _$this._fullName = fullName;

  String? _branchName;
  String? get branchName => _$this._branchName;
  set branchName(String? branchName) => _$this._branchName = branchName;

  String? _positionName;
  String? get positionName => _$this._positionName;
  set positionName(String? positionName) => _$this._positionName = positionName;

  String? _referrerCode;
  String? get referrerCode => _$this._referrerCode;
  set referrerCode(String? referrerCode) => _$this._referrerCode = referrerCode;

  String? _referrerName;
  String? get referrerName => _$this._referrerName;
  set referrerName(String? referrerName) => _$this._referrerName = referrerName;

  AppReferralRegisterCollabResponseBuilder() {
    AppReferralRegisterCollabResponse._defaults(this);
  }

  AppReferralRegisterCollabResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _fullName = $v.fullName;
      _branchName = $v.branchName;
      _positionName = $v.positionName;
      _referrerCode = $v.referrerCode;
      _referrerName = $v.referrerName;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppReferralRegisterCollabResponse other) {
    _$v = other as _$AppReferralRegisterCollabResponse;
  }

  @override
  void update(
      void Function(AppReferralRegisterCollabResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppReferralRegisterCollabResponse build() => _build();

  _$AppReferralRegisterCollabResponse _build() {
    final _$result = _$v ??
        _$AppReferralRegisterCollabResponse._(
          fullName: fullName,
          branchName: branchName,
          positionName: positionName,
          referrerCode: referrerCode,
          referrerName: referrerName,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
