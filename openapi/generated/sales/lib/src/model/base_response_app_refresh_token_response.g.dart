// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_refresh_token_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppRefreshTokenResponse
    extends BaseResponseAppRefreshTokenResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppRefreshTokenResponse? data;

  factory _$BaseResponseAppRefreshTokenResponse(
          [void Function(BaseResponseAppRefreshTokenResponseBuilder)?
              updates]) =>
      (BaseResponseAppRefreshTokenResponseBuilder()..update(updates))._build();

  _$BaseResponseAppRefreshTokenResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppRefreshTokenResponse rebuild(
          void Function(BaseResponseAppRefreshTokenResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppRefreshTokenResponseBuilder toBuilder() =>
      BaseResponseAppRefreshTokenResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppRefreshTokenResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAppRefreshTokenResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppRefreshTokenResponseBuilder
    implements
        Builder<BaseResponseAppRefreshTokenResponse,
            BaseResponseAppRefreshTokenResponseBuilder> {
  _$BaseResponseAppRefreshTokenResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppRefreshTokenResponseBuilder? _data;
  AppRefreshTokenResponseBuilder get data =>
      _$this._data ??= AppRefreshTokenResponseBuilder();
  set data(AppRefreshTokenResponseBuilder? data) => _$this._data = data;

  BaseResponseAppRefreshTokenResponseBuilder() {
    BaseResponseAppRefreshTokenResponse._defaults(this);
  }

  BaseResponseAppRefreshTokenResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppRefreshTokenResponse other) {
    _$v = other as _$BaseResponseAppRefreshTokenResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppRefreshTokenResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppRefreshTokenResponse build() => _build();

  _$BaseResponseAppRefreshTokenResponse _build() {
    _$BaseResponseAppRefreshTokenResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppRefreshTokenResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(r'BaseResponseAppRefreshTokenResponse',
            _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
