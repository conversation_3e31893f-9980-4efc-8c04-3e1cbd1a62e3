// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_search_users_request.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminSearchUsersRequest extends AdminSearchUsersRequest {
  @override
  final int? page;
  @override
  final int? size;
  @override
  final String? orderBy;
  @override
  final String? orderDir;
  @override
  final String? keyword;
  @override
  final String? departmentId;
  @override
  final bool? enabled;

  factory _$AdminSearchUsersRequest(
          [void Function(AdminSearchUsersRequestBuilder)? updates]) =>
      (AdminSearchUsersRequestBuilder()..update(updates))._build();

  _$AdminSearchUsersRequest._(
      {this.page,
      this.size,
      this.orderBy,
      this.orderDir,
      this.keyword,
      this.departmentId,
      this.enabled})
      : super._();
  @override
  AdminSearchUsersRequest rebuild(
          void Function(AdminSearchUsersRequestBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminSearchUsersRequestBuilder toBuilder() =>
      AdminSearchUsersRequestBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminSearchUsersRequest &&
        page == other.page &&
        size == other.size &&
        orderBy == other.orderBy &&
        orderDir == other.orderDir &&
        keyword == other.keyword &&
        departmentId == other.departmentId &&
        enabled == other.enabled;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, page.hashCode);
    _$hash = $jc(_$hash, size.hashCode);
    _$hash = $jc(_$hash, orderBy.hashCode);
    _$hash = $jc(_$hash, orderDir.hashCode);
    _$hash = $jc(_$hash, keyword.hashCode);
    _$hash = $jc(_$hash, departmentId.hashCode);
    _$hash = $jc(_$hash, enabled.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminSearchUsersRequest')
          ..add('page', page)
          ..add('size', size)
          ..add('orderBy', orderBy)
          ..add('orderDir', orderDir)
          ..add('keyword', keyword)
          ..add('departmentId', departmentId)
          ..add('enabled', enabled))
        .toString();
  }
}

class AdminSearchUsersRequestBuilder
    implements
        Builder<AdminSearchUsersRequest, AdminSearchUsersRequestBuilder> {
  _$AdminSearchUsersRequest? _$v;

  int? _page;
  int? get page => _$this._page;
  set page(int? page) => _$this._page = page;

  int? _size;
  int? get size => _$this._size;
  set size(int? size) => _$this._size = size;

  String? _orderBy;
  String? get orderBy => _$this._orderBy;
  set orderBy(String? orderBy) => _$this._orderBy = orderBy;

  String? _orderDir;
  String? get orderDir => _$this._orderDir;
  set orderDir(String? orderDir) => _$this._orderDir = orderDir;

  String? _keyword;
  String? get keyword => _$this._keyword;
  set keyword(String? keyword) => _$this._keyword = keyword;

  String? _departmentId;
  String? get departmentId => _$this._departmentId;
  set departmentId(String? departmentId) => _$this._departmentId = departmentId;

  bool? _enabled;
  bool? get enabled => _$this._enabled;
  set enabled(bool? enabled) => _$this._enabled = enabled;

  AdminSearchUsersRequestBuilder() {
    AdminSearchUsersRequest._defaults(this);
  }

  AdminSearchUsersRequestBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _page = $v.page;
      _size = $v.size;
      _orderBy = $v.orderBy;
      _orderDir = $v.orderDir;
      _keyword = $v.keyword;
      _departmentId = $v.departmentId;
      _enabled = $v.enabled;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminSearchUsersRequest other) {
    _$v = other as _$AdminSearchUsersRequest;
  }

  @override
  void update(void Function(AdminSearchUsersRequestBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminSearchUsersRequest build() => _build();

  _$AdminSearchUsersRequest _build() {
    final _$result = _$v ??
        _$AdminSearchUsersRequest._(
          page: page,
          size: size,
          orderBy: orderBy,
          orderDir: orderDir,
          keyword: keyword,
          departmentId: departmentId,
          enabled: enabled,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
