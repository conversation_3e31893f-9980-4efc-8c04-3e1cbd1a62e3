//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:sales_app_api/src/model/app_get_system_configs_by_group_code_response.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'base_response_app_get_system_configs_by_group_code_response.g.dart';

/// BaseResponseAppGetSystemConfigsByGroupCodeResponse
///
/// Properties:
/// * [success]
/// * [code]
/// * [message]
/// * [data]
@BuiltValue()
abstract class BaseResponseAppGetSystemConfigsByGroupCodeResponse
    implements
        Built<BaseResponseAppGetSystemConfigsByGroupCodeResponse,
            BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder> {
  @BuiltValueField(wireName: r'success')
  bool? get success;

  @BuiltValueField(wireName: r'code')
  int? get code;

  @BuiltValueField(wireName: r'message')
  String? get message;

  @BuiltValueField(wireName: r'data')
  AppGetSystemConfigsByGroupCodeResponse? get data;

  BaseResponseAppGetSystemConfigsByGroupCodeResponse._();

  factory BaseResponseAppGetSystemConfigsByGroupCodeResponse(
          [void updates(
              BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder b)]) =
      _$BaseResponseAppGetSystemConfigsByGroupCodeResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(
          BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder b) =>
      b;

  @BuiltValueSerializer(custom: true)
  static Serializer<BaseResponseAppGetSystemConfigsByGroupCodeResponse>
      get serializer =>
          _$BaseResponseAppGetSystemConfigsByGroupCodeResponseSerializer();
}

class _$BaseResponseAppGetSystemConfigsByGroupCodeResponseSerializer
    implements
        PrimitiveSerializer<
            BaseResponseAppGetSystemConfigsByGroupCodeResponse> {
  @override
  final Iterable<Type> types = const [
    BaseResponseAppGetSystemConfigsByGroupCodeResponse,
    _$BaseResponseAppGetSystemConfigsByGroupCodeResponse
  ];

  @override
  final String wireName = r'BaseResponseAppGetSystemConfigsByGroupCodeResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    BaseResponseAppGetSystemConfigsByGroupCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.success != null) {
      yield r'success';
      yield serializers.serialize(
        object.success,
        specifiedType: const FullType(bool),
      );
    }
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(int),
      );
    }
    if (object.message != null) {
      yield r'message';
      yield serializers.serialize(
        object.message,
        specifiedType: const FullType(String),
      );
    }
    if (object.data != null) {
      yield r'data';
      yield serializers.serialize(
        object.data,
        specifiedType: const FullType(AppGetSystemConfigsByGroupCodeResponse),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    BaseResponseAppGetSystemConfigsByGroupCodeResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.success = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.code = valueDes;
          break;
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.message = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType:
                const FullType(AppGetSystemConfigsByGroupCodeResponse),
          ) as AppGetSystemConfigsByGroupCodeResponse;
          result.data.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  BaseResponseAppGetSystemConfigsByGroupCodeResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = BaseResponseAppGetSystemConfigsByGroupCodeResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
