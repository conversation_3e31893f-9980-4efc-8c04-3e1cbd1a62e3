// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'action_dto.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$ActionDto extends ActionDto {
  @override
  final String? id;
  @override
  final String? code;
  @override
  final String? name;
  @override
  final String? description;

  factory _$ActionDto([void Function(ActionDtoBuilder)? updates]) =>
      (ActionDtoBuilder()..update(updates))._build();

  _$ActionDto._({this.id, this.code, this.name, this.description}) : super._();
  @override
  ActionDto rebuild(void Function(ActionDtoBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  ActionDtoBuilder toBuilder() => ActionDtoBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ActionDto &&
        id == other.id &&
        code == other.code &&
        name == other.name &&
        description == other.description;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, id.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, name.hashCode);
    _$hash = $jc(_$hash, description.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'ActionDto')
          ..add('id', id)
          ..add('code', code)
          ..add('name', name)
          ..add('description', description))
        .toString();
  }
}

class ActionDtoBuilder implements Builder<ActionDto, ActionDtoBuilder> {
  _$ActionDto? _$v;

  String? _id;
  String? get id => _$this._id;
  set id(String? id) => _$this._id = id;

  String? _code;
  String? get code => _$this._code;
  set code(String? code) => _$this._code = code;

  String? _name;
  String? get name => _$this._name;
  set name(String? name) => _$this._name = name;

  String? _description;
  String? get description => _$this._description;
  set description(String? description) => _$this._description = description;

  ActionDtoBuilder() {
    ActionDto._defaults(this);
  }

  ActionDtoBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _id = $v.id;
      _code = $v.code;
      _name = $v.name;
      _description = $v.description;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(ActionDto other) {
    _$v = other as _$ActionDto;
  }

  @override
  void update(void Function(ActionDtoBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  ActionDto build() => _build();

  _$ActionDto _build() {
    final _$result = _$v ??
        _$ActionDto._(
          id: id,
          code: code,
          name: name,
          description: description,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
