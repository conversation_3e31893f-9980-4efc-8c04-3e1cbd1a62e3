// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_list_parent_menus_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminListParentMenusResponse extends AdminListParentMenusResponse {
  @override
  final BuiltList<MenuDto>? menus;

  factory _$AdminListParentMenusResponse(
          [void Function(AdminListParentMenusResponseBuilder)? updates]) =>
      (AdminListParentMenusResponseBuilder()..update(updates))._build();

  _$AdminListParentMenusResponse._({this.menus}) : super._();
  @override
  AdminListParentMenusResponse rebuild(
          void Function(AdminListParentMenusResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminListParentMenusResponseBuilder toBuilder() =>
      AdminListParentMenusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminListParentMenusResponse && menus == other.menus;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, menus.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminListParentMenusResponse')
          ..add('menus', menus))
        .toString();
  }
}

class AdminListParentMenusResponseBuilder
    implements
        Builder<AdminListParentMenusResponse,
            AdminListParentMenusResponseBuilder> {
  _$AdminListParentMenusResponse? _$v;

  ListBuilder<MenuDto>? _menus;
  ListBuilder<MenuDto> get menus => _$this._menus ??= ListBuilder<MenuDto>();
  set menus(ListBuilder<MenuDto>? menus) => _$this._menus = menus;

  AdminListParentMenusResponseBuilder() {
    AdminListParentMenusResponse._defaults(this);
  }

  AdminListParentMenusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _menus = $v.menus?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminListParentMenusResponse other) {
    _$v = other as _$AdminListParentMenusResponse;
  }

  @override
  void update(void Function(AdminListParentMenusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminListParentMenusResponse build() => _build();

  _$AdminListParentMenusResponse _build() {
    _$AdminListParentMenusResponse _$result;
    try {
      _$result = _$v ??
          _$AdminListParentMenusResponse._(
            menus: _menus?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'menus';
        _menus?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminListParentMenusResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
