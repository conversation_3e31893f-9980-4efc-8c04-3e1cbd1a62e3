// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_logout_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AppLogoutResponse extends AppLogoutResponse {
  @override
  final DateTime? logoutAt;

  factory _$AppLogoutResponse(
          [void Function(AppLogoutResponseBuilder)? updates]) =>
      (AppLogoutResponseBuilder()..update(updates))._build();

  _$AppLogoutResponse._({this.logoutAt}) : super._();
  @override
  AppLogoutResponse rebuild(void Function(AppLogoutResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AppLogoutResponseBuilder toBuilder() =>
      AppLogoutResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AppLogoutResponse && logoutAt == other.logoutAt;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, logoutAt.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AppLogoutResponse')
          ..add('logoutAt', logoutAt))
        .toString();
  }
}

class AppLogoutResponseBuilder
    implements Builder<AppLogoutResponse, AppLogoutResponseBuilder> {
  _$AppLogoutResponse? _$v;

  DateTime? _logoutAt;
  DateTime? get logoutAt => _$this._logoutAt;
  set logoutAt(DateTime? logoutAt) => _$this._logoutAt = logoutAt;

  AppLogoutResponseBuilder() {
    AppLogoutResponse._defaults(this);
  }

  AppLogoutResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _logoutAt = $v.logoutAt;
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AppLogoutResponse other) {
    _$v = other as _$AppLogoutResponse;
  }

  @override
  void update(void Function(AppLogoutResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AppLogoutResponse build() => _build();

  _$AppLogoutResponse _build() {
    final _$result = _$v ??
        _$AppLogoutResponse._(
          logoutAt: logoutAt,
        );
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
