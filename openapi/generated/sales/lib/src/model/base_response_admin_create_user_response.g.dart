// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_create_user_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminCreateUserResponse
    extends BaseResponseAdminCreateUserResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminCreateUserResponse? data;

  factory _$BaseResponseAdminCreateUserResponse(
          [void Function(BaseResponseAdminCreateUserResponseBuilder)?
              updates]) =>
      (BaseResponseAdminCreateUserResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminCreateUserResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminCreateUserResponse rebuild(
          void Function(BaseResponseAdminCreateUserResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminCreateUserResponseBuilder toBuilder() =>
      BaseResponseAdminCreateUserResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminCreateUserResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminCreateUserResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminCreateUserResponseBuilder
    implements
        Builder<BaseResponseAdminCreateUserResponse,
            BaseResponseAdminCreateUserResponseBuilder> {
  _$BaseResponseAdminCreateUserResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminCreateUserResponseBuilder? _data;
  AdminCreateUserResponseBuilder get data =>
      _$this._data ??= AdminCreateUserResponseBuilder();
  set data(AdminCreateUserResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminCreateUserResponseBuilder() {
    BaseResponseAdminCreateUserResponse._defaults(this);
  }

  BaseResponseAdminCreateUserResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminCreateUserResponse other) {
    _$v = other as _$BaseResponseAdminCreateUserResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminCreateUserResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminCreateUserResponse build() => _build();

  _$BaseResponseAdminCreateUserResponse _build() {
    _$BaseResponseAdminCreateUserResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminCreateUserResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(r'BaseResponseAdminCreateUserResponse',
            _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
