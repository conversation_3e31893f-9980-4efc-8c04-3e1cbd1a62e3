// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_login_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppLoginResponse extends BaseResponseAppLoginResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppLoginResponse? data;

  factory _$BaseResponseAppLoginResponse(
          [void Function(BaseResponseAppLoginResponseBuilder)? updates]) =>
      (BaseResponseAppLoginResponseBuilder()..update(updates))._build();

  _$BaseResponseAppLoginResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppLoginResponse rebuild(
          void Function(BaseResponseAppLoginResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppLoginResponseBuilder toBuilder() =>
      BaseResponseAppLoginResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppLoginResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAppLoginResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppLoginResponseBuilder
    implements
        Builder<BaseResponseAppLoginResponse,
            BaseResponseAppLoginResponseBuilder> {
  _$BaseResponseAppLoginResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppLoginResponseBuilder? _data;
  AppLoginResponseBuilder get data =>
      _$this._data ??= AppLoginResponseBuilder();
  set data(AppLoginResponseBuilder? data) => _$this._data = data;

  BaseResponseAppLoginResponseBuilder() {
    BaseResponseAppLoginResponse._defaults(this);
  }

  BaseResponseAppLoginResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppLoginResponse other) {
    _$v = other as _$BaseResponseAppLoginResponse;
  }

  @override
  void update(void Function(BaseResponseAppLoginResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppLoginResponse build() => _build();

  _$BaseResponseAppLoginResponse _build() {
    _$BaseResponseAppLoginResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppLoginResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppLoginResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
