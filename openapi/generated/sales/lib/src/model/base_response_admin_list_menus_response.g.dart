// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_list_menus_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminListMenusResponse
    extends BaseResponseAdminListMenusResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminListMenusResponse? data;

  factory _$BaseResponseAdminListMenusResponse(
          [void Function(BaseResponseAdminListMenusResponseBuilder)?
              updates]) =>
      (BaseResponseAdminListMenusResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminListMenusResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminListMenusResponse rebuild(
          void Function(BaseResponseAdminListMenusResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminListMenusResponseBuilder toBuilder() =>
      BaseResponseAdminListMenusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminListMenusResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminListMenusResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminListMenusResponseBuilder
    implements
        Builder<BaseResponseAdminListMenusResponse,
            BaseResponseAdminListMenusResponseBuilder> {
  _$BaseResponseAdminListMenusResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminListMenusResponseBuilder? _data;
  AdminListMenusResponseBuilder get data =>
      _$this._data ??= AdminListMenusResponseBuilder();
  set data(AdminListMenusResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminListMenusResponseBuilder() {
    BaseResponseAdminListMenusResponse._defaults(this);
  }

  BaseResponseAdminListMenusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminListMenusResponse other) {
    _$v = other as _$BaseResponseAdminListMenusResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminListMenusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminListMenusResponse build() => _build();

  _$BaseResponseAdminListMenusResponse _build() {
    _$BaseResponseAdminListMenusResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminListMenusResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAdminListMenusResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
