// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_app_get_list_branch_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAppGetListBranchResponse
    extends BaseResponseAppGetListBranchResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AppGetListBranchResponse? data;

  factory _$BaseResponseAppGetListBranchResponse(
          [void Function(BaseResponseAppGetListBranchResponseBuilder)?
              updates]) =>
      (BaseResponseAppGetListBranchResponseBuilder()..update(updates))._build();

  _$BaseResponseAppGetListBranchResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAppGetListBranchResponse rebuild(
          void Function(BaseResponseAppGetListBranchResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAppGetListBranchResponseBuilder toBuilder() =>
      BaseResponseAppGetListBranchResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAppGetListBranchResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAppGetListBranchResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAppGetListBranchResponseBuilder
    implements
        Builder<BaseResponseAppGetListBranchResponse,
            BaseResponseAppGetListBranchResponseBuilder> {
  _$BaseResponseAppGetListBranchResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AppGetListBranchResponseBuilder? _data;
  AppGetListBranchResponseBuilder get data =>
      _$this._data ??= AppGetListBranchResponseBuilder();
  set data(AppGetListBranchResponseBuilder? data) => _$this._data = data;

  BaseResponseAppGetListBranchResponseBuilder() {
    BaseResponseAppGetListBranchResponse._defaults(this);
  }

  BaseResponseAppGetListBranchResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAppGetListBranchResponse other) {
    _$v = other as _$BaseResponseAppGetListBranchResponse;
  }

  @override
  void update(
      void Function(BaseResponseAppGetListBranchResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAppGetListBranchResponse build() => _build();

  _$BaseResponseAppGetListBranchResponse _build() {
    _$BaseResponseAppGetListBranchResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAppGetListBranchResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAppGetListBranchResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
