// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_get_actions_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminGetActionsResponse extends AdminGetActionsResponse {
  @override
  final BuiltList<ActionDto>? actions;

  factory _$AdminGetActionsResponse(
          [void Function(AdminGetActionsResponseBuilder)? updates]) =>
      (AdminGetActionsResponseBuilder()..update(updates))._build();

  _$AdminGetActionsResponse._({this.actions}) : super._();
  @override
  AdminGetActionsResponse rebuild(
          void Function(AdminGetActionsResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminGetActionsResponseBuilder toBuilder() =>
      AdminGetActionsResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminGetActionsResponse && actions == other.actions;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, actions.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminGetActionsResponse')
          ..add('actions', actions))
        .toString();
  }
}

class AdminGetActionsResponseBuilder
    implements
        Builder<AdminGetActionsResponse, AdminGetActionsResponseBuilder> {
  _$AdminGetActionsResponse? _$v;

  ListBuilder<ActionDto>? _actions;
  ListBuilder<ActionDto> get actions =>
      _$this._actions ??= ListBuilder<ActionDto>();
  set actions(ListBuilder<ActionDto>? actions) => _$this._actions = actions;

  AdminGetActionsResponseBuilder() {
    AdminGetActionsResponse._defaults(this);
  }

  AdminGetActionsResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _actions = $v.actions?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminGetActionsResponse other) {
    _$v = other as _$AdminGetActionsResponse;
  }

  @override
  void update(void Function(AdminGetActionsResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminGetActionsResponse build() => _build();

  _$AdminGetActionsResponse _build() {
    _$AdminGetActionsResponse _$result;
    try {
      _$result = _$v ??
          _$AdminGetActionsResponse._(
            actions: _actions?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'actions';
        _actions?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminGetActionsResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
