// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_assign_roles_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminAssignRolesResponse
    extends BaseResponseAdminAssignRolesResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminAssignRolesResponse? data;

  factory _$BaseResponseAdminAssignRolesResponse(
          [void Function(BaseResponseAdminAssignRolesResponseBuilder)?
              updates]) =>
      (BaseResponseAdminAssignRolesResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminAssignRolesResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminAssignRolesResponse rebuild(
          void Function(BaseResponseAdminAssignRolesResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminAssignRolesResponseBuilder toBuilder() =>
      BaseResponseAdminAssignRolesResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminAssignRolesResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminAssignRolesResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminAssignRolesResponseBuilder
    implements
        Builder<BaseResponseAdminAssignRolesResponse,
            BaseResponseAdminAssignRolesResponseBuilder> {
  _$BaseResponseAdminAssignRolesResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminAssignRolesResponseBuilder? _data;
  AdminAssignRolesResponseBuilder get data =>
      _$this._data ??= AdminAssignRolesResponseBuilder();
  set data(AdminAssignRolesResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminAssignRolesResponseBuilder() {
    BaseResponseAdminAssignRolesResponse._defaults(this);
  }

  BaseResponseAdminAssignRolesResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminAssignRolesResponse other) {
    _$v = other as _$BaseResponseAdminAssignRolesResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminAssignRolesResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminAssignRolesResponse build() => _build();

  _$BaseResponseAdminAssignRolesResponse _build() {
    _$BaseResponseAdminAssignRolesResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminAssignRolesResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'BaseResponseAdminAssignRolesResponse',
            _$failedField,
            e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
