//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'app_login_response.g.dart';

/// AppLoginResponse
///
/// Properties:
/// * [accessToken] - Access token for the user
/// * [expiresIn] - Time in seconds until the access token expires
/// * [refreshToken] - Refresh token for obtaining a new access token
/// * [refreshExpiresIn] - Time in seconds until the refresh token expires
/// * [tokenType] - Type of the token, typically 'Bearer'
/// * [code] - Mã nhân viên
/// * [cifNo] - Unique identifier for the user
/// * [fullName] - Full name of the user
/// * [email] - Email of the user
/// * [phoneNumber] - Phone number of the user
@BuiltValue()
abstract class AppLoginResponse
    implements Built<AppLoginResponse, AppLoginResponseBuilder> {
  /// Access token for the user
  @BuiltValueField(wireName: r'accessToken')
  String? get accessToken;

  /// Time in seconds until the access token expires
  @BuiltValueField(wireName: r'expiresIn')
  int? get expiresIn;

  /// Refresh token for obtaining a new access token
  @BuiltValueField(wireName: r'refreshToken')
  String? get refreshToken;

  /// Time in seconds until the refresh token expires
  @BuiltValueField(wireName: r'refreshExpiresIn')
  int? get refreshExpiresIn;

  /// Type of the token, typically 'Bearer'
  @BuiltValueField(wireName: r'tokenType')
  String? get tokenType;

  /// Mã nhân viên
  @BuiltValueField(wireName: r'code')
  String? get code;

  /// Unique identifier for the user
  @BuiltValueField(wireName: r'cifNo')
  String? get cifNo;

  /// Full name of the user
  @BuiltValueField(wireName: r'fullName')
  String? get fullName;

  /// Email of the user
  @BuiltValueField(wireName: r'email')
  String? get email;

  /// Phone number of the user
  @BuiltValueField(wireName: r'phoneNumber')
  String? get phoneNumber;

  AppLoginResponse._();

  factory AppLoginResponse([void updates(AppLoginResponseBuilder b)]) =
      _$AppLoginResponse;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AppLoginResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AppLoginResponse> get serializer =>
      _$AppLoginResponseSerializer();
}

class _$AppLoginResponseSerializer
    implements PrimitiveSerializer<AppLoginResponse> {
  @override
  final Iterable<Type> types = const [AppLoginResponse, _$AppLoginResponse];

  @override
  final String wireName = r'AppLoginResponse';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AppLoginResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.accessToken != null) {
      yield r'accessToken';
      yield serializers.serialize(
        object.accessToken,
        specifiedType: const FullType(String),
      );
    }
    if (object.expiresIn != null) {
      yield r'expiresIn';
      yield serializers.serialize(
        object.expiresIn,
        specifiedType: const FullType(int),
      );
    }
    if (object.refreshToken != null) {
      yield r'refreshToken';
      yield serializers.serialize(
        object.refreshToken,
        specifiedType: const FullType(String),
      );
    }
    if (object.refreshExpiresIn != null) {
      yield r'refreshExpiresIn';
      yield serializers.serialize(
        object.refreshExpiresIn,
        specifiedType: const FullType(int),
      );
    }
    if (object.tokenType != null) {
      yield r'tokenType';
      yield serializers.serialize(
        object.tokenType,
        specifiedType: const FullType(String),
      );
    }
    if (object.code != null) {
      yield r'code';
      yield serializers.serialize(
        object.code,
        specifiedType: const FullType(String),
      );
    }
    if (object.cifNo != null) {
      yield r'cifNo';
      yield serializers.serialize(
        object.cifNo,
        specifiedType: const FullType(String),
      );
    }
    if (object.fullName != null) {
      yield r'fullName';
      yield serializers.serialize(
        object.fullName,
        specifiedType: const FullType(String),
      );
    }
    if (object.email != null) {
      yield r'email';
      yield serializers.serialize(
        object.email,
        specifiedType: const FullType(String),
      );
    }
    if (object.phoneNumber != null) {
      yield r'phoneNumber';
      yield serializers.serialize(
        object.phoneNumber,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    AppLoginResponse object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object,
            specifiedType: specifiedType)
        .toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AppLoginResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'accessToken':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.accessToken = valueDes;
          break;
        case r'expiresIn':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.expiresIn = valueDes;
          break;
        case r'refreshToken':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.refreshToken = valueDes;
          break;
        case r'refreshExpiresIn':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(int),
          ) as int;
          result.refreshExpiresIn = valueDes;
          break;
        case r'tokenType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.tokenType = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        case r'cifNo':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.cifNo = valueDes;
          break;
        case r'fullName':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.fullName = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        case r'phoneNumber':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.phoneNumber = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AppLoginResponse deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AppLoginResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}
