// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_list_menus_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$AdminListMenusResponse extends AdminListMenusResponse {
  @override
  final BuiltList<MenuDto>? menuApp;
  @override
  final BuiltList<MenuDto>? menuWeb;

  factory _$AdminListMenusResponse(
          [void Function(AdminListMenusResponseBuilder)? updates]) =>
      (AdminListMenusResponseBuilder()..update(updates))._build();

  _$AdminListMenusResponse._({this.menuApp, this.menuWeb}) : super._();
  @override
  AdminListMenusResponse rebuild(
          void Function(AdminListMenusResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  AdminListMenusResponseBuilder toBuilder() =>
      AdminListMenusResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is AdminListMenusResponse &&
        menuApp == other.menuApp &&
        menuWeb == other.menuWeb;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, menuApp.hashCode);
    _$hash = $jc(_$hash, menuWeb.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'AdminListMenusResponse')
          ..add('menuApp', menuApp)
          ..add('menuWeb', menuWeb))
        .toString();
  }
}

class AdminListMenusResponseBuilder
    implements Builder<AdminListMenusResponse, AdminListMenusResponseBuilder> {
  _$AdminListMenusResponse? _$v;

  ListBuilder<MenuDto>? _menuApp;
  ListBuilder<MenuDto> get menuApp =>
      _$this._menuApp ??= ListBuilder<MenuDto>();
  set menuApp(ListBuilder<MenuDto>? menuApp) => _$this._menuApp = menuApp;

  ListBuilder<MenuDto>? _menuWeb;
  ListBuilder<MenuDto> get menuWeb =>
      _$this._menuWeb ??= ListBuilder<MenuDto>();
  set menuWeb(ListBuilder<MenuDto>? menuWeb) => _$this._menuWeb = menuWeb;

  AdminListMenusResponseBuilder() {
    AdminListMenusResponse._defaults(this);
  }

  AdminListMenusResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _menuApp = $v.menuApp?.toBuilder();
      _menuWeb = $v.menuWeb?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(AdminListMenusResponse other) {
    _$v = other as _$AdminListMenusResponse;
  }

  @override
  void update(void Function(AdminListMenusResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  AdminListMenusResponse build() => _build();

  _$AdminListMenusResponse _build() {
    _$AdminListMenusResponse _$result;
    try {
      _$result = _$v ??
          _$AdminListMenusResponse._(
            menuApp: _menuApp?.build(),
            menuWeb: _menuWeb?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'menuApp';
        _menuApp?.build();
        _$failedField = 'menuWeb';
        _menuWeb?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(
            r'AdminListMenusResponse', _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
