// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response_admin_update_menu_response.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

class _$BaseResponseAdminUpdateMenuResponse
    extends BaseResponseAdminUpdateMenuResponse {
  @override
  final bool? success;
  @override
  final int? code;
  @override
  final String? message;
  @override
  final AdminUpdateMenuResponse? data;

  factory _$BaseResponseAdminUpdateMenuResponse(
          [void Function(BaseResponseAdminUpdateMenuResponseBuilder)?
              updates]) =>
      (BaseResponseAdminUpdateMenuResponseBuilder()..update(updates))._build();

  _$BaseResponseAdminUpdateMenuResponse._(
      {this.success, this.code, this.message, this.data})
      : super._();
  @override
  BaseResponseAdminUpdateMenuResponse rebuild(
          void Function(BaseResponseAdminUpdateMenuResponseBuilder) updates) =>
      (toBuilder()..update(updates)).build();

  @override
  BaseResponseAdminUpdateMenuResponseBuilder toBuilder() =>
      BaseResponseAdminUpdateMenuResponseBuilder()..replace(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is BaseResponseAdminUpdateMenuResponse &&
        success == other.success &&
        code == other.code &&
        message == other.message &&
        data == other.data;
  }

  @override
  int get hashCode {
    var _$hash = 0;
    _$hash = $jc(_$hash, success.hashCode);
    _$hash = $jc(_$hash, code.hashCode);
    _$hash = $jc(_$hash, message.hashCode);
    _$hash = $jc(_$hash, data.hashCode);
    _$hash = $jf(_$hash);
    return _$hash;
  }

  @override
  String toString() {
    return (newBuiltValueToStringHelper(r'BaseResponseAdminUpdateMenuResponse')
          ..add('success', success)
          ..add('code', code)
          ..add('message', message)
          ..add('data', data))
        .toString();
  }
}

class BaseResponseAdminUpdateMenuResponseBuilder
    implements
        Builder<BaseResponseAdminUpdateMenuResponse,
            BaseResponseAdminUpdateMenuResponseBuilder> {
  _$BaseResponseAdminUpdateMenuResponse? _$v;

  bool? _success;
  bool? get success => _$this._success;
  set success(bool? success) => _$this._success = success;

  int? _code;
  int? get code => _$this._code;
  set code(int? code) => _$this._code = code;

  String? _message;
  String? get message => _$this._message;
  set message(String? message) => _$this._message = message;

  AdminUpdateMenuResponseBuilder? _data;
  AdminUpdateMenuResponseBuilder get data =>
      _$this._data ??= AdminUpdateMenuResponseBuilder();
  set data(AdminUpdateMenuResponseBuilder? data) => _$this._data = data;

  BaseResponseAdminUpdateMenuResponseBuilder() {
    BaseResponseAdminUpdateMenuResponse._defaults(this);
  }

  BaseResponseAdminUpdateMenuResponseBuilder get _$this {
    final $v = _$v;
    if ($v != null) {
      _success = $v.success;
      _code = $v.code;
      _message = $v.message;
      _data = $v.data?.toBuilder();
      _$v = null;
    }
    return this;
  }

  @override
  void replace(BaseResponseAdminUpdateMenuResponse other) {
    _$v = other as _$BaseResponseAdminUpdateMenuResponse;
  }

  @override
  void update(
      void Function(BaseResponseAdminUpdateMenuResponseBuilder)? updates) {
    if (updates != null) updates(this);
  }

  @override
  BaseResponseAdminUpdateMenuResponse build() => _build();

  _$BaseResponseAdminUpdateMenuResponse _build() {
    _$BaseResponseAdminUpdateMenuResponse _$result;
    try {
      _$result = _$v ??
          _$BaseResponseAdminUpdateMenuResponse._(
            success: success,
            code: code,
            message: message,
            data: _data?.build(),
          );
    } catch (_) {
      late String _$failedField;
      try {
        _$failedField = 'data';
        _data?.build();
      } catch (e) {
        throw BuiltValueNestedFieldError(r'BaseResponseAdminUpdateMenuResponse',
            _$failedField, e.toString());
      }
      rethrow;
    }
    replace(_$result);
    return _$result;
  }
}

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
