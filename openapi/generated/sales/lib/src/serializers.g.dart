// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'serializers.dart';

// **************************************************************************
// BuiltValueGenerator
// **************************************************************************

Serializers _$serializers = (Serializers().toBuilder()
      ..add(AccountDto.serializer)
      ..add(ActionDto.serializer)
      ..add(AdminAssignRolesRequest.serializer)
      ..add(AdminAssignRolesResponse.serializer)
      ..add(AdminCreateMenuRequest.serializer)
      ..add(AdminCreateMenuRequestPlatformEnum.serializer)
      ..add(AdminCreateMenuResponse.serializer)
      ..add(AdminCreateUserRequest.serializer)
      ..add(AdminCreateUserResponse.serializer)
      ..add(AdminDeleteMenuResponse.serializer)
      ..add(AdminDeleteUserResponse.serializer)
      ..add(AdminGetActionsResponse.serializer)
      ..add(AdminGetMenuResponse.serializer)
      ..add(AdminGetUserByIdResponse.serializer)
      ..add(AdminGetUserRolePermissionResponse.serializer)
      ..add(AdminImportUsersRequest.serializer)
      ..add(AdminImportUsersResponse.serializer)
      ..add(AdminListMenusResponse.serializer)
      ..add(AdminListParentMenusResponse.serializer)
      ..add(AdminResetPasswordResponse.serializer)
      ..add(AdminSearchUsersRequest.serializer)
      ..add(AdminSearchUsersResponse.serializer)
      ..add(AdminUpdateMenuRequest.serializer)
      ..add(AdminUpdateMenuRequestPlatformEnum.serializer)
      ..add(AdminUpdateMenuResponse.serializer)
      ..add(AdminUpdateUserRequest.serializer)
      ..add(AdminUpdateUserResponse.serializer)
      ..add(AppBranchDto.serializer)
      ..add(AppGetListBranchResponse.serializer)
      ..add(AppGetListPaymentAccountRequest.serializer)
      ..add(AppGetListPaymentAccountResponse.serializer)
      ..add(AppGetListProvinceResponse.serializer)
      ..add(AppGetListWardResponse.serializer)
      ..add(AppGetReferrerByCodeResponse.serializer)
      ..add(AppGetSystemConfigsByGroupCodeResponse.serializer)
      ..add(AppGetUserProfileResponse.serializer)
      ..add(AppKplusRegisterCollaboratorRequest.serializer)
      ..add(AppKplusRegisterCollaboratorRequestIdCardTypeEnum.serializer)
      ..add(AppKplusRegisterCollaboratorResponse.serializer)
      ..add(AppLoginRequest.serializer)
      ..add(AppLoginResponse.serializer)
      ..add(AppLogoutRequest.serializer)
      ..add(AppLogoutResponse.serializer)
      ..add(AppProvinceDto.serializer)
      ..add(AppReferralRegisterCollabRequest.serializer)
      ..add(AppReferralRegisterCollabRequestIdCardTypeEnum.serializer)
      ..add(AppReferralRegisterCollabResponse.serializer)
      ..add(AppRefreshTokenRequest.serializer)
      ..add(AppRefreshTokenResponse.serializer)
      ..add(AppRegisterCollaboratorRequest.serializer)
      ..add(AppRegisterCollaboratorRequestIdCardTypeEnum.serializer)
      ..add(AppRegisterCollaboratorResponse.serializer)
      ..add(AppWardDto.serializer)
      ..add(BaseResponseAdminAssignRolesResponse.serializer)
      ..add(BaseResponseAdminCreateMenuResponse.serializer)
      ..add(BaseResponseAdminCreateUserResponse.serializer)
      ..add(BaseResponseAdminDeleteMenuResponse.serializer)
      ..add(BaseResponseAdminDeleteUserResponse.serializer)
      ..add(BaseResponseAdminGetActionsResponse.serializer)
      ..add(BaseResponseAdminGetMenuResponse.serializer)
      ..add(BaseResponseAdminGetUserByIdResponse.serializer)
      ..add(BaseResponseAdminGetUserRolePermissionResponse.serializer)
      ..add(BaseResponseAdminImportUsersResponse.serializer)
      ..add(BaseResponseAdminListMenusResponse.serializer)
      ..add(BaseResponseAdminListParentMenusResponse.serializer)
      ..add(BaseResponseAdminResetPasswordResponse.serializer)
      ..add(BaseResponseAdminSearchUsersResponse.serializer)
      ..add(BaseResponseAdminUpdateMenuResponse.serializer)
      ..add(BaseResponseAdminUpdateUserResponse.serializer)
      ..add(BaseResponseAppGetListBranchResponse.serializer)
      ..add(BaseResponseAppGetListPaymentAccountResponse.serializer)
      ..add(BaseResponseAppGetListProvinceResponse.serializer)
      ..add(BaseResponseAppGetListWardResponse.serializer)
      ..add(BaseResponseAppGetReferrerByCodeResponse.serializer)
      ..add(BaseResponseAppGetSystemConfigsByGroupCodeResponse.serializer)
      ..add(BaseResponseAppGetUserProfileResponse.serializer)
      ..add(BaseResponseAppKplusRegisterCollaboratorResponse.serializer)
      ..add(BaseResponseAppLoginResponse.serializer)
      ..add(BaseResponseAppLogoutResponse.serializer)
      ..add(BaseResponseAppReferralRegisterCollabResponse.serializer)
      ..add(BaseResponseAppRefreshTokenResponse.serializer)
      ..add(BaseResponseAppRegisterCollaboratorResponse.serializer)
      ..add(BaseResponseGetArticleByCodeResponse.serializer)
      ..add(GetArticleByCodeResponse.serializer)
      ..add(MenuDto.serializer)
      ..add(PageUser.serializer)
      ..add(PageableObject.serializer)
      ..add(RoleDto.serializer)
      ..add(SortObject.serializer)
      ..add(SystemConfigDto.serializer)
      ..add(User.serializer)
      ..add(UserStatusEnum.serializer)
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AccountDto)]),
          () => ListBuilder<AccountDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ActionDto)]),
          () => ListBuilder<ActionDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ActionDto)]),
          () => ListBuilder<ActionDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(ActionDto)]),
          () => ListBuilder<ActionDto>())
      ..addBuilderFactory(
          const FullType(
              BuiltList, const [const FullType(AdminCreateUserRequest)]),
          () => ListBuilder<AdminCreateUserRequest>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AppBranchDto)]),
          () => ListBuilder<AppBranchDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AppProvinceDto)]),
          () => ListBuilder<AppProvinceDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(AppWardDto)]),
          () => ListBuilder<AppWardDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(MenuDto)]),
          () => ListBuilder<MenuDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(MenuDto)]),
          () => ListBuilder<MenuDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(MenuDto)]),
          () => ListBuilder<MenuDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(RoleDto)]),
          () => ListBuilder<RoleDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(MenuDto)]),
          () => ListBuilder<MenuDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(String)]),
          () => ListBuilder<String>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(SystemConfigDto)]),
          () => ListBuilder<SystemConfigDto>())
      ..addBuilderFactory(
          const FullType(BuiltList, const [const FullType(User)]),
          () => ListBuilder<User>()))
    .build();

// ignore_for_file: deprecated_member_use_from_same_package,type=lint
