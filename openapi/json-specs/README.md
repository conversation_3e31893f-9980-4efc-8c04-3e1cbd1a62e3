# OpenAPI JSON Specifications

Th<PERSON> mục này chứa các file JSON specification được export từ Swagger UI của backend APIs.

## 📋 Current Specifications

### 1. Sales App API - `sale-app-api-spec.json`
- **Base URL**: `http://localhost:8888`
- **Description**: API chính của Sales App bao gồm authentication, user management, registration, v.v.
- **Generated Client**: `lib/generated/api/sales/`

### 2. Media API - `media-spec.json`
- **Base URL**: `https://dev-ksapi.ssf.vn/media`
- **Description**: API để upload/download files, images và media
- **Generated Client**: `lib/generated/api/media/`

## 🔄 Workflow để cập nhật API client

### 1. Lấy JSON từ Swagger UI

**Cách 1: Download trực tiếp**
```bash
# Truy cập Swagger UI của backend
# Ví dụ: http://your-backend-url/swagger-ui.html

# Click vào link "Download" ở phía trên
# Chọn format "JSON"
# Lưu file với tên chính xác
```

**Cách 2: Copy từ browser**
```bash
# 1. Mở Developer Tools (F12)
# 2. Vào tab Network
# 3. Refresh trang Swagger UI
# 4. Tìm request với tên "swagger.json" hoặc "api-docs"
# 5. Copy response JSON
# 6. Paste vào file tương ứng
```

**Cách 3: Sử dụng curl**
```bash
# Sales App API
curl -o openapi/json-specs/sale-app-api-spec.json \
  http://localhost:8888/v3/api-docs

# Media API  
curl -o openapi/json-specs/media-spec.json \
  https://dev-ksapi.ssf.vn/media/v3/api-docs
```

### 2. Đặt files vào đúng vị trí

```bash
# Đảm bảo tên file chính xác:
openapi/json-specs/sale-app-api-spec.json    # Sales App API
openapi/json-specs/media-spec.json           # Media API
```

### 3. Generate API clients

```bash
# Generate tất cả APIs
./scripts/generate_api.sh

# Hoặc generate riêng lẻ:
./scripts/generate_api.sh sales   # Chỉ Sales App API
./scripts/generate_api.sh media   # Chỉ Media API
```

### 4. Kiểm tra kết quả

```bash
# Kiểm tra generated files
ls -la lib/generated/api/sales/lib/src/api/
ls -la lib/generated/api/media/lib/src/api/

# Kiểm tra config files
ls -la lib/data/network/openapi/

# Chạy analysis
fvm flutter analyze
```

## 🛠️ Troubleshooting

### Lỗi thường gặp:

**1. File JSON không tồn tại**
```
Missing JSON specification files:
  - sale-app-api-spec.json
```
→ **Giải pháp**: Export lại file JSON từ Swagger UI

**2. JSON syntax error**
```
Error parsing JSON specification
```
→ **Giải pháp**: Kiểm tra JSON syntax tại [jsonlint.com](https://jsonlint.com/)

**3. OpenAPI version không hỗ trợ**
```
Unsupported OpenAPI version
```
→ **Giải pháp**: Đảm bảo backend sử dụng OpenAPI 3.0+

**4. Generated code có lỗi compile**
```
undefined_class errors
```
→ **Giải pháp**: Thêm dependencies cần thiết vào `pubspec.yaml`

### Validation JSON specs:

```bash
# Online validation
# 1. Copy nội dung JSON file
# 2. Paste vào https://editor.swagger.io/
# 3. Kiểm tra errors/warnings

# CLI validation (nếu có openapi-generator-cli)
openapi-generator-cli validate -i openapi/json-specs/sale-app-api-spec.json
openapi-generator-cli validate -i openapi/json-specs/media-spec.json
```

## 📁 File Structure

```
openapi/json-specs/
├── README.md                    # File này
├── sale-app-api-spec.json      # Sales App API specification
└── media-spec.json             # Media API specification
```

## 🔗 Generated Code Structure

```
lib/generated/api/
├── sales/                      # Sales App API client
│   ├── lib/
│   │   ├── sales_app_api.dart  # Main API client
│   │   └── src/
│   │       ├── api/            # API endpoints
│   │       ├── auth/           # Authentication
│   │       └── model/          # Data models
│   └── pubspec.yaml
└── media/                      # Media API client
    ├── lib/
    │   ├── media_api.dart      # Main API client  
    │   └── src/
    │       ├── api/            # API endpoints
    │       ├── auth/           # Authentication
    │       └── model/          # Data models
    └── pubspec.yaml
```

## 💡 Tips

- **Tự động hóa**: Tạo script để tự động download JSON specs từ backend
- **Version control**: Theo dõi thay đổi của JSON specs trong git history
- **Testing**: Luôn test generated clients trước khi deploy
- **Dependencies**: Generated code có thể cần thêm dependencies như `built_value`, `built_collection` 