{"openapi": "3.1.0", "info": {"title": "Sale App API Documentation", "description": "Tài liệu API", "version": "1.0"}, "servers": [{"url": "https://api-internal-staging.kienlongbank.co", "description": "Generated server url"}], "security": [{"bearerAuth": []}], "tags": [{"name": "Ward", "description": "Phường xã"}, {"name": "Account", "description": "<PERSON><PERSON><PERSON>"}, {"name": "Users", "description": "Danh sách API quản lý user"}, {"name": "Branch", "description": "<PERSON> n<PERSON>h"}, {"name": "Article", "description": "<PERSON><PERSON><PERSON> hình nội dung"}, {"name": "Authentication", "description": "Authentication"}, {"name": "Province", "description": "Tỉnh thành phố"}, {"name": "Admin User Management", "description": "Admin user operations"}, {"name": "Registers", "description": "Registers"}], "paths": {"/system/admin/api/v1/users/{id}": {"get": {"tags": ["Admin User Management"], "summary": "Get user by ID", "description": "Admin can get any user", "operationId": "getUserById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminGetUserByIdResponse"}}}}}}, "put": {"tags": ["Admin User Management"], "summary": "Update user", "description": "Admin can update any user", "operationId": "updateUser", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminUpdateUserRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminUpdateUserResponse"}}}}}}, "delete": {"tags": ["Admin User Management"], "summary": "Delete user", "description": "Admin can delete any user", "operationId": "deleteUser", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminDeleteUserResponse"}}}}}}}, "/system/admin/api/v1/users/{id}/roles": {"put": {"tags": ["Admin User Management"], "summary": "Assign roles", "description": "Admin can assign roles to users", "operationId": "assignRoles", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminAssignRolesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminAssignRolesResponse"}}}}}}}, "/system/admin/api/v1/menu/{id}": {"get": {"tags": ["<PERSON><PERSON> Menu Management"], "summary": "API lấy chi tiết menu", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết menu theo ID", "operationId": "getMenu", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminGetMenuResponse"}}}}}}, "put": {"tags": ["<PERSON><PERSON> Menu Management"], "summary": "API cập nhật menu", "description": "API cập nhật thông tin menu và actions", "operationId": "updateMenu", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminUpdateMenuRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminUpdateMenuResponse"}}}}}}, "delete": {"tags": ["<PERSON><PERSON> Menu Management"], "summary": "API xóa menu", "description": "Xóa menu (soft delete - chỉ cập nhật is_deleted = true)", "operationId": "deleteMenu", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminDeleteMenuResponse"}}}}}}}, "/system/api/v1/registers/kplus/collaborators": {"post": {"tags": ["Registers"], "summary": "API đăng ký công tác viên trên app K+", "description": "API đăng ký công tác viên trên app K+", "operationId": "kplusRegister", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppKplusRegisterCollaboratorRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppKplusRegisterCollaboratorResponse"}}}}}}}, "/system/api/v1/registers/collaborators": {"post": {"tags": ["Registers"], "summary": "API đăng ký công tác viên", "description": "API đăng ký công tác viên", "operationId": "register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppRegisterCollaboratorRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppRegisterCollaboratorResponse"}}}}}}}, "/system/api/v1/registers/collaborators/referrals": {"post": {"tags": ["Registers"], "summary": "API giới thiệu công tác viên", "description": "API giới thiệu công tác viên", "operationId": "registerReferral", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppReferralRegisterCollabRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppReferralRegisterCollabResponse"}}}}}}}, "/system/api/v1/auth/refreshToken": {"post": {"tags": ["Authentication"], "summary": "API refresh token", "description": "ApI for refreshing access token for App Users", "operationId": "refreshToken", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppRefreshTokenRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppRefreshTokenResponse"}}}}}}}, "/system/api/v1/auth/logout": {"post": {"tags": ["Authentication"], "summary": "API logout", "description": "Api logout for App Users", "operationId": "logout", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppLogoutRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppLogoutResponse"}}}}}}}, "/system/api/v1/auth/login": {"post": {"tags": ["Authentication"], "summary": "API login", "description": "Login API for App Users", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppLoginRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppLoginResponse"}}}}}}}, "/system/api/v1/account/getPaymentAccount": {"post": {"tags": ["Account"], "summary": "API lây danh sách tài k<PERSON>h toán theo số GTTT", "operationId": "getPaymentAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppGetListPaymentAccountRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppGetListPaymentAccountResponse"}}}}}}}, "/system/admin/api/v1/users": {"get": {"tags": ["Admin User Management"], "summary": "Search users", "description": "Admin can search all users", "operationId": "search", "parameters": [{"name": "arg0", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/AdminSearchUsersRequest"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminSearchUsersResponse"}}}}}}, "post": {"tags": ["Admin User Management"], "summary": "Create user", "description": "Admin can create any user", "operationId": "createUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreateUserRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminCreateUserResponse"}}}}}}}, "/system/admin/api/v1/users/{id}/reset-password": {"post": {"tags": ["Admin User Management"], "summary": "Reset password", "description": "Admin can reset user passwords", "operationId": "resetPassword", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminResetPasswordResponse"}}}}}}}, "/system/admin/api/v1/users/import": {"post": {"tags": ["Admin User Management"], "summary": "Import users", "description": "Admin can import multiple users", "operationId": "importUsers", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminImportUsersRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminImportUsersResponse"}}}}}}}, "/system/admin/api/v1/menu": {"post": {"tags": ["<PERSON><PERSON> Menu Management"], "summary": "API tạo menu", "description": "API tạo mới menu (APP/WEB)", "operationId": "createMenu", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCreateMenuRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminCreateMenuResponse"}}}}}}}, "/system/api/v1/ward": {"get": {"tags": ["Ward"], "summary": "API lấy danh sách phường xã", "description": "API lấy danh sách phường xã", "operationId": "list", "parameters": [{"name": "provinceId", "in": "query", "description": "Mã tỉnh thành phố", "required": false, "schema": {"type": "string", "format": "uuid"}, "example": "01"}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppGetListWardResponse"}}}}}}}, "/system/api/v1/users/profile": {"get": {"tags": ["Users"], "summary": "<PERSON><PERSON> l<PERSON><PERSON> thông tin profile của user đang đăng nhập", "description": "<PERSON><PERSON> l<PERSON><PERSON> thông tin profile của user đang đăng nhập", "operationId": "getProfile", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppGetUserProfileResponse"}}}}}}}, "/system/api/v1/users/getReferrerByCode": {"get": {"tags": ["Users"], "summary": "<PERSON><PERSON> l<PERSON>y thông tin người giới thiệu", "description": "<PERSON><PERSON> l<PERSON>y thông tin người giới thiệu", "operationId": "getReferrerByCode", "parameters": [{"name": "referrerCode", "in": "query", "description": "ID of user to get information", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppGetReferrerByCodeResponse"}}}}}}}, "/system/api/v1/system-configs/getByCode": {"get": {"tags": ["System Config"], "summary": "API lấy danh sách cấu hình theo group code", "description": "<PERSON><PERSON><PERSON> tất cả cấu hình hệ thống theo group code", "operationId": "getSystemConfigsByGroupCode", "parameters": [{"name": "groupCode", "in": "query", "description": "<PERSON><PERSON> nhóm cấu hình", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppGetSystemConfigsByGroupCodeResponse"}}}}}}}, "/system/api/v1/province": {"get": {"tags": ["Province"], "summary": "API lấy danh sách tỉnh/thành phố", "description": "API lấy danh sách tỉnh/thành phố", "operationId": "list_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppGetListProvinceResponse"}}}}}}}, "/system/api/v1/branch": {"get": {"tags": ["Branch"], "summary": "<PERSON> lấy danh sách chi nh<PERSON>h", "description": "<PERSON> lấy danh sách chi nh<PERSON>h", "operationId": "list_2", "parameters": [{"name": "provinceId", "in": "query", "description": "Mã tỉnh thành phố", "required": false, "schema": {"type": "string", "format": "uuid"}, "example": "01"}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAppGetListBranchResponse"}}}}}}}, "/system/api/v1/article/getByCode": {"get": {"tags": ["Article"], "summary": "API lấy nội dung đã đc cấu hình", "description": "API lấy nội dung", "operationId": "getArticle", "parameters": [{"name": "code", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseGetArticleByCodeResponse"}}}}}}}, "/system/admin/api/v1/users/rolePermissions": {"get": {"tags": ["Admin User Management"], "summary": "Get menus", "description": "<PERSON><PERSON><PERSON> danh sách menu", "operationId": "getUserRolePermissions", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminGetUserRolePermissionResponse"}}}}}}}, "/system/admin/api/v1/menus": {"get": {"tags": ["<PERSON><PERSON> Menu Management"], "summary": "API lấy danh sách menu hierarchical", "description": "<PERSON><PERSON><PERSON> danh sách menu và submenu với cấu trúc cha/con (không phân trang)", "operationId": "listMenus", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminListMenusResponse"}}}}}}}, "/system/admin/api/v1/menus/parents": {"get": {"tags": ["<PERSON><PERSON> Menu Management"], "summary": "API lấy danh sách parent menu", "description": "<PERSON><PERSON>y tất cả parent menu (menu không có parentId)", "operationId": "listParentMenus", "parameters": [{"name": "platform", "in": "query", "description": "Platform filter (APP/WEB)", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminListParentMenusResponse"}}}}}}}, "/system/admin/api/v1/actions": {"get": {"tags": ["<PERSON><PERSON> Menu Management"], "summary": "API lấy danh sách action", "description": "<PERSON><PERSON><PERSON> tất cả <PERSON> không phân trang", "operationId": "getActions", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BaseResponseAdminGetActionsResponse"}}}}}}}}, "components": {"schemas": {"AdminUpdateUserRequest": {"type": "object", "properties": {"username": {"type": "string"}, "fullName": {"type": "string", "minLength": 1}, "email": {"type": "string"}, "phone": {"type": "string"}, "departmentId": {"type": "string", "minLength": 1}, "enabled": {"type": "boolean"}}}, "AdminUpdateUserResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}, "user": {"$ref": "#/components/schemas/User"}}}, "BaseResponseAdminUpdateUserResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminUpdateUserResponse"}}}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "isDeleted": {"type": "boolean"}, "keycloakId": {"type": "string"}, "username": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "SUSPENDED"]}}}, "AdminAssignRolesRequest": {"type": "object", "properties": {"userId": {"type": "string", "minLength": 1}, "roleIds": {"type": "array", "items": {"type": "string"}, "minItems": 1}}}, "AdminAssignRolesResponse": {"type": "object", "properties": {"userId": {"type": "string"}, "roleIds": {"type": "array", "items": {"type": "string"}}, "success": {"type": "boolean"}}}, "BaseResponseAdminAssignRolesResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminAssignRolesResponse"}}}, "AdminUpdateMenuRequest": {"type": "object", "properties": {"platform": {"type": "string", "description": "Menu theo platform", "enum": ["APP", "WEB"]}, "name": {"type": "string", "description": "Tên menu", "minLength": 1}, "description": {"type": "string", "description": "<PERSON>ô tả menu"}, "parentId": {"type": "string", "format": "uuid", "description": "<PERSON><PERSON> cha (n<PERSON>u có)"}, "order": {"type": "integer", "format": "int32", "description": "Sử dụng để order trên UI"}, "actionIds": {"type": "array", "description": "Danh sách ID của các action gắn với menu", "items": {"type": "string", "format": "uuid"}}}, "required": ["platform"]}, "AdminUpdateMenuResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}, "platform": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "parentId": {"type": "string", "format": "uuid"}, "order": {"type": "integer", "format": "int32"}, "actionIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}, "BaseResponseAdminUpdateMenuResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminUpdateMenuResponse"}}}, "AppKplusRegisterCollaboratorRequest": {"type": "object", "properties": {"fullName": {"type": "string", "description": "<PERSON><PERSON> tên", "example": "<PERSON><PERSON>"}, "idCardType": {"type": "string", "description": "Loại GTTT", "enum": ["CHIP_ID", "PASSPORT"], "example": "CHIP_ID;PASSPORT"}, "idCardNo": {"type": "string", "description": "GTTT", "example": "************"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********", "pattern": "^0\\d{9}$"}, "permanentAddress": {"type": "string", "description": "Địa chỉ thường trú", "example": 123}, "branchCode": {"type": "string", "description": "Mã chi nh<PERSON>h", "example": "CN001"}, "referrerCode": {"type": "string", "description": "Mã người giới thiệu. Mã cif", "example": "*********"}, "email": {"type": "string", "description": "Email", "example": "<EMAIL>"}, "frontCardUrl": {"type": "string", "description": "Ảnh mặt trước của giấy tờ", "example": "https://example.com/front-card.jpg"}, "backCardUrl": {"type": "string", "description": "Ảnh mặt sau của giấy tờ", "example": "https://example.com/back-card.jpg"}}, "required": ["branchCode", "fullName", "idCardNo", "idCardType", "phoneNumber"]}, "AppKplusRegisterCollaboratorResponse": {"type": "object", "properties": {"fullName": {"type": "string", "description": "<PERSON><PERSON> tên", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "branchName": {"type": "string", "description": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "example": "Chi nhánh Kien Long Bank - Quận 1"}, "positionName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON> danh", "example": "CTV"}}}, "BaseResponseAppKplusRegisterCollaboratorResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppKplusRegisterCollaboratorResponse"}}}, "AppRegisterCollaboratorRequest": {"type": "object", "properties": {"fullName": {"type": "string", "description": "<PERSON><PERSON> tên", "example": "<PERSON><PERSON>"}, "idCardType": {"type": "string", "description": "Loại GTTT", "enum": ["CHIP_ID", "PASSPORT"], "example": "CHIP_ID"}, "idCardNo": {"type": "string", "description": "GTTT", "example": "************"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********", "pattern": "^0\\d{9}$"}, "permanentAddress": {"type": "string", "description": "Địa chỉ thường trú", "example": 123}, "branchId": {"type": "string", "format": "uuid", "description": "Mã chi nh<PERSON>h", "example": "23836091-45ee-489d-9ac9-3a1a4a643955"}, "referrerCode": {"type": "string", "description": "Mã người giới thiệu. Mã cif", "example": "*********"}, "email": {"type": "string", "description": "Email", "example": "<EMAIL>"}, "frontCardUrl": {"type": "string", "description": "Ảnh mặt trước của giấy tờ", "example": "https://example.com/front-card.jpg"}, "backCardUrl": {"type": "string", "description": "Ảnh mặt sau của giấy tờ", "example": "https://example.com/back-card.jpg"}}, "required": ["backCardUrl", "branchId", "frontCardUrl", "fullName", "idCardNo", "idCardType", "phoneNumber"]}, "AppRegisterCollaboratorResponse": {"type": "object", "properties": {"fullName": {"type": "string", "description": "<PERSON><PERSON> tên", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "branchName": {"type": "string", "description": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "example": "Chi nhánh Kien Long Bank - Quận 1"}, "positionName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON> danh", "example": "CTV"}}}, "BaseResponseAppRegisterCollaboratorResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppRegisterCollaboratorResponse"}}}, "AppReferralRegisterCollabRequest": {"type": "object", "properties": {"fullName": {"type": "string", "description": "<PERSON><PERSON> tên", "example": "<PERSON><PERSON>"}, "idCardType": {"type": "string", "description": "Loại GTTT", "enum": ["CHIP_ID", "PASSPORT"], "example": "CHIP_ID"}, "idCardNo": {"type": "string", "description": "GTTT", "example": "************"}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "example": "**********", "pattern": "^0\\d{9}$"}, "permanentAddress": {"type": "string", "description": "Địa chỉ thường trú", "example": 123}, "branchId": {"type": "string", "format": "uuid", "description": "Mã chi nh<PERSON>h", "example": "8f2aad23-189d-4370-8bfc-25643852e7dc"}, "email": {"type": "string", "description": "Email", "example": "<EMAIL>"}, "frontCardUrl": {"type": "string", "description": "Ảnh mặt trước của giấy tờ", "example": "https://example.com/front-card.jpg"}, "backCardUrl": {"type": "string", "description": "Ảnh mặt sau của giấy tờ", "example": "https://example.com/back-card.jpg"}}, "required": ["backCardUrl", "branchId", "frontCardUrl", "fullName", "idCardNo", "idCardType", "phoneNumber"]}, "AppReferralRegisterCollabResponse": {"type": "object", "properties": {"fullName": {"type": "string", "description": "<PERSON><PERSON> tên", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "branchName": {"type": "string", "description": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "example": "Chi nhánh Kien Long Bank - Quận 1"}, "positionName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ch<PERSON> danh", "example": "CTV"}, "referrerCode": {"type": "string", "description": "<PERSON>ã người giới thiệu", "example": "********"}, "referrerName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i giới thiệu", "example": "<PERSON>ê <PERSON> B"}}}, "BaseResponseAppReferralRegisterCollabResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppReferralRegisterCollabResponse"}}}, "AppRefreshTokenRequest": {"type": "object", "properties": {"refreshToken": {"type": "string", "description": "The refresh token to be used for obtaining a new access token"}}, "required": ["refreshToken"]}, "AppRefreshTokenResponse": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "Access token for the user", "example": "eyJraWQiOiJrZW5sb25nYmFua2FwcCIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"}, "expiresIn": {"type": "integer", "format": "int64", "description": "Time in seconds until the access token expires", "example": 3600}, "refreshToken": {"type": "string", "description": "Refresh token for obtaining a new access token", "example": "dGhpcyBpcyBhIHJlZnJlc2ggdG9rZW4"}, "refreshExpiresIn": {"type": "integer", "format": "int64", "description": "Time in seconds until the refresh token expires", "example": 7200}, "tokenType": {"type": "string", "description": "Type of the token, typically 'Bearer'", "example": "Bearer"}}}, "BaseResponseAppRefreshTokenResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppRefreshTokenResponse"}}}, "AppLogoutRequest": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "Access token to be blacklisted", "minLength": 1, "pattern": "^[A-Za-z0-9._-]+\\.[A-Za-z0-9._-]+\\.[A-Za-z0-9._-]+$"}, "refreshToken": {"type": "string", "description": "Refresh token to be revoked at Keycloak", "minLength": 1, "pattern": "^[A-Za-z0-9._-]+\\.[A-Za-z0-9._-]+\\.[A-Za-z0-9._-]+$"}}, "required": ["accessToken", "refreshToken"]}, "AppLogoutResponse": {"type": "object", "properties": {"logoutAt": {"type": "string", "format": "date-time"}}}, "BaseResponseAppLogoutResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppLogoutResponse"}}}, "AppLoginRequest": {"type": "object", "properties": {"username": {"type": "string", "description": "Username for login", "example": "john_doe"}, "password": {"type": "string", "description": "Password for login", "example": "securePassword123"}}, "required": ["password", "username"]}, "AppLoginResponse": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "Access token for the user", "example": "eyJraWQiOiJrZW5sb25nYmFua2FwcCIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"}, "expiresIn": {"type": "integer", "format": "int64", "description": "Time in seconds until the access token expires", "example": 3600}, "refreshToken": {"type": "string", "description": "Refresh token for obtaining a new access token", "example": "dGhpcyBpcyBhIHJlZnJlc2ggdG9rZW4"}, "refreshExpiresIn": {"type": "integer", "format": "int64", "description": "Time in seconds until the refresh token expires", "example": 7200}, "tokenType": {"type": "string", "description": "Type of the token, typically 'Bearer'", "example": "Bearer"}, "code": {"type": "string", "description": "Mã nhân viên", "example": 14312}, "cifNo": {"type": "string", "description": "Unique identifier for the user", "example": **********}, "fullName": {"type": "string", "description": "Full name of the user", "example": "<PERSON>"}, "email": {"type": "string", "description": "Email of the user", "example": "<EMAIL>"}, "phoneNumber": {"type": "string", "description": "Phone number of the user", "example": "**********"}}}, "BaseResponseAppLoginResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppLoginResponse"}}}, "AppGetListPaymentAccountRequest": {"type": "object", "properties": {"idCardNo": {"type": "string", "description": "Số gi<PERSON>y tờ", "minLength": 1}}, "required": ["idCardNo"]}, "AccountDto": {"type": "object", "properties": {"accountNo": {"type": "string"}, "accountName": {"type": "string"}}}, "AppGetListPaymentAccountResponse": {"type": "object", "properties": {"accounts": {"type": "array", "description": "<PERSON><PERSON> s<PERSON> account", "items": {"$ref": "#/components/schemas/AccountDto"}}}}, "BaseResponseAppGetListPaymentAccountResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppGetListPaymentAccountResponse"}}}, "AdminCreateUserRequest": {"type": "object", "properties": {"username": {"type": "string", "minLength": 1}, "password": {"type": "string", "minLength": 1, "pattern": "^(?=.*[A-Za-z])(?=.*\\d)(?=.*[@$!%*#?&])[A-Za-z\\d@$!%*#?&]{8,}$"}, "fullName": {"type": "string", "minLength": 1}, "email": {"type": "string"}, "phone": {"type": "string"}, "departmentId": {"type": "string", "minLength": 1}}}, "AdminCreateUserResponse": {"type": "object", "properties": {"id": {"type": "string"}, "username": {"type": "string"}, "fullName": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "departmentId": {"type": "string"}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}}}, "BaseResponseAdminCreateUserResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminCreateUserResponse"}}}, "AdminResetPasswordResponse": {"type": "object", "properties": {"userId": {"type": "string"}, "success": {"type": "boolean"}}}, "BaseResponseAdminResetPasswordResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminResetPasswordResponse"}}}, "AdminImportUsersRequest": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/AdminCreateUserRequest"}, "minItems": 1}}}, "AdminImportUsersResponse": {"type": "object", "properties": {"totalRequested": {"type": "integer", "format": "int32"}, "totalImported": {"type": "integer", "format": "int32"}}}, "BaseResponseAdminImportUsersResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminImportUsersResponse"}}}, "AdminCreateMenuRequest": {"type": "object", "properties": {"code": {"type": "string", "description": "Mã menu", "minLength": 1}, "platform": {"type": "string", "description": "Menu theo platform", "enum": ["APP", "WEB"]}, "name": {"type": "string", "description": "Tên menu", "minLength": 1}, "description": {"type": "string", "description": "<PERSON>ô tả menu"}, "parentId": {"type": "string", "format": "uuid", "description": "<PERSON><PERSON> cha (n<PERSON>u có)"}, "orderNo": {"type": "integer", "format": "int32", "description": "Sử dụng để order trên UI"}, "actionIds": {"type": "array", "description": "Danh sách ID của các action gắn với menu", "items": {"type": "string", "format": "uuid"}}}, "required": ["platform"]}, "AdminCreateMenuResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}, "platform": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "parentId": {"type": "string", "format": "uuid"}, "order": {"type": "integer", "format": "int32"}, "actionIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}, "BaseResponseAdminCreateMenuResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminCreateMenuResponse"}}}, "AppGetListWardResponse": {"type": "object", "properties": {"wards": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch phường xã", "items": {"$ref": "#/components/schemas/AppWardDto"}}}}, "AppWardDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Id c<PERSON>a phường xã"}, "code": {"type": "string", "description": "Mã phường xã", "example": "BR001"}, "name": {"type": "string", "description": "<PERSON><PERSON>n ph<PERSON><PERSON>ng xã", "example": "<PERSON>"}, "provinceId": {"type": "string", "format": "uuid", "description": "ID của tỉnh thành phố", "example": "PROV001"}}}, "BaseResponseAppGetListWardResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppGetListWardResponse"}}}, "AppBranchDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Id c<PERSON>a chi nh<PERSON>h"}, "code": {"type": "string", "description": "Mã chi nh<PERSON>h", "example": "BR001"}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "example": "<PERSON>"}, "address": {"type": "string", "description": "Địa chỉ chi nh<PERSON>h", "example": "Số 16 Phạm Hùng"}, "provinceId": {"type": "string", "format": "uuid", "description": "ID của tỉnh thành phố", "example": "PROV001"}}}, "AppGetUserProfileResponse": {"type": "object", "properties": {"code": {"type": "string", "description": "Mã nhân viên", "example": 14312}, "cifNo": {"type": "string", "description": "Unique identifier for the user", "example": **********}, "fullName": {"type": "string", "description": "Full name of the user", "example": "<PERSON>"}, "email": {"type": "string", "description": "Email of the user", "example": "<EMAIL>"}, "phoneNumber": {"type": "string", "description": "Phone number of the user", "example": "**********"}, "branch": {"$ref": "#/components/schemas/AppBranchDto", "description": "<PERSON> nh<PERSON>h người dùng"}}}, "BaseResponseAppGetUserProfileResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppGetUserProfileResponse"}}}, "AppGetReferrerByCodeResponse": {"type": "object", "properties": {"referrerCode": {"type": "string", "description": "<PERSON>ã người giới thiệu", "example": "04321234"}, "referrerName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i giới thiệu"}}}, "BaseResponseAppGetReferrerByCodeResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppGetReferrerByCodeResponse"}}}, "AppGetSystemConfigsByGroupCodeResponse": {"type": "object", "properties": {"groupCode": {"type": "string"}, "configs": {"type": "array", "items": {"$ref": "#/components/schemas/SystemConfigDto"}}}}, "BaseResponseAppGetSystemConfigsByGroupCodeResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppGetSystemConfigsByGroupCodeResponse"}}}, "SystemConfigDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}, "label": {"type": "string"}}}, "AppGetListProvinceResponse": {"type": "object", "properties": {"provinces": {"type": "array", "items": {"$ref": "#/components/schemas/AppProvinceDto"}}}}, "AppProvinceDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Id của tỉnh/thành phố"}, "gsoCode": {"type": "string", "description": "Mã GSO", "example": "01"}, "name": {"type": "string", "description": "Tên tỉnh thành phố", "example": "<PERSON><PERSON>"}}}, "BaseResponseAppGetListProvinceResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppGetListProvinceResponse"}}}, "AppGetListBranchResponse": {"type": "object", "properties": {"branches": {"type": "array", "items": {"$ref": "#/components/schemas/AppBranchDto"}}}}, "BaseResponseAppGetListBranchResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AppGetListBranchResponse"}}}, "BaseResponseGetArticleByCodeResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/GetArticleByCodeResponse"}}}, "GetArticleByCodeResponse": {"type": "object", "properties": {"code": {"type": "string"}, "title": {"type": "string"}, "content": {"type": "string"}}}, "AdminSearchUsersRequest": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32", "minimum": 0}, "size": {"type": "integer", "format": "int32", "maximum": 100, "minimum": 1}, "orderBy": {"type": "string", "pattern": "^[a-zA-Z_][a-zA-Z0-9_]*$"}, "orderDir": {"type": "string", "pattern": "^(ASC|DESC)$"}, "keyword": {"type": "string"}, "departmentId": {"type": "string"}, "enabled": {"type": "boolean"}}}, "AdminSearchUsersResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}, "users": {"$ref": "#/components/schemas/PageUser"}}}, "BaseResponseAdminSearchUsersResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminSearchUsersResponse"}}}, "PageUser": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"unpaged": {"type": "boolean"}, "pageNumber": {"type": "integer", "format": "int32"}, "paged": {"type": "boolean"}, "pageSize": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int64"}, "sort": {"$ref": "#/components/schemas/SortObject"}}}, "SortObject": {"type": "object", "properties": {"unsorted": {"type": "boolean"}, "sorted": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "AdminGetUserByIdResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}, "user": {"$ref": "#/components/schemas/User"}}}, "BaseResponseAdminGetUserByIdResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminGetUserByIdResponse"}}}, "ActionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}}}, "AdminGetUserRolePermissionResponse": {"type": "object", "properties": {"roles": {"type": "array", "description": "<PERSON><PERSON> role", "items": {"$ref": "#/components/schemas/RoleDto"}}, "menus": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch menu người dùng có thể truy cập", "items": {"$ref": "#/components/schemas/MenuDto"}}}}, "BaseResponseAdminGetUserRolePermissionResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminGetUserRolePermissionResponse"}}}, "MenuDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}, "platform": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "parentId": {"type": "string", "format": "uuid"}, "order": {"type": "integer", "format": "int32"}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/ActionDto"}}}}, "RoleDto": {"type": "object", "properties": {"code": {"type": "string", "description": "<PERSON><PERSON> role", "example": "CTV"}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> role"}}}, "AdminListMenusResponse": {"type": "object", "properties": {"menuApp": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}}, "menuWeb": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}}}}, "BaseResponseAdminListMenusResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminListMenusResponse"}}}, "AdminListParentMenusResponse": {"type": "object", "properties": {"menus": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}}}}, "BaseResponseAdminListParentMenusResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminListParentMenusResponse"}}}, "AdminGetMenuResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string"}, "platform": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "parentId": {"type": "string", "format": "uuid"}, "order": {"type": "integer", "format": "int32"}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/ActionDto"}}}}, "BaseResponseAdminGetMenuResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminGetMenuResponse"}}}, "AdminGetActionsResponse": {"type": "object", "properties": {"actions": {"type": "array", "items": {"$ref": "#/components/schemas/ActionDto"}}}}, "BaseResponseAdminGetActionsResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminGetActionsResponse"}}}, "AdminDeleteUserResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "object"}, "userId": {"type": "string"}}}, "BaseResponseAdminDeleteUserResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminDeleteUserResponse"}}}, "AdminDeleteMenuResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}}}, "BaseResponseAdminDeleteMenuResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/AdminDeleteMenuResponse"}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "name": "<PERSON><PERSON><PERSON>", "scheme": "bearer", "bearerFormat": "JWT"}}}}