{"openapi": "3.0.1", "info": {"title": "Media API", "description": "Documentation Media API v1.0", "version": "1.0"}, "servers": [{"url": "https://dev-ksapi.ssf.vn/media"}], "security": [{"Authorization": []}], "paths": {"/v1/upload": {"post": {"tags": ["minio-controller"], "summary": "Upload file", "description": "Use this endpoint to upload file", "operationId": "uploadV1", "parameters": [{"name": "bucketName", "in": "query", "required": false, "schema": {"type": "string", "default": "salt"}}, {"name": "fileType", "in": "query", "required": true, "schema": {"type": "string", "enum": ["AVATAR", "IDCARD", "LIVENESS", "ICON", "COLLOCATION", "PROPOSAL", "STATEMENT", "SIGN", "OTHER"]}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileUploadResponseV1"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileUploadResponseV1"}}}}}, "security": []}}, "/upload": {"post": {"tags": ["minio-controller"], "summary": "Upload file", "description": "Use this endpoint to upload file", "operationId": "upload", "parameters": [{"name": "bucketName", "in": "query", "required": false, "schema": {"type": "string", "default": "salt"}}, {"name": "fileType", "in": "query", "required": true, "schema": {"type": "string", "enum": ["AVATAR", "IDCARD", "LIVENESS", "ICON", "COLLOCATION", "PROPOSAL", "STATEMENT", "SIGN", "OTHER"]}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}}}}, "security": []}}, "/private/upload": {"post": {"tags": ["minio-controller"], "summary": "Upload file", "description": "Use this endpoint to upload file", "operationId": "privateUpload", "parameters": [{"name": "bucketName", "in": "query", "required": false, "schema": {"type": "string", "default": "salt"}}, {"name": "fileType", "in": "query", "required": true, "schema": {"type": "string", "enum": ["AVATAR", "IDCARD", "LIVENESS", "ICON", "COLLOCATION", "PROPOSAL", "STATEMENT", "SIGN", "OTHER"]}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}}}}, "security": []}}, "/private/preview/{bucketName}/private/{fileType}/{objectName}": {"get": {"tags": ["minio-controller"], "summary": "Preview file", "description": "Use this endpoint to preview file", "operationId": "previewPrivatePicture", "parameters": [{"name": "bucketName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "objectName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "500": {"description": "Server Error"}}}}, "/preview/{bucketName}/{fileType}/{objectName}": {"get": {"tags": ["minio-controller"], "summary": "Preview file", "description": "Use this endpoint to preview file", "operationId": "previewImage", "parameters": [{"name": "bucketName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "objectName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "500": {"description": "Server Error"}}, "security": []}}, "/download/{bucketName}/{fileType}/{objectName}": {"get": {"tags": ["minio-controller"], "summary": "Download file", "description": "Use this endpoint to download file", "operationId": "downloadFile", "parameters": [{"name": "bucketName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "objectName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string", "format": "byte"}}}}}, "500": {"description": "Server Error", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string", "format": "byte"}}}}}}}}, "/delete/{bucketName}/{fileType}/{objectName}": {"delete": {"tags": ["minio-controller"], "summary": "Delete file", "description": "Use this endpoint to delete file", "operationId": "delete", "parameters": [{"name": "bucketName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "objectName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "500": {"description": "Server Error"}}}}}, "components": {"schemas": {"FileUploadResponseV1": {"type": "object", "properties": {"previewUrl": {"type": "string", "nullable": true, "x-nullable": true}, "privatePreviewUrl": {"type": "string", "nullable": true, "x-nullable": true}}}, "FileUploadResponse": {"type": "object", "properties": {"previewUrl": {"type": "string", "nullable": true, "x-nullable": true}}}, "NullType": {"description": "for adding nullability to a ref", "enum": [null]}}, "securitySchemes": {"Authorization": {"type": "http", "description": "Access token", "in": "header", "scheme": "bearer", "bearerFormat": "Bearer [token]"}}}}