import 'package:flutter_test/flutter_test.dart';
import 'package:sales_app/core/enums/network_connection_status.dart';

void main() {
  group('Network Monitoring System Tests', () {
    test('NetworkConnectionStatus should have correct properties', () {
      // Test connected status
      const connected = NetworkConnectionStatus.connected;
      expect(connected.isConnected, true);
      expect(connected.isDisconnected, false);
      expect(connected.isUnstable, false);
      expect(connected.isChecking, false);
      expect(connected.statusIcon, '✅');

      // Test disconnected status
      const disconnected = NetworkConnectionStatus.disconnected;
      expect(disconnected.isConnected, false);
      expect(disconnected.isDisconnected, true);
      expect(disconnected.isUnstable, false);
      expect(disconnected.isChecking, false);
      expect(disconnected.statusIcon, '❌');

      // Test unstable status
      const unstable = NetworkConnectionStatus.unstable;
      expect(unstable.isConnected, false);
      expect(unstable.isDisconnected, false);
      expect(unstable.isUnstable, true);
      expect(unstable.isChecking, false);
      expect(unstable.statusIcon, '⚠️');

      // Test checking status
      const checking = NetworkConnectionStatus.checking;
      expect(checking.isConnected, false);
      expect(checking.isDisconnected, false);
      expect(checking.isUnstable, false);
      expect(checking.isChecking, true);
      expect(checking.statusIcon, '🔄');
    });

    test('NetworkConnectionStatus equality should work correctly', () {
      const status1 = NetworkConnectionStatus.connected;
      const status2 = NetworkConnectionStatus.connected;
      const status3 = NetworkConnectionStatus.disconnected;

      expect(status1, equals(status2));
      expect(status1, isNot(equals(status3)));
    });

    test('NetworkConnectionStatus enum values should be correct', () {
      // Test all enum values exist
      expect(NetworkConnectionStatus.values.length, 4);
      expect(NetworkConnectionStatus.values, contains(NetworkConnectionStatus.connected));
      expect(NetworkConnectionStatus.values, contains(NetworkConnectionStatus.disconnected));
      expect(NetworkConnectionStatus.values, contains(NetworkConnectionStatus.unstable));
      expect(NetworkConnectionStatus.values, contains(NetworkConnectionStatus.checking));
    });

    test('NetworkConnectionStatus should have correct string representation', () {
      expect(NetworkConnectionStatus.connected.toString(), 'NetworkConnectionStatus.connected');
      expect(NetworkConnectionStatus.disconnected.toString(), 'NetworkConnectionStatus.disconnected');
      expect(NetworkConnectionStatus.unstable.toString(), 'NetworkConnectionStatus.unstable');
      expect(NetworkConnectionStatus.checking.toString(), 'NetworkConnectionStatus.checking');
    });
  });
}
