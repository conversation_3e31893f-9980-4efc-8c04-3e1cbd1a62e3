import 'package:flutter_test/flutter_test.dart';
import 'package:sales_app/data/network/interceptors/simple_retry_interceptor.dart';

void main() {
  group('SimpleRetryInterceptor', () {

    group('Configuration Tests', () {
      test('should use default config correctly', () {
        final interceptor = SimpleRetryInterceptor();

        expect(interceptor.maxRetries, 3);
        expect(interceptor.initialDelay, const Duration(milliseconds: 1000));
        expect(interceptor.enableLogging, true);
      });

      test('should use development config correctly', () {
        final interceptor = SimpleRetryInterceptor.development();

        expect(interceptor.maxRetries, 5);
        expect(interceptor.initialDelay, const Duration(milliseconds: 500));
        expect(interceptor.enableLogging, true);
      });

      test('should use auth config correctly', () {
        final interceptor = SimpleRetryInterceptor.auth();

        expect(interceptor.maxRetries, 2);
        expect(interceptor.initialDelay, const Duration(milliseconds: 1500));
        expect(interceptor.enableLogging, true);
      });

      test('should allow custom configuration', () {
        final interceptor = SimpleRetryInterceptor(
          maxRetries: 10,
          initialDelay: const Duration(milliseconds: 2000),
          enableLogging: false,
        );

        expect(interceptor.maxRetries, 10);
        expect(interceptor.initialDelay, const Duration(milliseconds: 2000));
        expect(interceptor.enableLogging, false);
      });
    });

    group('Delay Calculation Tests', () {
      test('should calculate exponential backoff correctly', () {
        final interceptor = SimpleRetryInterceptor(
          initialDelay: const Duration(milliseconds: 1000),
        );

        // Test private method through reflection or create a test helper
        // For now, we'll test the behavior indirectly
        expect(interceptor.maxRetries, 3);
        expect(interceptor.initialDelay, const Duration(milliseconds: 1000));
      });

      test('should respect max delay limit', () {
        final interceptor = SimpleRetryInterceptor(
          initialDelay: const Duration(milliseconds: 10000), // Large initial delay
        );

        // The interceptor should clamp delays to reasonable limits
        expect(interceptor.initialDelay, const Duration(milliseconds: 10000));
      });
    });
  });
}
