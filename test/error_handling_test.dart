import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sales_app/core/error/error_codes.dart';
import 'package:sales_app/core/error/error_types.dart';
import 'package:sales_app/core/error/failures.dart';
import 'package:sales_app/presentation/utils/error_message_mapper.dart';
import 'package:sales_app/generated/l10n.dart';

void main() {
  group('Error Handling System Tests', () {
    testWidgets('Local validation errors format correctly', (WidgetTester tester) async {
      // Build a widget to get BuildContext
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            S.delegate,
          ],
          supportedLocales: S.delegate.supportedLocales,
          home: const Scaffold(body: Text('Test')),
        ),
      );

      final context = tester.element(find.text('Test'));

      // Test invalid email error
      const emailFailure = ValidationFailure(ValidationErrorType.invalidEmail);
      final emailMessage = ErrorMessageMapper.getErrorMessage(context, emailFailure);
      expect(emailMessage, contains('email'));
      expect(emailMessage, contains('(VAL_INVALID_EMAIL)'));

      // Test required field error
      const requiredFailure = ValidationFailure(ValidationErrorType.requiredField);
      final requiredMessage = ErrorMessageMapper.getErrorMessage(context, requiredFailure);
      expect(requiredMessage, contains('required'));
      expect(requiredMessage, contains('(VAL_REQUIRED_FIELD)'));
    });

    testWidgets('Network errors format correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [S.delegate],
          supportedLocales: S.delegate.supportedLocales,
          home: const Scaffold(body: Text('Test')),
        ),
      );

      final context = tester.element(find.text('Test'));

      // Test connection timeout error
      const timeoutFailure = NetworkFailure(NetworkErrorType.connectionTimeout);
      final timeoutMessage = ErrorMessageMapper.getErrorMessage(context, timeoutFailure);
      expect(timeoutMessage, contains('timeout'));
      expect(timeoutMessage, contains('(NET_TIMEOUT)'));

      // Test no connection error
      const noConnFailure = NetworkFailure(NetworkErrorType.noConnection);
      final noConnMessage = ErrorMessageMapper.getErrorMessage(context, noConnFailure);
      expect(noConnMessage, contains('connection'));
      expect(noConnMessage, contains('(NET_NO_CONNECTION)'));
    });

    testWidgets('Server errors with backend data format correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [S.delegate],
          supportedLocales: S.delegate.supportedLocales,
          home: const Scaffold(body: Text('Test')),
        ),
      );

      final context = tester.element(find.text('Test'));

      // Test server error with custom message and code
      const serverFailure = ServerFailure(
        ServerErrorType.internalError,
        serverMessage: 'Database connection failed',
        serverErrorCode: 'DB_001',
      );
      final serverMessage = ErrorMessageMapper.getErrorMessage(context, serverFailure);
      expect(serverMessage, equals('Database connection failed (DB_001)'));

      // Test auth error with server message only
      const authFailure = AuthFailure(
        AuthErrorType.invalidCredentials,
        serverMessage: 'Username or password is incorrect',
      );
      final authMessage = ErrorMessageMapper.getErrorMessage(context, authFailure);
      expect(authMessage, equals('Username or password is incorrect (401)'));
    });

    group('Error Code Mapping Tests', () {
      test('Server error codes', () {
        expect(ErrorCodeMapper.getServerErrorCode(ServerErrorType.internalError), equals('500'));
        expect(ErrorCodeMapper.getServerErrorCode(ServerErrorType.notFound), equals('404'));
        expect(ErrorCodeMapper.getServerErrorCode(ServerErrorType.badRequest), equals('400'));
        expect(ErrorCodeMapper.getServerErrorCode(ServerErrorType.unknown), equals('SRV_UNKNOWN'));
      });

      test('Network error codes', () {
        expect(ErrorCodeMapper.getNetworkErrorCode(NetworkErrorType.connectionTimeout), equals('NET_TIMEOUT'));
        expect(ErrorCodeMapper.getNetworkErrorCode(NetworkErrorType.noConnection), equals('NET_NO_CONNECTION'));
        expect(ErrorCodeMapper.getNetworkErrorCode(NetworkErrorType.certificateError), equals('NET_CERT_ERROR'));
      });

      test('Auth error codes', () {
        expect(ErrorCodeMapper.getAuthErrorCode(AuthErrorType.invalidCredentials), equals('401'));
        expect(ErrorCodeMapper.getAuthErrorCode(AuthErrorType.accessDenied), equals('403'));
        expect(ErrorCodeMapper.getAuthErrorCode(AuthErrorType.tokenExpired), equals('AUTH_TOKEN_EXPIRED'));
      });

      test('Validation error codes', () {
        expect(ErrorCodeMapper.getValidationErrorCode(ValidationErrorType.invalidEmail), equals('VAL_INVALID_EMAIL'));
        expect(ErrorCodeMapper.getValidationErrorCode(ValidationErrorType.serverValidation), equals('422'));
        expect(ErrorCodeMapper.getValidationErrorCode(ValidationErrorType.requiredField), equals('VAL_REQUIRED_FIELD'));
      });

      test('Cache error codes', () {
        expect(ErrorCodeMapper.getCacheErrorCode(CacheErrorType.readError), equals('CACHE_READ_ERROR'));
        expect(ErrorCodeMapper.getCacheErrorCode(CacheErrorType.writeError), equals('CACHE_WRITE_ERROR'));
        expect(ErrorCodeMapper.getCacheErrorCode(CacheErrorType.dataCorrupted), equals('CACHE_CORRUPTED'));
      });
    });

    group('Failure Object Tests', () {
      test('ServerFailure with all parameters', () {
        const failure = ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Custom server message',
          serverErrorCode: 'CUSTOM_001',
        );

        expect(failure.errorType, equals(ServerErrorType.internalError));
        expect(failure.serverMessage, equals('Custom server message'));
        expect(failure.serverErrorCode, equals('CUSTOM_001'));
      });

      test('AuthFailure with partial parameters', () {
        const failure = AuthFailure(
          AuthErrorType.invalidCredentials,
          serverMessage: 'Invalid login',
        );

        expect(failure.errorType, equals(AuthErrorType.invalidCredentials));
        expect(failure.serverMessage, equals('Invalid login'));
        expect(failure.serverErrorCode, isNull);
      });

      test('NetworkFailure without optional parameters', () {
        const failure = NetworkFailure(NetworkErrorType.connectionTimeout);

        expect(failure.errorType, equals(NetworkErrorType.connectionTimeout));
      });
    });
  });
}
