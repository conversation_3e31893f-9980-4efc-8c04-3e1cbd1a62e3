fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## Android

### android test

```sh
[bundle exec] fastlane android test
```

Runs all the tests

### android beta

```sh
[bundle exec] fastlane android beta
```

Submit a new Beta Build to Crashlytics Beta

### android deploy

```sh
[bundle exec] fastlane android deploy
```

Deploy a new version to the Google Play

### android gradle_dev_release

```sh
[bundle exec] fastlane android gradle_dev_release
```

Build Development Release APK (Gradle)

### android gradle_staging_release

```sh
[bundle exec] fastlane android gradle_staging_release
```

Build Staging Release APK (Gradle)

### android gradle_prod_release

```sh
[bundle exec] fastlane android gradle_prod_release
```

Build Production Release APK (Gradle)

### android gradle_staging_aab

```sh
[bundle exec] fastlane android gradle_staging_aab
```

Build Staging Release AAB (Gradle) for Play Store

### android gradle_prod_aab

```sh
[bundle exec] fastlane android gradle_prod_aab
```

Build Production Release AAB (Gradle) for Play Store

### android gradle_all_release

```sh
[bundle exec] fastlane android gradle_all_release
```

Build all release variants (Gradle)

### android deploy_staging_playstore

```sh
[bundle exec] fastlane android deploy_staging_playstore
```

Deploy Staging to Google Play Store (Internal Test)

### android deploy_production_playstore

```sh
[bundle exec] fastlane android deploy_production_playstore
```

Deploy Production to Google Play Store (Internal Test)

### android promote_staging_to_external

```sh
[bundle exec] fastlane android promote_staging_to_external
```

Promote Staging to External Test

### android promote_production_to_production

```sh
[bundle exec] fastlane android promote_production_to_production
```

Promote Production to Production Track

### android version_info

```sh
[bundle exec] fastlane android version_info
```

Show current version info

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
