# This file contains the fastlane configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#     https://docs.fastlane.tools/plugins/available-plugins

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  # Variables
  GRADLE_FILE = "app/build.gradle.kts"
  
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleDevelopmentRelease")
    crashlytics
  
    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleProductionRelease")
    upload_to_play_store
  end

  # ===================
  # GRADLE BUILD LANES (Release Only)
  # ===================
  
  desc "Build Development Release APK (Gradle)"
  lane :gradle_dev_release do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    # Clean and build using Gradle only
    gradle(
      task: "clean assembleDevelopmentRelease",
      project_dir: ".",
      properties: {
        "versionCode" => build_number,
        "versionName" => build_name
      }
    )
    UI.success("✅ Development Release APK built via Gradle!")
  end

  desc "Build Staging Release APK (Gradle)"
  lane :gradle_staging_release do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    # Clean and build using Gradle only
    gradle(
      task: "clean assembleStagingRelease", 
      project_dir: ".",
      properties: {
        "versionCode" => build_number,
        "versionName" => build_name
      }
    )
    UI.success("✅ Staging Release APK built via Gradle!")
  end

  desc "Build Production Release APK (Gradle)"
  lane :gradle_prod_release do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    # Clean and build using Gradle only
    gradle(
      task: "clean assembleProductionRelease",
      project_dir: ".",
      properties: {
        "versionCode" => build_number,
        "versionName" => build_name
      }
    )
    UI.success("✅ Production Release APK built via Gradle!")
  end
  
  desc "Build Staging Release AAB (Gradle) for Play Store"
  lane :gradle_staging_aab do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    # Clean and build using Gradle only
    gradle(
      task: "clean bundleStagingRelease",
      project_dir: ".",
      properties: {
        "versionCode" => build_number,
        "versionName" => build_name
      }
    )
    UI.success("✅ Staging Release AAB built via Gradle!")
  end
  
  desc "Build Production Release AAB (Gradle) for Play Store"
  lane :gradle_prod_aab do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    # Clean and build using Gradle only
    gradle(
      task: "clean bundleProductionRelease",
      project_dir: ".",
      properties: {
        "versionCode" => build_number,
        "versionName" => build_name
      }
    )
    UI.success("✅ Production Release AAB built via Gradle!")
  end

  desc "Build all release variants (Gradle)"
  lane :gradle_all_release do
    # Get version info from CI environment
    build_number = ENV["CI_JOB_ID"] || "1"
    build_name = ENV["APP_VERSION"] || "1.0.0"
    
    UI.message("🔢 Build Number (Version Code): #{build_number}")
    UI.message("📱 Build Name (Version Name): #{build_name}")
    
    # Build all release variants using Gradle only
    gradle(
      task: "clean assembleDevelopmentRelease assembleStagingRelease assembleProductionRelease",
      project_dir: ".",
      properties: {
        "versionCode" => build_number,
        "versionName" => build_name
      }
    )
    
    UI.success("🎉 All release flavors built successfully!")
  end



  # ===================
  # DEPLOYMENT LANES
  # ===================
  
  desc "Deploy Staging to Google Play Store (Internal Test)"
  lane :deploy_staging_playstore do
    gradle_staging_aab
    upload_to_play_store(
      track: "internal",
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_STAGING"],
      package_name: "com.kienlongbank.sales_app.staging",
      aab: "../build/app/outputs/bundle/stagingRelease/app-staging-release.aab",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Deploy Production to Google Play Store (Internal Test)"
  lane :deploy_production_playstore do
    gradle_prod_aab
    upload_to_play_store(
      track: "internal",
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_PRODUCTION"],
      package_name: "com.kienlongbank.sales_app",
      aab: "../build/app/outputs/bundle/productionRelease/app-production-release.aab",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Promote Staging to External Test"
  lane :promote_staging_to_external do
    upload_to_play_store(
      track: "internal",
      track_promote_to: "alpha",
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_STAGING"],
      package_name: "com.kienlongbank.sales_app.staging",
      skip_upload_apk: true,
      skip_upload_aab: true,
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Promote Production to Production Track"
  lane :promote_production_to_production do
    upload_to_play_store(
      track: "internal",
      track_promote_to: "production",
      json_key: ENV["GOOGLE_PLAY_JSON_KEY_PRODUCTION"],
      package_name: "com.kienlongbank.sales_app",
      skip_upload_apk: true,
      skip_upload_aab: true,
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  # ===================
  # UTILITY LANES
  # ===================
  
  desc "Show current version info"
  lane :version_info do
    # Show current version from environment variables
    ci_job_id = ENV["CI_JOB_ID"] || "Local Build"
    app_version = ENV["APP_VERSION"] || "1.0.0"
    ci_pipeline_id = ENV["CI_PIPELINE_ID"] || "N/A"
    git_commit = ENV["CI_COMMIT_SHORT_SHA"] || "N/A"
    
    UI.message("🔢 Build Number (Version Code): #{ci_job_id}")
    UI.message("📱 Build Name (Version Name): #{app_version}")
    UI.message("🚀 Pipeline ID: #{ci_pipeline_id}")
    UI.message("📝 Git Commit: #{git_commit}")
  end

  # ===================
  # ERROR HANDLING
  # ===================
  
  error do |lane, exception|
    UI.error("❌ Lane '#{lane}' failed with error: #{exception}")
    # Có thể thêm notification hoặc cleanup ở đây
  end
end 