# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# macOS
macos/Flutter/ephemeral/
macos/Pods/
macos/Flutter/GeneratedPluginRegistrant.swift

# Windows
windows/flutter/generated_plugin_registrant.cc
windows/flutter/generated_plugin_registrant.h
windows/flutter/generated_plugins.cmake

# Linux
linux/flutter/generated_plugin_registrant.cc
linux/flutter/generated_plugin_registrant.h
linux/flutter/generated_plugins.cmake

# iOS specific - only ignore generated/dependency files
/ios/Pods/
/ios/.symlinks/
/ios/build/
/ios/Runner.xcodeproj.backup/
/ios/Runner.xcworkspace/xcshareddata/swiftpm/
/ios/Runner.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/
ios/Flutter/ephemeral/

# Note: iOS flavor files (xcconfig, schemes) are NOT ignored and will be tracked in git

# OpenAPI Generator
# JSON specs from Swagger - TRACK THESE in Git
# openapi/json-specs/*.json

# Generated API client code - có thể ignore nếu chỉ muốn track config
# openapi/generated/
# Hoặc chỉ ignore một số file cụ thể:
openapi/generated/*/.openapi-generator/
openapi/generated/*/.dart_tool/
openapi/generated/*/build/
openapi/generated/*/pubspec.lock

# Generated source files (tùy chọn - có thể muốn track để dễ debug)
# openapi/generated/*/lib/
# openapi/generated/*/doc/
# openapi/generated/*/test/

# OpenAPI Generator cache
.dart_tool/openapi-generator-cache.json
