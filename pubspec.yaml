name: sales_app
description: KienlongBank Sales App
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.10.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.6
  flutter_tabler_icons: ^1.43.0
  flutter_hooks: ^0.21.2
  hooks_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  get_it: ^7.6.7
  injectable: ^2.4.1
  dio: ^5.4.1
  connectivity_plus: ^5.0.2
  freezed_annotation: ^2.4.1
  json_annotation: ^4.9.0
  dartz: ^0.10.1
  shared_preferences: 2.2.0
  flutter_secure_storage: ^9.0.0
  image_picker: ^1.0.4
  camera: ^0.11.2
  file_picker: ^8.0.0+1
  permission_handler: ^11.3.1
  url_launcher: ^6.2.5
  flutter_screenutil: ^5.9.0
  logger: ^2.0.2+1
  intl: ^0.19.0
  mobile_scanner: ^5.2.3
  go_router: ^15.1.2
  # Local Database
  floor: ^1.4.2
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # Minimal dependencies for generated APIs
  built_value: ^8.4.0  # Required for JsonObject import in our code
  openapi_generator_annotations: ^6.1.0
  
  # Local Generated API Packages
  sales_app_api:
    path: openapi/generated/sales
  media_api:
    path: openapi/generated/media
  
  # Markdown support
  markdown_widget: ^2.3.2+8
  flutter_svg: ^2.1.0
  image: ^4.5.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.8
  freezed: ^2.4.7
  json_serializable: ^6.7.1
  injectable_generator: ^2.4.1
  riverpod_generator: ^2.4.0
  analyzer: ^6.4.1
  intl_utils: ^2.8.7

  # Floor code generation
  floor_generator: ^1.4.2
  
  # OpenAPI Generator
  openapi_generator: ^6.1.0
  


flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/

flutter_intl:
  enabled: true