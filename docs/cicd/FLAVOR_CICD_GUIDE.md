# 🍎 Hướng dẫn Toàn diện - C<PERSON>u hình Flavor và CI/CD

## 📋 Mục lục

1. [Tổng quan dự án](#-tổng-quan-dự-án)
2. [C<PERSON><PERSON> hình Flavor](#-cấu-hình-flavor)
3. [CI/CD Pipeline](#-cicd-pipeline)
4. [Cách sử dụng](#-cách-sử-dụng)
5. [Troubleshooting](#-troubleshooting)
6. [Best Practices](#-best-practices)

## 🎯 Tổng quan dự án

Sales App được thiết kế với **3 environments** tương ứng với **3 flavors** để hỗ trợ quy trình phát triển và triển khai chuyên nghiệp:

| Environment | Purpose | Branch | Deploy Target |
|-------------|---------|--------|---------------|
| **Development** 🟢 | Phát triển và debug | `develop`, `feature/*`, MR | Firebase/Internal |
| **Staging** 🟡 | UAT và testing | `uat`, `release/*` | TestFlight, Play Console |
| **Production** 🔴 | Sản xuất | `main`, `prod`, `tags` | App Store, Play Store |

## 🏗️ Cấu hình Flavor

### 1. Cấu trúc Flavor Flutter

#### **Entry Points**
```
lib/
├── main_development.dart    # Development entry point
├── main_staging.dart        # Staging entry point  
├── main_production.dart     # Production entry point
└── main.dart                # Shared app logic
```

#### **AppConfig Centralized**
```dart
// lib/core/config/app_config.dart
enum Flavor {
  development,
  staging,
  production,
}

class AppConfig {
  static Flavor? appFlavor;
  
  // App Display Names
  static String get title {
    switch (appFlavor) {
      case Flavor.development: return 'Sales App Dev';
      case Flavor.staging: return 'Sales App Staging';
      case Flavor.production: return 'Sales App';
      default: return 'Sales App';
    }
  }
  
  // API Base URLs
  static String get baseUrl {
    switch (appFlavor) {
      case Flavor.development: return 'https://dev-api.kienlongbank.com';
      case Flavor.staging: return 'https://staging-api.kienlongbank.com';
      case Flavor.production: return 'https://api.kienlongbank.com';
      default: return 'https://api.kienlongbank.com';
    }
  }
  
  // Bundle IDs
  static String get bundleId {
    switch (appFlavor) {
      case Flavor.development: return 'com.kienlongbank.sales_app.dev';
      case Flavor.staging: return 'com.kienlongbank.sales_app.staging';
      case Flavor.production: return 'com.kienlongbank.sales_app';
      default: return 'com.kienlongbank.sales_app';
    }
  }
}
```

### 2. Android Configuration

#### **Gradle Build (android/app/build.gradle.kts)**
```kotlin
android {
    defaultConfig {
        applicationId = "com.kienlongbank.sales_app"
        versionCode = project.findProperty("versionCode")?.toString()?.toIntOrNull() ?: flutter.versionCode
        versionName = project.findProperty("versionName")?.toString() ?: flutter.versionName
    }

    flavorDimensions.add("default")
    productFlavors {
        create("development") {
            dimension = "default"
            applicationIdSuffix = ".dev"
            versionNameSuffix = "-dev"
            resValue("string", "app_name", "Sales App Dev")
        }
        create("staging") {
            dimension = "default"
            applicationIdSuffix = ".staging"
            versionNameSuffix = "-staging"
            resValue("string", "app_name", "Sales App Staging")
        }
        create("production") {
            dimension = "default"
            resValue("string", "app_name", "Sales App")
        }
    }
}
```

#### **Package Names**
- **Development**: `com.kienlongbank.sales_app.dev`
- **Staging**: `com.kienlongbank.sales_app.staging`
- **Production**: `com.kienlongbank.sales_app`

### 3. iOS Configuration

#### **XCConfig Files**
```
ios/
├── Development.xcconfig     # Development configuration
├── Staging.xcconfig         # Staging configuration
└── Production.xcconfig      # Production configuration
```

#### **Development.xcconfig**
```bash
PRODUCT_BUNDLE_IDENTIFIER = com.kienlongbank.sales_app.dev
PRODUCT_NAME = Sales App Dev
APP_DISPLAY_NAME = Sales App Dev
APP_FLAVOR = development
API_BASE_URL = https://dev-api.kienlongbank.com
FLUTTER_BUILD_MODE = debug
DEBUG_MODE = 1
LOGGING_ENABLED = 1
```

#### **Staging.xcconfig**
```bash
PRODUCT_BUNDLE_IDENTIFIER = com.kienlongbank.sales_app.staging
PRODUCT_NAME = Sales App Staging
APP_DISPLAY_NAME = Sales App Staging
APP_FLAVOR = staging
API_BASE_URL = https://staging-api.kienlongbank.com
FLUTTER_BUILD_MODE = release
DEBUG_MODE = 0
LOGGING_ENABLED = 1
```

#### **Production.xcconfig**
```bash
PRODUCT_BUNDLE_IDENTIFIER = com.kienlongbank.sales_app
PRODUCT_NAME = Sales App
APP_DISPLAY_NAME = Sales App
APP_FLAVOR = production
API_BASE_URL = https://api.kienlongbank.com
FLUTTER_BUILD_MODE = release
DEBUG_MODE = 0
LOGGING_ENABLED = 0
```

#### **Xcode Schemes & Build Configurations**
```
Runner.xcodeproj/xcshareddata/xcschemes/
├── development.xcscheme
├── staging.xcscheme
└── production.xcscheme

Build Configurations:
├── Debug-development, Release-development, Profile-development
├── Debug-staging, Release-staging, Profile-staging
└── Debug-production, Release-production, Profile-production
```

#### **ExportOptions Files**
```
ios/
├── ExportOptions_Development.plist   # method: app-store (TestFlight)
├── ExportOptions_Staging.plist       # method: app-store (TestFlight)
└── ExportOptions_Production.plist    # method: app-store (TestFlight/App Store)
```

## 🚀 CI/CD Pipeline

### Pipeline Overview

```mermaid
graph TB
    A[Code Push] --> B[Prepare Stage]
    B --> C{Branch Check}
    C -->|develop/MR| D[Build Development]
    C -->|uat/release/*| E[Build Staging]
    C -->|main/prod/tags| F[Build Production]
    D --> G[Android APK]
    E --> H[Android APK + AAB]
    E --> I[iOS IPA - Staging]
    F --> J[Android APK + AAB]
    F --> K[iOS IPA - Production]
    I --> L[Deploy TestFlight Staging]
    K --> M[Deploy TestFlight Production]
    M --> N[Manual: Promote to App Store]
```

### Stages & Jobs

#### **1. Prepare Stage**
```yaml
stages:
  - prepare    # Code quality checks
  - build      # Build artifacts
  - deploy     # Deploy to stores
```

**Quality Checks:**
- ✅ `flutter_analyze` - Dart/Flutter code analysis (fail pipeline if errors)
- 🟡 `flutter_test` - Unit tests (disabled, allow_failure)
- 🟡 `code_format_check` - Dart code formatting (disabled)
- 🟡 `dependency_check` - Dependencies audit (disabled)
- 🟡 `flutter_doctor_check` - Flutter environment (disabled)

#### **2. Build Stage**

**Android Builds:**
```yaml
build_android_development:
  stage: build
  script:
    - cd android
    - fastlane gradle_dev_release
  artifacts:
    - build/app/outputs/apk/development/release/app-development-release.apk
  only:
    - merge_requests
    - develop

build_android_staging:
  stage: build
  script:
    - cd android
    - fastlane gradle_staging_release  # APK
    - fastlane gradle_staging_aab      # AAB for Play Store
  artifacts:
    - build/app/outputs/apk/staging/release/app-staging-release.apk
    - build/app/outputs/bundle/stagingRelease/app-staging-release.aab
  only:
    - /^release\/.*$/
    - uat

build_android_production:
  stage: build
  script:
    - cd android
    - fastlane gradle_prod_release     # APK
    - fastlane gradle_prod_aab         # AAB for Play Store
  artifacts:
    - build/app/outputs/apk/production/release/app-production-release.apk
    - build/app/outputs/bundle/productionRelease/app-production-release.aab
  only:
    - main
    - prod
    - tags
```

**iOS Builds:**
```yaml
build_ios_staging:
  stage: build
  tags:
    - banking-mobile
    - macos
  script:
    - fvm flutter build ipa --release --flavor staging --target lib/main_staging.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Staging.plist
  artifacts:
    - build/ios/ipa/
  only:
    - /^release\/.*$/
    - uat

build_ios_production:
  stage: build
  tags:
    - banking-mobile
    - macos
  script:
    - fvm flutter build ipa --release --flavor production --target lib/main_production.dart --build-number=$CI_JOB_ID --export-options-plist=ios/ExportOptions_Production.plist
  artifacts:
    - build/ios/ipa/
  only:
    - main
    - prod
    - tags
```

#### **3. Deploy Stage**

**iOS Deployment:**
```yaml
deploy_ios_staging:
  stage: deploy
  dependencies:
    - build_ios_staging
  script:
    - cd ios
    - bundle exec fastlane deploy_staging
  environment:
    name: staging-testflight
    url: https://appstoreconnect.apple.com/
  only:
    - uat

deploy_ios_production:
  stage: deploy
  dependencies:
    - build_ios_production
  script:
    - cd ios
    - bundle exec fastlane deploy_production  # Deploy to TestFlight
  environment:
    name: production-appstore
    url: https://appstoreconnect.apple.com/
  only:
    - main
    - prod
    - tags
```

### Build Tools Integration

#### **Android - Fastlane**
```ruby
# android/fastlane/Fastfile

desc "Build Development Release APK (Gradle)"
lane :gradle_dev_release do
  build_number = ENV["CI_JOB_ID"] || "1"
  build_name = ENV["APP_VERSION"] || "1.0.0"
  
  gradle(
    task: "clean assembleDevelopmentRelease",
    properties: {
      "versionCode" => build_number,
      "versionName" => build_name
    }
  )
end

desc "Build Staging Release APK + AAB"
lane :gradle_staging_release do
  # Similar setup for staging builds
end

desc "Build Production Release APK + AAB"  
lane :gradle_prod_release do
  # Similar setup for production builds
end
```

#### **iOS - Flutter + Fastlane**

**Build Process:**
1. **Flutter CLI**: `flutter build ipa` với ExportOptions
2. **Fastlane**: Upload IPA to TestFlight/App Store

```ruby
# ios/fastlane/Fastfile

desc "Deploy Staging IPA to TestFlight"
lane :deploy_staging do
  # Tìm IPA file được build bởi Flutter CLI
  ipa_path = Dir.glob("../build/ios/ipa/*.ipa").first
  UI.user_error!("No IPA file found") if ipa_path.nil?
  
  # Đọc changelog từ CHANGELOG.md
  changelog = read_changelog_for_staging()
  
  upload_to_testflight(
    ipa: ipa_path,
    skip_submission: true,
    distribute_external: true,
    groups: ["Internal Testing", "Staging Beta"],
    changelog: changelog,
    beta_app_description: "Sales App Staging - Internal testing build"
  )
end

desc "Deploy Production IPA to TestFlight"
lane :deploy_production do
  # Tương tự staging nhưng với production settings
  ipa_path = Dir.glob("../build/ios/ipa/*.ipa").first
  changelog = read_changelog_for_production()
  
  upload_to_testflight(
    ipa: ipa_path,
    skip_submission: true,
    distribute_external: false,
    groups: ["Production Testing", "Release Candidates"],
    changelog: changelog,
    beta_app_description: "Sales App Production - Final testing before App Store"
  )
end
```

### Environment Variables & Configuration

#### **GitLab CI Variables**
```yaml
variables:
  ANDROID_COMPILE_SDK: "34"
  ANDROID_BUILD_TOOLS: "34.0.0"
  APP_VERSION: "1.0.0"
  FLUTTER_VERSION: "3.27.4"
  LC_ALL: "en_US.UTF-8"
  LANG: "en_US.UTF-8"
```

#### **Self-hosted Runner Requirements**
```yaml
# Runner tags
tags:
  - banking-mobile     # Android + iOS (general)
  - macos             # iOS specific builds
```

**Required Tools:**
- ✅ Flutter SDK (via FVM)
- ✅ Android SDK & Tools
- ✅ Xcode (for iOS builds)
- ✅ Fastlane
- ✅ CocoaPods
- ✅ Git

## 📱 Cách sử dụng

### 1. Local Development

#### **Run với Command Line**
```bash
# Development flavor
fvm flutter run --flavor development -t lib/main_development.dart

# Staging flavor
fvm flutter run --flavor staging -t lib/main_staging.dart

# Production flavor
fvm flutter run --flavor production -t lib/main_production.dart
```

#### **VSCode/Cursor Launch Configurations**
```json
// .vscode/launch.json
{
  "configurations": [
    {
      "name": "Development",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_development.dart",
      "args": ["--flavor", "development"]
    },
    {
      "name": "Staging", 
      "request": "launch",
      "type": "dart",
      "program": "lib/main_staging.dart",
      "args": ["--flavor", "staging"]
    },
    {
      "name": "Production",
      "request": "launch", 
      "type": "dart",
      "program": "lib/main_production.dart",
      "args": ["--flavor", "production"]
    }
  ]
}
```

### 2. Manual Build

#### **Android APK/AAB**
```bash
# Development
fvm flutter build apk --release --flavor development -t lib/main_development.dart

# Staging
fvm flutter build apk --release --flavor staging -t lib/main_staging.dart
fvm flutter build appbundle --release --flavor staging -t lib/main_staging.dart

# Production
fvm flutter build apk --release --flavor production -t lib/main_production.dart
fvm flutter build appbundle --release --flavor production -t lib/main_production.dart
```

#### **iOS IPA**
```bash
# Staging
fvm flutter build ipa --release --flavor staging --target lib/main_staging.dart --export-options-plist=ios/ExportOptions_Staging.plist

# Production  
fvm flutter build ipa --release --flavor production --target lib/main_production.dart --export-options-plist=ios/ExportOptions_Production.plist
```

#### **Build Scripts**
```bash
# Build tất cả flavors cho tất cả platforms
./scripts/build_all_flavors.sh

# Build specific flavor + platform
./scripts/build_all_flavors.sh staging android release
./scripts/build_all_flavors.sh production ios release
```

### 3. Automated CI/CD

#### **Branch-based Triggers**
```yaml
# Development builds
Branches: develop, feature/*, merge_requests
→ build_android_development
→ Artifacts: APK for testing

# Staging builds  
Branches: uat, release/*
→ build_android_staging + build_ios_staging
→ deploy_ios_staging (TestFlight)
→ Artifacts: APK, AAB, IPA

# Production builds
Branches: main, prod, tags
→ build_android_production + build_ios_production  
→ deploy_ios_production (TestFlight)
→ Manual: Promote to App Store
→ Artifacts: APK, AAB, IPA
```

#### **Manual Jobs**
```bash
# System information
GitLab → Pipelines → Run Pipeline → system_info

# Version information  
GitLab → Pipelines → Run Pipeline → version_info

# Build all flavors
GitLab → Pipelines → Run Pipeline → build_all_*_flavors
```

### 4. Deployment Process

#### **iOS Workflow**
```
1. Push code to branch (uat/main/prod)
   ↓
2. CI builds IPA with flutter build ipa
   ↓ 
3. CI runs fastlane deploy_* 
   ↓
4. IPA uploaded to TestFlight
   ↓
5. [Manual] Promote TestFlight → App Store (production only)
```

#### **Android Workflow**
```
1. Push code to branch
   ↓
2. CI builds APK + AAB with Fastlane + Gradle
   ↓
3. Artifacts available for download
   ↓
4. [Manual] Upload to Play Console (disabled in CI)
```

## 🔧 Troubleshooting

### Common Issues

#### **1. Flutter Build Errors**
```bash
# Problem: Build configuration errors
Solution:
- fvm flutter clean
- fvm flutter pub get
- dart run build_runner build --delete-conflicting-outputs

# Problem: Wrong Flutter version
Solution:
- fvm install 3.27.4
- fvm use 3.27.4 --force
```

#### **2. iOS Code Signing Issues**
```bash
# Problem: Provisioning profile not found
Solution:
- cd ios
- bundle exec fastlane match appstore
- bundle exec fastlane setup_codesigning

# Problem: Certificate issues
Solution:
- Check Apple Developer account
- Update Matchfile configuration
- Re-run match setup
```

#### **3. Android Gradle Issues**
```bash
# Problem: Gradle build failures
Solution:
- cd android
- ./gradlew clean
- ./gradlew assembleDevelopmentRelease --info

# Problem: SDK version issues
Solution:
- Check android/app/build.gradle.kts
- Update compileSdk, targetSdk
- flutter doctor --android-licenses
```

#### **4. CI/CD Pipeline Issues**
```bash
# Problem: Runner setup failures
Solution:
- Check runner tags: banking-mobile, macos
- Verify tool installations
- Check environment variables

# Problem: Artifact not found
Solution:
- Check build job success
- Verify artifact paths
- Check job dependencies
```

### Debug Commands

```bash
# Check environment
fvm flutter doctor -v
fvm flutter --version

# Test flavor builds locally
fvm flutter run --flavor development -t lib/main_development.dart --debug
fvm flutter run --flavor staging -t lib/main_staging.dart --profile
fvm flutter run --flavor production -t lib/main_production.dart --release

# Check Android flavors
cd android
./gradlew tasks --all | grep assemble

# Check iOS schemes
cd ios
xcodebuild -list
```

## 📖 Best Practices

### 1. Development Workflow

#### **Branch Strategy**
```
main (production)
├── develop (development) 
├── uat (staging)
├── release/* (staging)
└── feature/* (development)
```

#### **Commit Strategy**
```bash
# Feature development
git checkout develop
git checkout -b feature/new-feature
# Development work with development flavor
git commit -m "feat: implement new feature"
git push origin feature/new-feature
# Create MR to develop

# Release preparation
git checkout -b release/v1.1.0
# Test with staging flavor
git push origin release/v1.1.0
# Deploy to staging environment

# Production release
git checkout main
git merge release/v1.1.0
git tag v1.1.0
git push origin main --tags
# Deploy to production
```

### 2. Testing Strategy

#### **Flavor-specific Testing**
```bash
# Test trên đúng flavor cho từng environment
Development: Unit tests + Widget tests + Integration tests
Staging: UAT + Performance tests + Security tests  
Production: Final acceptance tests + Monitoring
```

#### **Build Verification**
```bash
# Kiểm tra build cho tất cả flavors trước merge
./scripts/build_all_flavors.sh development android debug
./scripts/build_all_flavors.sh staging android release
./scripts/build_all_flavors.sh production ios release
```

### 3. Configuration Management

#### **Environment-specific Values**
```dart
// ✅ Good: Centralized configuration
class AppConfig {
  static String get apiUrl => /* flavor-based logic */;
  static bool get enableLogging => /* flavor-based logic */;
}

// ❌ Bad: Hardcoded values
const apiUrl = "https://api.kienlongbank.com";
```

#### **Secret Management**
```bash
# ✅ Good: Use environment variables in CI
CI_VARIABLES:
  - APPLE_ID
  - GOOGLE_PLAY_JSON_KEY_PRODUCTION
  - MATCH_PASSWORD

# ❌ Bad: Hardcode secrets in code
```

### 4. Release Management

#### **Version Numbering**
```
Version Name: 1.0.0 (Semantic versioning)
Version Code: CI_JOB_ID (Auto-increment)

Format: Major.Minor.Patch
- Major: Breaking changes
- Minor: New features
- Patch: Bug fixes
```

#### **Changelog Management**
```markdown
# CHANGELOG.md structure
## [Unreleased]

### All Flavors
- Common features for all environments

### Production Only  
- Features only in production builds

### Staging Only
- Features only in staging builds

### Development Only
- Debug/development features
```

### 5. Monitoring & Analytics

#### **Flavor-based Tracking**
```dart
// Track events with flavor context
analytics.track('user_login', {
  'flavor': AppConfig.name,
  'environment': AppConfig.environment,
  'version': AppConfig.version,
});
```

#### **Error Reporting**
```dart
// Environment-specific error handling
if (AppConfig.enableLogging) {
  crashlytics.recordError(error, stackTrace);
}
```

## 📞 Support & Documentation

### Related Documentation
- **[Architecture Guide](../architecture/ARCHITECTURE_GUIDE.md)** - Clean Architecture implementation
- **[Project Status](../project/PROJECT_STATUS.md)** - Implementation status
- **[Network Guide](../network/NETWORK_GUIDE.md)** - API & network layer
- **[Environment Configuration](../configuration/ENVIRONMENT_CONFIGURATION.md)** - Environment system
- **[Flavor Guide](FLAVOR_GUIDE.md)** - Detailed flavor setup
- **[iOS Flavors Final](../mobile/iOS_FLAVORS_FINAL.md)** - iOS configuration details

### Quick Reference Commands

```bash
# Development workflow
fvm flutter run --flavor development -t lib/main_development.dart

# Build commands
fvm flutter build apk --release --flavor staging -t lib/main_staging.dart
fvm flutter build ipa --release --flavor production -t lib/main_production.dart --export-options-plist=ios/ExportOptions_Production.plist

# CI/CD commands  
cd android && fastlane gradle_staging_release
cd ios && bundle exec fastlane deploy_production

# Debug commands
fvm flutter doctor -v
fvm flutter analyze
dart run build_runner build --delete-conflicting-outputs
```

---

**© 2024 KienlongBank. Tài liệu được cập nhật thường xuyên theo tiến độ dự án.** 