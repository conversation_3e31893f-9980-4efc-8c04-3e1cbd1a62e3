# Version Management Guide

## Overview

Hệ thống quản lý version của Sales App sử dụng **CI Job ID** làm version code để đảm bảo mỗi build có version duy nhất.

## Version Strategy

### Version Code (Build Number)
- **Source**: `CI_JOB_ID` environment variable từ GitLab CI
- **Format**: Integer (ví dụ: 12345, 98765)
- **Unique**: Mỗi CI job có ID duy nhất
- **Auto-increment**: Tự động tăng theo thứ tự job

### Version Name (Build Name)
- **Source**: `APP_VERSION` environment variable
- **Format**: Semantic versioning (ví dụ: 1.0.0, 1.1.0, 2.0.0)
- **Manual**: Cập nhật thủ công khi release version mới

## Implementation

### 1. Fastlane Configuration

Tất cả Flutter build commands đã được cấu hình để sử dụng:
```bash
--build-number=${CI_JOB_ID}
--build-name=${APP_VERSION}
```

### 2. Available Lanes

#### Build Lanes (with automatic versioning):
```bash
fastlane flutter_dev              # Development APK
fastlane flutter_staging          # Staging APK  
fastlane flutter_production       # Production APK
fastlane flutter_staging_aab      # Staging AAB for Play Store
fastlane flutter_production_aab   # Production AAB for Play Store
fastlane flutter_all              # All flavors
```

#### Version Info:
```bash
fastlane version_info             # Show current version and CI info
```

### 3. Environment Variables

#### GitLab CI Variables:
- `CI_JOB_ID`: Auto-provided by GitLab CI
- `CI_PIPELINE_ID`: Auto-provided by GitLab CI  
- `CI_COMMIT_SHORT_SHA`: Auto-provided by GitLab CI
- `APP_VERSION`: Manual setting in `.gitlab-ci.yml`

#### Local Development:
- `CI_JOB_ID`: Defaults to "1" if not set
- `APP_VERSION`: Defaults to "1.0.0" if not set

## Usage Examples

### 1. GitLab CI Builds

Trong CI pipeline, version tự động được set:
```yaml
# .gitlab-ci.yml
variables:
  APP_VERSION: "1.0.0"  # Update this for new releases

build_staging_release:
  script:
    - cd android
    - fastlane flutter_staging_aab
  # CI_JOB_ID sẽ tự động = 12345 (ví dụ)
  # Final version: versionCode=12345, versionName="1.0.0"
```

### 2. Local Development

```bash
# Build local với version mặc định
cd android
fastlane flutter_dev
# Result: versionCode=1, versionName="1.0.0"

# Build local với version custom
CI_JOB_ID=99999 APP_VERSION=1.1.0-dev fastlane flutter_dev
# Result: versionCode=99999, versionName="1.1.0-dev"
```

### 3. Version Info Check

```bash
# Local
fastlane version_info
# Output:
# 🔢 Build Number (Version Code): Local Build
# 📱 Build Name (Version Name): 1.0.0
# 🚀 Pipeline ID: N/A
# 📝 Git Commit: N/A

# CI Environment  
CI_JOB_ID=12345 CI_PIPELINE_ID=567 CI_COMMIT_SHORT_SHA=abc123 APP_VERSION=1.0.1 fastlane version_info
# Output:
# 🔢 Build Number (Version Code): 12345
# 📱 Build Name (Version Name): 1.0.1
# 🚀 Pipeline ID: 567
# 📝 Git Commit: abc123
```

## Version Release Process

### 1. Patch Release (1.0.0 → 1.0.1)

```yaml
# Update .gitlab-ci.yml
variables:
  APP_VERSION: "1.0.1"  # Changed from 1.0.0
```

### 2. Minor Release (1.0.x → 1.1.0)

```yaml
# Update .gitlab-ci.yml
variables:
  APP_VERSION: "1.1.0"  # New minor version
```

### 3. Major Release (1.x.x → 2.0.0)

```yaml
# Update .gitlab-ci.yml
variables:
  APP_VERSION: "2.0.0"  # New major version
```

## Benefits

### ✅ Advantages:

1. **Unique Version Codes**: CI Job ID đảm bảo không bao giờ trùng lặp
2. **Auto-increment**: Không cần manual increment version code
3. **Traceability**: Có thể trace build về exact CI job
4. **Simple**: Không cần complex version management scripts
5. **Consistent**: Work cho cả local development và CI builds

### ⚠️ Considerations:

1. **Sequential**: Version codes không theo thứ tự tăng dần đều
2. **Large Numbers**: CI Job IDs có thể là số lớn (6-7 digits)
3. **Reset**: Nếu GitLab instance reset, Job IDs sẽ restart

## Troubleshooting

### 1. Version Code Issues

```bash
# Check current version
fastlane version_info

# Test build với custom version
CI_JOB_ID=12345 APP_VERSION=1.0.0 fastlane flutter_dev

# Verify APK version
aapt dump badging build/app/outputs/flutter-apk/app-development-release.apk | grep version
```

### 2. GitLab CI Issues

Ensure these environment variables are available:
- `CI_JOB_ID` ✅ (auto-provided)
- `APP_VERSION` ✅ (set in variables)

### 3. Local Development

For consistent local builds:
```bash
# Set consistent local version
export CI_JOB_ID=1
export APP_VERSION=1.0.0-dev

# Build
fastlane flutter_dev
```

## Play Store Considerations

### Version Code Requirements:
- Must be positive integer
- Must be higher than previous version (for updates)
- CI Job IDs satisfy both requirements

### Version Name Guidelines:
- Use semantic versioning (1.0.0, 1.1.0)
- Update `APP_VERSION` in `.gitlab-ci.yml` for releases
- Consistent across all flavors

## Monitoring

### CI Pipeline Monitoring:
```bash
# In GitLab CI job logs, look for:
🔢 Build Number (Version Code): 12345
📱 Build Name (Version Name): 1.0.0
```

### APK/AAB Verification:
```bash
# Check built APK
aapt dump badging build/app/outputs/flutter-apk/app-*.apk | grep version

# Check built AAB  
bundletool dump manifest --bundle=build/app/outputs/bundle/*/app-*.aab | grep version
```

This version management system ensures reliable, traceable, and unique versioning for all Sales App builds! 🚀 