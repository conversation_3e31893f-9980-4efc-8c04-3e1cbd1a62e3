# Build Strategy Guide

## 📖 Overview

Ứng dụng Sales App sử dụng **Gradle-First Build Strategy** để đảm b<PERSON><PERSON> t<PERSON> nhất quán, kiểm soát tốt hơn và giảm thiểu dependency.

## 🎯 Why Gradle-Only Strategy?

### ✅ Benefits của việc sử dụng Gradle lanes

1. **Native Android Build System**
   - Gradle là native build system của Android
   - Kiểm soát chi tiết hơn build process
   - Hỗ trợ đầy đủ các Android features

2. **Ít Dependencies**
   - Không cần Flutter wrapper
   - Giảm thiểu external dependencies
   - Faster build execution

3. **Better Version Control**
   - Direct control qua Gradle properties
   - Consistent với Android ecosystem
   - Easier debugging khi có vấn đề

4. **Performance**
   - Direct Gradle execution
   - No Flutter overhead
   - Optimized build caching

### ❌ Tại sao loại bỏ Flutter lanes?

1. **Redundancy**
   - Cả Flutter và Gradle đều build cùng output
   - Duplicate functionality
   - Confusion trong maintenance

2. **Complexity**
   - Multiple build paths
   - Harder to maintain
   - Inconsistent results

3. **CI/CD Efficiency**
   - Less lanes to maintain
   - Clearer build pipeline
   - Easier troubleshooting

## 🏗️ Current Build Lanes

### APK Build Lanes
- `gradle_dev_release` - Development APK
- `gradle_staging_release` - Staging APK  
- `gradle_prod_release` - Production APK

### AAB Build Lanes (for Play Store)
- `gradle_staging_aab` - Staging AAB
- `gradle_prod_aab` - Production AAB

### Utility Lanes
- `gradle_all_release` - Build all flavors
- `version_info` - Show version information

## 🔧 Version Management

Gradle lanes sử dụng **project properties** để manage version:

```kotlin
// build.gradle.kts
versionCode = project.findProperty("versionCode")?.toString()?.toIntOrNull() ?: flutter.versionCode
versionName = project.findProperty("versionName")?.toString() ?: flutter.versionName
```

**Fastlane truyền version qua properties:**
```ruby
gradle(
  task: "assembleDevelopmentRelease",
  properties: {
    "versionCode" => ENV["CI_JOB_ID"],
    "versionName" => ENV["APP_VERSION"]
  }
)
```

## 🚀 CI/CD Integration

**GitLab CI Jobs:**
- `build_development` → `gradle_dev_release`
- `build_staging` → `gradle_staging_release` + `gradle_staging_aab`
- `build_production` → `gradle_prod_release` + `gradle_prod_aab`

**Deployment:**
- `deploy_staging_playstore` → sử dụng `gradle_staging_aab`
- `deploy_production_playstore` → sử dụng `gradle_prod_aab`

## 📱 Local Development

**Debug builds (development only):**
```bash
# Flutter commands vẫn dùng cho debug
fvm flutter run --flavor development -t lib/main_development.dart
fvm flutter run --flavor staging -t lib/main_staging.dart
```

**Release builds (CI/CD compatible):**
```bash
# Fastlane lanes cho release builds
cd android
fastlane gradle_dev_release
fastlane gradle_staging_aab
```

## 🎯 Build Output Paths

**APK Files:**
- Development: `build/app/outputs/apk/development/release/app-development-release.apk`
- Staging: `build/app/outputs/apk/staging/release/app-staging-release.apk`
- Production: `build/app/outputs/apk/production/release/app-production-release.apk`

**AAB Files:**
- Staging: `build/app/outputs/bundle/stagingRelease/app-staging-release.aab`
- Production: `build/app/outputs/bundle/productionRelease/app-production-release.aab`

## ⚡ Performance Comparison

| Aspect | Flutter Lanes | Gradle Lanes |
|--------|---------------|--------------|
| Build Time | Slower (wrapper overhead) | Faster (direct) |
| Dependencies | Flutter + Gradle | Gradle only |
| Control | Limited | Full control |
| Debugging | Complex | Straightforward |
| Maintenance | High (2 paths) | Low (1 path) |

## 🔄 Migration Notes

**What was removed:**
- ❌ `flutter_dev`, `flutter_staging`, `flutter_production`
- ❌ `flutter_staging_aab`, `flutter_production_aab`
- ❌ `flutter_all` 

**What was kept/enhanced:**
- ✅ All Gradle lanes với version management
- ✅ AAB support cho Play Store deployment
- ✅ CI/CD integration
- ✅ Local development workflow

## 🎉 Conclusion

**Gradle-First Strategy** giúp:
- Simplify build process
- Reduce maintenance overhead  
- Improve performance
- Better control over Android builds
- Easier troubleshooting

Đây là best practice cho Flutter Android projects với complex CI/CD requirements. 