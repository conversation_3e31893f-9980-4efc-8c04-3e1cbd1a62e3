# Google Play Store Setup Guide

## Overview

Hướng dẫn này sẽ giúp bạn thiết lập 2 app riêng biệt trên Google Play Store cho Sales App:
1. **Staging App** (`com.kienlongbank.sales_app.staging`) - Dành cho external testing
2. **Production App** (`com.kienlongbank.sales_app`) - <PERSON><PERSON><PERSON> cho người dùng chính thức

## Preparation

### 1. Prerequisites
- Google Play Console Developer Account ($25 one-time fee)
- Admin access to Google Play Console
- Access to Google Cloud Console (for service accounts)

### 2. App Information
| App Type | Package Name | Purpose | Distribution |
|----------|--------------|---------|--------------|
| Staging | `com.kienlongbank.sales_app.staging` | QA & UAT Testing | External Testers Only |
| Production | `com.kienlongbank.sales_app` | Live Users | Public Release |

## Step 1: Create Apps on Google Play Console

### 1.1 Create Staging App

1. Login to [Google Play Console](https://play.google.com/console/)
2. Click **"Create app"**
3. Fill in details:
   - **App name**: `Sales App - Staging`
   - **Default language**: Vietnamese (or English)
   - **App or game**: App
   - **Free or paid**: Free
4. Accept policies and create app
5. Note the **Package name**: `com.kienlongbank.sales_app.staging`

### 1.2 Create Production App

1. Click **"Create app"** again
2. Fill in details:
   - **App name**: `Sales App`
   - **Default language**: Vietnamese (or English)
   - **App or game**: App
   - **Free or paid**: Free
3. Accept policies and create app
4. Note the **Package name**: `com.kienlongbank.sales_app`

## Step 2: Complete App Store Listings

### 2.1 Required Information for Both Apps

Prepare the following for each app:

#### App Details
- **Short description** (80 characters max)
- **Full description** (4000 characters max)
- **App icon** (512 x 512 px, PNG/JPG)
- **Feature graphic** (1024 x 500 px)
- **Screenshots** (minimum 2, up to 8)
  - Phone: 320-3840px width/height
  - Tablet: 320-3840px width/height

#### Content Rating
- Complete content rating questionnaire
- Select appropriate rating based on app functionality

#### Target Audience
- Age range selection
- Country targeting

### 2.2 Staging App Specific Settings

1. **App Access**: Unrestricted (for testers)
2. **Content Rating**: Same as production
3. **Target Audience**: Internal testers + External testers
4. **Store Listing**: Mark as "Testing Version" in description

### 2.3 Production App Specific Settings

1. **App Access**: Unrestricted (public)
2. **Content Rating**: Complete questionnaire
3. **Target Audience**: Public users
4. **Store Listing**: Full marketing materials

## Step 3: Set Up Service Accounts

### 3.1 Create Service Account for Staging

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable **Google Play Android Developer API**
4. Create Service Account:
   - **Name**: `sales-app-staging-ci`
   - **Role**: Service Account User
5. Generate JSON key and download
6. Go back to Play Console → Settings → API Access
7. Link service account and grant permissions:
   - ✅ View app information and download bulk reports
   - ✅ Manage Production APKs
   - ✅ Manage Testing APKs

### 3.2 Create Service Account for Production

1. Create another Service Account:
   - **Name**: `sales-app-production-ci`
   - **Role**: Service Account User
2. Generate JSON key and download
3. Link to Production app with same permissions

### 3.3 Store Service Account Keys Securely

- **Staging Key**: Save as `google-play-staging-service-account.json`
- **Production Key**: Save as `google-play-production-service-account.json`
- **⚠️ Never commit these files to Git**
- Store in GitLab CI Variables as File type

## Step 4: Configure Testing Tracks

### 4.1 Staging App Testing Setup

1. Go to Staging App → Release → Testing
2. Set up **Internal testing**:
   - Create internal testing release
   - Add testers (email addresses)
   - Set testing instructions
3. Set up **External testing** (Closed):
   - Create closed testing track
   - Add tester groups
   - Set opt-in URL for testers

### 4.2 Production App Testing Setup

1. Go to Production App → Release → Testing
2. Set up **Internal testing** first
3. **External testing** (Optional - for beta users)
4. **Production** track for live release

## Step 5: Upload Initial Releases

### 5.1 Build App Bundles (AAB)

```bash
# Build staging AAB
cd android
fastlane flutter_staging_aab

# Build production AAB  
fastlane flutter_production_aab
```

### 5.2 Manual Upload for Initial Setup

1. **Staging App**:
   - Go to Release → Internal testing
   - Click "Create new release"
   - Upload `app-staging-release.aab`
   - Add release notes
   - Review and rollout

2. **Production App**:
   - Go to Release → Internal testing
   - Click "Create new release"
   - Upload `app-production-release.aab`
   - Add release notes
   - Review and rollout

## Step 6: Configure GitLab CI Variables

### 6.1 Upload Service Account Keys

1. Go to GitLab Project → Settings → CI/CD → Variables
2. Add variables:

| Variable Name | Type | Value | Protected | Masked |
|---------------|------|--------|-----------|---------|
| `GOOGLE_PLAY_JSON_KEY_STAGING` | File | staging service account JSON | ✅ | ❌ |
| `GOOGLE_PLAY_JSON_KEY_PRODUCTION` | File | production service account JSON | ✅ | ❌ |

### 6.2 Test Deployment

```bash
# Test staging deployment
cd android
fastlane deploy_staging_playstore

# Test production deployment  
fastlane deploy_production_playstore
```

## Step 7: Distribution Workflow

### 7.1 Staging App Workflow

```bash
# 1. Deploy to internal testing
git push origin staging
# → GitLab CI builds AAB
# → Manual job: deploy_staging_playstore

# 2. Promote to external testing (if needed)
# → Manual job: promote_staging_to_external
```

### 7.2 Production App Workflow

```bash
# 1. Deploy to internal testing
git push origin main
# → GitLab CI builds AAB
# → Manual job: deploy_production_playstore

# 2. Promote to production
# → Manual job: promote_production_to_production
```

## Testing & Validation

### 7.1 Test Staging App

1. **Internal Testing**:
   - Add your email to internal testers
   - Install from Play Console link
   - Verify app functions correctly

2. **External Testing**:
   - Share opt-in URL with QA team
   - Collect feedback through Play Console

### 7.2 Test Production App

1. **Internal Testing**:
   - Validate production build
   - Test all critical features
   - Performance testing

2. **Production Release**:
   - Start with limited rollout (5-10%)
   - Monitor crash reports and reviews
   - Gradually increase rollout

## Monitoring & Maintenance

### 8.1 Play Console Monitoring

- **Android Vitals**: Monitor app performance
- **Crash Reports**: Track and fix crashes
- **User Reviews**: Respond to feedback
- **Security**: Monitor for security issues

### 8.2 Version Management

```bash
# Before each release, increment version
cd android
fastlane increment_version
fastlane version_info
```

### 8.3 Regular Tasks

| Frequency | Task | Apps |
|-----------|------|------|
| Weekly | Review crash reports | Both |
| Weekly | Check user feedback | Production |
| Monthly | Update Play Console policies | Both |
| Quarterly | Review app performance metrics | Both |

## Troubleshooting

### Common Issues

#### Upload Failures
```bash
# Check AAB file
unzip -l ../build/app/outputs/bundle/stagingRelease/app-staging-release.aab

# Verify package name matches
grep "applicationId" app/build.gradle.kts
```

#### Service Account Issues
- Verify API access permissions in Play Console
- Check service account key expiration
- Ensure proper roles assigned in Cloud Console

#### Version Conflicts
- Always increment version code before upload
- Check existing versions in Play Console
- Use proper track (internal → external → production)

### Debug Commands

```bash
# Test Fastlane setup
cd android
fastlane version_info

# Validate AAB file
bundletool validate --bundle=../build/app/outputs/bundle/stagingRelease/app-staging-release.aab

# Check signing
jarsigner -verify -verbose -certs ../build/app/outputs/bundle/stagingRelease/app-staging-release.aab
```

## Security Best Practices

1. **Service Account Keys**:
   - Store only in GitLab CI Variables (File type)
   - Never commit to repository
   - Use separate keys for staging/production
   - Rotate keys annually

2. **App Signing**:
   - Use Play App Signing (recommended)
   - Store upload keystore securely
   - Backup signing certificates

3. **Access Control**:
   - Limit Play Console access to necessary team members
   - Use principle of least privilege
   - Regular access reviews

## Support

### Documentation Links
- [Google Play Console Help](https://support.google.com/googleplay/android-developer/)
- [Play App Signing](https://developer.android.com/studio/publish/app-signing)
- [Android App Bundle](https://developer.android.com/guide/app-bundle)

### Internal Contacts
- **DevOps Team**: For CI/CD issues
- **QA Team**: For testing coordination  
- **Product Team**: For app store listing content 