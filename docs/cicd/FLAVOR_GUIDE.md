# 🍎 Flutter Flavor Configuration Guide

## 📖 Tổng quan

Sales App được cấu hình với 3 flavor chính:

- **Development** 🟢: Môi trường phát triển với debug tools và mock APIs
- **Staging** 🟡: Môi trường testing và UAT 
- **Production** 🔴: Môi trường sản xuất (production)

## 🏗️ Cấu trúc Flavor

### 1. C<PERSON>u hình <PERSON> (Gradle)
- File: `android/app/build.gradle.kts`
- Mỗi flavor có applicationId và app name riêng
- Build variants tự động được tạo (development-debug, staging-release, v.v.)

### 2. Main Files cho từng Flavor
```
lib/
├── main_development.dart  # Entry point cho development
├── main_staging.dart      # Entry point cho staging
├── main_production.dart   # Entry point cho production
└── main.dart              # Main app logic (shared)
```

### 3. <PERSON><PERSON><PERSON> hình AppConfig
- File: `lib/core/config/app_config.dart`
- Quản lý URL, database names, logging settings cho từng flavor

### 4. Tích hợp với EnvironmentService
- Flavor config được tích hợp với hệ thống Environment hiện có
- Tự động switch API URLs dựa trên flavor

## 🚀 Cách sử dụng

### Chạy App với Flavor

#### 1. Sử dụng Command Line
```bash
# Development flavor
fvm flutter run --flavor development -t lib/main_development.dart

# Staging flavor  
fvm flutter run --flavor staging -t lib/main_staging.dart

# Production flavor
fvm flutter run --flavor production -t lib/main_production.dart
```

#### 2. Sử dụng VSCode/Cursor
- Sử dụng launch configurations trong `.vscode/launch.json`
- Chọn configuration phù hợp từ debug panel:
  - "Development"
  - "Staging" 
  - "Production"

#### 3. Build APK cho từng Flavor
```bash
# Build tất cả flavors
./scripts/build_flavors.sh

# Hoặc build từng flavor riêng
fvm flutter build apk --flavor development -t lib/main_development.dart --release
fvm flutter build apk --flavor staging -t lib/main_staging.dart --release
fvm flutter build apk --flavor production -t lib/main_production.dart --release
```

## 📱 Thông tin App cho từng Flavor

| Flavor | App Name | Bundle ID | Base URL |
|--------|----------|-----------|----------|
| Development | Sales App Dev | com.kienlongbank.sales_app.dev | https://dev-api.kienlongbank.com |
| Staging | Sales App Staging | com.kienlongbank.sales_app.staging | https://staging-api.kienlongbank.com |
| Production | Sales App | com.kienlongbank.sales_app | https://api.kienlongbank.com |

## ⚙️ Cấu hình chi tiết cho từng Flavor

### Development 🟢
- **Debug Mode**: Enabled
- **Logging**: Enabled
- **Database**: `sales_app_dev.db`
- **Features**: All debug features enabled

### Staging 🟡
- **Debug Mode**: Disabled
- **Logging**: Enabled 
- **Database**: `sales_app_staging.db`
- **Features**: Production-like with logging

### Production 🔴
- **Debug Mode**: Disabled
- **Logging**: Disabled
- **Database**: `sales_app.db`
- **Features**: Production optimized

## 🔧 Thêm Flavor mới

### 1. Thêm vào AppConfig
```dart
enum Flavor {
  development,
  staging,
  uat,        // Flavor mới
  production,
}
```

### 2. Cập nhật Android Gradle
```kotlin
productFlavors {
    create("uat") {
        dimension = "default"
        applicationIdSuffix = ".uat"
        versionNameSuffix = "-uat"
        resValue("string", "app_name", "Sales App UAT")
    }
}
```

### 3. Tạo main file
```dart
// lib/main_uat.dart
import 'core/config/app_config.dart';
import 'main.dart' as runner;

void main() async {
  AppConfig.appFlavor = Flavor.uat;
  runner.main();
}
```

### 4. Thêm launch config
```json
{
    "name": "UAT",
    "request": "launch",
    "type": "dart",
    "program": "lib/main_uat.dart",
    "args": ["--flavor", "uat"]
}
```

## 📝 Best Practices

1. **Luôn test trên đúng flavor** trước khi release
2. **Không hardcode URLs** - sử dụng AppConfig
3. **Kiểm tra app name và bundle ID** cho từng build
4. **Sử dụng development flavor** cho daily development
5. **Sử dụng staging flavor** cho testing và demo
6. **Chỉ sử dụng production flavor** khi deploy

## 🔍 Debug và Troubleshooting

### Kiểm tra flavor hiện tại
```dart
print('Current flavor: ${AppConfig.name}');
print('Base URL: ${AppConfig.baseUrl}');
print('Is Debug: ${AppConfig.enableDebugMode}');
```

### Build issues
- Đảm bảo chạy `fvm flutter clean` trước khi build
- Kiểm tra Android SDK và build tools version
- Verify flavor name spelling trong command

### Testing flavors
```bash
# Test build không cài đặt
fvm flutter build apk --flavor development -t lib/main_development.dart --debug

# Kiểm tra analyze
fvm flutter analyze
```

## 📱 iOS Configuration (Tương lai)

Hiện tại chưa config cho iOS. Khi cần sẽ bổ sung:
- Xcode schemes cho từng flavor
- Info.plist configurations
- Bundle identifiers cho iOS

## 🎯 Kết luận

Hệ thống flavor giúp:
- ✅ Dễ dàng switch giữa các môi trường
- ✅ Tự động config URLs và settings
- ✅ Build multiple variants cùng lúc
- ✅ Không conflict khi install nhiều version
- ✅ Tích hợp với CI/CD pipeline

Cho bất kỳ câu hỏi nào, liên hệ team development! 🚀 