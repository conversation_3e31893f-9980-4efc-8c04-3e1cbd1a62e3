# 🚀 Flavor & CI/CD Quick Start

## ⚡ <PERSON><PERSON><PERSON> nhanh cho Developer

### 🏃‍♂️ Run Local
```bash
# Development (debug, có debug tools)
fvm flutter run --flavor development -t lib/main_development.dart

# Staging (giống production, có logging)  
fvm flutter run --flavor staging -t lib/main_staging.dart

# Production (optimized, minimal logging)
fvm flutter run --flavor production -t lib/main_production.dart
```

### 🔨 Build Local
```bash
# Android APK
fvm flutter build apk --release --flavor staging -t lib/main_staging.dart

# iOS IPA  
fvm flutter build ipa --release --flavor production -t lib/main_production.dart --export-options-plist=ios/ExportOptions_Production.plist

# Build tất cả
./scripts/build_all_flavors.sh
```

### 🌳 Branch Strategy
```bash
feature/* → develop → uat → main/prod
   ↓          ↓       ↓      ↓
 dev APK    dev APK  staging  production
                     (TestFlight) (TestFlight→App Store)
```

### 📦 CI/CD Triggers
| Branch | Builds | Deploy |
|--------|--------|--------|
| `develop`, `feature/*`, MR | Android Development APK | Download |
| `uat`, `release/*` | Android Staging + iOS IPA | TestFlight |
| `main`, `prod`, `tags` | Android Production + iOS IPA | TestFlight → App Store |

### 🍎 Flavor Info
| Flavor | Bundle ID | API URL | Debug |
|--------|-----------|---------|-------|
| Development | `.dev` | `dev-api.kienlongbank.com` | ✅ |
| Staging | `.staging` | `staging-api.kienlongbank.com` | ❌ |
| Production | (base) | `api.kienlongbank.com` | ❌ |

## 🔧 Troubleshooting

### ❌ Build Error
```bash
fvm flutter clean
fvm flutter pub get
dart run build_runner build --delete-conflicting-outputs
```

### ❌ iOS Code Signing
```bash
cd ios
bundle exec fastlane setup_codesigning
```

### ❌ CI Pipeline Fail
1. Check runner tags: `banking-mobile`, `macos`
2. Verify `fvm flutter doctor -v`
3. Check branch naming matches triggers

## 📖 Full Documentation
- **Complete Guide**: [FLAVOR_CICD_GUIDE.md](FLAVOR_CICD_GUIDE.md)
- **Project Status**: [PROJECT_STATUS.md](../project/PROJECT_STATUS.md)
- **Architecture**: [ARCHITECTURE_GUIDE.md](../architecture/ARCHITECTURE_GUIDE.md) 