# GitLab Runner Setup Guide for Flutter CI/CD

## 📋 Tổng quan

Hướng dẫn này sẽ giúp bạn thiết lập GitLab Runner trên máy <PERSON>ể build cả iOS và Android cho dự án Flutter Sales App.

## 🖥️ Yêu cầu hệ thống

### macOS Requirements
- **macOS**: 12.0+ (Monterey hoặc mới hơn)
- **Xcode**: 15.0+ với Command Line Tools
- **RAM**: Tối thiểu 8GB, khuyến nghị 16GB+
- **Storage**: Tối thiểu 100GB khả dụng
- **Internet**: Kết nối ổn định cho download dependencies

### Software Requirements
- **Git**: 2.30+
- **Flutter**: 3.24.5 (quản lý bằng FVM)
- **FVM**: Flutter Version Management
- **Fastlane**: Automation tool
- **CocoaPods**: iOS dependency manager
- **Android Studio**: Android development
- **Xcode**: iOS development

## 🔧 Cài đặt môi trường

### 1. Cài đặt Homebrew
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

### 2. Cài đặt Git và các tools cơ bản
```bash
brew install git curl wget
```

### 3. Cài đặt Flutter và FVM
```bash
# Cài đặt FVM
dart pub global activate fvm

# Add FVM to PATH
echo 'export PATH="$PATH:$HOME/.pub-cache/bin"' >> ~/.zshrc
source ~/.zshrc

# Cài đặt Flutter 3.24.5
fvm install 3.24.5
fvm global 3.24.5
```

### 4. Cài đặt Android Studio và SDK
```bash
# Download và cài đặt Android Studio từ: https://developer.android.com/studio
# Hoặc sử dụng Homebrew
brew install --cask android-studio

# Thiết lập ANDROID_HOME
echo 'export ANDROID_HOME=$HOME/Library/Android/sdk' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/emulator' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools/bin' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/platform-tools' >> ~/.zshrc
source ~/.zshrc
```

### 5. Cài đặt Xcode
```bash
# Cài đặt từ App Store hoặc:
# https://developer.apple.com/xcode/

# Cài đặt Command Line Tools
sudo xcode-select --install

# Accept license
sudo xcodebuild -license accept
```

### 6. Cài đặt CocoaPods
```bash
sudo gem install cocoapods
```

### 7. Cài đặt Fastlane
```bash
sudo gem install fastlane
```

### 8. Cài đặt Java (cho Android build)
```bash
brew install openjdk@17
echo 'export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"' >> ~/.zshrc
echo 'export JAVA_HOME="/opt/homebrew/opt/openjdk@17"' >> ~/.zshrc
source ~/.zshrc
```

## 🏃‍♂️ Cài đặt GitLab Runner

### 1. Download và cài đặt GitLab Runner
```bash
# Download binary
sudo curl --output /usr/local/bin/gitlab-runner "https://gitlab-runner-downloads.s3.amazonaws.com/latest/binaries/gitlab-runner-darwin-amd64"

# Cấp quyền thực thi
sudo chmod +x /usr/local/bin/gitlab-runner

# Cài đặt service
gitlab-runner install
gitlab-runner start
```

### 2. Đăng ký Runner với GitLab

#### Lấy Registration Token
1. Vào GitLab project → **Settings** → **CI/CD**
2. Expand **Runners** section
3. Copy **Registration token**

#### Đăng ký Runner
```bash
gitlab-runner register
```

**Thông tin cần nhập:**
```
GitLab instance URL: https://gitlab.com/ (hoặc self-hosted URL)
Registration token: [paste token từ GitLab]
Description: Flutter CI/CD Runner - Mac
Tags: flutter-runner,macos
Executor: shell
```

### 3. Cấu hình Runner tags

Trong file `/Users/<USER>/.gitlab-runner/config.toml`, đảm bảo có:
```toml
[[runners]]
  name = "Flutter CI/CD Runner - Mac"
  url = "https://gitlab.com/"
  token = "..."
  executor = "shell"
  tags = ["flutter-runner", "macos"]
  [runners.shell]
  [runners.cache]
    Type = "s3"
```

## 🔐 Thiết lập Signing & Certificates

### iOS Code Signing
```bash
# Tạo thư mục cho certificates
mkdir -p ~/.ios-certificates

# Import certificates vào Keychain
# (Cần có Developer Certificate và Provisioning Profiles)
```

### Android Signing
```bash
# Tạo thư mục cho Android keystore
mkdir -p ~/.android-signing

# Copy file keystore vào thư mục này
# cp /path/to/your/keystore.jks ~/.android-signing/
```

## 🌍 Thiết lập Environment Variables

### Tạo file ~/.gitlab-runner-env
```bash
cat > ~/.gitlab-runner-env << 'EOF'
# Flutter & Dart
export PATH="$PATH:$HOME/.pub-cache/bin"
export FLUTTER_ROOT="$HOME/fvm/versions/3.24.5"

# Android
export ANDROID_HOME="$HOME/Library/Android/sdk"
export PATH="$PATH:$ANDROID_HOME/emulator"
export PATH="$PATH:$ANDROID_HOME/tools"
export PATH="$PATH:$ANDROID_HOME/tools/bin"
export PATH="$PATH:$ANDROID_HOME/platform-tools"

# Java
export JAVA_HOME="/opt/homebrew/opt/openjdk@17"
export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"

# iOS
export DEVELOPER_DIR="/Applications/Xcode.app/Contents/Developer"

# Locale
export LC_ALL="en_US.UTF-8"
export LANG="en_US.UTF-8"

# Fastlane
export FASTLANE_SKIP_UPDATE_CHECK=1
export FASTLANE_HIDE_GITHUB_ISSUES=1

# GitLab CI specific
export CI=true
EOF
```

### Load environment trong shell profile
```bash
echo 'source ~/.gitlab-runner-env' >> ~/.zshrc
source ~/.zshrc
```

## 🧪 Kiểm tra setup

### 1. Kiểm tra Flutter
```bash
fvm flutter doctor -v
```

### 2. Kiểm tra Android
```bash
fvm flutter doctor --android-licenses
```

### 3. Kiểm tra iOS (trên macOS)
```bash
xcrun simctl list
```

### 4. Test build project
```bash
# Clone project
git clone [your-project-url]
cd sales_app

# Setup FVM
fvm use 3.24.5

# Get dependencies
fvm flutter pub get

# iOS setup
cd ios && pod install && cd ..

# Test Android build
cd android && fastlane gradle_dev_release && cd ..

# Test iOS build (trên macOS)
cd ios && fastlane ios development && cd ..
```

## 🔄 Khởi động Runner

### Manual start
```bash
gitlab-runner run
```

### Auto start (LaunchDaemon)
```bash
# Tạo launch daemon
sudo gitlab-runner install --user=$(whoami) --working-directory=$HOME

# Start service
sudo gitlab-runner start
```

## 📊 Monitoring & Logs

### Xem logs
```bash
# GitLab Runner logs
tail -f /var/log/gitlab-runner.log

# Hoặc nếu chạy manual
gitlab-runner --debug run
```

### Xem runner status trong GitLab
1. Vào GitLab project → **Settings** → **CI/CD**
2. Expand **Runners** section
3. Kiểm tra runner có "green dot" (online)

## 🚨 Troubleshooting

### Lỗi thường gặp

#### 1. Permission denied
```bash
# Fix permissions cho gitlab-runner user
sudo chown -R gitlab-runner:staff /Users/<USER>/.pub-cache
sudo chown -R gitlab-runner:staff /Users/<USER>/fvm
```

#### 2. Command not found
```bash
# Kiểm tra PATH trong runner environment
which fvm
which flutter
which fastlane
```

#### 3. iOS signing issues
```bash
# List available certificates
security find-identity -v -p codesigning

# List provisioning profiles
ls ~/Library/MobileDevice/Provisioning\ Profiles/
```

#### 4. Android SDK issues
```bash
# Re-accept licenses
flutter doctor --android-licenses

# Check SDK path
echo $ANDROID_HOME
ls $ANDROID_HOME
```

## 🔧 Maintenance

### Cập nhật Runner
```bash
sudo gitlab-runner stop
sudo curl --output /usr/local/bin/gitlab-runner "https://gitlab-runner-downloads.s3.amazonaws.com/latest/binaries/gitlab-runner-darwin-amd64"
sudo chmod +x /usr/local/bin/gitlab-runner
sudo gitlab-runner start
```

### Cleanup builds
```bash
# Cleanup Flutter builds
flutter clean

# Cleanup iOS builds
rm -rf ios/build/

# Cleanup Android builds
cd android && ./gradlew clean && cd ..

# Cleanup Pub cache
flutter pub cache clean
```

## 📝 Notes

1. **Security**: Đảm bảo máy runner được bảo mật và cập nhật thường xuyên
2. **Resources**: Monitor CPU/RAM usage khi build
3. **Storage**: Định kỳ cleanup build artifacts
4. **Network**: Đảm bảo kết nối internet ổn định
5. **Backup**: Backup certificates và signing keys

## 🔗 References

- [GitLab Runner Documentation](https://docs.gitlab.com/runner/)
- [Flutter CI/CD Best Practices](https://docs.flutter.dev/deployment/cd)
- [Fastlane Documentation](https://docs.fastlane.tools/)
- [FVM Documentation](https://fvm.app/) 