# UI Components Demo Documentation

## Tổng quan

Mà<PERSON> hình **UI Components Demo** được tạo để showcase tất cả các UI components common và shared trong ứng dụng Sales App. Đ<PERSON>y là một công cụ hữu ích cho developers để:

- Xem trước tất cả các components có sẵn
- Hiểu cách sử dụng từng component
- Test các tính năng và trạng thái khác nhau
- <PERSON><PERSON><PERSON> bảo tính nhất quán trong thiết kế

## Cách truy cập

### Debug Mode Only
UI Components Demo chỉ có thể truy cập trong **debug mode** để tránh hiển thị trong production:

1. Mở ứng dụng trong debug mode
2. Vào màn hình Home (HomeScreenV2)
3. Nhấn vào **Debug FAB** (biểu tượng bug) ở góc dưới bên phải
4. Chọn **"UI Components Demo"** từ menu

### Navigation Path
```
Home Screen → Debug FAB → UI Components Demo
```

## Components được Demo

### 1. 🔘 Common Buttons

#### CommonButton
- **Primary Button**: Button chính với màu primary
- **Disabled Button**: Button bị vô hiệu hóa
- **Custom Color Button**: Button với màu tùy chỉnh
- **Button with Icon**: Button có icon và text

**Tính năng:**
- Responsive sizing với flutter_screenutil
- Consistent styling theo AppDimens
- Support disabled state
- Custom colors và content

### 2. 📝 Common Text Fields

#### CommonTextField
- **Email Field**: Text field cho email với validation
- **Password Field**: Text field có tính năng ẩn/hiện password
- **Disabled Field**: Text field bị vô hiệu hóa

**Tính năng:**
- Responsive design
- Built-in validation
- Prefix/suffix icons
- Focus state handling
- Sensitive text support (password)

### 3. 📋 Common Dropdown

#### CommonDropdown
- **Location Dropdown**: Dropdown chọn tỉnh/thành phố
- Support generic types
- Custom item builders
- Prefix icons

**Tính năng:**
- Type-safe với generics
- Custom styling
- Responsive design
- Validation support

### 4. 👤 User Avatar

#### UserAvatar
- **Small Avatar**: Size nhỏ (32w)
- **Medium Avatar**: Size trung bình (40w)
- **Large Avatar**: Size lớn (56w)

**Tính năng:**
- Multiple sizes
- Fallback icon khi không có image
- Gradient background
- Tap handling
- Shadow effects

### 5. ⏳ Loading Demo

#### LoadingOverlay
- **Loading Simulation**: Demo loading state trong 3 giây
- Adaptive loading cho iOS/Android
- Silent loading by default

**Tính năng:**
- Platform-adaptive design
- Overlay system
- Non-blocking UI
- Smooth animations

### 6. 📷 Photo Source Picker

#### showCupertinoPhotoSourcePicker
- **Photo Picker Demo**: Hiển thị action sheet chọn nguồn ảnh
- Cupertino-style design
- Custom title và message

**Tính năng:**
- iOS-style action sheet
- Customizable content
- Callback handling
- Responsive design

### 7. 📱 Custom App Bar (Info Only)

#### CustomAppBar
Được sử dụng trong BaseScreen, cung cấp:
- Consistent styling
- Automatic back button
- Customizable actions
- Center title option
- Bottom widget support

### 8. 🏠 Base Screen (Info Only)

#### BaseScreen
Foundation cho tất cả screens:
- Consistent structure
- Built-in CustomAppBar
- White background
- Easy to extend

## Cấu trúc Code

### File Structure
```
lib/presentation/screens/demo/
├── navigation_demo.dart     # Navigation patterns demo
└── ui_demo_screen.dart      # UI components demo
```

### Dependencies
```dart
// Core
import '../../../core/constants/app_dimens.dart';
import '../../../core/theme/app_color.dart';

// Widgets
import '../../widgets/widgets.dart';

// Hooks
import 'package:flutter_hooks/flutter_hooks.dart';
```

### Key Features
- **HookWidget**: Sử dụng Flutter Hooks cho state management
- **BaseScreen**: Extends từ BaseScreen để có consistent structure
- **LoadingOverlay**: Tích hợp loading system
- **Responsive Design**: Tất cả dimensions sử dụng AppDimens

## Best Practices

### 1. Responsive Design
Tất cả components sử dụng `flutter_screenutil` và `AppDimens`:
```dart
// ✅ Good
width: AppDimens.containerMd,
fontSize: AppDimens.fontLG,

// ❌ Bad
width: 48,
fontSize: 16,
```

### 2. Consistent Styling
Sử dụng `AppColors` và `AppDimens` constants:
```dart
// ✅ Good
backgroundColor: AppColors.primaryColor,
borderRadius: AppDimens.borderRadius8,

// ❌ Bad
backgroundColor: Colors.blue,
borderRadius: BorderRadius.circular(8),
```

### 3. State Management
Sử dụng Flutter Hooks cho local state:
```dart
// ✅ Good
final textController = useTextEditingController();
final isLoading = useState(false);

// ❌ Bad - StatefulWidget cho simple state
```

## Testing Guidelines

### Manual Testing
1. **Responsive Testing**: Test trên các screen sizes khác nhau
2. **Interaction Testing**: Test tất cả buttons và interactions
3. **State Testing**: Test loading states, disabled states
4. **Navigation Testing**: Test navigation từ demo screens

### Visual Testing
1. **Consistency**: Đảm bảo styling nhất quán
2. **Spacing**: Kiểm tra spacing và alignment
3. **Colors**: Verify colors theo design system
4. **Typography**: Kiểm tra font sizes và weights

## Maintenance

### Adding New Components
Khi thêm component mới vào common/shared:

1. **Update UI Demo**: Thêm demo cho component mới
2. **Update Documentation**: Cập nhật docs này
3. **Add Examples**: Thêm ví dụ sử dụng khác nhau
4. **Test Thoroughly**: Test trên nhiều devices

### Version Control
- UI Demo screen được version control cùng với main codebase
- Chỉ hiển thị trong debug mode
- Không ảnh hưởng đến production build

## Troubleshooting

### Common Issues

1. **Debug FAB không hiển thị**
   - Đảm bảo đang chạy trong debug mode
   - Check `kDebugMode` flag

2. **Navigation không hoạt động**
   - Verify routes đã được thêm vào `app_routes.dart`
   - Check navigation extensions

3. **Components không hiển thị đúng**
   - Verify imports từ `widgets.dart`
   - Check AppDimens và AppColors

### Performance
- UI Demo screen không ảnh hưởng performance production
- Chỉ load khi được truy cập
- Memory efficient với proper disposal

## Future Enhancements

### Planned Features
1. **Interactive Examples**: Thêm controls để thay đổi properties
2. **Code Examples**: Hiển thị code snippet cho mỗi component
3. **Theme Switching**: Demo components với different themes
4. **Export Documentation**: Generate docs từ demo screen

### Integration Ideas
1. **Design System**: Tích hợp với design tokens
2. **Storybook**: Tương tự Storybook cho React
3. **Automated Testing**: Generate tests từ demo examples
4. **Documentation**: Auto-generate component docs
