# 🏗️ Architecture & Development Guide

## 📋 Table of Contents

1. [Clean Architecture Overview](#-clean-architecture-overview)
2. [Layer Details](#-layer-details)
   - [Presentation Layer](#-presentation-layer)
   - [Domain Layer](#-domain-layer)
   - [Data Layer](#-data-layer)
   - [Core Layer](#-core-layer)
3. [Development Guidelines](#-development-guidelines)
4. [Coding Standards](#-coding-standards)
5. [Best Practices](#-best-practices)
6. [Getting Started](#-getting-started)

## 🎯 Clean Architecture Overview

Sales App được xây dựng theo **Clean Architecture** với **Hybrid Structure** cho presentation layer, đảm bảo:

- ✅ **Separation of Concerns** - Mỗi layer có trách nhiệm riêng biệt
- ✅ **Dependency Inversion** - Dependencies point inward
- ✅ **Testability** - Dễ dàng unit test từng layer
- ✅ **Maintainability** - Code dễ maintain và extend
- ✅ **Scalability** - Architecture scale theo nhu cầu business

### 🏗️ Architecture Diagram

```
┌─────────────────────────────────────────┐
│           Presentation Layer            │
│  • Screens (Hybrid Structure)          │
│  • Widgets (Co-located)                │
│  • Controllers (Riverpod)              │
│  • Router (Go Router)                  │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│              Domain Layer               │
│  • Entities (Business Objects)         │
│  • Repository Interfaces               │
│  • Service Interfaces                  │
│  • Use Cases (Future)                  │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│               Data Layer                │
│  • Repository Implementations          │
│  • Data Sources (API, Local)           │
│  • Models (JSON Serialization)         │
│  • Network Layer (Comprehensive)       │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│               Core Layer                │
│  • Constants & Configuration           │
│  • Error Handling                      │
│  • Utils & Services                    │
│  • Theme & Styling                     │
└─────────────────────────────────────────┘
```

### 🎯 Key Principles

1. **Dependency Direction**: Outer layers depend on inner layers, never the reverse
2. **Interface Segregation**: Use abstractions, not concrete implementations
3. **Single Responsibility**: Each class has one reason to change
4. **Open/Closed**: Open for extension, closed for modification

## 🏗️ Layer Details

### 📱 Presentation Layer

**Responsibility**: UI components, user interactions, state management

#### Controllers (State Management)
Sử dụng Riverpod với code generation:

```dart
@riverpod
class AuthController extends _$AuthController {
  @override
  AuthState build() => const AuthState.initial();

  Future<void> login({required String email, required String password}) async {
    state = const AuthState.loading();
    
    final result = await ref.read(authRepositoryProvider).login(
      email: email, 
      password: password
    );
    
    result.fold(
      (failure) => state = AuthState.error(failure.message),
      (user) => state = AuthState.authenticated(user),
    );
  }
}
```

#### Router System
Navigation system tuân thủ Clean Architecture:

```dart
// Type-safe navigation
context.goToSelectAccountType();    // Push navigation
context.replaceWithHome();          // Replace navigation
context.goBack();                   // Smart back with fallback
```

#### Hybrid Structure
Widgets được tổ chức theo Hybrid Structure:

```
lib/presentation/
├── screens/
│   ├── auth/
│   │   ├── login_screen.dart
│   │   └── widgets/              # Co-located widgets
│   │       ├── account_type_selector.dart
│   │       └── document_dropdown.dart
│   └── home/
│       ├── home_screen.dart
│       └── widgets/              # Co-located widgets
│           ├── modern_search_bar.dart
│           └── modern_dashboard_cards.dart
└── widgets/
    ├── common/                   # Truly reusable widgets
    ├── loading/                  # Adaptive loading system
    └── shared/                   # Cross-feature widgets
```

**Benefits:**
- ✅ Import paths ngắn hơn 80%
- ✅ High cohesion - widgets gần screens
- ✅ Clear ownership - biết widget thuộc screen nào
- ✅ Easy refactoring - di chuyển cả screen folder

### 🎯 Domain Layer

**Responsibility**: Business logic, entities, repository interfaces

#### Entities
Pure Dart classes representing business objects:

```dart
class User {
  final String id;
  final String email;
  final String name;
  final String role;

  const User({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
  });
}
```

#### Repository Interfaces
Abstract contracts for data access:

```dart
abstract class AuthRepository {
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
  });
  
  Future<Either<Failure, void>> logout();
  Future<Either<Failure, User>> getCurrentUser();
}
```

### 📊 Data Layer

**Responsibility**: Data access, API calls, local storage, caching

#### Network Layer
Comprehensive network architecture with token refresh and retry system:

```
lib/data/network/
├── api_client.dart                    # Multiple Dio instances management
├── base_api_service.dart              # Base class for manual APIs
├── dio_builder.dart                   # Factory for configured Dio instances
├── openapi_client.dart                # Wrapper for future generated APIs
└── interceptors/                      # Request/response interceptors
    ├── auth_interceptor.dart          # Authentication interceptor
    ├── token_refresh_interceptor.dart # Automatic token refresh (proactive + reactive)
    ├── error_interceptor.dart         # Error handling interceptor
    ├── logging_interceptor.dart       # Request/response logging
    ├── simple_retry_interceptor.dart  # Simple automatic retry logic
    ├── README_RETRY.md                # Retry system documentation
    └── mock/                          # Mock API system
        ├── base_mock_interceptor.dart
        ├── mock_auth_interceptor.dart (with refresh token support)
        └── test_mock_interceptors.dart
```

#### Repository Implementation
Concrete implementations of domain interfaces:

```dart
@injectable
class AuthRepositoryImpl implements AuthRepository {
  final AuthApiService _apiService;
  final StorageService _storageService;

  AuthRepositoryImpl(this._apiService, this._storageService);

  @override
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
  }) async {
    try {
      final result = await _apiService.login(LoginRequest(
        email: email,
        password: password,
      ));
      
      return result.fold(
        (failure) => Left(failure),
        (response) async {
          await _storageService.saveToken(response.token);
          return Right(response.user.toEntity());
        },
      );
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
```

### 🔧 Core Layer

**Responsibility**: Shared utilities, constants, configuration

#### Constants & Configuration
```dart
// API Paths - Centralized endpoint definitions
class ApiPaths {
  static const String authLogin = '/auth/login';
  static const String authLogout = '/auth/logout';
  static const String userProfile = '/profile';
  
  static bool isAuthPath(String path) => path.startsWith('/auth/');
}

// App Constants
class AppConstants {
  static const bool isMockMode = true;
  static const String appVersion = '1.0.0';
  static const String appName = 'Sales App';
}
```

#### Network Connection Status (Core Enum)
Simplified network monitoring với core enum:

```dart
// Core enum shared across all layers
enum NetworkConnectionStatus {
  connected,    // Normal internet connection
  disconnected, // No internet connection (shows overlay)
  unstable,     // Frequent disconnections (shows notifications)
  checking,     // Currently checking connection status
}

// Extension methods for convenience
extension NetworkConnectionStatusExtension on NetworkConnectionStatus {
  bool get isConnected => this == NetworkConnectionStatus.connected;
  bool get isDisconnected => this == NetworkConnectionStatus.disconnected;
  bool get isUnstable => this == NetworkConnectionStatus.unstable;
  bool get isChecking => this == NetworkConnectionStatus.checking;

  String get statusIcon {
    switch (this) {
      case NetworkConnectionStatus.connected: return '✅';
      case NetworkConnectionStatus.disconnected: return '❌';
      case NetworkConnectionStatus.unstable: return '⚠️';
      case NetworkConnectionStatus.checking: return '🔄';
    }
  }
}
```

#### Error Handling
Structured error handling với Freezed:

```dart
@freezed
class Failure with _$Failure {
  const factory Failure.server(String message) = ServerFailure;
  const factory Failure.network(String message) = NetworkFailure;
  const factory Failure.auth(String message) = AuthFailure;
  const factory Failure.validation(String message) = ValidationFailure;
}
```

## 🛠️ Development Guidelines

### 📁 Project Structure

```
lib/
├── core/                   # Core layer
│   ├── config/            # App configuration
│   ├── constants/         # Constants & API paths
│   ├── enums/             # Application enums
│   ├── error/             # Error handling
│   ├── router/            # App routing configuration
│   ├── services/          # Core services
│   ├── theme/             # App theming
│   └── utils/             # Utilities
├── data/                  # Data layer
│   ├── datasources/       # Remote/Local data sources
│   ├── models/            # Data models (JSON serialization)
│   ├── network/           # Network layer (API clients, interceptors, retry)
│   ├── repositories_impl/ # Repository implementations
│   └── services_impl/     # Service implementations
├── domain/                # Domain layer
│   ├── entities/          # Business entities
│   ├── repositories/      # Repository interfaces
│   └── services/          # Service interfaces
├── presentation/          # Presentation layer (Hybrid Structure)
│   ├── controllers/       # Riverpod controllers (state management)
│   ├── router/            # Navigation system
│   ├── screens/           # Screen widgets with co-located widgets
│   └── widgets/           # Truly reusable widgets only
├── generated/             # Generated files (l10n, etc.)
└── l10n/                  # Internationalization
```

### 🎯 Widget Organization Rules

#### 1. **Screen-specific widgets** → `screens/[feature]/widgets/`
```dart
// lib/presentation/screens/auth/widgets/account_type_selector.dart
// Chỉ được sử dụng trong auth screens
```

#### 2. **Truly reusable widgets** → `widgets/common/`
```dart
// lib/presentation/widgets/common/common_button.dart
// Được sử dụng trong nhiều features khác nhau
```

#### 3. **Cross-feature widgets** → `widgets/shared/`
```dart
// lib/presentation/widgets/shared/user_avatar.dart
// Được sử dụng bởi 2-3 features
```

### 📝 Import Conventions

```dart
// ✅ Screen widgets (same folder)
import 'widgets/widget_name.dart';

// ✅ Common widgets
import '../../widgets/common/widget_name.dart';

// ✅ Shared widgets  
import '../../widgets/shared/widget_name.dart';

// ❌ NEVER: Cross-screen widgets
// import '../other_screen/widgets/widget_name.dart';  // DON'T DO THIS
```

## 📝 Coding Standards

### 🎯 State Management

#### Riverpod Controllers
```dart
// ✅ DO: Use code generation
@riverpod
class FeatureController extends _$FeatureController {
  @override
  FeatureState build() => const FeatureState.initial();

  Future<void> performAction() async {
    state = const FeatureState.loading();
    // Business logic
  }
}

// ✅ DO: Use Freezed for states
@freezed
class FeatureState with _$FeatureState {
  const factory FeatureState.initial() = _Initial;
  const factory FeatureState.loading() = _Loading;
  const factory FeatureState.success(Data data) = _Success;
  const factory FeatureState.error(String message) = _Error;
}
```

#### Consumer Widgets
```dart
// ✅ DO: Use ConsumerWidget for reactive UI
class MyScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(featureControllerProvider);

    return state.when(
      initial: () => InitialWidget(),
      loading: () => LoadingWidget(),
      success: (data) => SuccessWidget(data),
      error: (message) => ErrorWidget(message),
    );
  }
}
```

### 🌐 Navigation

#### Type-safe Navigation
```dart
// ✅ DO: Use navigation extensions
context.goToSelectAccountType();
context.replaceWithHome();

// ❌ DON'T: Use Navigator directly
Navigator.push(context, MaterialPageRoute(...));
```

#### Navigation Patterns
```dart
// ✅ DO: Use correct patterns
void handleLogin() {
  context.replaceWithHome(); // No back to login
}

void handleRegistrationStep() {
  context.goToCtvPolicy(); // Can go back
}
```

### 🔗 Dependency Injection

#### Service Registration
```dart
// ✅ DO: Use Injectable annotations
@injectable
class MyService implements MyServiceInterface {
  final ApiClient _apiClient;

  MyService(this._apiClient);
}

// ✅ DO: Register interfaces
@Injectable(as: MyServiceInterface)
class MyServiceImpl implements MyServiceInterface {
  // Implementation
}
```

#### Service Usage
```dart
// ✅ DO: Inject dependencies
class MyRepository {
  final MyService _service;

  MyRepository(this._service);
}

// ✅ DO: Use getIt for manual access
final service = getIt<MyService>();
```

### 📊 Data Layer

#### API Services
```dart
// ✅ DO: Extend BaseApiService
@injectable
class AuthApiService extends BaseApiService {
  AuthApiService(@Named('auth') super.apiClient); // Uses auth client (no auth interceptor)

  Future<Either<Failure, LoginResponse>> login(LoginRequest request) async {
    return handleApiCall(
      () => dio.post(ApiPaths.authLogin, data: createRequestData(request.toJson())),
      (data) => LoginResponse.fromJson(data),
    );
  }

  Future<Either<Failure, RefreshTokenResponse>> refreshToken(RefreshTokenRequest request) async {
    return handleApiCall(
      () => dio.post(ApiPaths.authRefresh, data: createRequestData(request.toJson())),
      (data) => RefreshTokenResponse.fromJson(data),
    );
  }
}
```

#### Models & Entities
```dart
// ✅ DO: Use Freezed for models
@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    required String id,
    required String email,
    required String name,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  // ✅ DO: Add conversion methods
  factory UserModel.fromEntity(User user) => UserModel(
    id: user.id,
    email: user.email,
    name: user.name,
  );
}

// ✅ DO: Add toEntity method
extension UserModelX on UserModel {
  User toEntity() => User(
    id: id,
    email: email,
    name: name,
  );
}
```

#### Error Handling
```dart
// ✅ DO: Use structured error handling with retry integration
@injectable
class UserRepository {
  final UserApiService _apiService;
  final NetworkInfo _networkInfo;

  Future<Either<Failure, List<User>>> getUsers() async {
    // Check network connectivity first
    if (!await _networkInfo.isConnected) {
      return const Left(NetworkFailure(NetworkErrorType.noConnection));
    }

    // API call with automatic retry and error handling
    // SimpleRetryInterceptor handles transient failures
    // ErrorInterceptor transforms remaining errors to domain failures
    return await _apiService.getUsers();
  }
}

// ✅ DO: Handle errors in presentation layer
final result = await userRepository.getUsers();
result.fold(
  (failure) {
    // Handle different failure types after retry attempts
    final message = switch (failure) {
      NetworkFailure() => 'Check your internet connection',
      ServerFailure() => 'Server error occurred', // After retry attempts
      AuthFailure() => 'Authentication required',
      _ => 'An unexpected error occurred',
    };
    showErrorSnackBar(message);
  },
  (users) => updateUsersList(users),
);
```

### Error Flow with Retry Integration

```
API Error → SimpleRetryInterceptor → ErrorInterceptor → Domain Failure → Presentation Error Message
     ↓              ↓                      ↓                ↓               ↓
Network Error → Retry Logic → Transform Error → Map to Failure → Show User Message
```

**Key Points:**
- **Retry First**: SimpleRetryInterceptor handles transient failures automatically
- **Error Transformation**: ErrorInterceptor transforms remaining errors to domain failures
- **Structured Failures**: Domain layer receives typed failures with error codes
- **User Messages**: Presentation layer maps failures to localized messages
- **Consistent Format**: All errors display as `message (error code)`
- **Retry Context**: Error messages reflect final failures after retry attempts
```

### 🎨 UI Components

#### Loading System
```dart
// ✅ DO: Use BaseLoadingHookWidget
class MyScreen extends BaseLoadingHookWidget {
  @override
  bool isLoading(WidgetRef ref) => ref.watch(myProvider).isLoading;

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.loading;

  @override
  String? getLoadingMessage(WidgetRef ref) => 'Processing...'; // Optional

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(body: MyContent());
  }
}
```

#### Responsive Design
```dart
// ✅ DO: Use ScreenUtil for responsive design
Container(
  width: 200.w,        // Responsive width
  height: 100.h,       // Responsive height
  padding: EdgeInsets.all(16.r), // Responsive padding
  child: Text(
    'Hello',
    style: TextStyle(fontSize: 16.sp), // Responsive font
  ),
)
```

### 📝 Logging

#### AppLogger Usage
```dart
// ✅ DO: Use AppLogger for consistent logging
AppLogger.info('User logged in successfully');

AppLogger.event('Button clicked', data: {
  'button': 'login',
  'timestamp': DateTime.now().toIso8601String(),
});

AppLogger.error('API call failed', error: exception);
```

## 🎯 Best Practices

### 🏗️ Architecture

1. **Follow Dependency Direction**
   - Outer layers depend on inner layers
   - Use interfaces, not concrete implementations
   - Domain layer should be framework-agnostic

2. **Single Responsibility**
   - Each class has one reason to change
   - Separate concerns clearly
   - Keep methods focused and small

3. **Interface Segregation**
   - Create specific interfaces for specific needs
   - Don't force classes to depend on unused methods

### 📱 Presentation Layer

1. **Widget Organization**
   - Co-locate widgets with their screens
   - Only put truly reusable widgets in common/
   - Use clear naming conventions

2. **State Management**
   - Use Riverpod controllers for business logic
   - Keep UI state separate from business state
   - Use Freezed for immutable states

3. **Navigation**
   - Use type-safe navigation methods
   - Follow push vs replace patterns correctly
   - Don't use Navigator directly

### 📊 Data Layer

1. **API Integration**
   - Use BaseApiService for consistent error handling
   - Implement Either pattern for error handling
   - Use interceptors for cross-cutting concerns

2. **Models**
   - Use Freezed for immutable models
   - Implement proper JSON serialization
   - Add conversion methods to/from entities

### 🔧 Development Workflow

1. **Code Generation**
   ```bash
   # Run after changes to @freezed, @injectable, @riverpod
   dart run build_runner build --delete-conflicting-outputs

   # Watch mode for development
   dart run build_runner watch --delete-conflicting-outputs
   ```

2. **Testing**
   ```bash
   # Run all tests
   flutter test

   # Run with coverage
   flutter test --coverage
   ```

3. **Linting**
   ```bash
   # Analyze code
   flutter analyze

   # Format code
   dart format .
   ```

## 🚀 Getting Started

### 📋 Prerequisites

- Flutter >= 3.10.0
- Dart >= 3.0.0
- Android Studio or VS Code
- Git

### 🛠️ Setup

1. **Clone & Install**
   ```bash
   git clone <repository-url>
   cd sales_app
   flutter pub get
   ```

2. **Code Generation**
   ```bash
   dart run build_runner build --delete-conflicting-outputs
   flutter gen-l10n
   ```

3. **Run App**
   ```bash
   flutter run
   ```

### 📚 Learning Path

#### For New Developers:
1. **Read this guide** - Understand architecture
2. **Explore codebase** - Start with presentation layer
3. **Run the app** - See it in action
4. **Check examples** - Look at existing screens
5. **Start coding** - Follow the patterns

#### For Experienced Developers:
1. **Review architecture** - Understand Clean Architecture implementation
2. **Check dependencies** - See what libraries are used
3. **Understand patterns** - State management, navigation, etc.
4. **Review business logic** - Check domain layer
5. **Start contributing** - Follow coding standards

### 🎯 Key Files to Understand

1. **lib/presentation/controllers/auth_controller.dart** - State management example
2. **lib/presentation/router/navigation_extensions.dart** - Navigation patterns
3. **lib/data/network/base_api_service.dart** - API service pattern
4. **lib/core/utils/app_logger.dart** - Logging system
5. **lib/presentation/widgets/loading/** - Loading system

### 📞 Support

- **Architecture Questions**: Check this guide
- **Implementation Help**: Look at existing examples
- **Business Logic**: Check domain layer
- **UI Components**: Check presentation layer

---

**This guide covers the essential architecture and development practices for the Sales App. Follow these guidelines to maintain code quality and consistency.**
