# 🏗️ Base State Management System Guide

## 📋 Table of Contents

1. [Overview](#-overview)
2. [Architecture Design](#-architecture-design)
3. [Base State Types](#-base-state-types)
4. [Base Notifier Implementation](#-base-notifier-implementation)
5. [Base Controller Pattern](#-base-controller-pattern)
6. [Usage Examples](#-usage-examples)
7. [Best Practices](#-best-practices)
8. [Migration Guide](#-migration-guide)

## 🎯 Overview

Base State Management System cung cấp một framework hoàn chỉnh để xử lý các trạng thái API calls (initial, loading, error, submitting, submitted, success) một cách nhất quán và tái sử dụng được với Riverpod.

### 🎯 Mục tiêu

- ✅ **Tránh lặp code**: Không cần viết lại logic xử lý trạng thái cho mỗi API call
- ✅ **Consistent UX**: Tất cả màn hình có behavior loading/error giống nhau
- ✅ **Type Safety**: Sử dụng freezed để đảm bảo type safety
- ✅ **Error Handling**: Tích hợp với Error Handling System hiện có
- ✅ **Automatic Error Display**: Tự động hiển thị error qua SnackBar/Dialog
- ✅ **Retry Support**: Hỗ trợ retry tự động và manual
- ✅ **Offline Support**: Xử lý offline state một cách graceful
- ✅ **Hooks Support**: Hỗ trợ hooks_riverpod cho advanced use cases

## 🏗️ Architecture Design

### System Architecture

```
BaseState<T> → BaseStateMixin<T> → Controllers → UI Widgets
     ↓                ↓                ↓              ↓
State Types      State Logic      Business Logic    User Interface
                    ↓
              ErrorInterceptor (handles network issues)
                    ↓
              BaseStateErrorHandler (automatic error display)
```

### Component Hierarchy

```
BaseState<T> (freezed)
├── BaseStateMixin<T> (mixin)
│   ├── AuthController (@riverpod)
│   ├── UserController (@riverpod)
│   └── ProductController (@riverpod)
└── UI Widgets
    ├── BaseStateBuilder (StatelessWidget)
    ├── BaseStateConsumerBuilder (ConsumerWidget)
    ├── BaseStateHookBuilder (HookConsumerWidget)
    ├── BaseStateErrorHandler (automatic error display)
    ├── BaseLoadingWidget
    ├── BaseErrorWidget
    ├── BaseSuccessWidget
    ├── BaseEmptyWidget
    ├── BaseRefreshingWidget
    └── BaseSubmittingWidget
```

## 📊 Base State Types

### Core State Structure (Improved with ApiState ideas)

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'base_state.freezed.dart';

/// Base state để quản lý các trạng thái API call
@freezed
class BaseState<T> with _$BaseState<T> {
  /// Initial state - no data loaded yet
  const factory BaseState.initial() = _Initial<T>;

  /// Loading state - data is being fetched
  const factory BaseState.loading() = _Loading<T>;

  /// Success state - data loaded successfully
  const factory BaseState.success(T data) = _Success<T>;

  /// Error state - something went wrong (keeps previous data for better UX)
  const factory BaseState.error(Failure failure, {T? previousData}) = _Error<T>;

  /// Submitting state - form is being submitted (keeps previous data)
  const factory BaseState.submitting({T? previousData}) = _Submitting<T>;

  /// Empty state - no data available
  const factory BaseState.empty() = _Empty<T>;

  /// Refreshing state - refreshing existing data
  const factory BaseState.refreshing(T data) = _Refreshing<T>;
}
```

### State Extensions (Rich Helper Methods inspired by ApiState)

```dart
/// Extension methods để dễ dàng check trạng thái
extension BaseStateX<T> on BaseState<T> {
  /// Basic state checks
  bool get isInitial => this is _Initial<T>;
  bool get isLoading => this is _Loading<T>;
  bool get isSuccess => this is _Success<T>;
  bool get isError => this is _Error<T>;
  bool get isSubmitting => this is _Submitting<T>;
  bool get isEmpty => this is _Empty<T>;
  bool get isRefreshing => this is _Refreshing<T>;

  /// Lấy data nếu có (bao gồm cả data từ submitting/error state)
  T? get data => when(
        initial: () => null,
        loading: () => null,
        success: (data) => data,
        error: (failure, previousData) => previousData,
        submitting: (previousData) => previousData,
        empty: () => null,
        refreshing: (data) => data,
      );

  /// Lấy failure nếu có
  Failure? get failure => when(
        initial: () => null,
        loading: () => null,
        success: (_) => null,
        error: (failure, _) => failure,
        submitting: (_) => null,
        empty: () => null,
        refreshing: (_) => null,
      );

  /// Lấy error message nếu có
  String? get errorMessage => when(
        initial: () => null,
        loading: () => null,
        success: (_) => null,
        error: (failure, _) => failure.serverMessage,
        submitting: (_) => null,
        empty: () => null,
        refreshing: (_) => null,
      );

  /// Lấy error code nếu có
  String? get errorCode => when(
        initial: () => null,
        loading: () => null,
        success: (_) => null,
        error: (failure, _) => failure.serverErrorCode,
        submitting: (_) => null,
        empty: () => null,
        refreshing: (_) => null,
      );

  /// Check xem có đang thực hiện action nào không (loading hoặc submitting)
  bool get isProcessing => isLoading || isSubmitting;

  /// Check xem submitting có data hay không
  bool get isSubmittingWithData => when(
        initial: () => false,
        loading: () => false,
        success: (_) => false,
        error: (_, __) => false,
        submitting: (previousData) => previousData != null,
        empty: () => false,
        refreshing: (_) => false,
      );

  /// Check xem error có data hay không
  bool get isErrorWithData => when(
        initial: () => false,
        loading: () => false,
        success: (_) => false,
        error: (_, previousData) => previousData != null,
        submitting: (_) => false,
        empty: () => false,
        refreshing: (_) => false,
      );

  /// Check xem có thể retry không (error state)
  bool get canRetry => isError;

  /// Check xem có thể refresh không (success hoặc error with data)
  bool get canRefresh => isSuccess || isErrorWithData;

  /// Check xem có thể submit không (success state)
  bool get canSubmit => isSuccess;
}
```

## 🔧 Base Notifier Implementation

### Phương án 1: Base Notifier với Callback Functions (Recommended)

```dart
abstract class BaseNotifier<T> extends AutoDisposeNotifier<BaseState<T>> {
  /// Network info for connectivity check
  NetworkInfo get networkInfo;
  
  @override
  BaseState<T> build() {
    return const BaseState.initial();
  }
  
  /// Load data with automatic error handling
  Future<void> loadData(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isInProgress) return; // Prevent multiple calls
    
    state = const BaseState.loading();
    
    try {
      // Call provided data function
      // Network connectivity and retry logic are handled by ErrorInterceptor and SimpleRetryInterceptor
      final result = await dataCall();
      
      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = data != null 
          ? BaseState.success(data)
          : const BaseState.empty(),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in loadData', error: e);
      // Transform unexpected errors to domain failures
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }
  
  /// Submit data with automatic error handling
  Future<void> submitData<R>(
    R data,
    Future<Either<Failure, T>> Function(R data) submitCall,
  ) async {
    if (state.isSubmitting) return; // Prevent multiple submissions
    
    // Keep previous data if available for better UX
    final previousData = state.data;
    state = BaseState.submitting(previousData: previousData);
    
    try {
      // Call provided submit function
      // Network connectivity and retry logic are handled by ErrorInterceptor and SimpleRetryInterceptor
      final result = await submitCall(data);
      
      result.fold(
        (failure) => state = BaseState.error(failure, previousData: previousData),
        (response) => state = BaseState.success(response),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in submitData', error: e);
      // Transform unexpected errors to domain failures
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
        previousData: previousData,
      );
    }
  }
  
  /// Refresh data while keeping existing data visible
  Future<void> refreshData(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isInProgress) return;
    
    final currentData = state.data;
    if (currentData == null) {
      await loadData(dataCall);
      return;
    }
    
    state = BaseState.refreshing(currentData);
    
    try {
      // Network connectivity và retry logic đều do ErrorInterceptor và SimpleRetryInterceptor xử lý
      final result = await dataCall();
      
      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = data != null 
          ? BaseState.success(data)
          : const BaseState.empty(),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in refreshData', error: e);
      // Chuyển lỗi bất ngờ thành ServerFailure.internalError
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }
  
  /// Retry last operation
  Future<void> retry(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isError) {
      await loadData(dataCall);
    }
  }
  
  /// Reset to initial state
  void reset() {
    state = const BaseState.initial();
  }
  
  /// Set custom state
  void setState(BaseState<T> newState) {
    state = newState;
  }
}
```

### Phương án 2: Base Notifier với Generic Repository Interface

```dart
/// Generic repository interface for base operations
abstract class BaseRepository<T> {
  Future<Either<Failure, T?>> getData();
  Future<Either<Failure, T>> submitData(dynamic data);
}

abstract class BaseNotifier<T> extends AutoDisposeNotifier<BaseState<T>> {
  /// Generic repository for data operations
  BaseRepository<T> get repository;
  
  /// Network info for connectivity check
  NetworkInfo get networkInfo;
  
  @override
  BaseState<T> build() {
    return const BaseState.initial();
  }
  
  /// Load data with automatic error handling
  Future<void> loadData() async {
    if (state.isInProgress) return;
    
    state = const BaseState.loading();
    
    try {
      // Network connectivity is handled by ErrorInterceptor
      final result = await repository.getData();
      
      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = data != null 
          ? BaseState.success(data)
          : const BaseState.empty(),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in loadData', error: e);
      state = BaseState.error(
        AuthFailure(
          AuthErrorType.unknown,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }
  
  // ... other methods similar to above
}
```

### Phương án 3: Mixin Pattern (Simplest)

```dart
mixin BaseStateMixin<T> on AutoDisposeNotifier<BaseState<T>> {
  /// Network info for connectivity check
  NetworkInfo get networkInfo;
  
  /// Load data with automatic error handling
  Future<void> loadData(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isInProgress) return;
    
    state = const BaseState.loading();
    
    try {
      if (!await networkInfo.isConnected) {
        state = BaseState.error(
          const NetworkFailure(NetworkErrorType.noConnection),
        );
        return;
      }
      
      final result = await dataCall();
      
      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = data != null 
          ? BaseState.success(data)
          : const BaseState.empty(),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in loadData', error: e);
      state = BaseState.error(
        AuthFailure(
          AuthErrorType.unknown,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }
  
  /// Submit data with automatic error handling
  Future<void> submitData<R>(
    R data,
    Future<Either<Failure, T>> Function(R data) submitCall,
  ) async {
    if (state.isSubmitting) return;
    
    state = const BaseState.submitting();
    
    try {
      if (!await networkInfo.isConnected) {
        state = BaseState.error(
          const NetworkFailure(NetworkErrorType.noConnection),
        );
        return;
      }
      
      final result = await submitCall(data);
      
      result.fold(
        (failure) => state = BaseState.error(failure),
        (response) => state = BaseState.success(response),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in submitData', error: e);
      state = BaseState.error(
        AuthFailure(
          AuthErrorType.unknown,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }
  
  /// Refresh data while keeping existing data visible
  Future<void> refreshData(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isInProgress) return;
    
    final currentData = state.data;
    if (currentData == null) {
      await loadData(dataCall);
      return;
    }
    
    state = BaseState.refreshing(currentData);
    
    try {
      // Network connectivity và retry logic đều do ErrorInterceptor và SimpleRetryInterceptor xử lý
      final result = await dataCall();
      
      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = data != null 
          ? BaseState.success(data)
          : const BaseState.empty(),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in refreshData', error: e);
      // Chuyển lỗi bất ngờ thành ServerFailure.internalError
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }
  
  /// Retry last operation
  Future<void> retry(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isError) {
      await loadData(dataCall);
    }
  }
  
  /// Reset to initial state
  void reset() {
    state = const BaseState.initial();
  }
  
  /// Set custom state
  void setState(BaseState<T> newState) {
    state = newState;
  }
}
```

## 🎨 Common UI Components

### Tổng quan

Common UI Components cung cấp các widget tái sử dụng để hiển thị các trạng thái khác nhau của BaseState một cách nhất quán.

### 1. BaseLoadingWidget

```dart
class BaseLoadingWidget extends StatelessWidget {
  final String? message;
  final double? size;
  
  const BaseLoadingWidget({
    super.key,
    this.message,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size ?? 32,
            height: size ?? 32,
            child: const CircularProgressIndicator(),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

// Sử dụng
const BaseLoadingWidget(message: 'Đang tải dữ liệu...')
```

### 2. BaseErrorWidget

```dart
class BaseErrorWidget extends StatelessWidget {
  final Failure failure;
  final VoidCallback? onRetry;
  final String? customMessage;
  
  const BaseErrorWidget({
    super.key,
    required this.failure,
    this.onRetry,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    final errorMessage = customMessage ?? 
        ErrorMessageMapper.getErrorMessage(context, failure);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              errorMessage,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(S.of(context).retry),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Sử dụng
BaseErrorWidget(
  failure: failure,
  onRetry: () => ref.read(userControllerProvider.notifier).retry(),
)
```

### 3. BaseSuccessWidget

```dart
class BaseSuccessWidget<T> extends StatelessWidget {
  final T data;
  final Widget Function(T data) builder;
  final VoidCallback? onRefresh;
  
  const BaseSuccessWidget({
    super.key,
    required this.data,
    required this.builder,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh != null 
          ? () async => onRefresh!()
          : () async {},
      child: builder(data),
    );
  }
}

// Sử dụng
BaseSuccessWidget<User>(
  data: user,
  builder: (user) => UserProfileCard(user: user),
  onRefresh: () => ref.read(userControllerProvider.notifier).refreshData(),
)
```

### 4. BaseEmptyWidget

```dart
class BaseEmptyWidget extends StatelessWidget {
  final String? message;
  final IconData? icon;
  final VoidCallback? onAction;
  final String? actionLabel;
  
  const BaseEmptyWidget({
    super.key,
    this.message,
    this.icon,
    this.onAction,
    this.actionLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.inbox_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              message ?? S.of(context).noData,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            if (onAction != null && actionLabel != null) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: onAction,
                child: Text(actionLabel!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Sử dụng
BaseEmptyWidget(
  message: 'Không có sản phẩm nào',
  icon: Icons.inventory_2,
  onAction: () => ref.read(productControllerProvider.notifier).loadData(),
  actionLabel: 'Tải sản phẩm',
)
```

### 5. BaseRefreshingWidget

```dart
class BaseRefreshingWidget<T> extends StatelessWidget {
  final T data;
  final Widget Function(T data) builder;
  
  const BaseRefreshingWidget({
    super.key,
    required this.data,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        builder(data),
        Positioned(
          top: 16,
          right: 16,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Sử dụng
BaseRefreshingWidget<List<Product>>(
  data: products,
  builder: (products) => ProductList(products: products),
)
```

### 6. BaseStateBuilder (Helper Widget) - Updated

BaseStateBuilder đã được cải tiến để hỗ trợ hooks_riverpod và error handling tự động.

#### 6.1 BaseStateBuilder (StatelessWidget)

```dart
class BaseStateBuilder<T> extends StatelessWidget {
  final BaseState<T> state;
  final Widget Function(T data) successBuilder;
  final Widget Function(Failure failure, VoidCallback? onRetry)? errorBuilder;
  final Widget? loadingBuilder;
  final Widget? emptyBuilder;
  final Widget? initialBuilder;
  final Widget? submittedBuilder;
  final Widget? submittingOverlay;
  final Widget Function(T data)? refreshingBuilder;
  final VoidCallback? onRetry;
  final bool showContentWhileSubmitting;
  
  const BaseStateBuilder({
    super.key,
    required this.state,
    required this.successBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.initialBuilder,
    this.submittedBuilder,
    this.submittingOverlay,
    this.refreshingBuilder,
    this.onRetry,
    this.showContentWhileSubmitting = false,
  });

  @override
  Widget build(BuildContext context) {
    return state.when(
      initial: () => initialBuilder ?? const SizedBox.shrink(),
      loading: () => loadingBuilder ?? const BaseLoadingWidget(),
      success: (data) => successBuilder(data),
      error: (failure, previousData) => errorBuilder?.call(
        failure,
        onRetry,
      ) ?? BaseErrorWidget(
        failure: failure,
        onRetry: onRetry,
      ),
      submitting: (previousData) => _buildSubmittingState(previousData),
      submitted: (data) => _buildSubmittedState(data),
      empty: () => emptyBuilder ?? const BaseEmptyWidget(),
      refreshing: (data) => refreshingBuilder?.call(data) ?? 
          BaseRefreshingWidget(data: data, builder: successBuilder),
    );
  }

  Widget _buildSubmittingState(T? previousData) {
    // Nếu có data và muốn show content while submitting
    if (showContentWhileSubmitting && previousData != null) {
      return Stack(
        children: [
          // Hiển thị content bình thường
          successBuilder(previousData),
          // Overlay loading
          submittingOverlay ?? const DefaultSubmittingOverlay(),
        ],
      );
    }
    // Fallback: hiển thị full loading
    return const DefaultSubmittingWidget();
  }

  Widget _buildSubmittedState(T? data) {
    if (submittedBuilder != null) {
      return submittedBuilder!;
    }
    if (data != null) {
      return successBuilder(data);
    }
    return emptyBuilder ?? const BaseEmptyWidget();
  }
}
```

#### 6.2 BaseStateConsumerBuilder (ConsumerWidget)

```dart
class BaseStateConsumerBuilder<T> extends ConsumerWidget {
  final ProviderBase<BaseState<T>> stateProvider;
  final Widget Function(T data, WidgetRef ref) successBuilder;
  final Widget Function(Failure failure, VoidCallback? onRetry, WidgetRef ref)? errorBuilder;
  final Widget Function(WidgetRef ref)? loadingBuilder;
  final Widget Function(WidgetRef ref)? emptyBuilder;
  final Widget Function(WidgetRef ref)? initialBuilder;
  final Widget Function(WidgetRef ref)? submittedBuilder;
  final Widget? submittingOverlay;
  final Widget Function(T data, WidgetRef ref)? refreshingBuilder;
  final VoidCallback? onRetry;
  final bool showContentWhileSubmitting;
  
  const BaseStateConsumerBuilder({
    super.key,
    required this.stateProvider,
    required this.successBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.initialBuilder,
    this.submittedBuilder,
    this.submittingOverlay,
    this.refreshingBuilder,
    this.onRetry,
    this.showContentWhileSubmitting = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(stateProvider);
    
    return state.when(
      initial: () => initialBuilder?.call(ref) ?? const SizedBox.shrink(),
      loading: () => loadingBuilder?.call(ref) ?? const BaseLoadingWidget(),
      success: (data) => successBuilder(data, ref),
      error: (failure, previousData) => errorBuilder?.call(
        failure,
        onRetry,
        ref,
      ) ?? BaseErrorWidget(
        failure: failure,
        onRetry: onRetry,
      ),
      submitting: (previousData) => _buildSubmittingState(previousData, ref),
      submitted: (data) => _buildSubmittedState(data, ref),
      empty: () => emptyBuilder?.call(ref) ?? const BaseEmptyWidget(),
      refreshing: (data) => refreshingBuilder?.call(data, ref) ?? 
          BaseRefreshingWidget(data: data, builder: (data) => successBuilder(data, ref)),
    );
  }

  // ... _buildSubmittingState và _buildSubmittedState tương tự như BaseStateBuilder
}
```

#### 6.3 BaseStateHookBuilder (HookConsumerWidget)

```dart
class BaseStateHookBuilder<T> extends HookConsumerWidget {
  final ProviderBase<BaseState<T>> stateProvider;
  final Widget Function(T data, WidgetRef ref) successBuilder;
  // ... các parameter tương tự BaseStateConsumerBuilder

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(stateProvider);
    
    return state.when(
      // ... logic tương tự BaseStateConsumerBuilder
    );
  }
}
```

#### 6.4 Sử dụng

```dart
// Case 1: Đơn giản (không cần WidgetRef)
BaseStateBuilder<User>(
  state: state,
  successBuilder: (user) => UserProfileWidget(user: user),
)

// Case 2: Cần access WidgetRef (Consumer)
BaseStateConsumerBuilder<User>(
  stateProvider: userControllerProvider,
  successBuilder: (user, ref) => UserProfileWidget(
    user: user,
    onEdit: () => ref.read(userControllerProvider.notifier).editUser(),
  ),
  errorBuilder: (failure, onRetry, ref) => CustomErrorWidget(
    failure: failure,
    onRetry: onRetry,
    onRefresh: () => ref.refresh(userControllerProvider),
  ),
)

// Case 3: Cần sử dụng hooks
BaseStateHookBuilder<User>(
  stateProvider: userControllerProvider,
  successBuilder: (user, ref) {
    final controller = useTextEditingController();
    return UserProfileWidget(
      user: user,
      controller: controller,
    );
  },
)
```

### 7. Sử dụng trong Screen - Updated

#### 7.1 Sử dụng BaseStateConsumerBuilder (Recommended)

```dart
class UserProfileScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).profile),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(userControllerProvider.notifier).refreshData(),
          ),
        ],
      ),
      body: BaseStateConsumerBuilder<User>(
        stateProvider: userControllerProvider,
        successBuilder: (user, ref) => UserProfileContent(
          user: user,
          onEdit: () => ref.read(userControllerProvider.notifier).editUser(user),
        ),
        errorBuilder: (failure, onRetry, ref) => BaseErrorWidget(
          failure: failure,
          onRetry: onRetry,
        ),
        loadingBuilder: (ref) => const BaseLoadingWidget(message: 'Đang tải thông tin...'),
        emptyBuilder: (ref) => BaseEmptyWidget(
          message: 'Không tìm thấy thông tin người dùng',
          onAction: () => ref.read(userControllerProvider.notifier).loadData(),
          actionLabel: 'Tải lại',
        ),
      ).withErrorHandler(
        userControllerProvider,
        displayType: ErrorDisplayType.snackBar,
        onError: () {
          // Custom error handling logic
          print('Error occurred in UserProfileScreen');
        },
      ),
    );
  }
}
```

#### 7.2 Sử dụng BaseStateHookBuilder (với Hooks)

```dart
class UserProfileScreen extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController();
    
    return Scaffold(
      appBar: AppBar(title: Text(S.of(context).profile)),
      body: BaseStateHookBuilder<User>(
        stateProvider: userControllerProvider,
        successBuilder: (user, ref) => UserProfileContent(
          user: user,
          emailController: emailController,
          onSave: () => ref.read(userControllerProvider.notifier).updateUser(user),
        ),
        errorBuilder: (failure, onRetry, ref) => BaseErrorWidget(
          failure: failure,
          onRetry: onRetry,
        ),
      ).withErrorHandler(
        userControllerProvider,
        displayType: ErrorDisplayType.dialog,
        showErrorDialog: true,
        customErrorTitle: 'Lỗi tải thông tin',
      ),
    );
  }
}
```

#### 7.3 Sử dụng BaseStateBuilder (StatelessWidget)

```dart
class UserProfileScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userState = ref.watch(userControllerProvider);
    
    return Scaffold(
      appBar: AppBar(title: Text(S.of(context).profile)),
      body: BaseStateBuilder<User>(
        state: userState,
        successBuilder: (user) => UserProfileContent(user: user),
        errorBuilder: (failure, onRetry) => BaseErrorWidget(
          failure: failure,
          onRetry: onRetry,
        ),
        loadingBuilder: const BaseLoadingWidget(message: 'Đang tải thông tin...'),
        emptyBuilder: BaseEmptyWidget(
          message: 'Không tìm thấy thông tin người dùng',
          onAction: () => ref.read(userControllerProvider.notifier).loadData(),
          actionLabel: 'Tải lại',
        ),
        showContentWhileSubmitting: true,
        submittingOverlay: const DefaultSubmittingOverlay(),
      ),
    );
  }
}
```

### 8. Error Handling System - NEW

#### 8.1 BaseStateErrorHandler Widget

```dart
/// Widget helper để tự động handle error từ provider và hiển thị dialog/snackbar
class BaseStateErrorHandler<T> extends ConsumerWidget {
  final ProviderBase<BaseState<T>> stateProvider;
  final Widget child;
  final ErrorDisplayType displayType;
  final VoidCallback? onError;
  final bool showErrorSnackBar;
  final bool showErrorDialog;
  final String? customErrorTitle;
  final String? customErrorMessage;
  
  const BaseStateErrorHandler({
    super.key,
    required this.stateProvider,
    required this.child,
    this.displayType = ErrorDisplayType.snackBar,
    this.onError,
    this.showErrorSnackBar = true,
    this.showErrorDialog = false,
    this.customErrorTitle,
    this.customErrorMessage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to state changes and handle errors
    ref.listen<BaseState<T>>(stateProvider, (previous, next) {
      // Chỉ handle error state
      if (next.isError && previous?.isError != true) {
        final failure = next.failure;
        if (failure != null) {
          _handleError(context, failure);
        }
      }
    });

    return child;
  }

  void _handleError(BuildContext context, Failure failure) {
    // Call custom error handler if provided
    onError?.call();

    // Get error message
    final errorMessage = customErrorMessage ?? 
        ErrorMessageMapper.getErrorMessage(context, failure);

    // Show error based on display type
    switch (displayType) {
      case ErrorDisplayType.snackBar:
        if (showErrorSnackBar) {
          _showErrorSnackBar(context, errorMessage);
        }
        break;
      case ErrorDisplayType.dialog:
        if (showErrorDialog) {
          _showErrorDialog(context, errorMessage);
        }
        break;
      case ErrorDisplayType.both:
        if (showErrorSnackBar) {
          _showErrorSnackBar(context, errorMessage);
        }
        if (showErrorDialog) {
          _showErrorDialog(context, errorMessage);
        }
        break;
      case ErrorDisplayType.none:
        // Do nothing
        break;
    }
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: AppDimens.borderRadius8,
        ),
        margin: EdgeInsets.all(AppDimens.spacingMD),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    CommonDialog.showErrorDialog(
      context: context,
      title: customErrorTitle ?? 'Lỗi',
      message: message,
    );
  }
}
```

#### 8.2 ErrorDisplayType Enum

```dart
/// Enum để chọn cách hiển thị error
enum ErrorDisplayType {
  snackBar,    // Chỉ hiển thị SnackBar
  dialog,      // Chỉ hiển thị Dialog
  both,        // Hiển thị cả SnackBar và Dialog
  none,        // Không hiển thị gì
}
```

#### 8.3 Extension để dễ sử dụng

```dart
/// Extension để dễ dàng thêm error handling vào widget
extension BaseStateErrorHandlerExtension on Widget {
  /// Wrap widget với error handler cho provider
  Widget withErrorHandler<T>(
    ProviderBase<BaseState<T>> stateProvider, {
    ErrorDisplayType displayType = ErrorDisplayType.snackBar,
    VoidCallback? onError,
    bool showErrorSnackBar = true,
    bool showErrorDialog = false,
    String? customErrorTitle,
    String? customErrorMessage,
  }) {
    return BaseStateErrorHandler<T>(
      stateProvider: stateProvider,
      displayType: displayType,
      onError: onError,
      showErrorSnackBar: showErrorSnackBar,
      showErrorDialog: showErrorDialog,
      customErrorTitle: customErrorTitle,
      customErrorMessage: customErrorMessage,
      child: this,
    );
  }
}
```

#### 8.4 Cách sử dụng Error Handling

```dart
// Case 1: Sử dụng Extension (Recommended)
MyWidget().withErrorHandler(
  userControllerProvider,
  displayType: ErrorDisplayType.snackBar,
  onError: () {
    // Custom error handling logic
    print('Error occurred!');
  },
)

// Case 2: Sử dụng Widget trực tiếp
BaseStateErrorHandler<User>(
  stateProvider: userControllerProvider,
  displayType: ErrorDisplayType.dialog,
  showErrorDialog: true,
  customErrorTitle: 'Lỗi tải dữ liệu',
  onError: () {
    // Custom error handling
  },
  child: MyWidget(),
)

// Case 3: Hiển thị cả SnackBar và Dialog
MyWidget().withErrorHandler(
  userControllerProvider,
  displayType: ErrorDisplayType.both,
  showErrorSnackBar: true,
  showErrorDialog: true,
  customErrorTitle: 'Lỗi nghiêm trọng',
)

// Case 4: Custom error message
MyWidget().withErrorHandler(
  userControllerProvider,
  customErrorMessage: 'Có lỗi xảy ra khi tải dữ liệu người dùng',
  displayType: ErrorDisplayType.snackBar,
)
```

### 9. Customization

Bạn có thể customize các widget này bằng cách:

```dart
// Custom loading widget
class CustomLoadingWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black54,
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

// Custom error widget
class CustomErrorWidget extends StatelessWidget {
  final Failure failure;
  final VoidCallback? onRetry;
  
  const CustomErrorWidget({
    super.key,
    required this.failure,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text('Lỗi: ${failure.toString()}'),
          if (onRetry != null)
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('Thử lại'),
            ),
        ],
      ),
    );
  }
}
```

## 🎨 Usage Examples

### Basic Usage in Widget (Phương án 1 - Callback Functions)

```dart
class LoginScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authControllerProvider);
    
    return Scaffold(
      appBar: AppBar(title: Text(S.of(context).login)),
      body: authState.when(
        initial: () => _buildLoginForm(context, ref),
        loading: () => const BaseLoadingWidget(),
        success: (user) => _buildSuccessView(context, user),
        error: (failure) => BaseErrorWidget(
          failure: failure,
          onRetry: () => ref.read(authControllerProvider.notifier).retry(),
        ),
        submitting: () => const BaseLoadingWidget(),
        empty: () => _buildLoginForm(context, ref),
        refreshing: (user) => _buildSuccessView(context, user),
      ),
    );
  }
  
  Widget _buildLoginForm(BuildContext context, WidgetRef ref) {
    return LoginForm(
      onSubmit: (email, password) {
        ref.read(authControllerProvider.notifier).login(
          email: email,
          password: password,
        );
      },
    );
  }
  
  Widget _buildSuccessView(BuildContext context, User user) {
    return UserProfileView(user: user);
  }
}
```

### Basic Usage in Widget (Phương án 2 - Mixin)

```dart
class UserProfileScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userState = ref.watch(userControllerProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).profile),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(userControllerProvider.notifier).refreshUser(),
          ),
        ],
      ),
      body: userState.when(
        initial: () => _buildEmptyState(context, ref),
        loading: () => const BaseLoadingWidget(),
        success: (user) => _buildUserProfile(context, user, ref),
        error: (failure) => BaseErrorWidget(
          failure: failure,
          onRetry: () => ref.read(userControllerProvider.notifier).retry(),
        ),
        submitting: () => const BaseLoadingWidget(),
        empty: () => _buildEmptyState(context, ref),
        refreshing: (user) => _buildUserProfile(context, user, ref),
      ),
    );
  }
  
  Widget _buildUserProfile(BuildContext context, User user, WidgetRef ref) {
    return RefreshIndicator(
      onRefresh: () => ref.read(userControllerProvider.notifier).refreshUser(),
      child: UserProfileView(
        user: user,
        onUpdate: (updatedUser) {
          ref.read(userControllerProvider.notifier).updateUser(updatedUser);
        },
      ),
    );
  }
  
  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(S.of(context).noUserData),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.read(userControllerProvider.notifier).loadUser(),
            child: Text(S.of(context).loadUser),
          ),
        ],
      ),
    );
  }
}
```

### Advanced Usage with Custom Logic

```dart
class ProductListScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productsState = ref.watch(productsControllerProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).products),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(productsControllerProvider.notifier).refreshData(),
          ),
        ],
      ),
      body: productsState.when(
        initial: () => _buildEmptyState(context, ref),
        loading: () => const BaseLoadingWidget(),
        success: (products) => _buildProductList(context, products, ref),
        error: (failure) => BaseErrorWidget(
          failure: failure,
          onRetry: () => ref.read(productsControllerProvider.notifier).retry(),
        ),
        submitting: () => const BaseLoadingWidget(),
        empty: () => _buildEmptyState(context, ref),
        refreshing: (products) => _buildProductList(context, products, ref),
      ),
    );
  }
  
  Widget _buildProductList(BuildContext context, List<Product> products, WidgetRef ref) {
    return RefreshIndicator(
      onRefresh: () => ref.read(productsControllerProvider.notifier).refreshData(),
      child: ListView.builder(
        itemCount: products.length,
        itemBuilder: (context, index) => ProductCard(product: products[index]),
      ),
    );
  }
  
  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inventory_2, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(S.of(context).noProducts),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.read(productsControllerProvider.notifier).loadData(),
            child: Text(S.of(context).loadProducts),
          ),
        ],
      ),
    );
  }
}
```

## 🏗️ Architecture Comparison & Recommendations

### Phương án So sánh

| Aspect | Phương án 1: Callback Functions | Phương án 2: Generic Repository | Phương án 3: Mixin Pattern |
|--------|--------------------------------|--------------------------------|---------------------------|
| **Clean Architecture** | ✅ Tốt nhất | ❌ Vi phạm | ✅ Tốt |
| **Flexibility** | ✅ Cao | ❌ Thấp | ✅ Cao |
| **Type Safety** | ✅ Tốt | ✅ Tốt | ✅ Tốt |
| **Code Reuse** | ✅ Cao | ✅ Cao | ✅ Cao nhất |
| **Learning Curve** | ⚠️ Trung bình | ✅ Dễ | ✅ Dễ nhất |
| **Maintenance** | ✅ Dễ | ❌ Khó | ✅ Dễ |
| **Testing** | ✅ Dễ | ⚠️ Trung bình | ✅ Dễ |

### 🎯 Recommendation: Phương án 3 - Mixin Pattern

**Lý do chọn Mixin Pattern:**

1. **✅ Clean Architecture Compliant**: Không vi phạm layer boundaries
2. **✅ Maximum Reusability**: Có thể dùng với bất kỳ controller nào
3. **✅ Simple Implementation**: Dễ implement và maintain
4. **✅ Flexible**: Mỗi controller có thể customize logic riêng
5. **✅ Testable**: Dễ test và mock

### Implementation Strategy

```dart
// 1. Create BaseState<T> with freezed
@freezed
class BaseState<T> with _$BaseState<T> {
  const factory BaseState.initial() = _Initial<T>;
  const factory BaseState.loading() = _Loading<T>;
  const factory BaseState.success(T data) = _Success<T>;
  const factory BaseState.error(Failure failure) = _Error<T>;
  const factory BaseState.submitting() = _Submitting<T>;
  const factory BaseState.empty() = _Empty<T>;
  const factory BaseState.refreshing(T data) = _Refreshing<T>;
}

// 2. Create BaseStateMixin<T>
mixin BaseStateMixin<T> on AutoDisposeNotifier<BaseState<T>> {
  NetworkInfo get networkInfo;
  
  Future<void> loadData(Future<Either<Failure, T?>> Function() dataCall) async {
    // Implementation
  }
  
  Future<void> submitData<R>(
    R data,
    Future<Either<Failure, T>> Function(R data) submitCall,
  ) async {
    // Implementation
  }
  
  // ... other methods
}

// 3. Use in Controllers
@riverpod
class AuthController extends _$AuthController with BaseStateMixin<User> {
  @override
  BaseState<User> build() => const BaseState.initial();
  
  @override
  NetworkInfo get networkInfo => ref.read(networkInfoProvider);
  
  @override
  Future<Either<Failure, User?>> getDataCall() async {
    return await ref.read(authRepositoryProvider).getCurrentUser();
  }
  
  @override
  Future<Either<Failure, User>> submitDataCall(dynamic data) async {
    // Custom implementation
  }
}
```

## 🎯 Best Practices

### 1. Controller Design

```dart
// ✅ Good: Clear separation of concerns with Mixin
@riverpod
class UserController extends _$UserController with BaseStateMixin<User> {
  @override
  BaseState<User> build() => const BaseState.initial();
  
  @override
  NetworkInfo get networkInfo => ref.read(networkInfoProvider);
  
  @override
  Future<Either<Failure, User?>> getDataCall() async {
    return await ref.read(userRepositoryProvider).getCurrentUser();
  }
  
  @override
  Future<Either<Failure, User>> submitDataCall(dynamic data) async {
    final user = data as User;
    return await ref.read(userRepositoryProvider).updateUser(user);
  }
  
  // Custom business methods
  Future<void> loadUser() async => await loadData();
  Future<void> updateUser(User user) async => await submitData(user);
  Future<void> refreshUser() async => await refreshData();
}

// ❌ Bad: Mixed concerns
@riverpod
class UserController extends _$UserController {
  @override
  BaseState<User> build() => const BaseState.initial();
  
  Future<void> doEverything() async {
    // Too many responsibilities
  }
}
```

### 2. State Management

```dart
// ✅ Good: Use state extensions
if (state.isLoading) {
  return LoadingWidget();
}

// ✅ Good: Use when for exhaustive handling
return state.when(
  initial: () => InitialWidget(),
  loading: () => LoadingWidget(),
  success: (data) => SuccessWidget(data),
  error: (failure) => ErrorWidget(failure),
  submitting: () => LoadingWidget(),
  empty: () => EmptyWidget(),
  refreshing: (data) => SuccessWidget(data),
);

// ❌ Bad: Manual state checking
if (state is Loading) {
  return LoadingWidget();
} else if (state is Success) {
  return SuccessWidget((state as Success).data);
}
```

### 3. Error Handling

```dart
// ✅ Good: Proper error propagation
result.fold(
  (failure) => state = BaseState.error(failure),
  (data) => state = BaseState.success(data),
);

// ❌ Bad: Swallowing errors
try {
  final result = await repository.getData();
  state = BaseState.success(result);
} catch (e) {
  // Error swallowed
}
```

### 4. Network Handling

```dart
// ✅ Good: Let ErrorInterceptor handle network issues
final result = await repository.getData();
result.fold(
  (failure) => state = BaseState.error(failure),
  (data) => state = BaseState.success(data),
);

// ❌ Bad: Manual network check (redundant with ErrorInterceptor)
if (!await networkInfo.isConnected) {
  state = BaseState.error(
    const NetworkFailure(NetworkErrorType.noConnection),
  );
  return;
}
```

## 🔄 Migration Guide

### From Manual State Management

#### Before (Manual)
```dart
class ManualController extends StateNotifier<AsyncValue<User>> {
  ManualController() : super(const AsyncValue.loading());
  
  Future<void> loadUser() async {
    state = const AsyncValue.loading();
    try {
      final user = await repository.getUser();
      state = AsyncValue.data(user);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}
```

#### After (Base System)
```dart
@riverpod
class UserController extends _$UserController with BaseStateMixin<User> {
  @override
  BaseState<User> build() => const BaseState.initial();
  
  @override
  Repository get repository => ref.read(userRepositoryProvider);
  
  @override
  NetworkInfo get networkInfo => ref.read(networkInfoProvider);
  
  // loadData() is automatically implemented!
}
```

### From Custom State Classes

#### Before (Custom State)
```dart
abstract class UserState {}
class UserInitial extends UserState {}
class UserLoading extends UserState {}
class UserSuccess extends UserState {
  final User user;
  UserSuccess(this.user);
}
class UserError extends UserState {
  final String message;
  UserError(this.message);
}
```

#### After (Base State)
```dart
// Use BaseState<User> directly
BaseState<User> state = const BaseState.initial();
```

## 📋 Implementation Checklist

### Phase 1: Core Infrastructure (Mixin Pattern)
- [x] Create `BaseState<T>` with freezed
- [x] Create `BaseStateMixin<T>` with all state management logic
- [x] Add state extensions and helper methods
- [x] Create base UI components

### Phase 2: UI Components
- [x] Create `BaseLoadingWidget`
- [x] Create `BaseErrorWidget` with retry functionality
- [x] Create `BaseSuccessWidget`
- [x] Create `BaseEmptyWidget`
- [x] Create `BaseRefreshingWidget`
- [x] Create `BaseSubmittingWidget` and `DefaultSubmittingOverlay`

### Phase 3: BaseStateBuilder System
- [x] Create `BaseStateBuilder` (StatelessWidget)
- [x] Create `BaseStateConsumerBuilder` (ConsumerWidget)
- [x] Create `BaseStateHookBuilder` (HookConsumerWidget)
- [x] Add support for `submitted` state
- [x] Add `showContentWhileSubmitting` functionality

### Phase 4: Error Handling System
- [x] Create `BaseStateErrorHandler` widget
- [x] Create `ErrorDisplayType` enum
- [x] Create `BaseStateErrorHandlerExtension`
- [x] Integrate with `ErrorMessageMapper`
- [x] Support SnackBar and Dialog error display

### Phase 5: Migration (Recommended Order)
- [ ] Migrate `AuthController` (simplest case)
- [ ] Migrate `UserController` (CRUD operations)
- [ ] Migrate `ProductController` (list operations)
- [ ] Update all UI widgets to use new state management

### Phase 6: Testing
- [ ] Unit tests for `BaseState<T>`
- [ ] Unit tests for `BaseStateMixin<T>`
- [ ] Unit tests for `BaseStateErrorHandler`
- [ ] Integration tests for controllers
- [ ] Widget tests for UI components
- [ ] Performance tests for state transitions

### Phase 7: Documentation & Examples
- [x] Update existing controller documentation
- [x] Create migration examples
- [x] Create best practices guide
- [ ] Create troubleshooting guide
- [x] Add error handling documentation

---

**Base State Management System cung cấp một framework hoàn chỉnh để xử lý các trạng thái API calls một cách nhất quán và tái sử dụng được. Hệ thống này sẽ giúp giảm thiểu code duplication và cải thiện developer experience.**

## 📚 Advanced Patterns

Để xử lý các trường hợp phức tạp hơn, xem thêm:
- [Advanced State Patterns](./ADVANCED_STATE_PATTERNS.md) - Extended ApiState, Composition, Generic Metadata, Nested States 

## 🔄 BaseNotifier: AutoDispose & Persistent

### 1. Giới thiệu

- **BaseNotifier<T>**: sử dụng `AutoDisposeNotifier<BaseState<T>>` (mặc định, tự động dispose khi không còn dùng)
- **BasePersistentNotifier<T>**: sử dụng `Notifier<BaseState<T>>` (không tự động dispose, giữ state lâu dài)

> Chọn base phù hợp với nhu cầu quản lý vòng đời state của bạn.

### 2. Định nghĩa base class

```dart
// BaseNotifier: AutoDispose (mặc định)
abstract class BaseNotifier<T> extends AutoDisposeNotifier<BaseState<T>> {
  // ... giữ nguyên toàn bộ logic ...
}

// BasePersistentNotifier: Non-auto dispose
abstract class BasePersistentNotifier<T> extends Notifier<BaseState<T>> {
  // ... copy toàn bộ logic từ BaseNotifier ...
}
```

Nếu dùng mixin:
```dart
// Mixin cho cả hai loại
mixin BaseStateMixin<T> on NotifierBase<BaseState<T>> {
  // ... logic dùng chung ...
}
```

### 3. Đăng ký provider tương ứng

- **AutoDispose** (mặc định):
  ```dart
  final myControllerProvider = AutoDisposeNotifierProvider<MyController, BaseState<MyData>>(MyController.new);
  ```
- **Non-auto dispose**:
  ```dart
  final myControllerProvider = NotifierProvider<MyController, BaseState<MyData>>(MyController.new);
  ```

### 4. Khi nào nên dùng loại nào?

| Loại                | Khi nào dùng?                                      |
|---------------------|----------------------------------------------------|
| AutoDispose         | State chỉ dùng cho màn hình, form, logic ngắn hạn   |
| Non-auto dispose   | State cần giữ lâu dài (global, tab, background...)  |

> **Lưu ý:**
> - Không nên dùng non-auto dispose cho các màn hình/phần UI ngắn hạn để tránh memory leak.
> - Có thể refactor controller sang persistent bất cứ lúc nào chỉ bằng cách đổi base và provider.

## 🎮 Sử dụng BaseStateMixin

### Cách 1: Với @riverpod (Code Generation) - Khuyến nghị

```dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sales_app/core/base/base_state.dart';
import 'package:sales_app/core/base/base_state_mixin.dart';
import 'package:sales_app/domain/entities/user_entity.dart';
import 'package:sales_app/domain/repositories/user_repository.dart';
import 'package:sales_app/domain/services/network_info.dart';
import 'package:sales_app/di/injection.dart';

part 'user_controller.g.dart';

@riverpod
class UserController extends _$UserController with BaseStateMixin<User> {
  late final UserRepository _userRepository;
  late final NetworkInfo _networkInfo;

  @override
  BaseState<User> build() {
    _userRepository = getIt<UserRepository>();
    _networkInfo = getIt<NetworkInfo>();
    return const BaseState.initial();
  }

  @override
  NetworkInfo get networkInfo => _networkInfo;

  /// Load user data
  Future<void> loadUser() async {
    await loadData(() => _userRepository.getCurrentUser());
  }

  /// Update user data
  Future<void> updateUser(User user) async {
    await submitData(user, (u) => _userRepository.updateUser(u));
  }

  /// Refresh user data
  Future<void> refreshUser() async {
    await refreshData(() => _userRepository.getCurrentUser());
  }

  /// Retry last operation
  Future<void> retryUser() async {
    await retry(() => _userRepository.getCurrentUser());
  }
}

// Provider được tự động tạo bởi @riverpod
// final userControllerProvider = AutoDisposeNotifierProvider<UserController, BaseState<User>>(UserController.new);
```

### Cách 2: Manual Implementation (Không dùng @riverpod)

```dart
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/base/base_state.dart';
import 'package:sales_app/core/base/base_state_mixin.dart';
import 'package:sales_app/domain/entities/user_entity.dart';
import 'package:sales_app/domain/repositories/user_repository.dart';
import 'package:sales_app/domain/services/network_info.dart';

class UserController extends AutoDisposeNotifier<BaseState<User>> with BaseStateMixin<User> {
  UserController(this._userRepository, this._networkInfo);

  final UserRepository _userRepository;
  final NetworkInfo _networkInfo;

  @override
  BaseState<User> build() => const BaseState.initial();

  @override
  NetworkInfo get networkInfo => _networkInfo;

  /// Load user data
  Future<void> loadUser() async {
    await loadData(() => _userRepository.getCurrentUser());
  }

  /// Update user data
  Future<void> updateUser(User user) async {
    await submitData(user, (u) => _userRepository.updateUser(u));
  }

  /// Refresh user data
  Future<void> refreshUser() async {
    await refreshData(() => _userRepository.getCurrentUser());
  }

  /// Retry last operation
  Future<void> retryUser() async {
    await retry(() => _userRepository.getCurrentUser());
  }
}

// Manual provider registration
final userControllerProvider = AutoDisposeNotifierProvider<UserController, BaseState<User>>(
  (ref) => UserController(
    ref.read(userRepositoryProvider),
    ref.read(networkInfoProvider),
  ),
);
```

### So sánh 2 cách

| Aspect | @riverpod (Code Generation) | Manual Implementation |
|--------|---------------------------|----------------------|
| **Code Generation** | ✅ Tự động tạo provider | ❌ Phải viết tay |
| **Type Safety** | ✅ Tốt nhất | ✅ Tốt |
| **Boilerplate** | ✅ Ít nhất | ⚠️ Nhiều hơn |
| **Learning Curve** | ⚠️ Cần hiểu codegen | ✅ Dễ hiểu |
| **Flexibility** | ⚠️ Giới hạn bởi codegen | ✅ Linh hoạt nhất |
| **Maintenance** | ✅ Dễ maintain | ⚠️ Cần maintain thủ công |

### Khuyến nghị

- **Dùng @riverpod** cho các controller mới và đơn giản
- **Dùng Manual** cho các controller phức tạp hoặc cần customization cao
- **Cả hai đều tương thích** với BaseStateMixin

### Migration từ Manual sang @riverpod

```dart
// Trước (Manual)
class UserController extends AutoDisposeNotifier<BaseState<User>> with BaseStateMixin<User> {
  // ... implementation
}

// Sau (@riverpod)
@riverpod
class UserController extends _$UserController with BaseStateMixin<User> {
  // ... giữ nguyên implementation, chỉ thêm @riverpod và extends _$UserController
}
```

// ... existing code ... 