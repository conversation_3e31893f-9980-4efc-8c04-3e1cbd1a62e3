# Advanced State Patterns

> Hướng dẫn mở rộng Base State Management cho các trường hợp phức tạp

## 📋 <PERSON>ụ<PERSON> lục

- [Tổng quan](#tổng-quan)
- [Pattern 1: Extended ApiState](#pattern-1-extended-apistate)
- [Pattern 2: Composition Pattern](#pattern-2-composition-pattern)
- [Pattern 3: Generic Metadata](#pattern-3-generic-metadata)
- [Pattern 4: Nested States](#pattern-4-nested-states)
- [Hướng dẫn chọn Pattern](#hướng-dẫn-chọn-pattern)

## 🎯 Tổng quan

Khi Base State Management cơ bản không đủ cho các trường hợp phức tạp, bạn có thể sử dụng các pattern mở rộng sau:

## 🔧 Pattern 1: Extended ApiState

### Khi nào sử dụng:
- Cần thêm trạng thái đặc biệt cho một feature cụ thể
- <PERSON><PERSON>n giữ cấu trúc đơn giản nhưng mở rộng được

### Ví dụ: LoanState với trạng thái đặc biệt

```dart
@freezed
class LoanState with _$LoanState {
  const factory LoanState.initial() = _Initial;
  const factory LoanState.loading() = _Loading;
  const factory LoanState.success(LoanData data) = _Success;
  const factory LoanState.error(String message, {LoanData? previousData}) = _Error;
  
  // Trạng thái đặc biệt cho loan
  const factory LoanState.submitting() = _Submitting;
  const factory LoanState.submitted() = _Submitted;
  const factory LoanState.validating() = _Validating;
  const factory LoanState.approved() = _Approved;
  const factory LoanState.rejected(String reason) = _Rejected;
  const factory LoanState.expired() = _Expired;
}

// Extension methods
extension LoanStateX on LoanState {
  bool get isSubmitting => this is _Submitting;
  bool get isSubmitted => this is _Submitted;
  bool get isApproved => this is _Approved;
  bool get isRejected => this is _Rejected;
  
  String? get rejectionReason {
    return whenOrNull(
      rejected: (reason) => reason,
    );
  }
}
```

### Controller tương ứng:

```dart
class LoanController extends StateNotifier<LoanState> {
  LoanController(this._loanRepository) : super(const LoanState.initial());

  final LoanRepository _loanRepository;

  Future<void> submitLoan(LoanRequest request) async {
    state = const LoanState.submitting();
    
    try {
      final result = await _loanRepository.submitLoan(request);
      state = LoanState.submitted();
    } catch (e) {
      state = LoanState.error(e.toString());
    }
  }

  Future<void> validateLoan(String loanId) async {
    state = const LoanState.validating();
    
    try {
      final validation = await _loanRepository.validateLoan(loanId);
      if (validation.isApproved) {
        state = const LoanState.approved();
      } else {
        state = LoanState.rejected(validation.reason);
      }
    } catch (e) {
      state = LoanState.error(e.toString());
    }
  }
}
```

## 🔧 Pattern 2: Composition Pattern

### Khi nào sử dụng:
- Cần kết hợp nhiều trạng thái độc lập
- Muốn tái sử dụng logic từ nhiều base state

### Ví dụ: Multi-step Form State

```dart
@freezed
class MultiStepFormState with _$MultiStepFormState {
  const factory MultiStepFormState({
    required ApiState<PersonalInfo> personalInfo,
    required ApiState<AddressInfo> addressInfo,
    required ApiState<DocumentInfo> documents,
    required int currentStep,
    required bool canProceed,
  }) = _MultiStepFormState;
}

// Controller sử dụng composition
class MultiStepFormController extends StateNotifier<MultiStepFormState> {
  MultiStepFormController({
    required PersonalInfoRepository personalRepo,
    required AddressRepository addressRepo,
    required DocumentRepository documentRepo,
  }) : super(MultiStepFormState(
          personalInfo: const ApiState.initial(),
          addressInfo: const ApiState.initial(),
          documents: const ApiState.initial(),
          currentStep: 0,
          canProceed: false,
        ));

  Future<void> savePersonalInfo(PersonalInfo info) async {
    state = state.copyWith(
      personalInfo: const ApiState.loading(),
    );

    try {
      final result = await _personalRepo.save(info);
      state = state.copyWith(
        personalInfo: ApiState.success(result),
        canProceed: _canProceedToNextStep(),
      );
    } catch (e) {
      state = state.copyWith(
        personalInfo: ApiState.error(e.toString()),
      );
    }
  }

  void nextStep() {
    if (state.canProceed && state.currentStep < 2) {
      state = state.copyWith(currentStep: state.currentStep + 1);
    }
  }

  bool _canProceedToNextStep() {
    return state.personalInfo.isSuccess &&
           state.addressInfo.isSuccess &&
           state.documents.isSuccess;
  }
}
```

## 🔧 Pattern 3: Generic Metadata

### Khi nào sử dụng:
- Cần thêm thông tin bổ sung cho mỗi trạng thái
- Muốn tracking progress, timestamps, user actions

### Ví dụ: ApiState với Metadata

```dart
@freezed
class ApiStateWithMetadata<T> with _$ApiStateWithMetadata<T> {
  const factory ApiStateWithMetadata.initial() = _Initial<T>;
  const factory ApiStateWithMetadata.loading({
    required DateTime timestamp,
    String? message,
  }) = _Loading<T>;
  const factory ApiStateWithMetadata.success({
    required T data,
    required DateTime timestamp,
    Map<String, dynamic>? metadata,
  }) = _Success<T>;
  const factory ApiStateWithMetadata.error({
    required String message,
    required DateTime timestamp,
    T? previousData,
    Map<String, dynamic>? errorMetadata,
  }) = _Error<T>;
  const factory ApiStateWithMetadata.submitting({
    required DateTime timestamp,
    T? previousData,
  }) = _Submitting<T>;
  const factory ApiStateWithMetadata.submitted({
    required DateTime timestamp,
    Map<String, dynamic>? metadata,
  }) = _Submitted<T>;
}

// Extension với metadata helpers
extension ApiStateWithMetadataX<T> on ApiStateWithMetadata<T> {
  bool get isSuccess => this is _Success<T>;
  bool get isLoading => this is _Loading<T>;
  bool get isError => this is _Error<T>;
  bool get isSubmitting => this is _Submitting<T>;
  bool get isSubmitted => this is _Submitted<T>;

  T? get data => whenOrNull(
    success: (data, timestamp, metadata) => data,
    error: (message, timestamp, previousData, errorMetadata) => previousData,
    submitting: (timestamp, previousData) => previousData,
  );

  DateTime? get timestamp => whenOrNull(
    loading: (timestamp, message) => timestamp,
    success: (data, timestamp, metadata) => timestamp,
    error: (message, timestamp, previousData, errorMetadata) => timestamp,
    submitting: (timestamp, previousData) => timestamp,
    submitted: (timestamp, metadata) => timestamp,
  );

  Map<String, dynamic>? get metadata => whenOrNull(
    success: (data, timestamp, metadata) => metadata,
    error: (message, timestamp, previousData, errorMetadata) => errorMetadata,
    submitted: (timestamp, metadata) => metadata,
  );

  Duration? get age {
    final ts = timestamp;
    if (ts == null) return null;
    return DateTime.now().difference(ts);
  }
}
```

### Controller với Metadata:

```dart
class MetadataAwareController<T> extends StateNotifier<ApiStateWithMetadata<T>> {
  MetadataAwareController(this._repository) : super(const ApiStateWithMetadata.initial());

  final Repository<T> _repository;

  Future<void> fetchData({Map<String, dynamic>? metadata}) async {
    state = ApiStateWithMetadata.loading(
      timestamp: DateTime.now(),
      message: 'Đang tải dữ liệu...',
    );

    try {
      final data = await _repository.fetch();
      state = ApiStateWithMetadata.success(
        data: data,
        timestamp: DateTime.now(),
        metadata: metadata,
      );
    } catch (e) {
      state = ApiStateWithMetadata.error(
        message: e.toString(),
        timestamp: DateTime.now(),
        errorMetadata: {
          'error_type': e.runtimeType.toString(),
          'retry_count': 0,
        },
      );
    }
  }

  Future<void> submitData(T data, {Map<String, dynamic>? metadata}) async {
    final currentData = this.state.data;
    
    state = ApiStateWithMetadata.submitting(
      timestamp: DateTime.now(),
      previousData: currentData,
    );

    try {
      await _repository.submit(data);
      state = ApiStateWithMetadata.submitted(
        timestamp: DateTime.now(),
        metadata: metadata,
      );
    } catch (e) {
      state = ApiStateWithMetadata.error(
        message: e.toString(),
        timestamp: DateTime.now(),
        previousData: currentData,
        errorMetadata: {
          'error_type': e.runtimeType.toString(),
          'submitted_data': data.toString(),
        },
      );
    }
  }
}
```

## 🔧 Pattern 4: Nested States

### Khi nào sử dụng:
- Có trạng thái con phức tạp cần quản lý riêng
- Muốn tách biệt logic của từng phần

### Ví dụ: Dashboard với nhiều widget state

```dart
@freezed
class DashboardState with _$DashboardState {
  const factory DashboardState({
    required ApiState<List<Loan>> loans,
    required ApiState<UserProfile> profile,
    required ApiState<List<Notification>> notifications,
    required DashboardViewState viewState,
  }) = _DashboardState;
}

@freezed
class DashboardViewState with _$DashboardViewState {
  const factory DashboardViewState.initial() = _Initial;
  const factory DashboardViewState.loading() = _Loading;
  const factory DashboardViewState.success() = _Success;
  const factory DashboardViewState.error(String message) = _Error;
  const factory DashboardViewState.refreshing() = _Refreshing;
}

// Controller với nested states
class DashboardController extends StateNotifier<DashboardState> {
  DashboardController({
    required LoanRepository loanRepo,
    required UserRepository userRepo,
    required NotificationRepository notificationRepo,
  }) : super(DashboardState(
          loans: const ApiState.initial(),
          profile: const ApiState.initial(),
          notifications: const ApiState.initial(),
          viewState: const DashboardViewState.initial(),
        ));

  Future<void> loadDashboard() async {
    state = state.copyWith(
      viewState: const DashboardViewState.loading(),
    );

    try {
      // Load tất cả data song song
      final results = await Future.wait([
        _loanRepo.getLoans(),
        _userRepo.getProfile(),
        _notificationRepo.getNotifications(),
      ]);

      state = state.copyWith(
        loans: ApiState.success(results[0] as List<Loan>),
        profile: ApiState.success(results[1] as UserProfile),
        notifications: ApiState.success(results[2] as List<Notification>),
        viewState: const DashboardViewState.success(),
      );
    } catch (e) {
      state = state.copyWith(
        viewState: DashboardViewState.error(e.toString()),
      );
    }
  }

  Future<void> refreshLoans() async {
    state = state.copyWith(
      loans: const ApiState.loading(),
    );

    try {
      final loans = await _loanRepo.getLoans();
      state = state.copyWith(
        loans: ApiState.success(loans),
      );
    } catch (e) {
      state = state.copyWith(
        loans: ApiState.error(e.toString()),
      );
    }
  }

  Future<void> refreshAll() async {
    state = state.copyWith(
      viewState: const DashboardViewState.refreshing(),
    );

    await loadDashboard();
  }
}
```

## 🎯 Hướng dẫn chọn Pattern

### Chọn **Extended ApiState** khi:
- ✅ Cần thêm 2-3 trạng thái đặc biệt
- ✅ Logic đơn giản, không phức tạp
- ✅ Muốn giữ cấu trúc dễ hiểu

```dart
// Ví dụ: UserProfileState
const factory UserProfileState.verifying() = _Verifying;
const factory UserProfileState.verified() = _Verified;
const factory UserProfileState.unverified() = _Unverified;
```

### Chọn **Composition Pattern** khi:
- ✅ Có nhiều trạng thái độc lập
- ✅ Muốn tái sử dụng logic từ base state
- ✅ Cần quản lý form multi-step

```dart
// Ví dụ: RegistrationFormState
const factory RegistrationFormState({
  required ApiState<PersonalInfo> personalInfo,
  required ApiState<AddressInfo> addressInfo,
  required ApiState<DocumentInfo> documents,
}) = _RegistrationFormState;
```

### Chọn **Generic Metadata** khi:
- ✅ Cần tracking progress, timestamps
- ✅ Muốn thêm thông tin bổ sung cho mọi trạng thái
- ✅ Cần analytics hoặc debugging

```dart
// Ví dụ: ApiStateWithMetadata<User>
ApiStateWithMetadata.success(
  data: user,
  timestamp: DateTime.now(),
  metadata: {'source': 'cache', 'ttl': 300},
)
```

### Chọn **Nested States** khi:
- ✅ Có trạng thái con phức tạp
- ✅ Muốn tách biệt logic của từng phần
- ✅ Cần quản lý dashboard với nhiều widget

```dart
// Ví dụ: DashboardState
const factory DashboardState({
  required ApiState<List<Loan>> loans,
  required ApiState<UserProfile> profile,
  required DashboardViewState viewState,
}) = _DashboardState;
```

## 🔄 Migration Guide

### Từ Base State sang Advanced Pattern:

1. **Đánh giá nhu cầu**: Xác định pattern phù hợp
2. **Tạo state mới**: Implement pattern đã chọn
3. **Cập nhật controller**: Migrate logic sang pattern mới
4. **Cập nhật UI**: Sử dụng state mới
5. **Test**: Đảm bảo tất cả functionality hoạt động

### Ví dụ Migration:

```dart
// Trước: Base ApiState
class UserController extends StateNotifier<ApiState<User>> {
  // Logic đơn giản
}

// Sau: Extended ApiState
class UserController extends StateNotifier<UserState> {
  // Logic mở rộng với trạng thái đặc biệt
}
```

## 📚 Best Practices

### 1. **Chọn Pattern đúng**
- Bắt đầu với Base State Management
- Chỉ mở rộng khi thực sự cần thiết
- Đánh giá complexity vs benefit

### 2. **Consistency**
- Sử dụng cùng pattern cho các feature tương tự
- Đặt tên state và method nhất quán
- Follow naming conventions

### 3. **Testing**
- Test tất cả trạng thái transitions
- Mock dependencies đúng cách
- Test error scenarios

### 4. **Documentation**
- Comment cho các trạng thái đặc biệt
- Document business logic
- Update API docs khi thay đổi

## 🎯 Kết luận

Advanced State Patterns giúp xử lý các trường hợp phức tạp mà Base State Management không đủ. Chọn pattern phù hợp dựa trên:

- **Complexity** của business logic
- **Reusability** requirements  
- **Maintainability** needs
- **Team expertise**

Luôn bắt đầu với Base State Management và chỉ mở rộng khi thực sự cần thiết. 