# BaseStateMixin Guide

Hướng dẫn sử dụng BaseStateMixin cho Riverpod Controllers

## 📋 Tổng quan

Dự án cung cấp 2 mixin để quản lý state với `BaseState<T>`:

1. **`BaseStateMixin<T>`** - Cho `Notifier<BaseState<T>>` (non-auto-dispose)
2. **`AutoDisposeBaseStateMixin<T>`** - Cho `AutoDisposeNotifier<BaseState<T>>` (@riverpod)

## 🎯 Khi nào sử dụng loại nào?

### BaseStateMixin (Non-auto-dispose)
- **Sử dụng khi**: Controller cần persist state khi không có listener
- **<PERSON><PERSON> hợp cho**: Global state, user session, app settings
- **Provider type**: `NotifierProvider`
- **Code generation**: Manual hoặc `@Riverpod(keepAlive: true)`

### AutoDisposeBaseStateMixin (Auto-dispose)
- **Sử dụng khi**: Controller chỉ cần tồn tại khi có listener
- **<PERSON><PERSON> hợp cho**: Screen-specific state, form data, temporary data
- **Provider type**: `AutoDisposeNotifierProvider`
- **Code generation**: `@riverpod` (default)

## 📝 Cách sử dụng

### 1. BaseStateMixin (Non-auto-dispose)

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sales_app/core/base/base_state_mixin.dart';

class UserController extends Notifier<BaseState<User>> 
    with BaseStateMixin<User> {
  
  late final UserRepository _userRepository;

  @override
  BaseState<User> build() {
    _userRepository = GetIt.I.get<UserRepository>();
    return const BaseState.initial();
  }

  @override
  NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();

  /// Load user data
  Future<void> loadUser() async {
    await loadData(
      () => _userRepository.getCurrentUser(),
    );
  }

  /// Update user data
  Future<void> updateUser(User user) async {
    await submitData(
      user,
      (User user) => _userRepository.updateUser(user),
    );
  }

  /// Refresh user data
  Future<void> refreshUser() async {
    await refreshData(
      () => _userRepository.getCurrentUser(),
    );
  }

  /// Retry last operation
  Future<void> retryOperation() async {
    await retry(
      () => _userRepository.getCurrentUser(),
    );
  }
}

// Manual provider registration
final userControllerProvider = NotifierProvider<UserController, BaseState<User>>(
  () => UserController(),
);
```

### 2. AutoDisposeBaseStateMixin (@riverpod)

```dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sales_app/core/base/base_state_mixin.dart';

part 'media_upload_controller.g.dart';

@riverpod
class MediaUploadController extends _$MediaUploadController 
    with AutoDisposeBaseStateMixin<MediaUploadResponse> {
  
  late final MediaRepository _mediaRepository;

  @override
  BaseState<MediaUploadResponse> build() {
    _mediaRepository = GetIt.I.get<MediaRepository>();
    return const BaseState.initial();
  }

  @override
  NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();

  /// Upload file
  Future<void> uploadFile(File file) async {
    await submitData(
      file,
      (File file) => _mediaRepository.uploadFile(
        file: file,
        fileType: FileType.idCard,
      ),
    );
  }

  /// Retry upload
  Future<void> retryUpload(File file) async {
    if (state.canRetry) {
      await uploadFile(file);
    }
  }
}
```

### 3. Sử dụng @Riverpod(keepAlive: true)

```dart
@Riverpod(keepAlive: true)
class GlobalSettingsController extends _$GlobalSettingsController 
    with BaseStateMixin<AppSettings> {
  
  @override
  BaseState<AppSettings> build() => const BaseState.initial();
  
  @override
  NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();
  
  Future<void> loadSettings() async {
    await loadData(
      () => GetIt.I.get<SettingsRepository>().getSettings(),
    );
  }
}
```

## 🔧 Các method có sẵn

Cả 2 mixin đều cung cấp các method sau:

### Core Methods
- `loadData(Future<Either<Failure, T?>> Function() dataCall)` - Load data với error handling
- `submitData<R>(R data, Future<Either<Failure, T>> Function(R data) submitCall)` - Submit data
- `refreshData(Future<Either<Failure, T?>> Function() dataCall)` - Refresh data
- `retry(Future<Either<Failure, T?>> Function() dataCall)` - Retry operation

### Utility Methods
- `reset()` - Reset về initial state
- `setState(BaseState<T> newState)` - Set custom state

### State Checkers
- `isProcessing` - Đang loading hoặc submitting
- `canRetry` - Có thể retry (error state)
- `canRefresh` - Có thể refresh (success hoặc error với data)
- `canSubmit` - Có thể submit (success state)

## 📊 State Flow

```
Initial → Loading → Success/Error
   ↑         ↓         ↓
   ←─── Retry ←───────┘
   ↑         ↓
   ←─── Refresh ←─────┘
```

## 🎨 UI Integration

### Sử dụng với BaseStateBuilder

```dart
class MyScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userState = ref.watch(userControllerProvider);
    
    return BaseStateConsumerBuilder<User>(
      stateProvider: userControllerProvider,
      successBuilder: (user, ref) => UserProfile(user: user),
      errorBuilder: (failure, onRetry, ref) => ErrorWidget(
        failure: failure,
        onRetry: onRetry,
      ),
      loadingBuilder: (ref) => const LoadingWidget(),
      onRetry: () => ref.read(userControllerProvider.notifier).retryOperation(),
    );
  }
}
```

### Sử dụng với when()

```dart
class MyScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userState = ref.watch(userControllerProvider);
    
    return userState.when(
      initial: () => const InitialWidget(),
      loading: () => const LoadingWidget(),
      success: (user) => UserProfile(user: user),
      error: (failure) => ErrorWidget(
        failure: failure,
        onRetry: () => ref.read(userControllerProvider.notifier).retryOperation(),
      ),
      submitting: () => const LoadingWidget(),
      empty: () => const EmptyWidget(),
      refreshing: (user) => UserProfile(user: user),
    );
  }
}
```

## 🔄 Migration Guide

### Từ manual implementation sang mixin

**Trước:**
```dart
@riverpod
class MyController extends _$MyController {
  @override
  BaseState<Data> build() => const BaseState.initial();
  
  Future<void> loadData() async {
    state = const BaseState.loading();
    try {
      final result = await repository.getData();
      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = BaseState.success(data),
      );
    } catch (e) {
      state = BaseState.error(ServerFailure(...));
    }
  }
}
```

**Sau:**
```dart
@riverpod
class MyController extends _$MyController with AutoDisposeBaseStateMixin<Data> {
  @override
  BaseState<Data> build() => const BaseState.initial();
  
  @override
  NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();
  
  Future<void> loadData() async {
    await loadData(() => repository.getData());
  }
}
```

## ⚠️ Lưu ý quan trọng

1. **NetworkInfo**: Phải implement `networkInfo` getter trong cả 2 mixin
2. **Type Safety**: Đảm bảo return type của repository method khớp với generic type T
3. **Error Handling**: Mixin tự động handle errors và transform thành `Failure`
4. **State Management**: Không cần manually set state, mixin sẽ handle
5. **Retry Logic**: Sử dụng `retry()` method thay vì gọi lại `loadData()`

## 🧪 Testing

```dart
testWidgets('should handle loading state', (tester) async {
  final container = ProviderContainer();
  
  // Test initial state
  expect(
    container.read(userControllerProvider),
    const BaseState.initial(),
  );
  
  // Test loading state
  container.read(userControllerProvider.notifier).loadUser();
  expect(
    container.read(userControllerProvider),
    const BaseState.loading(),
  );
});
```

## 📚 Ví dụ thực tế

Xem các file sau để tham khảo:
- `lib/presentation/controllers/media/media_upload_controller.dart` - AutoDisposeBaseStateMixin
- `lib/presentation/controllers/media/non_auto_dispose_media_controller.dart` - BaseStateMixin
- `lib/presentation/controllers/article/article_controller.dart` - Manual implementation 