# 📚 Sales App Documentation

Tài liệu dự án Sales App được tổ chức theo các thư mục chuyên môn để dễ dàng tìm kiếm và quản lý.

## 📁 Cấu trúc thư mục

### 🏗️ `architecture/`
Tài liệu về kiến trúc và thiết kế hệ thống:
- **ARCHITECTURE_GUIDE.md** - Hướng dẫn kiến trúc Clean Architecture
- **SYSTEMS_GUIDE.md** - Hướng dẫn tổng quan về các hệ thống
- **UI_COMPONENTS_DEMO.md** - Demo các UI components

### 🚀 `cicd/`
Tài liệu về CI/CD, Build & Deployment:
- **CI_CD_GUIDE.md** - Hướng dẫn CI/CD tổng quan
- **FLAVOR_CICD_GUIDE.md** - Hướng dẫn CI/CD cho các flavor
- **FLAVOR_CICD_QUICKSTART.md** - Quickstart guide cho flavor CI/CD
- **BUILD_STRATEGY.md** - <PERSON>ến lược build
- **FLAVOR_GUIDE.md** - Hướng dẫn về flavors
- **GITLAB_RUNNER_SETUP_GUIDE.md** - Setup GitLab Runner
- **GOOGLE_PLAY_SETUP_GUIDE.md** - Setup Google Play Console
- **VERSION_MANAGEMENT_GUIDE.md** - Quản lý phiên bản

### 📱 `mobile/`
Tài liệu về các platform mobile (iOS/Android):
- **iOS_FLAVORS.md** - Cấu hình iOS flavors
- **iOS_FLAVORS_FINAL.md** - iOS flavors final setup
- **FASTLANE_MATCH_SETUP.md** - Setup Fastlane Match

### 🌐 `network/`
Tài liệu về Network, API & Error Handling:
- **NETWORK_GUIDE.md** - Hướng dẫn network layer
- **NETWORK_MONITORING_GUIDE.md** - Giám sát network
- **ERROR_HANDLING_GUIDE.md** - Xử lý lỗi
- **TOKEN_REFRESH_GUIDE.md** - Refresh token
- **TOKEN_REFRESH_EXAMPLES.md** - Ví dụ refresh token
- **TOKEN_REFRESH_IMPLEMENTATION_SUMMARY.md** - Tóm tắt implementation
- **RETRY_SYSTEM_SUMMARY.md** - Hệ thống retry
- **OFFLINE_DATA_IMPLEMENTATION.md** - Xử lý dữ liệu offline

### ⚙️ `configuration/`
Tài liệu về Environment & Configuration:
- **ENVIRONMENT_CONFIGURATION.md** - Cấu hình môi trường

### 📋 `project/`
Tài liệu về quản lý dự án:
- **PROJECT_STATUS.md** - Trạng thái dự án

### 📝 `requirements/`
Tài liệu yêu cầu và đặc tả (SRS):
- **SRS/** - Thư mục chứa các Software Requirements Specification

## 🔍 Cách sử dụng

1. **Tìm kiếm tài liệu**: Vào thư mục tương ứng với chủ đề bạn quan tâm
2. **Đóng góp tài liệu**: Đặt tài liệu mới vào đúng thư mục theo chủ đề
3. **Cập nhật**: Luôn giữ README.md này được cập nhật khi có thay đổi

## 🏷️ Quy tắc đặt tên

- Sử dụng **SCREAMING_SNAKE_CASE** cho tên file (ví dụ: `NETWORK_GUIDE.md`)
- Sử dụng **snake_case** cho tên thư mục (ví dụ: `architecture/`)
- Luôn có phần mở rộng `.md` cho các file markdown

## 📞 Liên hệ

Nếu có thắc mắc về cấu trúc tài liệu hoặc cần hỗ trợ, vui lòng liên hệ team development. 