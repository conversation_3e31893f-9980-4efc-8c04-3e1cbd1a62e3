
# 📄 SRS – <PERSON><PERSON><PERSON> hình <PERSON> Chủ App Sale

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu
### 1.1 <PERSON><PERSON><PERSON> đích

<PERSON>n hình <PERSON>rang chủ (Homepage) của App Sale được thiết kế nhằm cung cấp cho **tất cả người dùng** một cái nhìn tổng quan, trực quan về hiệu suất công việ<PERSON>, qu<PERSON><PERSON> lý kh<PERSON> hàng, s<PERSON><PERSON> ph<PERSON>m, bá<PERSON> cáo nhanh và các thao tác nghiệp vụ liên quan đến hoạt động bán hàng, giới thiệu sản phẩm vay tại KienlongBank. Trang chủ giúp người dùng dễ dàng truy cập các chức năng chính, theo dõi thông tin quan trọng và thực hiện các thao tác nghiệp vụ một cách thuận tiện, <PERSON><PERSON><PERSON> chón<PERSON>.

### 1.2 Phạm vi

Trang chủ là điểm truy cập trung tâm của hệ thống <PERSON>pp <PERSON>, t<PERSON><PERSON> hợ<PERSON> các dữ liệu tài chính, quản lý khách hàng, báo cáo nhanh, thao tác nghiệp vụ và hướng dẫn sử dụng. Các thông tin và chức năng trên Trang chủ sẽ được hiển thị hoặc ẩn đi tùy theo vai trò đăng nhập của người dùng, đảm bảo mỗi user chỉ nhìn thấy và thao tác với các phần phù hợp với quyền hạn của mình.

### 1.3 Vai trò sử dụng

- **Tất cả các user** sử dụng hệ thống App Sale đều truy cập được màn hình Trang chủ.
- Các thông tin tài chính, quản lý, báo cáo, thao tác nghiệp vụ sẽ được phân quyền hiển thị theo từng vai trò (CTV, Tổ trưởng, Trưởng nhóm, Cán bộ bán hàng, Quản lý, Back-office...).
- Hệ thống back-office hỗ trợ phân quyền, đảm bảo bảo mật và đúng chức năng cho từng nhóm người dùng.

## 2. Mô tả UI-UX 
### 2.1 Khung chung của trang chủ với tất cả các role gồm có:
- 1 - Header (phần đầu) 
- 2 - Body (phần thân)
- 3 - Footer (phần cuối)

Trong đó Header và footer là cố định với tất cả các Role, Body sẽ hiển thị tuỳ chỉnh theo từng loại role

### 2.2 Mô tả Header của trang
| STT | Trường thông tin     | Kiểu hiển thị | Kiểu thao tác  | Bắt buộc nhập | Mô tả chi tiết                                                                                                               |
| --- | -------------------- | ------------- | -------------- | ------------- | ---------------------------------------------------------------------------------------------------------------------------- |
| 1   | Background header    | Hình ảnh      | Không thao tác | ✘             | Hình nền cho phần header, tạo điểm nhấn giao diện                                                                            |
| 2   | Ảnh avatar           | Ảnh tròn      | Click          | ✘             | Hiển thị hình đại diện người dùng, click để vào trang cá nhân                                                                |
| 3   | Câu chào             | Text          | Không thao tác | ✘             | Hiển thị câu chào theo thời gian trong ngày (sáng/chiều/tối) kèm họ tên người đăng nhập                                      |
| 4   | Chuông thông báo     | Icon          | Click          | ✘             | Hiển thị số lượng thông báo chưa đọc, click để xem danh sách thông báo                                                       | ### 2.4 Mô tả Body của trang hiển thị với role CTV, Nhóm trưởng, Tổ phó, Tổ trưởng |
| 5   | Textbox tìm kiếm     | Textbox       | Nhập           | ✘             | Nhập số CCCD, số điện thoại hoặc thông tin định danh để tìm kiếm khách hàng                                                  |
| 6   | Khung thao tác nhanh | Nhóm button   | Click          | ✘             | Hiển thị các nút thao tác nhanh (Tạo khoản vay, Giới thiệu CTV mới, Thông tin sản phẩm, v.v...), người dùng tự chọn hiển thị |


### 2.3 Mô tả Footer của trang

| STT | Tên button | Biểu tượng (icon) | Kiểu thao tác | Mô tả chi tiết                                  |
| --- | ---------- | ----------------- | ------------- | ----------------------------------------------- |
| 1   | Trang chủ  | Home              | Click         | Chuyển về màn hình Trang chủ                    |
| 2   | Báo cáo    | Report/Chart      | Click         | Truy cập nhanh vào chức năng báo cáo, thống kê  |
| 3   | Cá nhân    | User/Profile      | Click         | Truy cập trang thông tin cá nhân của người dùng |

### 2.4 Mô tả Body của trang hiển thị với role CTV, Nhóm trưởng, Tổ phó, Tổ trưởng.

1. **Danh sách các Module sản phẩm**
   - Hiển thị dạng lưới các icon, mỗi icon đại diện cho một sản phẩm (ví dụ: Vay trả góp ngày, Trợ lý bán hàng, Thu tiền khách hàng, Báo cáo...).
   - Dưới mỗi icon là tên sản phẩm.

2. **Danh sách hồ sơ chưa hoàn tất của mỗi sản phẩm**
   - Hiển thị danh sách các sản phẩm, mỗi item gồm:
     - Icon sản phẩm
     - Tên sản phẩm
     - Số lượng hồ sơ chưa hoàn tất (bằng số)

3. **Danh sách thông tin sản phẩm**
   - Hiển thị danh sách sản phẩm, mỗi item gồm:
     - Tên sản phẩm
     - Mã sản phẩm
     - Đoạn mô tả ngắn gọn về sản phẩm

4. **Mục Đã xem gần đây**
   - Gồm 2 tab:
     - **Tab Khách hàng:** Danh sách khách hàng đã xem gần đây, mỗi khách hàng gồm avatar, họ tên, số điện thoại.
     - **Tab Khoản vay:** Danh sách khoản vay đã xem gần đây, mỗi khoản vay gồm icon, họ tên khách hàng vay, số tiền vay.

5. **Hướng dẫn sử dụng**
   - Hiển thị các hướng dẫn cơ bản giúp người dùng thao tác và sử dụng các chức năng chính trên App Sale.

### 2.5 Mô tả Body của trang hiển thị với role còn lại

1. **Danh sách các Module sản phẩm**
   - Hiển thị dạng lưới các icon, mỗi icon đại diện cho một sản phẩm (ví dụ: Vay trả góp ngày, Trợ lý bán hàng, Thu tiền khách hàng, Báo cáo...).
   - Dưới mỗi icon là tên sản phẩm.

2. **Danh sách thông tin sản phẩm**
   - Hiển thị danh sách sản phẩm, mỗi item gồm:
     - Tên sản phẩm
     - Mã sản phẩm
     - Đoạn mô tả ngắn gọn về sản phẩm

3. **Thông tin Banner các sản phẩm chính của KLB**
    - Hiển thị List ảnh các Banner sản phẩm chính của KLB
4. **Hướng dẫn sử dụng**
   - Hiển thị các hướng dẫn cơ bản giúp người dùng thao tác và sử dụng các chức năng chính trên App Sale.










