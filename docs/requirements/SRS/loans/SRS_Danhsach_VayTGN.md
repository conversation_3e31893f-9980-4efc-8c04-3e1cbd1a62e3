# SRS – Mo<PERSON>le Vay Trả góp ngày

Từ màn hình Trang chủ => Nhấn Vay trả góp ngày -> Hiển thị màn hình gồm 2 tab **Tổng quan** và **<PERSON>h sách**. Mặc định hiển thị tab danh sách

## 1. <PERSON><PERSON><PERSON> hình Tổng quan (chỉ dành cho CTV)

### 1.1 Mục đích
- Cung cấp cho **CTV** cái nhìn tổng thể về tình hình tài chính cá nhân liên quan đến hoạt động vay trả góp ngày, bao gồm các thông tin về dư nợ, hạn mức được cấp và số tiền còn lại có thể giới thiệu/hỗ trợ khách vay.

### 1.2 Đối tượng sử dụng
- **Chỉ Cộng tác viên (CTV)** mớ<PERSON> đ<PERSON> phép truy cập và xem các thông tin trong tab Tổng quan này. (<PERSON><PERSON> gồm CTV, nh<PERSON><PERSON> trưởng, tổ phó, tổ trưởng)

### 1.3 Thông tin hiển thị trên màn hình Tổng quan

#### a. Thống kê

**Tab Hạn mức**

| Trường dữ liệu          | Đơn vị     | Kiểu hiển thị | Mô tả/Ý nghĩa                                                                            |
| ----------------------- | ---------- | ------------- | ---------------------------------------------------------------------------------------- |
| Số hạn mức được cấp     | Triệu đồng | Read-only     | Tổng hạn mức tín chấp được cấp cho CTV                                                   |
| Giá trị TSBĐ            | Triệu đồng | Read-only     | Tổng giá trị tài sản bảo đảm mà CTV đã cung cấp                                          |
| HMBL tín chấp còn lại   | Triệu đồng | Read-only     | Hiển thị dạng a/x: a là số hạn mức tín chấp còn lại, x là số hạn mức tín chấp được cấp   |
| % HMBL tín chấp còn lại | %          | Biểu đồ tròn  | Biểu đồ tròn thể hiện tỷ lệ hạn mức tín chấp còn lại trên tổng hạn mức tín chấp được cấp |
| HMBL TSBĐ còn lại       | Triệu đồng | Read-only     | Hiển thị dạng b/y: b là số hạn mức TSBĐ còn lại, y là số hạn mức TSBĐ được cấp           |
| % HMBL TSBĐ còn lại     | %          | Biểu đồ tròn  | Biểu đồ tròn thể hiện tỷ lệ hạn mức TSBĐ còn lại trên tổng hạn mức TSBĐ được cấp         |

> **Lưu ý:**  
> - Tất cả các trường tài chính đều hiển thị ở đơn vị **triệu đồng**.
> - Các biểu đồ tròn giúp trực quan hóa tỷ lệ sử dụng hạn mức còn lại.
**Tab Dư nợ**

| Trường dữ liệu          | Đơn vị     | Kiểu hiển thị | Mô tả/Ý nghĩa                                                                         |
| ----------------------- | ---------- | ------------- | ------------------------------------------------------------------------------------- |
| Tổng dư nợ              | Triệu đồng | Read-only     | Tổng số tiền dư nợ của tất cả các khoản vay trả góp ngày do CTV quản lý/giới thiệu    |
| Dư nợ có TSBĐ           | Triệu đồng | Read-only     | Hiển thị dạng a/x: a là số dư nợ có TSBĐ, x là tổng dư nợ                             |
| Dư nợ không TSBĐ        | Triệu đồng | Read-only     | Hiển thị dạng b/x: b là số dư nợ không TSBĐ, x là tổng dư nợ                          |
| Biểu đồ tròn tổng dư nợ | %          | Biểu đồ tròn  | Biểu đồ tròn thể hiện tỷ lệ dư nợ có TSBĐ và dư nợ không TSBĐ trên tổng dư nợ của CTV |
> **Lưu ý:**  
> - Tất cả các trường tài chính đều hiển thị ở đơn vị **triệu đồng**.  
> - Biểu đồ tròn giúp trực quan hóa tỷ lệ dư nợ có TSBĐ và không TSBĐ trên tổng dư nợ.

**Tab Số tiền**

| Trường dữ liệu   | Đơn vị     | Kiểu hiển thị | Mô tả/Ý nghĩa                                                   |
| ---------------- | ---------- | ------------- | --------------------------------------------------------------- |
| Tiền gốc/ngày    | Triệu đồng | Read-only     | Tổng số tiền gốc khách hàng phải nộp mỗi ngày cho các khoản vay |
| Tiền lãi/ngày    | Triệu đồng | Read-only     | Tổng số tiền lãi khách hàng phải nộp mỗi ngày cho các khoản vay |
| Số tiền nộp/ngày | Triệu đồng | Read-only     | Tổng số tiền khách hàng đã thực tế nộp vào hệ thống trong ngày  |
> **Lưu ý:**  
> - Tất cả các trường tài chính đều hiển thị ở đơn vị **triệu đồng**.
> - Các trường này giúp CTV theo dõi sát sao nghĩa vụ thanh toán hàng ngày của khách hàng. nộp**


**b. Báo cáo nhanh**

| Trường dữ liệu        | Đơn vị     | Kiểu hiển thị | Mô tả/Ý nghĩa                                                   |
| --------------------- | ---------- | ------------- | --------------------------------------------------------------- |
| Số tiền gốc chậm nộp  | Triệu đồng | Read-only     | Tổng số tiền gốc khách hàng chưa nộp đúng hạn cho các khoản vay |
| Số ngày chậm nộp      | Ngày       | Read-only     | Tổng số ngày khách hàng bị chậm nộp tiền gốc                    |
| Bình quân dư nợ/tháng | Triệu đồng | Read-only     | Giá trị dư nợ bình quân của các khoản vay trong 1 tháng         |
| Tổng số khách hàng    | Khách hàng | Read-only     | Tổng số khách hàng do CTV quản lý/giới thiệu                    |

> **Lưu ý:**  
> - Các trường tài chính đều hiển thị ở đơn vị **triệu đồng**.
> - Báo cáo nhanh giúp CTV kiểm soát rủi ro và hiệu quả hoạt động.

## 2. Màn hình Danh sách 

### 2.1 Mục đích
Tài liệu này mô tả chi tiết yêu cầu hệ thống cho chức năng "Vay trả góp ngày" trên ứng dụng App Sale của KienlongBank. Mục tiêu là chuẩn hoá quy trình tạo và xử lý hồ sơ vay vốn thông qua cộng tác viên và nhân viên bán hàng, đảm bảo đồng bộ với các hệ thống khác và tối ưu trải nghiệm người dùng.

### 1.2 Phạm vi
- App Sale: Ứng dụng mobile dành cho CTV và nhân viên 

- Web Sale: Hệ thống Web sale cho back-office

**Tích hợp hệ thống:**

- FCC: Lấy CIF, TKTT

- CIC: Tra cứu tín dụng khách hàng

- Mango: Đồng bộ dư nợ, HMBL, tạo khế ước vay

**Đối tượng sử dụng:** CTV, CBBH, GDV, Trưởng ĐVKD, GĐKV, RB HO, Admin

# Module "Vay trả góp ngày"

## 1. Mục đích sử dụng

Module **"Vay trả góp ngày"** trong ứng dụng **App Sale** cho phép **Cộng tác viên (CTV)** và các bộ backoffice (CBBH, GDV, Trưởng ĐVKD/GĐKV) quản lý các khoản vay trả góp của khách hàng, bao gồm các chức năng tìm kiếm, lọc trạng thái hồ sơ, và tạo mới khoản vay. Màn hình này giúp **CTV** và các bộ backoffice (CBBH, GDV, Trưởng ĐVKD/GĐKV) dễ dàng theo dõi, quản lý, và xử lý các khoản vay từ khi tạo mới đến khi giải ngân.

## 2. Mô tả màn hình UI/UX 

### 2.1 Thanh tìm kiếm
**Tìm kiếm (Textbox)**: Hiển thị dòng text nhạt màu trong trong textbox tìm kiếm nhằm cho người dùng biết cho phep tìm kiếm theo những trường nào
  - **Họ tên / SĐT/ Số CCCD**

**Tìm kiếm theo thời gian**: CTV có thể lọc các khoản vay theo thời gian:
  - **Hôm nay (Button)**: Tìm kiếm các khoản vay được tạo trong ngày hiện tại.
  - **Tuần trước (Button)**: Tìm kiếm các khoản vay được tạo trong tuần trước.
  - **Tháng trước (button)**: Tìm kiếm các khoản vay được tạo trong tháng trước.

### 2.2 Bộ lọc trạng thái hồ sơ
- **Mục đích**: Bộ lọc trạng thái hồ sơ sẽ được hiển thị dưới dạng **thanh bar** ở **phía trên cùng** của màn hình, cho phép **CTV** lọc các khoản vay theo các trạng thái khác nhau. Mỗi trạng thái sẽ có một **tab** riêng biệt, thể hiện số lượng hồ sơ đang ở trạng thái đó.

- **Các trạng thái hồ sơ bao gồm**:
  - **Tất cả (X)**: Hiển thị tổng số hồ sơ của **CTV**.
  - **Đang tạo (X)**: Hồ sơ đang trong quá trình tạo mới.
  - **CBBH (X)**: Hồ sơ đang được xử lý bởi **Cán bộ bán hàng (CBBH)**.
  - **GDV**: Hồ sơ đang được xử lý bởi **Giao dịch viên (GDV)**.
  - **Chờ phê duyệt**: Hồ sơ đang chờ được **phê duyệt**.
  - **Đã phê duyệt**: Hồ sơ đã được **phê duyệt**.
  - **Từ chối**: Hồ sơ bị **từ chối**.
  - **Giải ngân**: Hồ sơ đã được **giải ngân**.

- **Khi ấn vào một tab**: Hệ thống sẽ hiển thị **danh sách các hồ sơ** đang ở trạng thái đó, giúp **CTV** dễ dàng theo dõi và quản lý các khoản vay theo từng bước trong quy trình.

| Trạng thái        | Số hồ sơ |
| ----------------- | -------- |
| **Tất cả**        | 50       |
| **Đang tạo**      | 5        |
| **CBBH**          | 10       |
| **GDV**           | 3        |
| **Chờ phê duyệt** | 12       |
| **Đã phê duyệt**  | 7        |
| **Từ chối**       | 2        |
| **Giải ngân**     | 11       |

### 2.3 Danh sách các khoản vay
- **Mục đích**: Hiển thị **danh sách các khoản vay** của **CTV**, bao gồm thông tin như **họ tên khách hàng**, **số tiền vay**, **thời gian vay**, và **trạng thái hồ sơ**.
  
- **Các thông tin hiển thị** cho mỗi khoản vay:
  - **Tên khách hàng vay chính**
  - **CIF khách hàng** (Mã khách hàng)
  - **Số tiền vay** và **thời gian vay**
  - **Trạng thái hồ sơ** (Ví dụ: Đang tạo, CBBH, GDV, Chờ phê duyệt, Đã phê duyệt, Từ chối, Giải ngân)
  - **Ngày tạo hồ sơ** và **Ngày cập nhật hồ sơ**
  - **Họ tên CTV tạo khoản vay**: Tên của **CTV** đã tạo hồ sơ vay này, giúp dễ dàng phân biệt ai đã xử lý hồ sơ cho khách hàng.

### 2.4 Các hành động thao tác trên khoản vay
- **Button "Vay lại"**:
  - **CTV** có thể **sao chép hồ sơ vay cũ** để tạo một khoản vay mới cho khách hàng đó mà không cần nhập lại thông tin.
  
- **Button "Phone"**:
  - Khi **CTV** click vào **nút "Phone"**, **số điện thoại** của khách hàng sẽ được hiển thị. **CTV** có thể **gọi điện trực tiếp** đến khách hàng từ ứng dụng.
  
- **Button "Message"**:
  - Khi **CTV** click vào **nút "Message"**, **màn hình nhắn tin SMS** sẽ được mở ra với số điện thoại của khách hàng. **CTV** có thể nhắn tin trực tiếp đến khách hàng.

## 3. Quy trình tạo khoản vay mới

Khi **CTV** nhấn vào nút **"Tạo khoản vay mới"**, hệ thống sẽ chuyển sang **tiến trình tạo khoản vay mới** với **10 bước**.

- **Bước 1**: Cung cấp giấy tờ tùy thân (người vay chính)
- **Bước 2**: Xác nhận thông tin người vay chính
- **Bước 3**: Cung cấp giấy tờ tùy thân (người đồng vay)
- **Bước 4**: Xác nhận thông tin người đồng vay
- **Bước 5**: Cung cấp thông tin đề nghị vay vốn
- **Bước 6**: Cung cấp thông tin tình hình tài chính
- **Bước 7**: Cung cấp thông tin tài sản bảo đảm (nếu có)
- **Bước 8**: Cung cấp danh mục chứng từ
- **Bước 9**: Xác nhận thông tin khoản vay
- **Bước 10**: Màn hình khởi tạo khoản vay thành công

Mỗi bước trong quy trình sẽ yêu cầu người dùng nhập thông tin cần thiết và xác nhận trước khi tiếp tục đến bước kế tiếp.
