# Permission Debug Guide

## Vấn đề thường gặp

### 1. <PERSON><PERSON><PERSON> cả permission đều hiện "Từ chối" (PermissionStatus.denied)

#### Nguyên nhân có thể:

**A. Thiếu quyền trong manifest/plist**
- **Android**: Thiếu `<uses-permission>` trong `AndroidManifest.xml`
- **iOS**: Thiếu `NS*UsageDescription` trong `Info.plist`

**B. Thiếu cấu hình permission trong Podfile (iOS)**
- **iOS**: Thiếu cấu hình `GCC_PREPROCESSOR_DEFINITIONS` trong `Podfile`
- <PERSON><PERSON><PERSON> là nguyên nhân chính khiến permission luôn trả về "denied" trên iOS

**C. Test trên Simulator/Emulator**
- Camera, microphone, location không hoạt động trên simulator
- <PERSON>ôn tr<PERSON> về "denied"

**D. App chưa đượ<PERSON> build lại**
- <PERSON><PERSON> <PERSON>hi thêm quyền vào manifest/plist/Podfile, phải build lại app

**E. Permission bị từ chối vĩnh viễn**
- User đã từ chối nhiều lần → cần vào Settings để cấp lại

#### Cách debug:

1. **Kiểm tra manifest/plist**
   ```bash
   # Android
   cat android/app/src/main/AndroidManifest.xml
   
   # iOS  
   cat ios/Runner/Info.plist
   ```

2. **Kiểm tra cấu hình Podfile (iOS)**
   ```bash
   # Kiểm tra xem có cấu hình permission trong Podfile không
   cat ios/Podfile | grep -A 30 "GCC_PREPROCESSOR_DEFINITIONS"
   ```

3. **Clean và rebuild (iOS)**
   ```bash
   # Xóa Pods và rebuild
   cd ios && rm -rf Pods Podfile.lock && cd ..
   fvm flutter clean
   fvm flutter pub get
   ```

4. **Test trên thiết bị thật**
   ```bash
   fvm flutter run --release
   ```

5. **Xem log chi tiết**
   ```bash
   fvm flutter run --debug
   # Mở PermissionDebug widget và xem console log
   ```

6. **Reset permission**
   - Xoá app khỏi thiết bị
   - Cài lại app

### 2. Permission request không hiện dialog

#### Nguyên nhân:
- Thiếu usage description (iOS)
- Permission đã bị từ chối vĩnh viễn
- Logic request bị lỗi

#### Cách debug:
1. Kiểm tra log khi request permission
2. Test với `PermissionDebug` widget
3. Kiểm tra `PermissionService.requestPermission()`

### 3. Permission được cấp nhưng không hoạt động

#### Nguyên nhân:
- Logic xử lý permission bị lỗi
- Mapping sai giữa `PermissionType` và `Permission`
- Platform-specific issues

## Cách sử dụng PermissionDebug

### 1. Mở PermissionDebug
```dart
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => const PermissionDebug(),
  ),
);
```

### 2. Test từng permission
- Nhấn "Check" để xem trạng thái hiện tại
- Nhấn "Request" để yêu cầu quyền
- Xem log console để debug

### 3. Kiểm tra log
Log sẽ hiển thị:
```
=== DEBUG: Checking camera permission ===
Camera status: PermissionStatus.denied
Is granted: false
Is denied: true
Is permanently denied: false
```

## Checklist Debug

### Android
- [ ] `AndroidManifest.xml` có `<uses-permission android:name="android.permission.CAMERA" />`
- [ ] Test trên thiết bị Android thật
- [ ] App được build lại sau khi thêm quyền
- [ ] Không có lỗi trong log

### iOS
- [ ] `Info.plist` có `NSCameraUsageDescription`
- [ ] `Podfile` có cấu hình `GCC_PREPROCESSOR_DEFINITIONS` với permission cần thiết
- [ ] Đã clean và rebuild sau khi thêm cấu hình Podfile
- [ ] Test trên thiết bị iOS thật (không phải simulator)
- [ ] App được build lại sau khi thêm quyền
- [ ] Không có lỗi trong log

### Code
- [ ] `PermissionService` không có lỗi logic
- [ ] `PermissionType` mapping đúng với `Permission`
- [ ] Không có exception được catch và trả về denied
- [ ] Log hiển thị đúng trạng thái

## Ví dụ log thành công

```
=== DEBUG: Requesting camera permission ===
Camera request result: PermissionStatus.granted
Is granted: true
Is denied: false
Is permanently denied: false
```

## Ví dụ log thất bại

```
=== DEBUG: Requesting camera permission ===
Camera request result: PermissionStatus.denied
Is granted: false
Is denied: true
Is permanently denied: false
```

## Cấu hình iOS Podfile

### Vấn đề chính
Theo tài liệu [permission_handler](https://pub.dev/packages/permission_handler), **iOS cần cấu hình permission trong `Podfile`** để enable các permission cần thiết. Nếu thiếu cấu hình này, tất cả permission sẽ luôn trả về "denied".

### Cấu hình cần thiết
Thêm vào phần `post_install` trong `ios/Podfile`:

```ruby
post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    target.build_configurations.each do |config|
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        
        ## dart: PermissionGroup.camera
        'PERMISSION_CAMERA=1',

        ## dart: PermissionGroup.microphone
        'PERMISSION_MICROPHONE=1',

        ## dart: PermissionGroup.photos
        'PERMISSION_PHOTOS=1',

        ## dart: PermissionGroup.location
        'PERMISSION_LOCATION=1',
      ]
    end
  end
end
```

### Clean và rebuild
Sau khi thêm cấu hình:
```bash
cd ios && rm -rf Pods Podfile.lock && cd ..
fvm flutter clean
fvm flutter pub get
```

## Lưu ý quan trọng

1. **Podfile configuration**: iOS cần cấu hình permission trong Podfile
2. **Simulator/Emulator**: Camera, microphone, location không hoạt động
3. **Thiết bị thật**: Luôn test trên thiết bị thật
4. **Build lại**: Sau khi thêm quyền, phải build lại app
5. **Reset app**: Xoá app và cài lại để reset permission state
6. **Log**: Luôn xem log console để debug

## Lỗi Stack Overflow khi nhấn "Vào cài đặt"

### Triệu chứng
```
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: Stack Overflow
#0      new AsyncError (dart:async/async_error.dart:15:3)
#1      Future._propagateToListeners.handleValueCallback (dart:async/future_impl.dart:934:40)
...
```

### Nguyên nhân
- **Vòng lặp vô hạn** trong `PermissionService.openAppSettings()`
- Method gọi chính nó thay vì gọi API thực tế
- Thiếu dependency `url_launcher`

### Giải pháp

#### 1. Kiểm tra dependencies
Đảm bảo `pubspec.yaml` có:
```yaml
dependencies:
  url_launcher: ^6.2.5
```

#### 2. Clean và rebuild
```bash
fvm flutter clean
fvm flutter pub get
```

#### 3. Kiểm tra code
Đảm bảo `PermissionService.openAppSettings()` không gọi chính nó:
```dart
// ❌ SAI - Vòng lặp vô hạn
Future<bool> openAppSettings() async {
  return await openAppSettings(); // Gọi chính nó!
}

// ✅ ĐÚNG - Sử dụng url_launcher
Future<bool> openAppSettings() async {
  final Uri settingsUri = Uri(scheme: 'app-settings');
  if (await canLaunchUrl(settingsUri)) {
    return await launchUrl(settingsUri);
  }
  return false;
}
```

#### 4. Alternative solution
Nếu `url_launcher` không hoạt động, thử:
```dart
Future<bool> openAppSettings() async {
  try {
    // Sử dụng permission_handler trực tiếp
    return await Permission.openAppSettings();
  } catch (e) {
    return false;
  }
}
```

### Prevention
- Luôn test method `openAppSettings()` trước khi deploy
- Sử dụng `PermissionDebug` widget để test
- Kiểm tra log console khi nhấn "Vào cài đặt"

## Liên hệ

Nếu vẫn gặp vấn đề, hãy:
1. Chụp màn hình log console
2. Gửi nội dung manifest/plist
3. Mô tả thiết bị và OS version
4. Mô tả bước thực hiện 