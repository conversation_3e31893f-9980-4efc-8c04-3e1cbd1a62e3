# iOS Flavors Configuration

## Tổng quan
Dự án đã được cấu hình với 3 flavors cho iOS:
- **Development** (`com.kienlongbank.sales_app.dev`)
- **Staging** (`com.kienlongbank.sales_app.staging`)  
- **Production** (`com.kienlongbank.sales_app`)

## Cách sử dụng

### Build với Flutter CLI:
```bash
# Development
fvm flutter build ios --debug --flavor development --target lib/main_development.dart --no-codesign

# Staging  
fvm flutter build ios --debug --flavor staging --target lib/main_staging.dart --no-codesign

# Production
fvm flutter build ios --release --flavor production --target lib/main_production.dart --no-codesign
```

### Sử dụng scripts:
```bash
# Build một flavor
./scripts/build_flavors_ios.sh development debug
./scripts/build_flavors_ios.sh staging release
./scripts/build_flavors_ios.sh production release

# Build tất cả flavors
./scripts/build_flavors_ios.sh all debug

# Run trên simulator
./scripts/run_ios_flavor.sh development "iPhone 16 Pro"
```

## Cấu trúc files

### Xcconfig files:
- `ios/Development.xcconfig` - Development flavor configuration
- `ios/Staging.xcconfig` - Staging flavor configuration  
- `ios/Production.xcconfig` - Production flavor configuration

### Schemes:
- `development` - Debug builds cho development
- `staging` - Release builds cho staging
- `production` - Release builds cho production

### Build configurations:
- `Debug-development`
- `Release-development`
- `Profile-development`
- `Debug-staging`
- `Release-staging`
- `Profile-staging`
- `Debug-production`
- `Release-production`
- `Profile-production`

## Lưu ý
- Đã xóa hardcode `PRODUCT_BUNDLE_IDENTIFIER` khỏi `project.pbxproj`
- Schemes được đặt tên lowercase theo yêu cầu của Flutter CLI
- Xcconfig files include đúng path `Flutter/Generated.xcconfig` 