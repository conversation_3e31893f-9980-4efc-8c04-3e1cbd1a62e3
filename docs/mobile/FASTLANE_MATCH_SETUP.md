# 🎯 Fastlane Match Setup Guide

## 📋 **Tổng quan**

Fastlane Match là giải pháp code signing automation cho iOS, giúp quản lý certificates và provisioning profiles một cách tự động và đồng bộ giữa các developer và CI/CD.

## 🚀 **Setup Process**

### **Bước 1: Tạo Private Repository cho Certificates**

```bash
# Trên GitLab/GitHub, tạo repository mới
# Tên đề xuất: sales-app-certificates
# Private repository (BẮT BUỘC)
```

**Repository URL mặc định:** `**************:kienlongbank/sales-app-certificates.git`

### **Bước 2: Cập nhật Matchfile**

Chỉnh sửa `ios/fastlane/Matchfile`:

```ruby
# Update với thông tin thực tế
git_url("**************:kienlongbank/sales-app-certificates.git")
team_id("YOUR_TEAM_ID_HERE")          # Tìm tại developer.apple.com
username("<EMAIL>")    # Apple ID của bạn
```

### **Bước 3: Initialize Match (First Time Only)**

```bash
cd ios
bundle exec fastlane match_init
```

**Lệnh này sẽ:**
- Tạo repository structure
- Setup encryption passphrase
- Tạo initial certificates

### **Bước 4: Environment Variables**

Thêm vào CI/CD environment variables:

```bash
# GitLab CI/CD Variables
MATCH_GIT_BASIC_AUTHORIZATION="base64_encoded_git_credentials"
MATCH_KEYCHAIN_PASSWORD="your_keychain_password"
MATCH_PASSWORD="encryption_passphrase_from_step3"
FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD="app_specific_password"
FASTLANE_SESSION="session_token"
```

## 📱 **Available Lanes**

### **Code Signing Setup**
```bash
# Setup tất cả certificates và profiles
bundle exec fastlane setup_codesigning

# Update certificates khi có device mới
bundle exec fastlane match_update

# Thêm device mới vào development
bundle exec fastlane match_add_device name:"iPhone 14" udid:"device_udid_here"
```

### **Build with Match**
```bash
# Build individual flavors
bundle exec fastlane development
bundle exec fastlane staging  
bundle exec fastlane production

# Build tất cả flavors
bundle exec fastlane all
```

### **Deploy with Match**
```bash
# Deploy staging to TestFlight
bundle exec fastlane deploy_staging

# Deploy production to App Store Connect
bundle exec fastlane deploy_production

# Promote builds
bundle exec fastlane promote_staging_to_external
bundle exec fastlane promote_production_to_appstore
```

## 🔐 **Certificate Types & Bundle IDs**

### **Development Certificates**
```
• com.kienlongbank.sales_app.dev
• com.kienlongbank.sales_app.staging  
• com.kienlongbank.sales_app
```

### **Ad-Hoc Certificates** (Staging Distribution)
```
• com.kienlongbank.sales_app.staging
```

### **App Store Certificates** (Production)
```
• com.kienlongbank.sales_app
```

## 🛠 **Troubleshooting**

### **Lỗi thường gặp:**

#### 1. **"Could not download/parse profile"**
```bash
# Solution: Re-run match to regenerate profiles
bundle exec fastlane match_update
```

#### 2. **"Certificate not found in keychain"**
```bash
# Solution: Clear keychain và setup lại
security delete-keychain fastlane_tmp_keychain
bundle exec fastlane setup_codesigning
```

#### 3. **"Git repository access denied"**
```bash
# Solution: Check SSH keys và repository permissions
ssh -T **************
```

#### 4. **"App identifier không match"**
```bash
# Solution: Kiểm tra bundle IDs trong Matchfile và xcconfig files
grep -r "PRODUCT_BUNDLE_IDENTIFIER" ios/
```

### **Debug Mode:**
```bash
# Run với verbose output
bundle exec fastlane setup_codesigning --verbose
```

## 🔄 **CI/CD Integration**

### **GitLab CI Update**

Thêm vào `.gitlab-ci.yml`:

```yaml
# iOS Build Job với Match
ios_build:
  stage: build
  script:
    - cd ios
    - bundle install
    - bundle exec fastlane setup_codesigning
    - bundle exec fastlane all
  artifacts:
    paths:
      - build/ios/ipa/
    expire_in: 1 week
  only:
    - main
    - develop
```

## 📁 **File Structure After Setup**

```
ios/
├── fastlane/
│   ├── Fastfile          # Updated với Match lanes
│   ├── Matchfile         # Match configuration  
│   └── Appfile           # Existing
├── Development.xcconfig   # Loại bỏ hardcoded signing
├── Staging.xcconfig      # Loại bỏ hardcoded signing
└── Production.xcconfig   # Loại bỏ hardcoded signing
```

## ✅ **Verification Steps**

### **1. Test Local Build:**
```bash
cd ios
bundle exec fastlane development
```

### **2. Check Certificate Installation:**
```bash
security find-identity -v -p codesigning
```

### **3. Verify Provisioning Profiles:**
```bash
ls -la ~/Library/MobileDevice/Provisioning\ Profiles/
```

## 🚨 **Security Best Practices**

1. **KHÔNG commit certificates vào main repository**
2. **Dùng private repository riêng cho certificates**
3. **Encrypt repository với strong passphrase**
4. **Rotate certificates định kỳ (hàng năm)**
5. **Limit access đến certificate repository**
6. **Use environment variables cho sensitive data**

## 🎯 **Next Steps**

1. ✅ **Setup Match** - Hoàn thành
2. ⏳ **Setup Apple Developer Account** - Cần có để test
3. ⏳ **Configure CI/CD** - Update environment variables
4. ⏳ **Test deployment** - Deploy staging build
5. ⏳ **App Store submission** - Production release

---

## 📞 **Support**

Nếu gặp vấn đề:
1. Check logs: `fastlane --verbose`
2. Check documentation: https://docs.fastlane.tools/actions/match/
3. Team support: Contact iOS team lead 