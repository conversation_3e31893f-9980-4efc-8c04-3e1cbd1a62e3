# OpenAPI Generator Integration Guide

> **🎯 Objective**: Automatic API client generation from Swagger JSON exports for KienlongBank Sales App

## 📋 Overview

This guide covers the setup and usage of OpenAPI Generator to automatically generate Dart/Flutter API clients from JSON specifications exported from Swagger UI.

### Benefits:
- ✅ **Automatic code generation** từ API specifications
- ✅ **Type-safe API calls** với generated models
- ✅ **Dio integration** với interceptors
- ✅ **Multiple API support** (Sales App + Media APIs)
- ✅ **Local file workflow** - no HTTP server required

## 🏗️ Architecture

```
Backend APIs (Swagger) → JSON Export → OpenAPI Generator → Dart API Clients
                                    ↓
                              Build integration
                                    ↓
                              Flutter App Usage
```

### Current APIs:
1. **Sales App API** - Authentication, users, registrations
2. **Media API** - File upload/download, image processing

## ⚙️ Setup

### 1. Dependencies

```yaml
# pubspec.yaml
dependencies:
  # Core dependencies for generated code
  dio: ^5.4.0
  json_annotation: ^4.8.1
  
dev_dependencies:
  # OpenAPI Generator
  openapi_generator_annotations: ^6.1.0
  openapi_generator: ^6.1.0
  
  # Code generation
  build_runner: ^2.4.8
  json_serializable: ^6.7.1
```

### 2. Configuration Files

**Sales App API Config** (`lib/data/network/openapi/sales_api_config.dart`):
```dart
import 'package:openapi_generator_annotations/openapi_generator_annotations.dart';

@Openapi(
  additionalProperties: AdditionalProperties(
    pubName: 'sales_app_api',
    pubAuthor: 'KienlongBank Sales Team',
    pubDescription: 'Auto-generated API client for KienlongBank Sales App',
  ),
  inputSpec: InputSpec(path: 'openapi/json-specs/sale-app-api-spec.json'),
  generatorName: Generator.dio,
  outputDirectory: 'lib/generated/api/sales',
  typeMappings: {
    'DateTime': 'DateTime',
  },
  importMappings: {
    'DateTime': 'dart:core',
  },
)
class SalesApiConfig {}
```

**Media API Config** (`lib/data/network/openapi/media_api_config.dart`):
```dart
import 'package:openapi_generator_annotations/openapi_generator_annotations.dart';

@Openapi(
  additionalProperties: AdditionalProperties(
    pubName: 'media_api',
    pubAuthor: 'KienlongBank Sales Team',
    pubDescription: 'Auto-generated API client for Media Upload/Download',
  ),
  inputSpec: InputSpec(path: 'openapi/json-specs/media-spec.json'),
  generatorName: Generator.dio,
  outputDirectory: 'lib/generated/api/media',
  typeMappings: {
    'DateTime': 'DateTime',
  },
  importMappings: {
    'DateTime': 'dart:core',
  },
)
class MediaApiConfig {}
```

### 3. Directory Structure

```
openapi/
├── README.md
├── json-specs/
│   ├── README.md
│   ├── sale-app-api-spec.json      # Sales App API specification
│   └── media-spec.json             # Media API specification
└── examples/                       # Optional examples

scripts/
└── generate_api.sh                 # Generation automation script

lib/
├── data/network/
│   ├── openapi/                    # OpenAPI Generator configs
│   │   ├── README.md               # Config documentation
│   │   ├── sales_api_config.dart   # Sales API config
│   │   └── media_api_config.dart   # Media API config
│   ├── api_client.dart             # Base API client
│   ├── base_api_service.dart       # Base API service
│   ├── dio_builder.dart            # Dio configuration
│   ├── openapi_client.dart         # OpenAPI client wrapper
│   └── interceptors/               # HTTP interceptors
└── generated/api/
    ├── sales/                      # Generated Sales API client
    └── media/                      # Generated Media API client
```

## 🔄 Workflow

### Step 1: Export JSON Specifications

**From Swagger UI:**
```bash
# Method 1: Direct download
# 1. Open Swagger UI (e.g., http://localhost:8888/swagger-ui.html)
# 2. Click "Download" → "JSON"
# 3. Save as sale-app-api-spec.json

# Method 2: Using curl
curl -o openapi/json-specs/sale-app-api-spec.json \
  http://localhost:8888/v3/api-docs

curl -o openapi/json-specs/media-spec.json \
  https://dev-ksapi.ssf.vn/media/v3/api-docs
```

**File placement:**
```bash
openapi/json-specs/sale-app-api-spec.json    # Sales App API
openapi/json-specs/media-spec.json           # Media API
```

### Step 2: Generate API Clients

```bash
# Generate all API clients
./scripts/generate_api.sh

# Or generate individually:
./scripts/generate_api.sh sales    # Sales App API only
./scripts/generate_api.sh media    # Media API only
```

### Step 3: Integration

**Basic Usage:**
```dart
// Import generated clients
import 'package:sales_app/generated/api/sales/lib/sales_app_api.dart';
import 'package:sales_app/generated/api/media/lib/media_api.dart';

// Initialize clients
final salesApi = SalesAppApi();
final mediaApi = MediaApi();

// Configure base URLs and auth
salesApi.dio.options.baseUrl = 'http://localhost:8888';
mediaApi.dio.options.baseUrl = 'https://dev-ksapi.ssf.vn/media';

// Make API calls
final users = await salesApi.getUsersApi().getUsers();
final uploadResult = await mediaApi.getMinioControllerApi().uploadFile(file);
```

**With Dependency Injection:**
```dart
// Register in GetIt
@module
abstract class ApiModule {
  @singleton
  SalesAppApi get salesApi => SalesAppApi();
  
  @singleton
  MediaApi get mediaApi => MediaApi();
}

// Usage in repositories
@injectable
class UserRepository {
  final SalesAppApi _salesApi;
  
  UserRepository(this._salesApi);
  
  Future<List<User>> getUsers() async {
    final response = await _salesApi.getUsersApi().getUsers();
    return response.data ?? [];
  }
}
```

## 🔧 Advanced Configuration

### Custom Type Mappings

```dart
@Openapi(
  // Map OpenAPI types to Dart types
  typeMappings: {
    'DateTime': 'DateTime',
    'Date': 'DateTime',
    'UUID': 'String',
    'BigDecimal': 'double',
  },
  importMappings: {
    'DateTime': 'dart:core',
  },
  // Custom properties
  additionalProperties: AdditionalProperties(
    pubName: 'custom_api_client',
    pubVersion: '1.0.0',
    pubAuthor: 'Your Team',
    pubDescription: 'Custom API client',
    pubHomepage: 'https://your-repo.com',
  ),
)
```

### Custom Templates

```bash
# Create custom templates directory
mkdir -p openapi-generator/templates

# Copy default templates and modify
# Templates available at: https://github.com/OpenAPITools/openapi-generator/tree/master/modules/openapi-generator/src/main/resources/dart-dio

# Update config to use custom templates
@Openapi(
  templateDirectory: 'openapi-generator/templates',
  // ... other config
)
```

### Multiple Environments

```dart
// Development config
@Openapi(
  inputSpec: InputSpec(path: 'openapi/json-specs/dev/sale-app-api-spec.json'),
  outputDirectory: 'lib/generated/api/dev/sales',
)
class DevSalesApiConfig {}

// Production config
@Openapi(
  inputSpec: InputSpec(path: 'openapi/json-specs/prod/sale-app-api-spec.json'),
  outputDirectory: 'lib/generated/api/prod/sales',
)
class ProdSalesApiConfig {}
```

## 🛠️ Troubleshooting

### Common Issues

**1. Missing Dependencies**
```bash
# Error: Package 'built_value' not found
# Solution: Add to pubspec.yaml
dependencies:
  built_value: ^8.8.0
  built_collection: ^5.1.1
```

**2. JSON Validation Errors**
```bash
# Error: Invalid OpenAPI specification
# Solution: Validate JSON at https://editor.swagger.io/
```

**3. Build Runner Errors**
```bash
# Error: build_runner fails
# Solution: Clean and rebuild
fvm flutter clean
fvm flutter pub get
fvm flutter packages pub run build_runner build --delete-conflicting-outputs
```

**4. Import Path Issues**
```dart
// Error: Cannot resolve import
// Solution: Check generated file structure and update imports
import 'package:sales_app/generated/api/sales/lib/sales_app_api.dart';
```

### Debugging Generation

**Verbose Output:**
```bash
# Enable verbose logging during generation
fvm flutter packages pub run build_runner build --verbose
```

**Check Generated Files:**
```bash
# List generated API files
find lib/generated/api -name "*.dart" -type f

# Check specific API structure
ls -la lib/generated/api/sales/lib/src/api/
ls -la lib/generated/api/media/lib/src/api/
```

**Validate JSON Specs:**
```bash
# Online validation
# 1. Copy JSON content
# 2. Paste at https://editor.swagger.io/
# 3. Check for errors/warnings

# CLI validation (if available)
openapi-generator-cli validate -i openapi/json-specs/sale-app-api-spec.json
```

## 🎯 Best Practices

### 1. Version Control

```bash
# Track JSON specifications
git add openapi/json-specs/*.json

# Track generated code (optional)
git add lib/generated/api/

# Ignore cache files
echo "**/.openapi-generator*" >> .gitignore
echo "**/.dart_tool/openapi-generator-cache.json" >> .gitignore
```

### 2. API Evolution

```dart
// Handle breaking changes gracefully
class UserApiWrapper {
  final SalesAppApi _api;
  
  UserApiWrapper(this._api);
  
  Future<List<User>> getUsers() async {
    try {
      final response = await _api.getUsersApi().getUsers();
      return response.data?.map((u) => User.fromApi(u)).toList() ?? [];
    } catch (e) {
      // Handle API changes
      throw ApiException('Failed to get users: $e');
    }
  }
}
```

### 3. Testing

```dart
// Mock generated clients for testing
@GenerateMocks([SalesAppApi, MediaApi])
void main() {
  group('UserRepository Tests', () {
    late MockSalesAppApi mockApi;
    late UserRepository repository;
    
    setUp(() {
      mockApi = MockSalesAppApi();
      repository = UserRepository(mockApi);
    });
    
    test('should return users when API call succeeds', () async {
      // Arrange
      when(mockApi.getUsersApi()).thenReturn(MockUsersApi());
      when(mockApi.getUsersApi().getUsers())
          .thenAnswer((_) async => UsersResponse(data: [testUser]));
      
      // Act
      final result = await repository.getUsers();
      
      // Assert
      expect(result, [testUser]);
    });
  });
}
```

### 4. Error Handling

```dart
// Centralized error handling
class ApiErrorHandler {
  static Future<T> handleApiCall<T>(Future<T> Function() apiCall) async {
    try {
      return await apiCall();
    } on DioException catch (e) {
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
          throw NetworkException('Connection timeout');
        case DioExceptionType.receiveTimeout:
          throw NetworkException('Receive timeout');
        case DioExceptionType.badResponse:
          throw ServerException('Server error: ${e.response?.statusCode}');
        default:
          throw UnknownException('Unknown error: ${e.message}');
      }
    }
  }
}

// Usage
final users = await ApiErrorHandler.handleApiCall(
  () => salesApi.getUsersApi().getUsers(),
);
```

## 📈 CI/CD Integration

### GitHub Actions Example

```yaml
# .github/workflows/api-generation.yml
name: Generate API Clients

on:
  push:
    paths:
      - 'openapi/json-specs/**'
  pull_request:
    paths:
      - 'openapi/json-specs/**'

jobs:
  generate-and-test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.x'
          
      - name: Install FVM
        run: dart pub global activate fvm
        
      - name: Install dependencies
        run: fvm flutter pub get
        
      - name: Validate JSON specs
        run: |
          # Add JSON validation step
          python -m json.tool openapi/json-specs/sale-app-api-spec.json > /dev/null
          python -m json.tool openapi/json-specs/media-spec.json > /dev/null
          
      - name: Generate API clients
        run: ./scripts/generate_api.sh
        
      - name: Run analysis
        run: fvm flutter analyze
        
      - name: Run tests
        run: fvm flutter test
        
      - name: Check for changes
        run: |
          if [[ -n $(git status --porcelain) ]]; then
            echo "Generated code has changes"
            git diff
          fi
```

## 🔗 Resources

- **OpenAPI Generator**: https://openapi-generator.tech/
- **Swagger Editor**: https://editor.swagger.io/
- **Flutter OpenAPI Generator**: https://pub.dev/packages/openapi_generator_annotations
- **Project JSON Specs**: [openapi/json-specs/README.md](../../openapi/json-specs/README.md)
- **Network Layer Guide**: [NETWORK_GUIDE.md](NETWORK_GUIDE.md)

## 📞 Support

For issues and questions:
1. Check this guide and troubleshooting section
2. Validate JSON specs at Swagger Editor
3. Review OpenAPI Generator documentation
4. Check project-specific guides in `docs/network/` 