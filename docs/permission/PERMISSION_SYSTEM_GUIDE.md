# 🔐 Permission System Guide

## 📋 Tổng quan

Hệ thống quản lý permission được xây dựng để xử lý các quyền truy cập trong app một cách thống nhất và dễ sử dụng. Hệ thống bao gồm:

- **PermissionService**: Service để quản lý permission
- **PermissionController**: Controller để quản lý state và logic
- **PermissionUtils**: Utility functions để xử lý permission
- **PermissionDialog**: UI components để hiển thị dialog permission

## 🏗️ Kiến trúc

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│  PermissionController  │  PermissionDialog  │  PermissionExample │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Core Layer                              │
├─────────────────────────────────────────────────────────────┤
│  PermissionService  │  PermissionUtils  │  PermissionType   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Platform Layer                            │
├─────────────────────────────────────────────────────────────┤
│                    permission_handler                       │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Cách sử dụng

### 1. Kiểm tra permission đơn lẻ

```dart
// Trong widget
final permissionController = ref.read(permissionControllerProvider.notifier);

// Kiểm tra permission
await permissionController.checkPermission(PermissionType.camera);

// Yêu cầu permission
await permissionController.requestPermission(PermissionType.camera);
```

### 2. Kiểm tra nhiều permission

```dart
// Kiểm tra nhiều permission
await permissionController.checkPermissions([
  PermissionType.camera,
  PermissionType.photos,
  PermissionType.microphone,
]);

// Yêu cầu nhiều permission
await permissionController.requestPermissions([
  PermissionType.camera,
  PermissionType.photos,
  PermissionType.microphone,
]);
```

### 3. Xử lý permission cho tính năng cụ thể

```dart
// Tự động xử lý permission cho tính năng
final granted = await permissionController.handleFeaturePermissions(
  context,
  'qr_scan', // Tên tính năng
  title: 'Quyền truy cập cần thiết',
  message: 'Ứng dụng cần quyền truy cập để sử dụng tính năng này.',
  showDialog: true, // Hiển thị dialog hay không
);

if (granted) {
  // Tiếp tục thực hiện tính năng
} else {
  // Xử lý khi không có quyền
}
```

### 4. Hiển thị dialog permission

```dart
// Hiển thị dialog cho một permission
await permissionController.showPermissionDialog(
  context,
  PermissionType.camera,
  PermissionStatus.denied,
  title: 'Quyền truy cập Camera',
  message: 'Ứng dụng cần quyền truy cập camera để chụp ảnh',
  onGrantPermission: () {
    // Xử lý khi user cấp quyền
  },
  onOpenSettings: () {
    // Xử lý khi user vào cài đặt
  },
);

// Hiển thị dialog cho nhiều permission
await permissionController.showMultiPermissionDialog(
  context,
  {
    PermissionType.camera: PermissionStatus.denied,
    PermissionType.photos: PermissionStatus.permanentlyDenied,
  },
  title: 'Quyền truy cập cần thiết',
  message: 'Ứng dụng cần một số quyền để hoạt động đầy đủ.',
  onGrantPermissions: () {
    // Xử lý khi user cấp quyền
  },
  onOpenSettings: () {
    // Xử lý khi user vào cài đặt
  },
);
```

### 5. Sử dụng trực tiếp PermissionService

```dart
// Lấy service từ DI
final permissionService = getIt<PermissionService>();

// Kiểm tra permission
final status = await permissionService.checkPermission(PermissionType.camera);

// Yêu cầu permission
final status = await permissionService.requestPermission(PermissionType.camera);

// Kiểm tra xem đã có quyền chưa
final granted = await permissionService.isPermissionGranted(PermissionType.camera);

// Mở cài đặt app
await permissionService.openAppSettings();
```

## 📱 Các loại Permission được hỗ trợ

### Camera & Media
- `PermissionType.camera` - Quyền truy cập camera
- `PermissionType.photos` - Quyền truy cập thư viện ảnh
- `PermissionType.photoLibrary` - Quyền truy cập thư viện ảnh (iOS)
- `PermissionType.videos` - Quyền truy cập video
- `PermissionType.audio` - Quyền truy cập âm thanh

### Location
- `PermissionType.location` - Quyền truy cập vị trí
- `PermissionType.locationWhenInUse` - Quyền truy cập vị trí khi sử dụng
- `PermissionType.locationAlways` - Quyền truy cập vị trí luôn luôn

### Storage
- `PermissionType.storage` - Quyền ghi file
- `PermissionType.manageExternalStorage` - Quyền quản lý bộ nhớ ngoài

### Audio & Communication
- `PermissionType.microphone` - Quyền truy cập microphone
- `PermissionType.phone` - Quyền truy cập điện thoại
- `PermissionType.sms` - Quyền truy cập SMS

### Bluetooth
- `PermissionType.bluetooth` - Quyền truy cập Bluetooth
- `PermissionType.bluetoothConnect` - Quyền kết nối Bluetooth
- `PermissionType.bluetoothScan` - Quyền quét Bluetooth
- `PermissionType.bluetoothAdvertise` - Quyền quảng cáo Bluetooth

### System & Others
- `PermissionType.notification` - Quyền gửi thông báo
- `PermissionType.calendar` - Quyền truy cập lịch
- `PermissionType.contacts` - Quyền truy cập danh bạ
- `PermissionType.sensors` - Quyền truy cập cảm biến

## 🎯 Các tính năng được định nghĩa sẵn

Hệ thống đã định nghĩa sẵn các tính năng và permission tương ứng:

### QR Scan
```dart
// Tự động yêu cầu camera permission
await permissionController.handleFeaturePermissions(context, 'qr_scan');
```

### Identity Verification
```dart
// Tự động yêu cầu camera, photos, photoLibrary permissions
await permissionController.handleFeaturePermissions(context, 'identity_verification');
```

### Camera
```dart
// Tự động yêu cầu camera permission
await permissionController.handleFeaturePermissions(context, 'camera');
```

### Gallery
```dart
// Tự động yêu cầu photos, photoLibrary permissions
await permissionController.handleFeaturePermissions(context, 'gallery');
```

### Audio Recording
```dart
// Tự động yêu cầu microphone permission
await permissionController.handleFeaturePermissions(context, 'audio');
```

### Location
```dart
// Tự động yêu cầu location permissions
await permissionController.handleFeaturePermissions(context, 'location');
```

## 🔧 Tùy chỉnh

### Thêm tính năng mới

```dart
// Trong PermissionService, thêm vào method getRequiredPermissionsForFeature
List<PermissionType> getRequiredPermissionsForFeature(String feature) {
  switch (feature.toLowerCase()) {
    // ... existing cases
    
    case 'my_new_feature':
      return [
        PermissionType.camera,
        PermissionType.microphone,
        PermissionType.location,
      ];
    
    default:
      return [];
  }
}
```

### Tùy chỉnh dialog

```dart
// Sử dụng PermissionDialog với tùy chỉnh
PermissionDialog(
  permissionType: PermissionType.camera,
  status: PermissionStatus.denied,
  title: 'Tùy chỉnh tiêu đề',
  message: 'Tùy chỉnh nội dung',
  grantButtonText: 'Đồng ý',
  settingsButtonText: 'Cài đặt',
  cancelButtonText: 'Hủy',
  onGrantPermission: () {
    // Xử lý khi cấp quyền
  },
  onOpenSettings: () {
    // Xử lý khi vào cài đặt
  },
  onCancel: () {
    // Xử lý khi hủy
  },
)
```

## 📊 Trạng thái Permission

### PermissionStatus
- `PermissionStatus.granted` - Đã cấp quyền
- `PermissionStatus.denied` - Bị từ chối
- `PermissionStatus.permanentlyDenied` - Bị từ chối vĩnh viễn
- `PermissionStatus.restricted` - Bị hạn chế
- `PermissionStatus.limited` - Bị giới hạn
- `PermissionStatus.provisional` - Tạm thời

### PermissionState
- `PermissionState.initial()` - Trạng thái ban đầu
- `PermissionState.loading()` - Đang loading
- `PermissionState.checked(permissions)` - Đã kiểm tra
- `PermissionState.requested(permissions)` - Đã request
- `PermissionState.error(message)` - Lỗi

## 🎨 UI Components

### PermissionDialog
Dialog hiển thị thông tin một permission với các tùy chọn:
- Cấp quyền
- Vào cài đặt
- Hủy

### MultiPermissionDialog
Dialog hiển thị thông tin nhiều permission với các tùy chọn:
- Cấp quyền cho tất cả
- Vào cài đặt
- Hủy

## 🔍 Monitoring & Debugging

### Logging
Hệ thống tự động log các hoạt động:
```dart
// Log được tạo tự động
AppLogger.info('Checking permission: camera');
AppLogger.info('Permission check completed: camera = PermissionStatus.granted');
AppLogger.info('Requesting permission: camera');
AppLogger.info('Permission request completed: camera = PermissionStatus.granted');
```

### Error Handling
Hệ thống xử lý lỗi một cách graceful:
```dart
try {
  final status = await permissionService.checkPermission(PermissionType.camera);
  // Xử lý thành công
} catch (e) {
  // Xử lý lỗi
  AppLogger.error('Error checking permission: camera', error: e);
}
```

## 📝 Best Practices

### 1. Luôn kiểm tra permission trước khi sử dụng
```dart
// ✅ Tốt
final granted = await permissionController.areFeaturePermissionsGranted('camera');
if (granted) {
  // Sử dụng camera
} else {
  // Xử lý khi không có quyền
}

// ❌ Không tốt
// Sử dụng camera trực tiếp mà không kiểm tra
```

### 2. Sử dụng handleFeaturePermissions cho tính năng
```dart
// ✅ Tốt
final granted = await permissionController.handleFeaturePermissions(
  context,
  'qr_scan',
  showDialog: true,
);

// ❌ Không tốt
// Tự xử lý từng permission riêng lẻ
```

### 3. Xử lý trạng thái permanentlyDenied
```dart
// ✅ Tốt
if (status.isPermanentlyDenied) {
  // Hướng dẫn user vào cài đặt
  await permissionController.openAppSettings();
}

// ❌ Không tốt
// Request lại permission đã bị từ chối vĩnh viễn
```

### 4. Sử dụng đúng permission cho từng tính năng
```dart
// ✅ Tốt
// Cho QR scan: chỉ cần camera
await permissionController.handleFeaturePermissions(context, 'qr_scan');

// Cho identity verification: cần camera + gallery
await permissionController.handleFeaturePermissions(context, 'identity_verification');

// ❌ Không tốt
// Request tất cả permission cho mọi tính năng
```

## 🧪 Testing

### Ví dụ test
```dart
// Xem file: lib/presentation/widgets/common/permission_example.dart
// Chạy PermissionExample để test các tính năng
```

### Unit Test
```dart
// Test PermissionService
test('should check camera permission', () async {
  final service = PermissionService();
  final status = await service.checkPermission(PermissionType.camera);
  expect(status, isA<PermissionStatus>());
});

// Test PermissionController
test('should handle feature permissions', () async {
  final controller = PermissionController();
  final granted = await controller.areFeaturePermissionsGranted('camera');
  expect(granted, isA<bool>());
});
```

## 🔄 Migration từ code cũ

### Trước đây
```dart
// Code cũ
final status = await Permission.camera.status;
if (status.isDenied) {
  final result = await Permission.camera.request();
  if (result.isGranted) {
    // Sử dụng camera
  }
}
```

### Bây giờ
```dart
// Code mới
final granted = await permissionController.handleFeaturePermissions(
  context,
  'camera',
);
if (granted) {
  // Sử dụng camera
}
```

## 📚 Tài liệu tham khảo

- [permission_handler package](https://pub.dev/packages/permission_handler)
- [Flutter Permission Best Practices](https://flutter.dev/docs/deployment/android#permissions)
- [iOS Permission Guidelines](https://developer.apple.com/design/human-interface-guidelines/ios/user-interaction/permissions/) 