# OpenAPI Generator Setup Guide

## Tổng quan

Dự án này sử dụng **OpenAPI Generator** với **build_runner** để tự động generate API client code từ JSON specifications.

## Cấu trúc

```
openapi/
├── json-specs/                    # JSON specifications từ Swagger UI
│   ├── sale-app-api-spec.json    # Sales App API spec
│   └── media-spec.json           # Media API spec
├── generated/                     # Generated code (không commit)
│   ├── sales/                    # Sales App API client
│   └── media/                    # Media API client
└── generated/                    # Generated packages

lib/data/network/openapi/
├── sales_api_config.dart         # Config cho Sales API
├── media_api_config.dart         # Config cho Media API
└── openapi.dart                  # Barrel export
```

## Cài đặt

### 1. Dependencies

Đảm bảo các dependencies sau đã có trong `pubspec.yaml`:

```yaml
dependencies:
  openapi_generator_annotations: ^6.1.0
  built_value: ^8.4.0

dev_dependencies:
  build_runner: ^2.4.8
  openapi_generator: ^6.1.0
```

### 2. <PERSON><PERSON><PERSON> hình

#### File `build.yaml`
```yaml
targets:
  $default:
    builders:
      openapi_generator:
        enabled: true
        generate_for:
          - lib/data/network/openapi/sales_api_config.dart
          - lib/data/network/openapi/media_api_config.dart
        options:
          verbose: false
```

#### File `openapi_generator_config.json`
```json
{
  "openapiGeneratorVersion": "7.9.0",
  "additionalCommands": "",
  "downloadUrlOverride": null,
  "jarCacheDir": ".dart_tool/openapi_generator_cache",
  "customGeneratorUrls": [
    "https://repo1.maven.org/maven2/com/bluetrainsoftware/maven/openapi-dart-generator/7.2/openapi-dart-generator-7.2.jar"
  ]
}
```

## Sử dụng

### Generate API

#### Sử dụng script (Khuyến nghị)
```bash
# Generate tất cả APIs
./scripts/generate_api.sh

# Generate riêng lẻ
./scripts/generate_api.sh sales
./scripts/generate_api.sh media

# Clean cache và generate
./scripts/generate_api.sh all --clean
```

#### Sử dụng build_runner trực tiếp
```bash
# Generate tất cả
fvm flutter packages pub run build_runner build --delete-conflicting-outputs

# Generate riêng lẻ
fvm flutter packages pub run build_runner build --delete-conflicting-outputs --build-filter="lib/data/network/openapi/sales_api_config.dart"
```

### Sử dụng trong code

```dart
// Import generated APIs
import 'package:sales_app_api/sales_app_api.dart';
import 'package:media_api/media_api.dart' as media_api;

// Sử dụng trong OpenApiClient
@singleton
class OpenApiClient {
  late final AuthenticationApi _authenticationApi;
  late final UsersApi _usersApi;
  late final media_api.MinioControllerApi _minioControllerApi;
  
  // Initialize APIs
  void _initializeApis() {
    _authenticationApi = AuthenticationApi(_defaultDio, _serializers);
    _usersApi = UsersApi(_defaultDio, _serializers);
    _minioControllerApi = media_api.MinioControllerApi(_uploadDio, _serializers);
  }
  
  // Sử dụng APIs
  Future<Response<BaseResponseAppLoginResponse>> login(AppLoginRequest request) {
    return _authenticationApi.login(appLoginRequest: request);
  }
}
```

## Troubleshooting

### Lỗi thường gặp

#### 1. Infinite Loop trong build_runner
**Triệu chứng**: Build_runner chạy mãi không dừng
**Giải pháp**: 
```bash
# Clean cache
rm -rf .dart_tool/openapi_generator_cache .dart_tool/openapi-generator-cache.json .dart_tool/build
fvm flutter clean
fvm flutter pub get
```

#### 2. Validation errors trong JSON spec
**Triệu chứng**: "SpecValidationException: There were issues with the specification"
**Giải pháp**: 
- Kiểm tra path parameters trong JSON spec
- Đảm bảo tất cả `{param}` trong path đều có định nghĩa trong `parameters`
- Sửa JSON spec và regenerate

#### 3. Method không tìm thấy
**Triệu chứng**: "The method 'list' isn't defined for the type 'ProvinceApi'"
**Giải pháp**: 
- Kiểm tra tên method trong generated API
- OpenAPI generator có thể đổi tên method nếu có conflict
- Ví dụ: `list()` → `list1()`, `list2()`

### Debug

#### Enable debug logging
```dart
@Openapi(
  // ... other config
  debugLogging: true,
)
class SalesApiConfig {}
```

#### Verbose build_runner
```bash
fvm flutter packages pub run build_runner build --delete-conflicting-outputs --verbose
```

## Workflow

### 1. Cập nhật API spec
1. Export JSON từ Swagger UI
2. Cập nhật file trong `openapi/json-specs/`
3. Commit JSON spec

### 2. Generate code
```bash
./scripts/generate_api.sh --clean
```

### 3. Kiểm tra generated code
```bash
fvm flutter analyze
```

### 4. Cập nhật implementation
- Review generated models
- Update repository implementations
- Test API calls

### 5. Commit changes
```bash
git add openapi/generated/
git commit -m "feat: regenerate API clients"
```

## Best Practices

### 1. JSON Spec Management
- Luôn export từ Swagger UI chính thức
- Validate JSON spec trước khi commit
- Sử dụng semantic versioning cho API specs

### 2. Generated Code
- Không edit generated code trực tiếp
- Sử dụng barrel exports để import
- Wrap generated APIs trong service layer

### 3. Error Handling
- Sử dụng ErrorInterceptor cho consistent error handling
- Log API calls và responses
- Implement retry logic cho transient failures

### 4. Testing
- Test generated APIs với mock data
- Validate serialization/deserialization
- Test error scenarios

## Migration từ Manual APIs

### Trước (Manual)
```dart
class AuthApiService extends BaseApiService {
  Future<Either<Failure, LoginResponse>> login(LoginRequest request) async {
    return handleApiCall(
      () => dio.post(ApiPaths.authLogin, data: request.toJson()),
      (data) => LoginResponse.fromJson(data),
    );
  }
}
```

### Sau (Generated)
```dart
// Sử dụng trực tiếp trong Repository
@injectable
class AuthRepositoryImpl implements AuthRepository {
  final OpenApiClient _openApiClient;
  
  Future<Either<Failure, LoginResponse>> login(LoginRequest request) async {
    try {
      final response = await _openApiClient.authenticationApi.login(
        appLoginRequest: request,
      );
      return Right(response.data!.data!);
    } catch (e) {
      return Left(ServerFailure(ServerErrorType.unknown));
    }
  }
}
```

## Lợi ích

1. **Type Safety**: Compile-time checking cho API calls
2. **Consistency**: Tất cả APIs sử dụng cùng pattern
3. **Maintainability**: Tự động sync với API spec
4. **Documentation**: Generated docs từ OpenAPI spec
5. **Error Handling**: Consistent error handling
6. **Testing**: Dễ dàng mock và test 