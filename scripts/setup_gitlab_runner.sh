#!/bin/bash

# GitLab Runner Setup Script for Flutter CI/CD on macOS
# Usage: ./scripts/setup_gitlab_runner.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check macOS version
check_macos_version() {
    print_info "Checking macOS version..."
    
    local os_version=$(sw_vers -productVersion)
    local major_version=$(echo $os_version | cut -d. -f1)
    local minor_version=$(echo $os_version | cut -d. -f2)
    
    if [[ $major_version -lt 12 ]]; then
        print_error "macOS 12.0+ required. Current version: $os_version"
        exit 1
    fi
    
    print_success "macOS version $os_version is supported"
}

# Function to install Homebrew
install_homebrew() {
    if command_exists brew; then
        print_success "Homebrew already installed"
        return
    fi
    
    print_info "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH for Apple Silicon Macs
    if [[ $(uname -m) == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zshrc
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
    
    print_success "Homebrew installed successfully"
}

# Function to install basic tools
install_basic_tools() {
    print_info "Installing basic tools..."
    
    brew install git curl wget
    
    print_success "Basic tools installed"
}

# Function to install FVM and Flutter
install_flutter() {
    print_info "Installing FVM and Flutter..."
    
    # Install FVM
    if ! command_exists fvm; then
        dart pub global activate fvm
        
        # Add pub cache to PATH
        if ! grep -q "pub-cache/bin" ~/.zshrc; then
            echo 'export PATH="$PATH:$HOME/.pub-cache/bin"' >> ~/.zshrc
        fi
        
        export PATH="$PATH:$HOME/.pub-cache/bin"
    fi
    
    # Install Flutter 3.24.5
    fvm install 3.24.5
    fvm global 3.24.5
    
    print_success "Flutter and FVM installed"
}

# Function to install Android tools
install_android_tools() {
    print_info "Installing Android Studio and SDK..."
    
    # Install Android Studio
    if ! command_exists studio; then
        print_warning "Installing Android Studio via Homebrew..."
        brew install --cask android-studio
    fi
    
    # Setup Android environment variables
    local android_home="$HOME/Library/Android/sdk"
    
    if ! grep -q "ANDROID_HOME" ~/.zshrc; then
        echo "export ANDROID_HOME=$android_home" >> ~/.zshrc
        echo 'export PATH=$PATH:$ANDROID_HOME/emulator' >> ~/.zshrc
        echo 'export PATH=$PATH:$ANDROID_HOME/tools' >> ~/.zshrc
        echo 'export PATH=$PATH:$ANDROID_HOME/tools/bin' >> ~/.zshrc
        echo 'export PATH=$PATH:$ANDROID_HOME/platform-tools' >> ~/.zshrc
    fi
    
    export ANDROID_HOME="$android_home"
    export PATH="$PATH:$ANDROID_HOME/emulator:$ANDROID_HOME/tools:$ANDROID_HOME/tools/bin:$ANDROID_HOME/platform-tools"
    
    print_success "Android tools configured"
}

# Function to install iOS tools
install_ios_tools() {
    print_info "Setting up iOS development tools..."
    
    # Check if Xcode is installed
    if ! command_exists xcodebuild; then
        print_error "Xcode not found. Please install Xcode from App Store first."
        print_info "After installing Xcode, run: sudo xcode-select --install"
        exit 1
    fi
    
    # Install Command Line Tools if not installed
    if ! xcode-select -p >/dev/null 2>&1; then
        print_info "Installing Xcode Command Line Tools..."
        sudo xcode-select --install
    fi
    
    # Accept Xcode license
    sudo xcodebuild -license accept 2>/dev/null || true
    
    # Install CocoaPods
    if ! command_exists pod; then
        print_info "Installing CocoaPods..."
        sudo gem install cocoapods
    fi
    
    print_success "iOS tools configured"
}

# Function to install Java
install_java() {
    print_info "Installing Java 17..."
    
    if ! command_exists java || [[ $(java -version 2>&1 | grep "17\." | wc -l) -eq 0 ]]; then
        brew install openjdk@17
        
        # Setup Java environment
        if ! grep -q "openjdk@17" ~/.zshrc; then
            echo 'export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"' >> ~/.zshrc
            echo 'export JAVA_HOME="/opt/homebrew/opt/openjdk@17"' >> ~/.zshrc
        fi
        
        export JAVA_HOME="/opt/homebrew/opt/openjdk@17"
        export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"
    fi
    
    print_success "Java 17 installed"
}

# Function to install Fastlane
install_fastlane() {
    print_info "Installing Fastlane..."
    
    if ! command_exists fastlane; then
        sudo gem install fastlane
    fi
    
    print_success "Fastlane installed"
}

# Function to install GitLab Runner
install_gitlab_runner() {
    print_info "Installing GitLab Runner..."
    
    # Download GitLab Runner binary
    if [[ $(uname -m) == "arm64" ]]; then
        local binary_url="https://gitlab-runner-downloads.s3.amazonaws.com/latest/binaries/gitlab-runner-darwin-arm64"
    else
        local binary_url="https://gitlab-runner-downloads.s3.amazonaws.com/latest/binaries/gitlab-runner-darwin-amd64"
    fi
    
    sudo curl --output /usr/local/bin/gitlab-runner "$binary_url"
    sudo chmod +x /usr/local/bin/gitlab-runner
    
    # Install and start service
    gitlab-runner install
    gitlab-runner start
    
    print_success "GitLab Runner installed"
}

# Function to setup environment file
setup_environment() {
    print_info "Setting up environment configuration..."
    
    cat > ~/.gitlab-runner-env << 'EOF'
# Flutter & Dart
export PATH="$PATH:$HOME/.pub-cache/bin"
export FLUTTER_ROOT="$HOME/fvm/versions/3.24.5"

# Android
export ANDROID_HOME="$HOME/Library/Android/sdk"
export PATH="$PATH:$ANDROID_HOME/emulator"
export PATH="$PATH:$ANDROID_HOME/tools"
export PATH="$PATH:$ANDROID_HOME/tools/bin"
export PATH="$PATH:$ANDROID_HOME/platform-tools"

# Java
export JAVA_HOME="/opt/homebrew/opt/openjdk@17"
export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"

# iOS
export DEVELOPER_DIR="/Applications/Xcode.app/Contents/Developer"

# Locale
export LC_ALL="en_US.UTF-8"
export LANG="en_US.UTF-8"

# Fastlane
export FASTLANE_SKIP_UPDATE_CHECK=1
export FASTLANE_HIDE_GITHUB_ISSUES=1

# GitLab CI specific
export CI=true
EOF
    
    # Add to shell profile
    if ! grep -q "gitlab-runner-env" ~/.zshrc; then
        echo 'source ~/.gitlab-runner-env' >> ~/.zshrc
    fi
    
    print_success "Environment configuration created"
}

# Function to register runner
register_runner() {
    print_info "GitLab Runner registration..."
    print_warning "You need to register the runner manually with GitLab."
    print_info "Run the following command and follow the prompts:"
    echo ""
    echo "gitlab-runner register"
    echo ""
    print_info "Registration details:"
    echo "- GitLab instance URL: https://gitlab.com/ (or your GitLab URL)"
    echo "- Registration token: [Get from GitLab Project → Settings → CI/CD → Runners]"
    echo "- Description: Flutter CI/CD Runner - Mac"
    echo "- Tags: flutter-runner,macos"
    echo "- Executor: shell"
    echo ""
}

# Function to verify installation
verify_installation() {
    print_info "Verifying installation..."
    
    local errors=0
    
    # Check Flutter
    if fvm flutter doctor >/dev/null 2>&1; then
        print_success "Flutter: OK"
    else
        print_error "Flutter: FAILED"
        ((errors++))
    fi
    
    # Check Android
    if [[ -d "$ANDROID_HOME" ]]; then
        print_success "Android SDK: OK"
    else
        print_error "Android SDK: FAILED"
        ((errors++))
    fi
    
    # Check iOS
    if command_exists xcodebuild; then
        print_success "iOS tools: OK"
    else
        print_error "iOS tools: FAILED"
        ((errors++))
    fi
    
    # Check Java
    if command_exists java; then
        print_success "Java: OK"
    else
        print_error "Java: FAILED"
        ((errors++))
    fi
    
    # Check Fastlane
    if command_exists fastlane; then
        print_success "Fastlane: OK"
    else
        print_error "Fastlane: FAILED"
        ((errors++))
    fi
    
    # Check GitLab Runner
    if command_exists gitlab-runner; then
        print_success "GitLab Runner: OK"
    else
        print_error "GitLab Runner: FAILED"
        ((errors++))
    fi
    
    if [[ $errors -eq 0 ]]; then
        print_success "All components installed successfully!"
    else
        print_error "$errors components failed to install"
        exit 1
    fi
}

# Main execution
main() {
    print_info "Starting GitLab Runner setup for Flutter CI/CD..."
    echo ""
    
    # Check if running on macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "This script is for macOS only"
        exit 1
    fi
    
    check_macos_version
    install_homebrew
    install_basic_tools
    install_flutter
    install_android_tools
    install_ios_tools
    install_java
    install_fastlane
    install_gitlab_runner
    setup_environment
    
    echo ""
    print_success "Setup completed successfully!"
    echo ""
    
    register_runner
    
    echo ""
    print_info "Next steps:"
    echo "1. Register the GitLab Runner with your project"
    echo "2. Restart your terminal or run: source ~/.zshrc"
    echo "3. Test the setup by running: fvm flutter doctor -v"
    echo "4. For detailed instructions, see: docs/cicd/GITLAB_RUNNER_SETUP_GUIDE.md"
    echo ""
    
    verify_installation
}

# Run main function
main "$@" 