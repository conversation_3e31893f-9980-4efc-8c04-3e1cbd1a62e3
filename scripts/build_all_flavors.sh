#!/bin/bash
# Build All Flavors Script (iOS + Android)
# Usage: ./scripts/build_all_flavors.sh [flavor] [platform] [mode]
# Examples:
#   ./scripts/build_all_flavors.sh development ios debug
#   ./scripts/build_all_flavors.sh all android release
#   ./scripts/build_all_flavors.sh staging all release

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_header() {
    echo -e "${BLUE}===============================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}===============================================${NC}"
}

print_info() {
    echo -e "${GREEN}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_separator() {
    echo -e "${YELLOW}-----------------------------------------------${NC}"
}

# Validate FVM
check_fvm() {
    if ! command -v fvm &> /dev/null; then
        print_error "FVM not found. Please install FVM first."
        exit 1
    fi
    
    print_info "Using Flutter version: $(fvm flutter --version | head -n 1)"
}

# Build functions
build_android_flavor() {
    local flavor=$1
    local mode=$2
    
    print_header "Building Android $flavor ($mode)"
    
    cd android
    case $flavor in
        "development"|"dev")
            if [ "$mode" = "release" ]; then
                fastlane gradle_dev_release
            else
                print_warning "Debug mode not supported in Fastlane, using Flutter CLI"
                cd ..
                fvm flutter build apk --debug --flavor development --target lib/main_development.dart
                cd android
            fi
            ;;
        "staging"|"stg")
            fastlane gradle_staging_release
            fastlane gradle_staging_aab
            ;;
        "production"|"prod")
            fastlane gradle_prod_release
            fastlane gradle_prod_aab
            ;;
        *)
            print_error "Invalid Android flavor: $flavor"
            cd ..
            return 1
            ;;
    esac
    cd ..
    print_success "Android $flavor build completed!"
}

build_ios_flavor() {
    local flavor=$1
    local mode=$2
    
    print_header "Building iOS $flavor ($mode)"
    
    # Check if we're on macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_warning "iOS build requires macOS. Skipping..."
        return 0
    fi
    
    cd ios
    case $flavor in
        "development"|"dev")
            fastlane development
            ;;
        "staging"|"stg")
            fastlane staging
            ;;
        "production"|"prod")
            fastlane production
            ;;
        *)
            print_error "Invalid iOS flavor: $flavor"
            cd ..
            return 1
            ;;
    esac
    cd ..
    print_success "iOS $flavor build completed!"
}

build_all_android() {
    local mode=$1
    print_header "Building All Android Flavors ($mode)"
    
    build_android_flavor "development" "$mode"
    print_separator
    build_android_flavor "staging" "$mode"
    print_separator
    build_android_flavor "production" "$mode"
    
    print_success "All Android flavors built successfully!"
}

build_all_ios() {
    local mode=$1
    print_header "Building All iOS Flavors ($mode)"
    
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_warning "iOS build requires macOS. Skipping..."
        return 0
    fi
    
    cd ios
    fastlane all
    cd ..
    
    print_success "All iOS flavors built successfully!"
}

# Main script
main() {
    local flavor=${1:-"all"}
    local platform=${2:-"all"}
    local mode=${3:-"release"}
    
    print_header "Sales App Build Script"
    print_info "Flavor: $flavor"
    print_info "Platform: $platform"
    print_info "Mode: $mode"
    
    # Validate environment
    check_fvm
    
    # Clean and setup
    print_info "Cleaning project..."
    fvm flutter clean
    fvm flutter pub get
    
    # Build based on parameters
    case $flavor in
        "all")
            case $platform in
                "android")
                    build_all_android "$mode"
                    ;;
                "ios")
                    build_all_ios "$mode"
                    ;;
                "all")
                    build_all_android "$mode"
                    print_separator
                    build_all_ios "$mode"
                    ;;
                *)
                    print_error "Invalid platform: $platform"
                    exit 1
                    ;;
            esac
            ;;
        *)
            case $platform in
                "android")
                    build_android_flavor "$flavor" "$mode"
                    ;;
                "ios")
                    build_ios_flavor "$flavor" "$mode"
                    ;;
                "all")
                    build_android_flavor "$flavor" "$mode"
                    print_separator
                    build_ios_flavor "$flavor" "$mode"
                    ;;
                *)
                    print_error "Invalid platform: $platform"
                    exit 1
                    ;;
            esac
            ;;
    esac
    
    print_header "Build Summary"
    print_success "🎉 Build completed successfully!"
    
    # Show artifacts
    print_info "📦 Artifacts location:"
    if [[ -d "build/app/outputs" ]]; then
        echo "   Android: build/app/outputs/"
    fi
    if [[ -d "build/ios/ipa" ]]; then
        echo "   iOS: build/ios/ipa/"
    fi
}

# Show usage if no arguments
if [ $# -eq 0 ]; then
    echo "Usage: $0 [flavor] [platform] [mode]"
    echo ""
    echo "Flavors: development, staging, production, all"
    echo "Platforms: android, ios, all"
    echo "Modes: debug, release"
    echo ""
    echo "Examples:"
    echo "  $0 development android debug"
    echo "  $0 staging ios release"
    echo "  $0 all all release"
    echo "  $0 production android release"
    exit 1
fi

# Run main function
main "$@" 