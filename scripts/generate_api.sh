#!/bin/bash

# <PERSON><PERSON>t để generate OpenAPI code
# Usage: ./scripts/generate_api.sh [sales|media|all]

set -e

echo "🚀 Starting API generation..."

# <PERSON><PERSON><PERSON> tra tham số
if [ $# -eq 0 ]; then
    echo "Generating all APIs..."
    TARGET="all"
else
    TARGET=$1
fi

# Clean cache nếu cần
if [ "$2" = "--clean" ]; then
    echo "🧹 Cleaning cache..."
    rm -rf .dart_tool/openapi_generator_cache .dart_tool/openapi-generator-cache.json .dart_tool/build
fi

# Generate theo target
case $TARGET in
    "sales")
        echo "📦 Generating Sales API..."
        fvm flutter packages pub run build_runner build --delete-conflicting-outputs --build-filter="lib/data/network/openapi/sales_api_config.dart"
        ;;
    "media")
        echo "📦 Generating Media API..."
        fvm flutter packages pub run build_runner build --delete-conflicting-outputs --build-filter="lib/data/network/openapi/media_api_config.dart"
        ;;
    "all")
        echo "📦 Generating all APIs..."
        fvm flutter packages pub run build_runner build --delete-conflicting-outputs
        ;;
    *)
        echo "❌ Invalid target: $TARGET"
        echo "Usage: $0 [sales|media|all] [--clean]"
        exit 1
        ;;
esac

echo "✅ API generation completed successfully!"
echo "📁 Generated files:"
ls -la openapi/generated/ 