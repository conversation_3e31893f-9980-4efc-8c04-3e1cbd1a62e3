#!/bin/bash

# iOS Flavor Build Script
# Script này giúp build ứng dụng iOS cho các flavor khác nhau với đúng scheme

set -e  # Exit on any error

# Colors for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_separator() {
    echo "=================================================="
}

# Ensure we're using FVM
check_fvm() {
    if ! command -v fvm &> /dev/null; then
        print_error "FVM không được cài đặt. Vui lòng cài đặt FVM trước."
        exit 1
    fi
    
    if [ ! -f ".fvm/fvm_config.json" ]; then
        print_error "FVM chưa được cấu hình cho project này. Chạy 'fvm use' trước."
        exit 1
    fi
}

# Build function with scheme
build_ios_flavor() {
    local flavor=$1
    local target=$2
    local scheme=$3
    local mode=${4:-debug}
    local device=${5:-simulator}
    
    print_info "Building iOS $flavor flavor..."
    print_info "Target: $target"
    print_info "Scheme: $scheme"
    print_info "Mode: $mode"
    print_info "Device: $device"
    
    # Set SDK based on device
    local sdk=""
    if [ "$device" = "device" ]; then
        sdk="iphoneos"
    else
        sdk="iphonesimulator"
    fi
    
    # Build with Flutter using scheme
    print_info "Building with Flutter..."
    if [ "$mode" = "release" ]; then
        fvm flutter build ios --release --target="$target" --no-codesign
    else
        fvm flutter build ios --debug --target="$target" --no-codesign
    fi
    
    # Build with xcodebuild using specific scheme and configuration
    print_info "Building with xcodebuild using scheme $scheme..."
    local config=""
    if [ "$mode" = "release" ]; then
        config="Release-${flavor}"
    else
        config="Debug-${flavor}"
    fi
    
    cd ios
    xcodebuild -workspace Runner.xcworkspace \
        -scheme "$scheme" \
        -configuration "$config" \
        -sdk "$sdk" \
        -destination "generic/platform=iOS Simulator" \
        CODE_SIGN_IDENTITY="" \
        CODE_SIGNING_REQUIRED=NO \
        CODE_SIGNING_ALLOWED=NO \
        build
    cd ..
    
    print_success "Build completed for $flavor flavor!"
    
    # Show bundle ID from built app
    if [ -f "build/ios/${sdk}/Runner.app/Info.plist" ]; then
        local bundle_id=$(plutil -p "build/ios/${sdk}/Runner.app/Info.plist" | grep CFBundleIdentifier | awk -F '"' '{print $4}')
        print_info "Bundle ID: $bundle_id"
    fi
}

# Main script
main() {
    print_separator
    print_info "🍎 iOS Flavor Build Script"
    print_separator
    
    check_fvm
    
    # Parse arguments
    FLAVOR=${1:-"all"}
    MODE=${2:-"debug"}
    DEVICE=${3:-"simulator"}
    
    case $FLAVOR in
        "development"|"dev")
            build_ios_flavor "development" "lib/main_development.dart" "development" "$MODE" "$DEVICE"
            ;;
        "staging"|"stg")
            build_ios_flavor "staging" "lib/main_staging.dart" "staging" "$MODE" "$DEVICE"
            ;;
        "production"|"prod")
            build_ios_flavor "production" "lib/main_production.dart" "production" "$MODE" "$DEVICE"
            ;;
        "all")
            print_info "Building all flavors..."
            build_ios_flavor "development" "lib/main_development.dart" "development" "$MODE" "$DEVICE"
            print_separator
            build_ios_flavor "staging" "lib/main_staging.dart" "staging" "$MODE" "$DEVICE"
            print_separator
            build_ios_flavor "production" "lib/main_production.dart" "production" "$MODE" "$DEVICE"
            ;;
        *)
            print_error "Invalid flavor: $FLAVOR"
            print_info "Usage: $0 [development|staging|production|all] [debug|release] [simulator|device]"
            print_info "Examples:"
            print_info "  $0 development debug simulator"
            print_info "  $0 staging release device"
            print_info "  $0 all debug simulator"
            exit 1
            ;;
    esac
    
    print_separator
    print_success "🎉 Build script completed!"
}

# Run main function
main "$@" 