#!/bin/bash

# iOS Flavor Run Script
# Script để run iOS app với flavor cụ thể

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Parse arguments
FLAVOR=${1:-"development"}
DEVICE=${2:-"iPhone 16 Pro"}

case $FLAVOR in
    "development"|"dev")
        TARGET="lib/main_development.dart"
        SCHEME="development"
        ;;
    "staging"|"stg")
        TARGET="lib/main_staging.dart"
        SCHEME="staging"
        ;;
    "production"|"prod")
        TARGET="lib/main_production.dart"
        SCHEME="production"
        ;;
    *)
        print_error "Invalid flavor: $FLAVOR"
        echo "Usage: $0 [development|staging|production] [device_name]"
        echo "Examples:"
        echo "  $0 development"
        echo "  $0 staging \"iPhone 15 Pro\""
        exit 1
        ;;
esac

print_info "🍎 Running iOS app with $FLAVOR flavor"
print_info "Target: $TARGET"
print_info "Scheme: $SCHEME"
print_info "Device: $DEVICE"

# Method 1: Try Flutter run with custom scheme
print_info "Method 1: Flutter run with scheme selection..."

# Temporarily modify ios/Runner.xcodeproj/project.pbxproj to set default scheme
# Or use xcodebuild to run with specific scheme

# Method 2: Use xcodebuild to run
print_info "Method 2: Using xcodebuild to run with specific scheme..."

cd ios

# Build first
print_info "Building with scheme $SCHEME..."
xcodebuild -workspace Runner.xcworkspace \
    -scheme "$SCHEME" \
    -configuration "Debug-$FLAVOR" \
    -sdk iphonesimulator \
    -destination "platform=iOS Simulator,name=$DEVICE" \
    CODE_SIGN_IDENTITY="" \
    CODE_SIGNING_REQUIRED=NO \
    CODE_SIGNING_ALLOWED=NO \
    build

if [ $? -eq 0 ]; then
    print_success "Build successful!"
    
    # Check bundle ID from built app
    APP_PATH=$(find build -name "Runner.app" -type d | head -1)
    if [ -n "$APP_PATH" ]; then
        BUNDLE_ID=$(plutil -p "$APP_PATH/Info.plist" | grep CFBundleIdentifier | awk -F '"' '{print $4}')
        APP_NAME=$(plutil -p "$APP_PATH/Info.plist" | grep CFBundleDisplayName | awk -F '"' '{print $4}')
        print_success "Bundle ID: $BUNDLE_ID"
        print_success "App Name: $APP_NAME"
    fi
    
    # Install and run on simulator
    print_info "Installing app on simulator..."
    xcrun simctl install "$DEVICE" "$APP_PATH" 2>/dev/null || print_info "App already installed or install failed"
    
    print_info "Launching app on simulator..."
    xcrun simctl launch "$DEVICE" "$BUNDLE_ID" 2>/dev/null || print_info "Launch may have failed"
    
    print_success "🎉 App should be running on $DEVICE"
else
    print_error "Build failed!"
    exit 1
fi

cd .. 