/// <PERSON><PERSON><PERSON> quả giới thiệu CTV
class CTVReferralResult {
  /// Họ tên
  final String? fullName;
  
  /// Tên chi nhánh
  final String? branchName;
  
  /// Tên chức danh
  final String? positionName;

  /// Mã người giới thiệu
  final String? referrerCode;

  /// Tên người giới thiệu
  final String? referrerName;

  const CTVReferralResult({
    this.fullName,
    this.branchName,
    this.positionName,
    this.referrerCode,
    this.referrerName,
  });

  /// Tạo từ JSON
  factory CTVReferralResult.fromJson(Map<String, dynamic> json) {
    return CTVReferralResult(
      fullName: json['fullName'] as String?,
      branchName: json['branchName'] as String?,
      positionName: json['positionName'] as String?,
      referrerCode: json['referrerCode'] as String?,
      referrerName: json['referrerName'] as String?,
    );
  }

  /// <PERSON><PERSON><PERSON><PERSON> thành JSON
  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'branchName': branchName,
      'positionName': positionName,
      'referrerCode': referrerCode,
      'referrerName': referrerName,
    };
  }

  /// Copy with
  CTVReferralResult copyWith({
    String? fullName,
    String? branchName,
    String? positionName,
    String? referrerCode,
    String? referrerName,
  }) {
    return CTVReferralResult(
      fullName: fullName ?? this.fullName,
      branchName: branchName ?? this.branchName,
      positionName: positionName ?? this.positionName,
      referrerCode: referrerCode ?? this.referrerCode,
      referrerName: referrerName ?? this.referrerName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CTVReferralResult &&
        other.fullName == fullName &&
        other.branchName == branchName &&
        other.positionName == positionName &&
        other.referrerCode == referrerCode &&
        other.referrerName == referrerName;
  }

  @override
  int get hashCode {
    return fullName.hashCode ^
        branchName.hashCode ^
        positionName.hashCode ^
        referrerCode.hashCode ^
        referrerName.hashCode;
  }

  @override
  String toString() {
    return 'CTVReferralResult(fullName: $fullName, branchName: $branchName, positionName: $positionName, referrerCode: $referrerCode, referrerName: $referrerName)';
  }
} 