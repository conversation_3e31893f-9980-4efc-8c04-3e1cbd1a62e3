import 'package:sales_app/core/enums/enums.dart';

/// <PERSON><PERSON><PERSON> qu<PERSON> upload GTTT
class IdentityUploadResult {
  final DocumentType documentType;
  final String frontImage; // previewUrl
  final String backImage; // previewUrl
  final DateTime uploadedAt;

  const IdentityUploadResult({
    required this.documentType,
    required this.frontImage,
    required this.backImage,
    required this.uploadedAt,
  });

  @override
  String toString() {
    return 'IdentityUploadResult(documentType: $documentType, frontImage: $frontImage, backImage: $backImage, uploadedAt: $uploadedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is IdentityUploadResult &&
        other.documentType == documentType &&
        other.frontImage == frontImage &&
        other.backImage == backImage &&
        other.uploadedAt == uploadedAt;
  }

  @override
  int get hashCode {
    return documentType.hashCode ^
        frontImage.hashCode ^
        backImage.hashCode ^
        uploadedAt.hashCode;
  }
} 