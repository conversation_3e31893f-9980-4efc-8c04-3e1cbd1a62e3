/// Entity chứa thông tin CCCD được trích xuất từ ảnh
class CCCDInfoEntity {
  final String fullName;
  final String idNumber;
  final String dateOfBirth;
  final String gender;
  final String address;
  final String issueDate;
  final String issuePlace;
  final String expiryDate;
  final String? nationality;
  final String? religion;
  final String? ethnicity;

  const CCCDInfoEntity({
    required this.fullName,
    required this.idNumber,
    required this.dateOfBirth,
    required this.gender,
    required this.address,
    required this.issueDate,
    required this.issuePlace,
    required this.expiryDate,
    this.nationality,
    this.religion,
    this.ethnicity,
  });

  /// Tạo bản sao với các thuộc tính được cập nhật
  CCCDInfoEntity copyWith({
    String? fullName,
    String? idNumber,
    String? dateOfBirth,
    String? gender,
    String? address,
    String? issueDate,
    String? issuePlace,
    String? expiryDate,
    String? nationality,
    String? religion,
    String? ethnicity,
  }) {
    return CCCDInfoEntity(
      fullName: fullName ?? this.fullName,
      idNumber: idNumber ?? this.idNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      issueDate: issueDate ?? this.issueDate,
      issuePlace: issuePlace ?? this.issuePlace,
      expiryDate: expiryDate ?? this.expiryDate,
      nationality: nationality ?? this.nationality,
      religion: religion ?? this.religion,
      ethnicity: ethnicity ?? this.ethnicity,
    );
  }

  /// Tạo từ JSON
  factory CCCDInfoEntity.fromJson(Map<String, dynamic> json) {
    return CCCDInfoEntity(
      fullName: json['fullName'] as String? ?? '',
      idNumber: json['idNumber'] as String? ?? '',
      dateOfBirth: json['dateOfBirth'] as String? ?? '',
      gender: json['gender'] as String? ?? '',
      address: json['address'] as String? ?? '',
      issueDate: json['issueDate'] as String? ?? '',
      issuePlace: json['issuePlace'] as String? ?? '',
      expiryDate: json['expiryDate'] as String? ?? '',
      nationality: json['nationality'] as String?,
      religion: json['religion'] as String?,
      ethnicity: json['ethnicity'] as String?,
    );
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'idNumber': idNumber,
      'dateOfBirth': dateOfBirth,
      'gender': gender,
      'address': address,
      'issueDate': issueDate,
      'issuePlace': issuePlace,
      'expiryDate': expiryDate,
      if (nationality != null) 'nationality': nationality,
      if (religion != null) 'religion': religion,
      if (ethnicity != null) 'ethnicity': ethnicity,
    };
  }

  /// Tạo CCCDInfoEntity rỗng
  factory CCCDInfoEntity.empty() => const CCCDInfoEntity(
    fullName: '',
    idNumber: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    issueDate: '',
    issuePlace: '',
    expiryDate: '',
    nationality: '',
    religion: '',
    ethnicity: '',
  );

  @override
  String toString() {
    return 'CCCDInfoEntity(fullName: $fullName, idNumber: $idNumber, dateOfBirth: $dateOfBirth, gender: $gender, address: $address, issueDate: $issueDate, issuePlace: $issuePlace, expiryDate: $expiryDate, nationality: $nationality, religion: $religion, ethnicity: $ethnicity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CCCDInfoEntity &&
        other.fullName == fullName &&
        other.idNumber == idNumber &&
        other.dateOfBirth == dateOfBirth &&
        other.gender == gender &&
        other.address == address &&
        other.issueDate == issueDate &&
        other.issuePlace == issuePlace &&
        other.expiryDate == expiryDate &&
        other.nationality == nationality &&
        other.religion == religion &&
        other.ethnicity == ethnicity;
  }

  @override
  int get hashCode {
    return fullName.hashCode ^
        idNumber.hashCode ^
        dateOfBirth.hashCode ^
        gender.hashCode ^
        address.hashCode ^
        issueDate.hashCode ^
        issuePlace.hashCode ^
        expiryDate.hashCode ^
        nationality.hashCode ^
        religion.hashCode ^
        ethnicity.hashCode;
  }
} 