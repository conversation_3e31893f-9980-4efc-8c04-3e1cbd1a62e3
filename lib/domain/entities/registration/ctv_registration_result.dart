/// <PERSON><PERSON><PERSON> quả đăng ký CTV
class CTVRegistrationResult {
  /// H<PERSON> tên
  final String? fullName;
  
  /// Tên chi nhánh
  final String? branchName;
  
  /// Tên chức danh
  final String? positionName;

  const CTVRegistrationResult({
    this.fullName,
    this.branchName,
    this.positionName,
  });

  /// Tạo từ JSON
  factory CTVRegistrationResult.fromJson(Map<String, dynamic> json) {
    return CTVRegistrationResult(
      fullName: json['fullName'] as String?,
      branchName: json['branchName'] as String?,
      positionName: json['positionName'] as String?,
    );
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'branchName': branchName,
      'positionName': positionName,
    };
  }

  /// Copy with
  CTVRegistrationResult copyWith({
    String? fullName,
    String? branchName,
    String? positionName,
  }) {
    return CTVRegistrationResult(
      fullName: fullName ?? this.fullName,
      branchName: branchName ?? this.branchName,
      positionName: positionName ?? this.positionName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CTVRegistrationResult &&
        other.fullName == fullName &&
        other.branchName == branchName &&
        other.positionName == positionName;
  }

  @override
  int get hashCode {
    return fullName.hashCode ^
        branchName.hashCode ^
        positionName.hashCode;
  }

  @override
  String toString() {
    return 'CTVRegistrationResult(fullName: $fullName, branchName: $branchName, positionName: $positionName)';
  }
} 