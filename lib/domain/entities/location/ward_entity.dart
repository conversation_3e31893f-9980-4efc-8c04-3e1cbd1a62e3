class WardEntity {
  final String id;
  final String name;

  const WardEntity({
    required this.id,
    required this.name,
  });

  factory WardEntity.fromJson(Map<String, dynamic> json) {
    return WardEntity(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
    );
  }

  @override
  String toString() => 'WardEntity(id: $id, name: $name)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WardEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
} 