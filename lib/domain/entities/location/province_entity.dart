class ProvinceEntity {
  final String id;
  final String name;

  const ProvinceEntity({
    required this.id,
    required this.name,
  });

  @override
  String toString() => 'ProvinceEntity(id: $id, name: $name)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProvinceEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
} 