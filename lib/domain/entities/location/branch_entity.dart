class BranchEntity {
  final String id;
  final String code; // Mã chi nhánh
  final String name;
  final String? address;
  final String? provinceId; // ID của tỉnh thành phố

  const BranchEntity({
    required this.id,
    required this.code,
    required this.name,
    this.address,
    this.provinceId,
  });

  /// Tạo từ JSON
  factory BranchEntity.fromJson(Map<String, dynamic> json) {
    return BranchEntity(
      id: json['id'] as String? ?? '',
      code: json['code'] as String? ?? '',
      name: json['name'] as String? ?? '',
      address: json['address'] as String?,
      provinceId: json['provinceId'] as String?,
    );
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      if (address != null) 'address': address,
      if (provinceId != null) 'provinceId': provinceId,
    };
  }

  @override
  String toString() => 'BranchEntity(id: $id, code: $code, name: $name, address: $address, provinceId: $provinceId)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BranchEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          code == other.code &&
          name == other.name &&
          address == other.address &&
          provinceId == other.provinceId;

  @override
  int get hashCode => id.hashCode ^ code.hashCode ^ name.hashCode ^ (address?.hashCode ?? 0) ^ (provinceId?.hashCode ?? 0);
} 