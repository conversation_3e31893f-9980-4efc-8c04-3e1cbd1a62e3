/// Environment types supported by the application
enum Environment {
  dev('Dev', 'Development'),
  staging('Staging', 'Staging'),
  prod('Prod', 'Production');

  const Environment(this.key, this.displayName);

  final String key;
  final String displayName;

  /// Get environment from key
  static Environment fromKey(String key) {
    return Environment.values.firstWhere(
      (env) => env.key == key,
      orElse: () => Environment.dev, // Default to dev
    );
  }
}

/// Environment configuration entity
/// Domain layer entity representing environment configuration
class EnvironmentEntity {
  final Environment environment;
  final String baseUrl;
  final String apiVersion;
  final bool enableLogging;
  final bool enableMockApi;
  final int timeoutDuration;

  const EnvironmentEntity({
    required this.environment,
    required this.baseUrl,
    required this.apiVersion,
    required this.enableLogging,
    required this.enableMockApi,
    required this.timeoutDuration,
  });

  /// Get full API base URL
  String get fullApiUrl => '$baseUrl/api/$apiVersion';

  @override
  String toString() {
    return 'EnvironmentEntity(environment: $environment, baseUrl: $baseUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnvironmentEntity && other.environment == environment;
  }

  @override
  int get hashCode => environment.hashCode;
}
