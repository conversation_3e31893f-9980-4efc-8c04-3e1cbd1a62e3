/// Entity cho System Config từ API
/// Map từ SystemConfigDto của OpenAPI
class SystemConfigEntity {
  final String id;
  final String code;
  final String label;

  const SystemConfigEntity({
    required this.id,
    required this.code,
    required this.label,
  });

  factory SystemConfigEntity.fromJson(Map<String, dynamic> json) {
    return SystemConfigEntity(
      id: json['id'] as String? ?? '',
      code: json['code'] as String? ?? '',
      label: json['label'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'label': label,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SystemConfigEntity &&
        other.id == id &&
        other.code == code &&
        other.label == label;
  }

  @override
  int get hashCode => Object.hash(id, code, label);

  @override
  String toString() {
    return 'SystemConfigEntity(id: $id, code: $code, label: $label)';
  }
} 