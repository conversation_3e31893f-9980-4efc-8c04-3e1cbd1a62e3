/// Article entity - domain layer representation of article data
class ArticleEntity {
  final String code;
  final String title;
  final String content;

  const ArticleEntity({
    required this.code,
    required this.title,
    required this.content,
  });

  /// Tạo bản sao với các thuộc t<PERSON>h đ<PERSON>ợc cập nhật
  ArticleEntity copyWith({
    String? code,
    String? title,
    String? content,
  }) {
    return ArticleEntity(
      code: code ?? this.code,
      title: title ?? this.title,
      content: content ?? this.content,
    );
  }

  /// Tạo từ JSON
  factory ArticleEntity.fromJson(Map<String, dynamic> json) {
    return ArticleEntity(
      code: json['code'] as String? ?? '',
      title: json['title'] as String? ?? '',
      content: json['content'] as String? ?? '',
    );
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'title': title,
      'content': content,
    };
  }

  @override
  String toString() {
    return 'ArticleEntity(code: $code, title: $title, content: $content)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ArticleEntity &&
        other.code == code &&
        other.title == title &&
        other.content == content;
  }

  @override
  int get hashCode {
    return code.hashCode ^ title.hashCode ^ content.hashCode;
  }
} 