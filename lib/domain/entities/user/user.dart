/// User Entities Barrel Export
/// Entities related to user management and authentication
/// 
/// This file provides a single import point for all user-related entities.
/// These entities represent user data for both authentication and user management.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/domain/entities/user/user.dart';
/// 
/// // Now you can use any user entity:
/// final user = User(id: '123', email: '<EMAIL>', name: '<PERSON>');
/// ```
library user_entities;

import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/entities/location/branch_entity.dart';

/// Entity quản lý thông tin người dùng trong hệ thống App Sale
/// Kết hợp từ cả User (authentication) và User (user management)
class User {
  // Core fields (từ user.dart cũ)
  final String id; // cifNo từ API
  final String email;
  final String name; // fullName từ API
  final String? phone; // phoneNumber từ API
  final String? avatar;

  // Extended fields (từ user_entity.dart cũ)
  final String? avatarUrl;
  final UserRole? role;
  final String? phoneNumber;
  final int unreadNotifications;
  final DateTime? lastLoginAt;
  final bool isActive;
  final BranchEntity? branch; // Branch information từ profile API

  const User({
    required this.id,
    required this.email,
    required this.name,
    this.phone,
    this.avatar,
    this.avatarUrl,
    this.role,
    this.phoneNumber,
    this.unreadNotifications = 0,
    this.lastLoginAt,
    this.isActive = true,
    this.branch,
  });

  /// Tạo bản sao với các thuộc tính được cập nhật
  User copyWith({
    String? id,
    String? email,
    String? name,
    String? phone,
    String? avatar,
    String? avatarUrl,
    UserRole? role,
    String? phoneNumber,
    int? unreadNotifications,
    DateTime? lastLoginAt,
    bool? isActive,
    BranchEntity? branch,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      role: role ?? this.role,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      unreadNotifications: unreadNotifications ?? this.unreadNotifications,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
      branch: branch ?? this.branch,
    );
  }

  /// Tạo User rỗng để sử dụng làm giá trị mặc định
  factory User.empty() {
    return const User(
      id: '',
      email: '',
      name: '',
      role: UserRole.ctv,
    );
  }

  /// Tạo từ JSON (cho authentication)
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String? ?? json['cifNo'] as String? ?? '',
      email: json['email'] as String? ?? '',
      name: json['name'] as String? ?? json['fullName'] as String? ?? '',
      phone: json['phone'] as String? ?? json['phoneNumber'] as String?,
      avatar: json['avatar'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      role: json['role'] != null ? UserRole.values.firstWhere(
        (role) => role.toString() == 'UserRole.${json['role']}',
        orElse: () => UserRole.ctv,
      ) : null,
      phoneNumber: json['phoneNumber'] as String?,
      unreadNotifications: json['unreadNotifications'] as int? ?? 0,
      lastLoginAt: json['lastLoginAt'] != null 
        ? DateTime.parse(json['lastLoginAt'] as String) 
        : null,
      isActive: json['isActive'] as bool? ?? true,
      branch: json['branch'] != null ? BranchEntity.fromJson(json['branch'] as Map<String, dynamic>) : null,
    );
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      if (phone != null) 'phone': phone,
      if (avatar != null) 'avatar': avatar,
      if (avatarUrl != null) 'avatarUrl': avatarUrl,
      if (role != null) 'role': role.toString().split('.').last,
      if (phoneNumber != null) 'phoneNumber': phoneNumber,
      'unreadNotifications': unreadNotifications,
      if (lastLoginAt != null) 'lastLoginAt': lastLoginAt!.toIso8601String(),
      'isActive': isActive,
      if (branch != null) 'branch': branch!.toJson(),
    };
  }

  /// Lấy tên hiển thị ngắn gọn (tên cuối)
  String get shortName {
    final names = name.trim().split(' ');
    return names.isNotEmpty ? names.last : name;
  }

  /// Lấy lời chào theo thời gian trong ngày
  String getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Chào buổi sáng 👋';
    } else if (hour < 17) {
      return 'Chào buổi chiều 👋';
    } else {
      return 'Chào buổi tối 👋';
    }
  }

  /// Lấy avatar URL (ưu tiên avatarUrl, sau đó avatar)
  String? get avatarUrlOrAvatar => avatarUrl ?? avatar;

  /// Lấy phone number (ưu tiên phoneNumber, sau đó phone)
  String? get phoneNumberOrPhone => phoneNumber ?? phone;

  /// Alias cho name (để tương thích với code cũ)
  String get fullName => name;
  
  /// Alias cho name (để tương thích với code cũ)
  String get displayName => name;

  // UI Configuration fields (để tương thích với code cũ)
  bool get shouldShowIncompleteRecords => true;
  bool get shouldShowRecentViewed => true;
  bool get shouldShowProductBanners => true;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User &&
        other.id == id &&
        other.email == email &&
        other.name == name &&
        other.phone == phone &&
        other.avatar == avatar &&
        other.avatarUrl == avatarUrl &&
        other.role == role &&
        other.phoneNumber == phoneNumber &&
        other.unreadNotifications == unreadNotifications &&
        other.lastLoginAt == lastLoginAt &&
        other.isActive == isActive &&
        other.branch == branch;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      email,
      name,
      phone,
      avatar,
      avatarUrl,
      role,
      phoneNumber,
      unreadNotifications,
      lastLoginAt,
      isActive,
      branch,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, phone: $phone, avatar: $avatar, avatarUrl: $avatarUrl, role: $role, phoneNumber: $phoneNumber, unreadNotifications: $unreadNotifications, lastLoginAt: $lastLoginAt, isActive: $isActive, branch: $branch)';
  }
} 