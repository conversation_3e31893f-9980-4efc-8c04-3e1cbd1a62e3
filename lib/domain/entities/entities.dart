/// Domain Entities Barrel Export
/// Business entities representing core domain objects
/// 
/// This file provides a single import point for all domain entities.
/// Entities represent the core business objects and contain business logic.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/domain/entities/entities.dart';
/// 
/// // Now you can use any entity:
/// final user = User(
///   id: '123',
///   name: '<PERSON>',
///   email: '<EMAIL>',
/// );
/// 
/// final environment = EnvironmentEntity(
///   name: 'Development',
///   baseUrl: 'https://dev-api.example.com',
/// );
/// ```
/// 
/// ## Entity Categories:
/// - **User Entities**: User domain objects and related entities
/// - **Environment Entities**: Environment configuration entities
/// - **Identity Entities**: Identity document and upload entities
library domain_entities;

// User entities - user management and authentication
export 'user/user.dart';

// Common entities - used across multiple features
export 'common/article_entity.dart';
export 'common/environment_entity.dart';
export 'common/system_config_entity.dart';

// Location entities - geographical and branch data
export 'location/branch_entity.dart';
export 'location/province_entity.dart';
export 'location/ward_entity.dart';

// Referral entities - referral and recommendation system
export 'referral/referrer_entity.dart';

// Registration entities - identity verification and CTV registration
export 'registration/cccd_info_entity.dart';
export 'registration/identity_upload_result.dart';
export 'registration/ctv_registration_result.dart';
export 'registration/ctv_referral_result.dart';

export 'payment/payment_account_entity.dart';
