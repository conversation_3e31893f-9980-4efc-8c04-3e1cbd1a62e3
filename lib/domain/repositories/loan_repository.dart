import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/entities/common/system_config_entity.dart';
import 'package:sales_app/domain/entities/location/province_entity.dart';
import 'package:sales_app/domain/entities/location/ward_entity.dart';
import 'package:sales_app/domain/entities/payment/payment_account_entity.dart';

/// Repository interface cho loan operations
/// Định nghĩa contract cho việc tạo khoản vay và upload documents
abstract class LoanRepository {
  /// Upload identity documents cho loan creation
  /// 
  /// [frontFile] - File ảnh mặt trước giấy tờ
  /// [backFile] - File ảnh mặt sau giấy tờ (có thể null cho passport)
  /// 
  /// Returns: Either<Failure, Map<String, String>> - URLs của ảnh đã upload
  /// Map keys: 'frontUrl', 'backUrl' (backUrl có thể null cho passport)
  Future<Either<Failure, Map<String, String>>> uploadIdentityDocuments({
    required File frontFile,
    File? backFile,
  });

  /// Upload co-borrower documents cho loan creation
  /// 
  /// [frontFile] - File ảnh mặt trước giấy tờ người đồng vay
  /// [backFile] - File ảnh mặt sau giấy tờ người đồng vay
  /// 
  /// Returns: Either<Failure, Map<String, String>> - URLs của ảnh đã upload
  /// Map keys: 'frontUrl', 'backUrl'
  Future<Either<Failure, Map<String, String>>> uploadCoBorrowerDocuments({
    required File frontFile,
    File? backFile,
  });
//Đọc kĩ toàn bộ các file, mô tả luồng kiểm tra lỗi và show dialog, chỉ ra rõ các file
  /// Upload additional documents cho loan
  /// 
  /// [files] - List các file cần upload
  /// [documentType] - Loại tài liệu (marriage, residence, etc.)
  /// 
  /// Returns: Either<Failure, List<String>> - URLs của các file đã upload
  Future<Either<Failure, List<String>>> uploadAdditionalDocuments({
    required List<File> files,
    required String documentType,
  });

  /// Tạo khoản vay mới
  /// 
  /// [loanData] - Dữ liệu khoản vay đầy đủ
  /// 
  /// Returns: Either<Failure, Map<String, dynamic>> - Thông tin khoản vay đã tạo
  // Future<Either<Failure, Map<String, dynamic>>> createLoan({
  //   required Map<String, dynamic> loanData,
  // });

  // /// Lấy danh sách khoản vay của user
  // /// 
  // /// Returns: Either<Failure, List<Map<String, dynamic>>> - Danh sách khoản vay
  // Future<Either<Failure, List<Map<String, dynamic>>>> getLoans();

  // /// Lấy chi tiết khoản vay
  // /// 
  // /// [loanId] - ID khoản vay
  // /// 
  // /// Returns: Either<Failure, Map<String, dynamic>> - Chi tiết khoản vay
  // Future<Either<Failure, Map<String, dynamic>>> getLoanDetail(String loanId);

  // ==================== LOCATION DATA ====================
  
  /// Lấy danh sách tỉnh/thành phố (delegate to LocationRepository)
  /// 
  /// Returns: Either<Failure, List<ProvinceEntity>> - Danh sách tỉnh/thành phố
  Future<Either<Failure, List<ProvinceEntity>>> getProvinces();

  /// Lấy danh sách phường/xã theo tỉnh/thành phố (delegate to LocationRepository)
  /// 
  /// [provinceId] - ID tỉnh/thành phố
  /// 
  /// Returns: Either<Failure, List<WardEntity>> - Danh sách phường/xã
  Future<Either<Failure, List<WardEntity>>> getWards(String provinceId);

  // ==================== SYSTEM CONFIG DATA ====================
  
  /// Lấy danh sách tình trạng hôn nhân (delegate to SystemConfigRepository)
  /// 
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách tình trạng hôn nhân
  Future<Either<Failure, List<SystemConfigEntity>>> getMaritalStatuses();

  /// Lấy danh sách giới tính (delegate to SystemConfigRepository)
  /// 
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách giới tínhcus
  Future<Either<Failure, List<SystemConfigEntity>>> getGenders();

  /// Lấy danh sách mục đích sử dụng vốn (delegate to SystemConfigRepository)
  /// 
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách mục đích sử dụng vốn
  Future<Either<Failure, List<SystemConfigEntity>>> getLoanPurposes();

  /// Lấy danh sách thời hạn vay (delegate to SystemConfigRepository)
  ///
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách thời hạn vay
  Future<Either<Failure, List<SystemConfigEntity>>> getLoanTerms();

  /// Lấy danh sách nguồn thu (INCOME_SOURCE)
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách nguồn thu
  Future<Either<Failure, List<SystemConfigEntity>>> getIncomeSources();

  /// Lấy danh sách tài khoản thanh toán theo số giấy tờ tuỳ thân (GTTT)
  ///
  /// [idNumber] - Số giấy tờ tuỳ thân (CMND/CCCD)
  /// Returns: Either<Failure, List<PaymentAccountEntity>>
  Future<Either<Failure, List<PaymentAccountEntity>>> getPaymentAccounts(String idNumber);

  Future<Either<Failure, List<SystemConfigEntity>>> getAssetTypes();
} 