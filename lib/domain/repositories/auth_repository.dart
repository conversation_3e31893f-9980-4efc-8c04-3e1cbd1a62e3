import 'package:dartz/dartz.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/entities/entities.dart';

abstract class AuthRepository {
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
  });

  Future<Either<Failure, void>> logout();

  Future<bool> isLoggedIn();

  Future<Either<Failure, User>> getCurrentUser();

  /// Get user profile from API
  /// Gọi API system/api/v1/users/profile để lấy thông tin chi tiết user
  Future<Either<Failure, User>> getProfile();
}