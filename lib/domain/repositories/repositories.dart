/// Domain Repositories Barrel Export
/// Repository interfaces defining data access contracts
/// 
/// This file provides a single import point for all repository interfaces.
/// Repositories define the contracts for data access without implementation details.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/domain/repositories/repositories.dart';
/// 
/// // Now you can use any repository interface:
/// class AuthUseCase {
///   final AuthRepository _authRepository;
///   
///   AuthUseCase(this._authRepository);
///   
///   Future<Either<Failure, User>> login(String email, String password) {
///     return _authRepository.login(email, password);
///   }
/// }
/// ```
/// 
/// ## Repository Categories:
/// - **Auth Repositories**: Authentication and user management
/// - **Article Repositories**: Article and content management  
/// - **Media Repositories**: File upload and media management
/// - **CTV Registration Repositories**: CTV registration workflow
/// - **Location Repositories**: Province and branch data management
/// - **Referrer Repositories**: Referral and recommendation system
/// - **System Config Repositories**: System configuration data
library repositories;

// Auth repositories - authentication and user management
export 'auth_repository.dart';

// Article repositories - content and article management
export 'article_repository.dart';

// Media repositories - file upload and media operations
export 'media_repository.dart';

// CTV Registration repositories - CTV registration workflow
export 'ctv_registration_repository.dart';

// Location repositories - geographical and branch data
export 'location_repository.dart';

// Referrer repositories - referral and recommendation system
export 'referrer_repository.dart';

// Loan repositories - loan creation and management
export 'loan_repository.dart';

// System Config repositories - system configuration data
export 'system_config_repository.dart';
