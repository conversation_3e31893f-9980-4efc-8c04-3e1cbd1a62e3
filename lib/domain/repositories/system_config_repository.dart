import 'package:dartz/dartz.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/entities/common/system_config_entity.dart';

/// Repository interface cho System Config operations
/// Đ<PERSON><PERSON> nghĩa contract cho việc lấy cấu hình hệ thống từ API
abstract class SystemConfigRepository {
  /// Lấy danh sách cấu hình theo group code
  /// 
  /// [groupCode] - <PERSON><PERSON> nhóm cấu hình (ví dụ: 'MARITAL_STATUS', 'GENDER', etc.)
  /// 
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách cấu hình
  /// 
  /// Example:
  /// ```dart
  /// final result = await systemConfigRepository.getSystemConfigsByGroupCode('MARITAL_STATUS');
  /// result.fold(
  ///   (failure) => print('Error: $failure'),
  ///   (configs) => print('Marital statuses: ${configs.length}'),
  /// );
  /// ```
  Future<Either<Failure, List<SystemConfigEntity>>> getSystemConfigsByGroupCode(
    String groupCode,
  );

  /// Lấy danh sách tình trạng hôn nhân
  /// 
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách tình trạng hôn nhân
  /// 
  /// Example:
  /// ```dart
  /// final result = await systemConfigRepository.getMaritalStatuses();
  /// result.fold(
  ///   (failure) => print('Error: $failure'),
  ///   (statuses) => print('Statuses: ${statuses.length}'),
  /// );
  /// ```
  Future<Either<Failure, List<SystemConfigEntity>>> getMaritalStatuses();

  /// Lấy danh sách giới tính
  /// 
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách giới tính
  /// 
  /// Example:
  /// ```dart
  /// final result = await systemConfigRepository.getGenders();
  /// result.fold(
  ///   (failure) => print('Error: $failure'),
  ///   (genders) => print('Genders: ${genders.length}'),
  /// );
  /// ```
  Future<Either<Failure, List<SystemConfigEntity>>> getGenders();

  /// Lấy danh sách mục đích sử dụng vốn
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách mục đích sử dụng vốn
  Future<Either<Failure, List<SystemConfigEntity>>> getLoanPurposes();

  /// Lấy danh sách thời hạn vay
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách thời hạn vay
  Future<Either<Failure, List<SystemConfigEntity>>> getLoanTerms();

  /// Lấy danh sách nguồn thu (INCOME_SOURCE)
  /// Returns: Either<Failure, List<SystemConfigEntity>> - Danh sách nguồn thu
  Future<Either<Failure, List<SystemConfigEntity>>> getIncomeSources();
  
  Future<Either<Failure, List<SystemConfigEntity>>> getAssetTypes();
} 