import 'package:dartz/dartz.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/entities/entities.dart';
import 'package:sales_app/domain/entities/location/ward_entity.dart';

abstract class LocationRepository {
  /// Lấy danh sách tỉnh/thành phố (có cache)
  Future<Either<Failure, List<ProvinceEntity>>> getProvinces();

  /// Lấy danh sách chi nhánh theo tỉnh/thành phố (có cache)
  Future<Either<Failure, List<BranchEntity>>> getBranches(String provinceId);

  /// Lấy danh sách phường/xã theo tỉnh/thành phố (API v1/ward)
  Future<Either<Failure, List<WardEntity>>> getWards(String provinceId);
} 