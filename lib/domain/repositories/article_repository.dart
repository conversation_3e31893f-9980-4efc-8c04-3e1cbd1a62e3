import 'package:dartz/dartz.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/entities/entities.dart';

/// Repository interface cho article và content management
/// Định nghĩa contract cho việc truy xuất dữ liệu bài viết
abstract class ArticleRepository {
  /// Lấy thông tin bài viết theo mã code
  /// 
  /// [code] - Mã code của bài viết
  /// 
  /// Returns: Either<Failure, ArticleEntity> - Thông tin bài viết
  Future<Either<Failure, ArticleEntity>> getArticleByCode(String code);
} 