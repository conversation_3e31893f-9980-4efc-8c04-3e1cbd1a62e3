import 'package:dartz/dartz.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/entities/entities.dart';

/// Repository interface cho việc lấy thông tin người giới thiệu
abstract class ReferrerRepository {
  /// Lấy thông tin người giới thiệu theo mã
  /// 
  /// [referrerCode] - Mã người giới thiệu
  /// Returns [Either<Failure, ReferrerEntity>] - Thông tin người giới thiệu hoặc lỗi
  Future<Either<Failure, ReferrerEntity>> getReferrerByCode(String referrerCode);
} 