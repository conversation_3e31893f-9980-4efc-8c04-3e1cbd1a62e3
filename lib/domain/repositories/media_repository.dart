import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:sales_app/core/core.dart';

/// Repository interface cho media upload operations
/// Đ<PERSON><PERSON> nghĩa contract cho việc upload, x<PERSON>a và quản lý file trên Media API
/// 
/// Usage:
/// ```dart
/// class MediaUseCase {
///   final MediaRepository _mediaRepository;
///   
///   MediaUseCase(this._mediaRepository);
///   
///   Future<Either<Failure, String>> uploadIdentityDocument(File file) {
///     return _mediaRepository.uploadFile(
///       file: file,
///       fileType: FileType.idcard,
///       bucketName: 'salt',
///     );
///   }
/// }
/// ```
abstract class MediaRepository {
  /// Upload file lên Media API
  /// 
  /// [file] - File cần upload
  /// [fileType] - Loại file (IDCARD, AVATAR, etc.)
  /// [bucketName] - Tên bucket (optional, default: 'salt')
  /// [onProgress] - Callback để theo dõi tiến trình upload
  /// 
  /// Returns: Either<Failure, String> - previewUrl của file đã upload
  /// 
  /// Example:
  /// ```dart
  /// final result = await mediaRepository.uploadFile(
  ///   file: File('/path/to/image.jpg'),
  ///   fileType: FileType.idcard,
  ///   onProgress: (sent, total) => print('Uploaded: $sent/$total bytes'),
  /// );
  /// ```
  Future<Either<Failure, String>> uploadFile({
    required File file,
    required FileType fileType,
    String? bucketName,
    void Function(int sent, int total)? onProgress,
  });

  /// Upload multiple files cùng lúc
  /// 
  /// [files] - List các file cần upload
  /// [fileType] - Loại file cho tất cả files
  /// [bucketName] - Tên bucket (optional)
  /// [onProgress] - Callback cho từng file upload
  /// 
  /// Returns: Either<Failure, List<String>> - List previewUrl của các file đã upload
  /// 
  /// Example:
  /// ```dart
  /// final result = await mediaRepository.uploadMultipleFiles(
  ///   files: [frontImage, backImage],
  ///   fileType: FileType.idcard,
  ///   onProgress: (fileIndex, sent, total) => 
  ///     print('File $fileIndex: $sent/$total bytes'),
  /// );
  /// ```
  Future<Either<Failure, List<String>>> uploadMultipleFiles({
    required List<File> files,
    required FileType fileType,
    String? bucketName,
    void Function(int fileIndex, int sent, int total)? onProgress,
  });

  /// Xóa file từ Media API
  /// 
  /// [bucketName] - Tên bucket chứa file
  /// [fileType] - Loại file (enum FileType)
  /// [objectName] - Tên file cần xóa
  /// 
  /// Returns: Either<Failure, void>
  /// 
  /// Example:
  /// ```dart
  /// final result = await mediaRepository.deleteFile(
  ///   bucketName: 'salt',
  ///   fileType: FileType.idcard,
  ///   objectName: 'user_123_front.jpg',
  /// );
  /// ```
  Future<Either<Failure, void>> deleteFile({
    required String bucketName,
    required FileType fileType,
    required String objectName,
  });

  /// Lấy preview URL cho file
  /// 
  /// [bucketName] - Tên bucket
  /// [fileType] - Loại file (enum FileType)
  /// [objectName] - Tên file
  /// [isPrivate] - Có phải private URL không (default: false)
  /// 
  /// Returns: Either<Failure, String> - URL để preview file
  /// 
  /// Example:
  /// ```dart
  /// final result = await mediaRepository.getPreviewUrl(
  ///   bucketName: 'salt',
  ///   fileType: FileType.idcard,
  ///   objectName: 'user_123_front.jpg',
  ///   isPrivate: true,
  /// );
  /// ```
  Future<Either<Failure, String>> getPreviewUrl({
    required String bucketName,
    required FileType fileType,
    required String objectName,
    bool isPrivate = false,
  });
} 