import 'package:dartz/dartz.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/entities/entities.dart';
import 'dart:io';

abstract class CTVRegistrationRepository {
  /// L<PERSON>y danh sách tỉnh/thành phố (delegate to LocationRepository)
  Future<Either<Failure, List<ProvinceEntity>>> getProvinces();

  /// Lấy danh sách chi nhánh theo tỉnh/thành phố (delegate to LocationRepository)
  Future<Either<Failure, List<BranchEntity>>> getBranches(String provinceId);

  /// Lấy thông tin người giới thiệu theo mã (delegate to ReferrerRepository)
  Future<Either<Failure, ReferrerEntity>> getReferrerByCode(String referrerCode);

  /// Đăng ký CTV
  Future<Either<Failure, CTVRegistrationResult>> registerCTV({
    required String fullName,
    required DocumentType? documentType,
    required String idCardNo,
    required String phoneNumber,
    required String permanentAddress,
    required String provinceId,
    required String branchId,
    required String frontCardUrl,
    required String backCardUrl,
    String? referrerCode,
    String? email,
  });

  /// Giới thiệu CTV
  Future<Either<Failure, CTVReferralResult>> referCTV({
    required String fullName,
    required DocumentType? documentType,
    required String idCardNo,
    required String phoneNumber,
    required String permanentAddress,
    required String provinceId,
    required String branchId,
    required String frontCardUrl,
    required String backCardUrl,
    String? email,
  });

  /// Upload identity documents và trả về URLs
  Future<Either<Failure, Map<String, String>>> uploadIdentityDocuments({
    required File frontFile,
    required File backFile,
  });
} 