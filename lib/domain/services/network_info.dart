/// Domain service interface cho network connectivity checking
/// <PERSON><PERSON><PERSON> nghĩa contract cho việc kiểm tra kết nối mạng
/// 
/// Usage:
/// ```dart
/// class NetworkUseCase {
///   final NetworkInfo _networkInfo;
///   
///   NetworkUseCase(this._networkInfo);
///   
///   Future<void> performNetworkOperation() async {
///     if (await _networkInfo.isConnected) {
///       // Thực hiện operation cần network
///     } else {
///       // Xử lý khi không có network
///     }
///   }
/// }
/// ```
abstract class NetworkInfo {
  /// Kiểm tra thiết bị có kết nối internet không
  /// 
  /// Returns: Future<bool> - true nếu có kết nối, false nếu không
  /// 
  /// Example:
  /// ```dart
  /// final isConnected = await networkInfo.isConnected;
  /// if (isConnected) {
  ///   print('Có kết nối internet');
  /// } else {
  ///   print('Không có kết nối internet');
  /// }
  /// ```
  Future<bool> get isConnected;
  
  /// Stream theo dõi thay đổi trạng thái kết nối
  /// 
  /// Returns: Stream<bool> - Stream phát ra true/false khi trạng thái thay đổi
  /// 
  /// Example:
  /// ```dart
  /// networkInfo.connectivityStream.listen((isConnected) {
  ///   if (isConnected) {
  ///     print('Đã kết nối lại internet');
  ///   } else {
  ///     print('Mất kết nối internet');
  ///   }
  /// });
  /// ```
  Stream<bool> get connectivityStream;
}
