import '../entities/entities.dart';

/// Domain service interface cho local storage operations
/// <PERSON><PERSON><PERSON> nghĩa contract cho việc lưu trữ dữ liệu local (SharedPreferences, SecureStorage)
/// 
/// Usage:
/// ```dart
/// class AuthUseCase {
///   final StorageService _storage;
///   
///   AuthUseCase(this._storage);
///   
///   Future<void> saveUserSession(User user, String token) async {
///     await _storage.saveUserInfo(user);
///     await _storage.saveAccessToken(token);
///   }
///   
///   Future<void> clearSession() async {
///     await _storage.clearTokens();
///   }
/// }
/// ```
abstract class StorageService {
  // Auth token methods
  Future<void> saveAccessToken(String token);
  Future<String?> getAccessToken();

  Future<void> saveRefreshToken(String token);
  Future<String?> getRefreshToken();

  // Token expiry tracking
  Future<void> saveTokenExpiryTime(DateTime expiryTime);
  Future<DateTime?> getTokenExpiryTime();
  Future<bool> isTokenExpired();

  // User info methods
  Future<void> saveUserInfo(User user);
  Future<User?> getUserInfo();

  // Environment configuration methods
  Future<void> saveSelectedEnvironment(String environmentKey);
  Future<String?> getSelectedEnvironment();

  // Location cache methods
  Future<void> saveProvincesCache(List<dynamic> provincesJson, DateTime cachedAt);
  Future<List<dynamic>?> getProvincesCache();
  Future<DateTime?> getProvincesCacheTime();
  Future<void> clearProvincesCache();

  Future<void> saveBranchesCache(String provinceId, List<dynamic> branchesJson, DateTime cachedAt);
  Future<List<dynamic>?> getBranchesCache(String provinceId);
  Future<DateTime?> getBranchesCacheTime(String provinceId);
  Future<void> clearBranchesCache(String provinceId);

  Future<void> saveWardsCache(String provinceId, List<dynamic> wardsJson, DateTime cachedAt);
  Future<List<dynamic>?> getWardsCache(String provinceId);
  Future<DateTime?> getWardsCacheTime(String provinceId);
  Future<void> clearWardsCache(String provinceId);

  // Clear methods
  Future<void> clearAll();
  Future<void> clearTokens();
}