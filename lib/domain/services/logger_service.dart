/// Domain service interface cho logging operations
/// Đ<PERSON><PERSON> nghĩa contract cho việc ghi log trong ứng dụng
/// 
/// Usage:
/// ```dart
/// class AuthUseCase {
///   final LoggerService _logger;
///   
///   AuthUseCase(this._logger);
///   
///   Future<void> login(String email, String password) async {
///     _logger.logInfo('User attempting login', data: {'email': email});
///     
///     try {
///       // Login logic
///       _logger.logEvent('login_success', data: {'email': email});
///     } catch (e) {
///       _logger.logError('Login failed', error: e, data: {'email': email});
///     }
///   }
/// }
/// ```
abstract class LoggerService {
  /// Log thông tin chung
  void logInfo(String message, {Map<String, dynamic>? data});

  /// Log cho quá trình debug
  void logDebug(String message, {Map<String, dynamic>? data});

  /// Log cảnh báo
  void logWarning(String message, {Map<String, dynamic>? data});

  /// Log lỗi nghiêm trọng
  void logError(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  });

  /// Log sự kiện (user action, API call, etc.)
  void logEvent(String event, {Map<String, dynamic>? data});

  /// Log network request
  void logNetworkRequest(
    String method,
    String url, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? body,
  });

  /// Log network response
  void logNetworkResponse(
    String method,
    String url,
    int statusCode, {
    Map<String, dynamic>? response,
    Duration? duration,
  });
} 