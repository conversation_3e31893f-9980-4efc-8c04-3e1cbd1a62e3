# Domain Services

This directory contains domain service interfaces that define contracts for external concerns.

## Services

### NetworkInfo
**Purpose**: Check network connectivity status
**Location**: Domain layer (business concern)
**Integration**: Works with retry system for comprehensive network handling

```dart
@injectable
class SomeRepository {
  final NetworkInfo _networkInfo;
  final SomeApiService _apiService; // Has automatic retry via SimpleRetryInterceptor

  Future<Either<Failure, Data>> fetchData() async {
    if (!await _networkInfo.isConnected) {
      return Left(NetworkFailure('No internet connection'));
    }

    // API call with automatic retry for transient failures
    // SimpleRetryInterceptor handles network timeouts, server errors
    return await _apiService.fetchData();
  }
}
```

### StorageService
**Purpose**: Abstract local storage operations
**Location**: Domain layer (business concern)

```dart
// Specific methods for different storage needs
await storageService.saveAccessToken(token);
final token = await storageService.getAccessToken();
```

### LoggerService
**Purpose**: Abstract logging operations
**Location**: Domain layer (business concern)

```dart
// Structured logging
loggerService.logInfo('User action', data: {'userId': user.id});
loggerService.logError('API error', error: exception);
```

## Architecture Notes

- **Domain Services** define contracts for external concerns
- **Implementations** are in `lib/data/services_impl/`
- **Dependency Injection** automatically wires interfaces to implementations
- **Clean Architecture** - Domain doesn't depend on implementation details
