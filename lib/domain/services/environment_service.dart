import '../entities/entities.dart';

/// Abstract Environment Service
/// Domain layer interface for environment configuration management
/// Follows Clean Architecture - domain layer defines contracts
abstract class EnvironmentService {

  /// Get current environment configuration
  EnvironmentEntity get currentConfig;

  /// Get current environment type
  Environment get currentEnvironment;

  /// Get all available environments
  List<Environment> get availableEnvironments;

  /// Stream of environment changes for reactive UI
  Stream<EnvironmentEntity> get environmentChanges;

  /// Switch to a new environment
  Future<void> switchEnvironment(Environment environment);

  /// Initialize service (load saved environment)
  Future<void> initialize();

  /// Get base URL for current environment
  String get baseUrl;

  /// Get Sales API URL for current environment
  String get salesApiUrl;

  /// Get Media API URL for current environment  
  String get mediaApiUrl;

  /// Get full API URL for current environment
  String get fullApiUrl;

  /// Check if logging is enabled for current environment
  bool get isLoggingEnabled;

  /// Check if mock API is enabled for current environment
  bool get isMockApiEnabled;

  /// Get timeout duration for current environment
  int get timeoutDuration;

  /// Reset to default environment (Dev)
  Future<void> resetToDefault();

  /// Get environment display info for UI
  String get environmentDisplayInfo;

  /// Check if current environment is production
  bool get isProduction;

  /// Check if current environment is development
  bool get isDevelopment;

  /// Check if current environment is staging
  bool get isStaging;
}
