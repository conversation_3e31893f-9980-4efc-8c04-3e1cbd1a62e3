/// Domain Services Barrel Export
/// Service interfaces defining business logic contracts
/// 
/// This file provides a single import point for all domain service interfaces.
/// Services define contracts for business logic and external integrations.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/domain/services/services.dart';
/// 
/// // Now you can use any service interface:
/// class MyUseCase {
///   final LoggerService _logger;
///   final NetworkInfo _networkInfo;
///   final StorageService _storage;
///   final DatabaseService _database;
///   final EnvironmentService _environment;
///   
///   MyUseCase(this._logger, this._networkInfo, this._storage, 
///             this._database, this._environment);
///   
///   Future<void> performAction() async {
///     _logger.logInfo('Starting action');
///     
///     if (await _networkInfo.isConnected) {
///       // Perform network operation
///     }
///     
///     await _storage.saveAccessToken('token');
///     await _database.saveUser(user);
///   }
/// }
/// ```
/// 
/// ## Service Categories:
/// - **Infrastructure Services**: Database, logging, storage
/// - **Network Services**: Network monitoring, connectivity
/// - **Environment Services**: Environment configuration
library services;

// Infrastructure services - database, logging, storage
export 'database_service.dart';
export 'logger_service.dart';
export 'storage_service.dart';

// Network services - connectivity and monitoring
export 'network_info.dart';
export 'network_monitoring_service.dart';

// Environment services - configuration management
export 'environment_service.dart';
