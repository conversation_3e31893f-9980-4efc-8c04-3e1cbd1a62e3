/// Utility class ch<PERSON><PERSON> c<PERSON>c hàm validate phổ biến
/// Sử dụng trong toàn bộ app để đảm bảo consistency
import 'package:intl/intl.dart';

class ValidationUtils {
  // Private constructor để không thể tạo instance
  ValidationUtils._();

  /// Validate email format
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationEmailRequired';
    }
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'validationEmailInvalid';
    }
    return null;
  }

  /// Validate phone number (Vietnam format)
  static String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationPhoneRequired';
    }
    final phone = value.trim();
    if (!phone.startsWith('0')) {
      return 'validationPhoneStartWithZero';
    }
    if (phone.length != 10) {
      return 'validationPhoneLength';
    }
    final phoneRegex = RegExp(r'^0[0-9]{9}$');
    if (!phoneRegex.hasMatch(phone)) {
      return 'validationPhoneInvalid';
    }
    return null;
  }

  /// Validate CCCD/CMND (Vietnam ID)
  static String? validateIdNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationIdNumberRequired';
    }
    final id = value.trim();
    if (id.length != 12) {
      return 'validationIdNumberLength';
    }
    final idRegex = RegExp(r'^[0-9]+$');
    if (!idRegex.hasMatch(id)) {
      return 'validationIdNumberNumeric';
    }
    return null;
  }

  /// Validate full name
  static String? validateFullName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationFullNameRequired';
    }
    final name = value.trim();
    if (name.isEmpty) {
      return 'validationFullNameMinLength';
    }
    if (name.length > 100) {
      return 'validationFullNameMaxLength';
    }
    final nameRegex = RegExp(r'^[a-zA-ZÀ-ỹ\s]+$');
    if (!nameRegex.hasMatch(name)) {
      return 'validationFullNameCharacters';
    }
    return null;
  }

  /// Validate address
  static String? validateAddress(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationAddressRequired';
    }
    final address = value.trim();
    if (address.length > 250) {
      return 'validationAddressMaxLength';
    }
    // Cho phép chữ, số, dấu cách, dấu phẩy, dấu gạch ngang, dấu gạch chéo
    final addressRegex = RegExp(r'^[a-zA-ZÀ-ỹ0-9\s,\-/]+$');
    if (!addressRegex.hasMatch(address)) {
      // Chỉ cho phép chữ, số, dấu cách, dấu phẩy, dấu gạch ngang, dấu gạch chéo
      return 'validationAddressCharacters'; // Địa chỉ chỉ được chứa chữ, số, dấu phẩy, dấu gạch ngang, dấu gạch chéo
    }
    return null;
  }

  /// Validate required field (not empty)
  static String? validateRequired(String? value, String fieldName,{int? maxLength}) {
    if (value == null || value.trim().isEmpty) {
      return 'validationRequired';
    }
    if (maxLength != null && value.trim().length > maxLength) {
      return 'validationMaxLength';
    }
    return null;
  }

  /// Validate minimum length
  static String? validateMinLength(String? value, int minLength, String fieldName) {
    if (value == null || value.trim().length < minLength) {
      return 'validationMinLength';
    }
    return null;
  }

  /// Validate maximum length
  static String? validateMaxLength(String? value, int maxLength, String fieldName) {
    if (value != null && value.trim().length > maxLength) {
      return 'validationMaxLength';
    }
    return null;
  }

  /// Validate length range
  static String? validateLength(String? value, int minLength, int maxLength, String fieldName) {
    if (value == null || value.trim().length < minLength) {
      return 'validationMinLength';
    }
    if (value.trim().length > maxLength) {
      return 'validationMaxLength';
    }
    return null;
  }

  /// Validate numeric only
  static String? validateNumeric(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return 'validationRequired';
    }
    final numericRegex = RegExp(r'^[0-9]+$');
    if (!numericRegex.hasMatch(value.trim())) {
      return 'validationNumericOnly';
    }
    return null;
  }

  /// Validate password strength
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'validationPasswordRequired';
    }
    if (value.length < 8) {
      return 'validationPasswordMinLength';
    }
    if (value.length > 50) {
      return 'validationPasswordMaxLength';
    }
    final hasUpperCase = RegExp(r'[A-Z]').hasMatch(value);
    final hasLowerCase = RegExp(r'[a-z]').hasMatch(value);
    final hasNumbers = RegExp(r'[0-9]').hasMatch(value);
    if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
      return 'validationPasswordStrength';
    }
    return null;
  }

  /// Validate confirm password
  static String? validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return 'validationConfirmPasswordRequired';
    }
    if (value != password) {
      return 'validationConfirmPasswordMismatch';
    }
    return null;
  }

  /// Validate date of birth (must be 18+ years old)
  static String? validateDateOfBirth(DateTime? value) {
    if (value == null) {
      return 'validationDateOfBirthRequired';
    }
    final now = DateTime.now();
    final age = now.year - value.year;
    if (age < 18) {
      return 'validationDateOfBirthAge';
    }
    if (age > 100) {
      return 'validationDateOfBirthInvalid';
    }
    return null;
  }

  /// Validate issue date
  static String? validateIssueDate(DateTime? value) {
    if (value == null) {
      return 'validationIssueDateRequired';
    }
    return null;
  }

  /// Validate expiry date
  static String? validateExpiryDate(DateTime? value) {
    if (value == null) {
      return 'validationExpiryDateRequired';
    }
    return null;
  }

  /// Validate birth date
  static String? validateBirthDate(DateTime? value) {
    if (value == null) {
      return 'validationBirthDateRequired';
    }
    return null;
  }

  /// Validate referrer code
  static String? validateReferrerCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Referrer code là optional
    }
    final code = value.trim();
    if (code.length < 3) {
      return 'validationReferrerCodeMinLength';
    }
    if (code.length > 20) {
      return 'validationReferrerCodeMaxLength';
    }
    final codeRegex = RegExp(r'^[a-zA-Z0-9\-]+$');
    if (!codeRegex.hasMatch(code)) {
      return 'validationReferrerCodeCharacters';
    }
    return null;
  }

  /// Validate amount (currency)
  static String? validateAmount(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationAmountRequired';
    }
    final amount = value.trim();
    final amountRegex = RegExp(r'^[0-9,]+$');
    if (!amountRegex.hasMatch(amount)) {
      return 'validationAmountFormat';
    }
    final numericAmount = amount.replaceAll(',', '');
    final amountValue = int.tryParse(numericAmount);
    if (amountValue == null || amountValue <= 0) {
      return 'validationAmountPositive';
    }
    if (amountValue > 999999999999) {
      return 'validationAmountTooLarge';
    }
    return null;
  }

  /// Validate URL
  static String? validateUrl(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationUrlRequired';
    }
    final url = value.trim();
    final urlRegex = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
    );
    if (!urlRegex.hasMatch(url)) {
      return 'validationUrlInvalid';
    }
    return null;
  }

  /// Validate OTP code
  static String? validateOtp(String? value, int length) {
    if (value == null || value.trim().isEmpty) {
      return 'validationOtpRequired';
    }
    final otp = value.trim();
    if (otp.length != length) {
      return 'validationOtpLength';
    }
    final otpRegex = RegExp(r'^[0-9]+$');
    if (!otpRegex.hasMatch(otp)) {
      return 'validationOtpNumeric';
    }
    return null;
  }

  /// Validate bank account number
  static String? validateBankAccount(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationBankAccountRequired';
    }
    final account = value.trim();
    if (account.length < 8 || account.length > 20) {
      return 'validationBankAccountLength';
    }
    final accountRegex = RegExp(r'^[0-9]+$');
    if (!accountRegex.hasMatch(account)) {
      return 'validationBankAccountNumeric';
    }
    return null;
  }

  /// Validate tax code
  static String? validateTaxCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationTaxCodeRequired';
    }
    final taxCode = value.trim();
    if (taxCode.length != 10 && taxCode.length != 13) {
      return 'validationTaxCodeLength';
    }
    final taxCodeRegex = RegExp(r'^[0-9]+$');
    if (!taxCodeRegex.hasMatch(taxCode)) {
      return 'validationTaxCodeNumeric';
    }
    return null;
  }
  static String? validateEmailOptional(String? value) {
  if (value == null || value.trim().isEmpty) {
    return null; // optional, empty is valid
  }
  final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
  if (!emailRegex.hasMatch(value.trim())) {
    return 'validationEmailInvalid';
  }
  return null;
}

  /// Validate issue place (không null, tối đa 50 ký tự)
  static String? validateIssuePlace(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationIssuePlaceRequired';
    }
    if (value.trim().length > 50) {
      return 'validationIssuePlaceMaxLength';
    }
    return null;
  }

  /// Validate passport number: alphanumeric only, max 50 chars, no special chars
  static String? validatePassportNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationPassportNumberRequired';
    }
    final passport = value.trim();
    if (passport.length > 50) {
      return 'validationPassportNumberMaxLength';
    }
    final passportRegex = RegExp(r'^[a-zA-Z0-9]+$');
    if (!passportRegex.hasMatch(passport)) {
      return 'validationPassportNumberCharacters';
    }
    return null;
  }

  /// Validate own capital (vốn tự có không vượt quá 1 tỉ)
  static String? validateOwnCapital(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationOwnCapitalRequired';
    }
    final amount = value.replaceAll('.', '').trim();
    final amountValue = int.tryParse(amount);
    if (amountValue == null || amountValue < 0) {
      return 'validationOwnCapitalInvalid';
    }
    if (amountValue > 1000000000) {
      return 'validationOwnCapitalTooLarge';
    }
    return null;
  }

  /// Validate doanh số 
  static String? validateMoney(String? value, String fileErrror) {
    if (value == null || value.trim().isEmpty) {
      return 'Vui lòng nhập $fileErrror';
    }
    final amount = value.replaceAll('.', '').trim();
    final amountValue = int.tryParse(amount);
    if (amountValue == null || amountValue < 0) {
      return '$fileErrror lớn hơn 0';
    }
    if (amountValue > 1000000000) {
      return '$fileErrror nhỏ hơn 1.000.000.000';
    }
    return null;
  }
  
  /// Validate loan amount (số tiền đề nghị vay tối thiểu 1.000.000, tối đa 1 tỉ)
  static String? validateLoanAmount(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'validationLoanAmountRequired';
    }
    final amount = value.replaceAll('.', '').trim();
    final amountValue = int.tryParse(amount);
    if (amountValue == null || amountValue < 1000000) {
      return 'validationLoanAmountTooSmall';
    }
    if (amountValue > 1000000000) {
      return 'validationLoanAmountTooLarge';
    }
    return null;
  }

  /// Convert String to DateTime, hỗ trợ truyền format hoặc tự động thử các format phổ biến
  static DateTime? convertStringToDateTime(String dateRaw, {String? format}) {
    if (dateRaw.isEmpty) return null;
    try {
      if (format != null) {
        return DateFormat(format).parseStrict(dateRaw);
      }
      // Đặc biệt: nếu là dạng 8 số liên tục (DDMMYYYY)
      final ddMMyyyyRegex = RegExp(r'^(\d{2})(\d{2})(\d{4})$');
      final match = ddMMyyyyRegex.firstMatch(dateRaw);
      if (match != null) {
        final day = int.parse(match.group(1)!);
        final month = int.parse(match.group(2)!);
        final year = int.parse(match.group(3)!);
        return DateTime(year, month, day);
      }
      // Thử các format phổ biến
      final formats = [
        'dd/MM/yyyy',
        'dd-MM-yyyy',
        'yyyy-MM-dd',
        'yyyy/MM/dd',
        'MM/dd/yyyy',
        'MM-dd-yyyy',
      ];
      for (final fmt in formats) {
        try {
          return DateFormat(fmt).parseStrict(dateRaw);
        } catch (_) {}
      }
      // Thử parse chuẩn ISO
      return DateTime.parse(dateRaw);
    } catch (e) {
      // AppLogger.error('convertStringToDateTime failed: $dateRaw', error: e);
      return null;
    }
  }
} 