import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/domain/entities/registration/cccd_info_entity.dart';
import 'package:sales_app/core/enums/gender.dart';

/// Utility class for parsing dates from various formats
/// 
/// This utility provides methods to parse dates from CCCDInfoEntity and other sources.
/// It supports multiple date formats and provides consistent error handling.
/// 
/// ## Usage Examples:
/// 
/// ```dart
/// // Parse date from string (generic usage)
/// final birthDate = DateParser.parseDate('15/03/1990');
/// final issueDate = DateParser.parseDate('2020-01-01');
/// final gender = DateParser.parseGender('Nam');
/// 
/// // Parse date from CCCDInfoEntity (convenience methods)
/// final cccdInfo = CCCDInfoEntity(...);
/// final birthDate = DateParser.parseBirthDateFromCCCD(cccdInfo);
/// final issueDate = DateParser.parseIssueDateFromCCCD(cccdInfo);
/// final gender = DateParser.parseGenderFromCCCD(cccdInfo);
/// 
/// // Parse date from any string format
/// final date = DateParser.parseDate('15/03/1990');
/// 
/// // Format date for display
/// final displayDate = DateParser.formatDateForDisplay(date);
/// 
/// // Validate date
/// final isValid = DateParser.isValidDate(date);
/// ```
/// 
/// ## Supported Date Formats:
/// - `dd/MM/yyyy` (e.g., "15/03/1990")
/// - `yyyy-MM-dd` (e.g., "1990-03-15") 
/// - `yyyy/MM/dd` (e.g., "1990/03/15")
/// - `DDMMYYYY` (e.g., "20072024") - from QR scan
/// 
/// ## Gender Parsing:
/// Supports Vietnamese and English gender strings:
/// - Male: "Nam", "male", "1"
/// - Female: "Nữ", "female", "0"
class DateParser {
  /// Parse date from string with multiple formats support
  /// Supports formats: "dd/MM/yyyy", "yyyy-MM-dd", "yyyy/MM/dd", "DDMMYYYY"
  static DateTime? parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return null;
    }

    try {
      // Try parsing ISO format first (yyyy-MM-dd)
      if (dateString.contains('-')) {
        final parsed = DateTime.tryParse(dateString);
        if (parsed != null) {
          AppLogger.info('Parsed date from ISO format', data: {
            'originalDate': dateString,
            'parsedDate': parsed.toIso8601String(),
          });
          return parsed;
        }
      }

      // Try parsing dd/MM/yyyy format
      if (dateString.contains('/')) {
        final parts = dateString.split('/');
        if (parts.length == 3) {
          final day = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          final year = int.tryParse(parts[2]);
          
          if (day != null && month != null && year != null) {
            // Giống logic cũ - không validate strict
            final parsed = DateTime(year, month, day);
            AppLogger.info('Parsed date from dd/MM/yyyy format', data: {
              'originalDate': dateString,
              'parsedDate': parsed.toIso8601String(),
            });
            return parsed;
          }
        }
      }

      // Try parsing yyyy/MM/dd format
      if (dateString.contains('/') && dateString.length == 10) {
        final parts = dateString.split('/');
        if (parts.length == 3) {
          final year = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          final day = int.tryParse(parts[2]);
          
          if (year != null && month != null && day != null) {
            // Giống logic cũ - không validate strict
            final parsed = DateTime(year, month, day);
            AppLogger.info('Parsed date from yyyy/MM/dd format', data: {
              'originalDate': dateString,
              'parsedDate': parsed.toIso8601String(),
            });
            return parsed;
          }
        }
      }

      // Try parsing DDMMYYYY format (from QR scan)
      if (dateString.length == 8 && !dateString.contains('/') && !dateString.contains('-')) {
        final day = int.tryParse(dateString.substring(0, 2));
        final month = int.tryParse(dateString.substring(2, 4));
        final year = int.tryParse(dateString.substring(4, 8));
        
        if (day != null && month != null && year != null) {
          // Giống logic cũ - không validate strict
          final parsed = DateTime(year, month, day);
          AppLogger.info('Parsed date from DDMMYYYY format', data: {
            'originalDate': dateString,
            'parsedDate': parsed.toIso8601String(),
          });
          return parsed;
        }
      }

      AppLogger.warning('Failed to parse date', data: {
        'dateString': dateString,
        'reason': 'Unsupported format',
      });
      return null;
    } catch (e) {
      AppLogger.error('Error parsing date', data: {
        'dateString': dateString,
        'error': e.toString(),
      });
      return null;
    }
  }

  /// Parse birth date from date string (alias for parseDate)
  static DateTime? parseBirthDate(String? dateString) {
    return parseDate(dateString);
  }

  /// Parse issue date from date string (alias for parseDate)
  static DateTime? parseIssueDate(String? dateString) {
    return parseDate(dateString);
  }

  /// Parse gender from gender string and convert to Gender enum
  static Gender parseGender(String? genderString) {
    if (genderString == null || genderString.isEmpty) {
      return Gender.male; // Default value
    }

    final genderLower = genderString.toLowerCase();
    
    if (genderLower.contains('nam') || 
        genderLower.contains('male') || 
        genderLower == '1' ||
        genderLower == 'nam') {
      return Gender.male;
    } else if (genderLower.contains('nữ') || 
               genderLower.contains('female') || 
               genderLower == '0' ||
               genderLower == 'nữ') {
      return Gender.female;
    }
    
    // Default to male if cannot parse
    return Gender.male;
  }

  /// Parse birth date from CCCDInfoEntity (convenience method)
  static DateTime? parseBirthDateFromCCCD(CCCDInfoEntity cccdInfo) {
    AppLogger.info('parseBirthDateFromCCCD called', data: {
      'dateOfBirth': cccdInfo.dateOfBirth,
      'isEmpty': cccdInfo.dateOfBirth.isEmpty,
    });
    final result = parseBirthDate(cccdInfo.dateOfBirth);
    AppLogger.info('parseBirthDateFromCCCD result', data: {
      'result': result?.toIso8601String(),
      'isNull': result == null,
    });
    return result;
  }

  /// Parse issue date from CCCDInfoEntity (convenience method)
  static DateTime? parseIssueDateFromCCCD(CCCDInfoEntity cccdInfo) {
    AppLogger.info('parseIssueDateFromCCCD called', data: {
      'issueDate': cccdInfo.issueDate,
      'isEmpty': cccdInfo.issueDate.isEmpty,
    });
    final result = parseIssueDate(cccdInfo.issueDate);
    AppLogger.info('parseIssueDateFromCCCD result', data: {
      'result': result?.toIso8601String(),
      'isNull': result == null,
    });
    return result;
  }

  /// Parse gender from CCCDInfoEntity (convenience method)
  static Gender parseGenderFromCCCD(CCCDInfoEntity cccdInfo) {
    return parseGender(cccdInfo.gender);
  }

  /// Format date to display string (dd/MM/yyyy)
  static String formatDateForDisplay(DateTime? date) {
    if (date == null) return '';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Check if date is valid (not in future and reasonable past)
  static bool isValidDate(DateTime? date) {
    if (date == null) return false;
    
    final now = DateTime.now();
    final minDate = DateTime(1900, 1, 1);
    
    return date.isAfter(minDate) && date.isBefore(now);
  }
} 