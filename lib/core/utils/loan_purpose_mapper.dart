import 'package:sales_app/core/enums/loan_purpose.dart';
import 'package:sales_app/domain/entities/common/system_config_entity.dart';

/// Utility class để map giữa SystemConfigEntity từ API và LoanPurpose enum
/// 
/// Mapping rules dựa trên dữ liệu API trả về:
/// - LIVING_NEEDS -> LoanPurpose.livingNeeds
/// - BUSINESS_ACTIVITY -> LoanPurpose.businessActivity
/// - OTHER -> LoanPurpose.other
class LoanPurposeMapper {
  /// Map từ SystemConfigEntity sang LoanPurpose enum
  static LoanPurpose? fromSystemConfig(SystemConfigEntity systemConfig) {
    switch (systemConfig.code) {
      case 'LIVING_NEEDS':
        return LoanPurpose.livingNeeds;
      case 'BUSINESS_ACTIVITY':
        return LoanPurpose.businessActivity;
      case 'OTHER':
        return LoanPurpose.other;
      default:
        return null;
    }
  }

  /// Map từ LoanPurpose enum sang SystemConfigEntity
  static SystemConfigEntity? toSystemConfig(
    LoanPurpose loanPurpose,
    List<SystemConfigEntity> systemConfigs,
  ) {
    String targetCode;
    switch (loanPurpose) {
      case LoanPurpose.livingNeeds:
        targetCode = 'LIVING_NEEDS';
        break;
      case LoanPurpose.businessActivity:
        targetCode = 'BUSINESS_ACTIVITY';
        break;
      case LoanPurpose.other:
        targetCode = 'OTHER';
        break;
    }
    try {
      return systemConfigs.firstWhere((config) => config.code == targetCode);
    } catch (e) {
      return null;
    }
  }

  /// Tìm LoanPurpose mặc định (BUSINESS_ACTIVITY) từ danh sách SystemConfigEntity
  static SystemConfigEntity? getDefaultSystemConfig(List<SystemConfigEntity> systemConfigs) {
    try {
      return systemConfigs.firstWhere((config) => config.code == 'BUSINESS_ACTIVITY');
    } catch (e) {
      return null;
    }
  }
} 