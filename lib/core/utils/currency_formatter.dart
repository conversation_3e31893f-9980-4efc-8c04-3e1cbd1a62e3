import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

/// Utility class for currency formatting
class CurrencyFormatter {
  static final NumberFormat _formatter = NumberFormat('#,###', 'vi_VN');

  /// Format number to currency string with thousand separators
  /// Example: 1000000 -> "1,000,000"
  static String formatCurrency(num amount) {
    return _formatter.format(amount);
  }

  /// Parse currency string to number
  /// Example: "1,000,000" -> 1000000
  static int parseCurrency(String value) {
    final cleanValue = value.replaceAll(',', '').replaceAll('.', '');
    return int.tryParse(cleanValue) ?? 0;
  }

  /// Format currency with VND suffix
  /// Example: 1000000 -> "1,000,000 VNĐ"
  static String formatCurrencyWithSuffix(num amount) {
    return '${formatCurrency(amount)} VNĐ';
  }

  /// Convert number to words in Vietnamese
  /// Example: 1000000 -> "Một triệu đồng"
  static String numberToWords(int amount) {
    if (amount == 0) return 'Không đồng';
    
    final units = ['', 'nghìn', 'triệu', 'tỷ'];
    final ones = ['', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];
    final tens = ['', '', 'hai mươi', 'ba mươi', 'bốn mươi', 'năm mươi', 'sáu mươi', 'bảy mươi', 'tám mươi', 'chín mươi'];
    final teens = ['mười', 'mười một', 'mười hai', 'mười ba', 'mười bốn', 'mười lăm', 'mười sáu', 'mười bảy', 'mười tám', 'mười chín'];

    String convertGroup(int num) {
      if (num == 0) return '';
      
      String result = '';
      int hundreds = num ~/ 100;
      int remainder = num % 100;
      
      if (hundreds > 0) {
        result += '${ones[hundreds]} trăm';
        if (remainder > 0) result += ' ';
      }
      
      if (remainder >= 10 && remainder < 20) {
        result += teens[remainder - 10];
      } else {
        int tensDigit = remainder ~/ 10;
        int onesDigit = remainder % 10;
        
        if (tensDigit > 0) {
          if (tensDigit == 1) {
            result += 'mười';
          } else {
            result += tens[tensDigit];
          }
          if (onesDigit > 0) result += ' ';
        }
        
        if (onesDigit > 0) {
          if (tensDigit > 1 && onesDigit == 1) {
            result += 'mốt';
          } else if (tensDigit > 0 && onesDigit == 5) {
            result += 'lăm';
          } else {
            result += ones[onesDigit];
          }
        }
      }
      
      return result;
    }

    String result = '';
    int unitIndex = 0;
    
    while (amount > 0) {
      int group = amount % 1000;
      if (group > 0) {
        String groupText = convertGroup(group);
        if (unitIndex > 0) {
          groupText += ' ${units[unitIndex]}';
        }
        result = groupText + (result.isEmpty ? '' : ' $result');
      }
      amount ~/= 1000;
      unitIndex++;
    }
    
    // Capitalize first letter
    if (result.isNotEmpty) {
      result = result[0].toUpperCase() + result.substring(1);
    }
    
    return '$result đồng';
  }
}

/// TextInputFormatter for currency input with thousand separators
class CurrencyInputFormatter extends TextInputFormatter {
  final int? maxValue;

  CurrencyInputFormatter({this.maxValue}); // maxValue optional, nếu null thì không check

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Remove all non-digit characters
    String digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    
    if (digitsOnly.isEmpty) {
      return const TextEditingValue(text: '');
    }

    // Parse to int and check max value
    int value = int.tryParse(digitsOnly) ?? 0;
    if (maxValue != null && value > maxValue!) {
      value = maxValue!;
      digitsOnly = value.toString();
    }

    // Format with thousand separators
    String formatted = CurrencyFormatter.formatCurrency(value);

    // Calculate cursor position
    int cursorPosition = formatted.length;
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: cursorPosition),
    );
  }
}
