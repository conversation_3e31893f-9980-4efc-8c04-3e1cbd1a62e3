import 'package:sales_app/core/enums/income_source.dart';
import 'package:sales_app/domain/entities/common/system_config_entity.dart';

/// Utility class để map giữa SystemConfigEntity từ API và IncomeSource enum
/// 
/// Mapping rules dựa trên dữ liệu API trả về:
/// - BUSINESS -> IncomeSource.business
/// - SALARY -> IncomeSource.salary
/// - OTHER -> IncomeSource.other
class IncomeSourceMapper {
  /// Map từ SystemConfigEntity sang IncomeSource enum
  static IncomeSource? fromSystemConfig(SystemConfigEntity systemConfig) {
    switch (systemConfig.code) {
      case 'BUSINESS':
        return IncomeSource.business;
      case 'SALARY':
        return IncomeSource.salary;
      case 'OTHER':
        return IncomeSource.other;
      default:
        return null;
    }
  }

  /// Map từ IncomeSource enum sang SystemConfigEntity
  static SystemConfigEntity? toSystemConfig(
    IncomeSource incomeSource,
    List<SystemConfigEntity> systemConfigs,
  ) {
    String targetCode;
    switch (incomeSource) {
      case IncomeSource.business:
        targetCode = 'BUSINESS';
        break;
      case IncomeSource.salary:
        targetCode = 'SALARY';
        break;
      case IncomeSource.other:
        targetCode = 'OTHER';
        break;
    }
    try {
      return systemConfigs.firstWhere((config) => config.code == targetCode);
    } catch (e) {
      return null;
    }
  }

  /// Tìm IncomeSource mặc định (BUSINESS) từ danh sách SystemConfigEntity
  static SystemConfigEntity? getDefaultSystemConfig(List<SystemConfigEntity> systemConfigs) {
    try {
      return systemConfigs.firstWhere((config) => config.code == 'BUSINESS');
    } catch (e) {
      return null;
    }
  }
} 