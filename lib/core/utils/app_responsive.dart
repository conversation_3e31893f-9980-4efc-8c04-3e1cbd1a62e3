import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Extension methods cho responsive widgets
extension ResponsiveWidget on Widget {
  /// Thêm responsive padding
  Widget responsivePadding({
    double? all,
    double? horizontal,
    double? vertical,
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    return Padding(
      padding: EdgeInsets.only(
        left: (left ?? horizontal ?? all ?? 0).w,
        top: (top ?? vertical ?? all ?? 0).h,
        right: (right ?? horizontal ?? all ?? 0).w,
        bottom: (bottom ?? vertical ?? all ?? 0).h,
      ),
      child: this,
    );
  }

  /// Thêm responsive margin
  Widget responsiveMargin({
    double? all,
    double? horizontal,
    double? vertical,
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    return Container(
      margin: EdgeInsets.only(
        left: (left ?? horizontal ?? all ?? 0).w,
        top: (top ?? vertical ?? all ?? 0).h,
        right: (right ?? horizontal ?? all ?? 0).w,
        bottom: (bottom ?? vertical ?? all ?? 0).h,
      ),
      child: this,
    );
  }
}

/// Helper class cho responsive sizing
class AppResponsive {
  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width <= 600.w;

  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width > 600.w &&
      MediaQuery.of(context).size.width <= 900.w;

  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width > 900.w;

  static double screenWidth(BuildContext context) =>
      MediaQuery.of(context).size.width;

  static double screenHeight(BuildContext context) =>
      MediaQuery.of(context).size.height;

  static double statusBarHeight(BuildContext context) =>
      MediaQuery.of(context).padding.top;

  static double bottomBarHeight(BuildContext context) =>
      MediaQuery.of(context).padding.bottom;

  static double keyboardHeight(BuildContext context) =>
      MediaQuery.of(context).viewInsets.bottom;
} 