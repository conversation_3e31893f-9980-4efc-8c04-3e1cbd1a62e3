import 'package:sales_app/core/enums/marital_status.dart';
import 'package:sales_app/domain/entities/common/system_config_entity.dart';

/// Utility class để map giữa SystemConfigEntity từ API và MaritalStatus enum
/// 
/// Mapping rules dựa trên dữ liệu API trả về:
/// - SINGLE -> MaritalStatus.single
/// - MARRIED -> MaritalStatus.married  
/// - DIVORCED -> MaritalStatus.divorced
/// - OTHER -> MaritalStatus.other
class MaritalStatusMapper {
  /// Map từ SystemConfigEntity sang MaritalStatus enum
  /// 
  /// [systemConfig] - SystemConfigEntity từ API
  /// 
  /// Returns: MaritalStatus tương ứng hoặc null nếu không tìm thấy
  /// 
  /// Example:
  /// ```dart
  /// final maritalStatus = MaritalStatusMapper.fromSystemConfig(systemConfigEntity);
  /// if (maritalStatus != null) {
  ///   print('Mapped to: ${maritalStatus.label}');
  /// }
  /// ```
  static MaritalStatus? fromSystemConfig(SystemConfigEntity systemConfig) {
    switch (systemConfig.code) {
      case 'SINGLE':
        return MaritalStatus.single;
      case 'MARRIED':
        return MaritalStatus.married;
      case 'DIVORCED':
        return MaritalStatus.divorced;
      case 'OTHER':
        return MaritalStatus.other;
      default:
        return null;
    }
  }

  /// Map từ MaritalStatus enum sang SystemConfigEntity
  /// 
  /// [maritalStatus] - MaritalStatus enum
  /// [systemConfigs] - Danh sách SystemConfigEntity từ API
  /// 
  /// Returns: SystemConfigEntity tương ứng hoặc null nếu không tìm thấy
  /// 
  /// Example:
  /// ```dart
  /// final systemConfig = MaritalStatusMapper.toSystemConfig(
  ///   MaritalStatus.single, 
  ///   maritalStatuses
  /// );
  /// ```
  static SystemConfigEntity? toSystemConfig(
    MaritalStatus maritalStatus,
    List<SystemConfigEntity> systemConfigs,
  ) {
    String targetCode;
    switch (maritalStatus) {
      case MaritalStatus.single:
        targetCode = 'SINGLE';
        break;
      case MaritalStatus.married:
        targetCode = 'MARRIED';
        break;
      case MaritalStatus.divorced:
        targetCode = 'DIVORCED';
        break;
      case MaritalStatus.other:
        targetCode = 'OTHER';
        break;
    }

    try {
      return systemConfigs.firstWhere((config) => config.code == targetCode);
    } catch (e) {
      return null;
    }
  }

  /// Tìm MaritalStatus mặc định (SINGLE) từ danh sách SystemConfigEntity
  /// 
  /// [systemConfigs] - Danh sách SystemConfigEntity từ API
  /// 
  /// Returns: SystemConfigEntity cho SINGLE hoặc null nếu không tìm thấy
  /// 
  /// Example:
  /// ```dart
  /// final defaultConfig = MaritalStatusMapper.getDefaultSystemConfig(maritalStatuses);
  /// if (defaultConfig != null) {
  ///   controller.selectMaritalStatus(defaultConfig);
  /// }
  /// ```
  static SystemConfigEntity? getDefaultSystemConfig(List<SystemConfigEntity> systemConfigs) {
    try {
      return systemConfigs.firstWhere((config) => config.code == 'SINGLE');
    } catch (e) {
      return null;
    }
  }
} 