import 'dart:convert';

// Domain entities
import 'package:sales_app/domain/entities/entities.dart';

/// Utility class for parsing different types of QR codes
class QRParser {
  /// Parse QR code data to CCCDInfoEntity
  /// Supports both JSON format and pipe-separated format
  static CCCDInfoEntity? parseCccdInfoFromQr(String raw) {
    try {
      // Thử parse JSON
      final map = jsonDecode(raw);
      if (map is Map<String, dynamic>) {
        return CCCDInfoEntity.fromJson(map);
      }
    } catch (_) {
      // Nếu không phải JSON, thử parse dạng |
      final parts = raw.split('|');
      if (parts.length >= 7) {
        return CCCDInfoEntity(
          idNumber: parts[0],
          fullName: parts[2],
          dateOfBirth: parts[3],
          gender: parts[4],
          address: parts[5],
          issueDate: parts[6],
          issuePlace: parts.length > 7 ? parts[7] : '',
          expiryDate: '',
          nationality: null,
          religion: null,
          ethnicity: null,
        );
      }
    }
    // Không có fallback data, tr<PERSON> về null nếu không parse được
    return null;
  }

  /// Parse QR code data to any custom format
  /// You can extend this class to add more parse methods
  static T? parseCustom<T>(String raw, T Function(Map<String, dynamic> json) fromJson) {
    try {
      final map = jsonDecode(raw);
      if (map is Map<String, dynamic>) {
        return fromJson(map);
      }
    } catch (_) {
      // Handle other formats if needed
    }
    return null;
  }

  /// Parse simple string QR code
  /// Returns the raw string if it's not JSON
  static String parseString(String raw) {
    try {
      final map = jsonDecode(raw);
      if (map is Map<String, dynamic>) {
        // If it's JSON, return a string representation
        return raw;
      }
    } catch (_) {
      // If not JSON, return as is
    }
    return raw;
  }
} 