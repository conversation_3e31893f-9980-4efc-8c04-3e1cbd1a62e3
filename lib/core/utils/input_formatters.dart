import 'package:flutter/services.dart';

/// Chỉ cho nhập họ tên (chữ, d<PERSON>u, kho<PERSON><PERSON> trắng, nh<PERSON><PERSON> đơn, g<PERSON><PERSON> ngang)
final List<TextInputFormatter> nameInputFormatters = [
  FilteringTextInputFormatter.allow(RegExp(r"[a-zA-ZÀ-ỹ\s'\-]")),
  LengthLimitingTextInputFormatter(100),
];

/// Chỉ cho nhập số giấy tờ (tối đa 12 số)
final List<TextInputFormatter> documentNumberInputFormatters = [
  FilteringTextInputFormatter.digitsOnly,
  LengthLimitingTextInputFormatter(12),
];

/// Nơi cấp (tối đa 100 ký tự)
final List<TextInputFormatter> issuePlaceInputFormatters = [
  LengthLimitingTextInputFormatter(100),
];

/// Địa chỉ thường trú (tối đa 250 ký tự)
final List<TextInputFormatter> permanentAddressInputFormatters = [
  LengthLimitingTextInputFormatter(250),
];

/// Số điện thoại (chỉ số, tối đa 10 số)
final List<TextInputFormatter> phoneInputFormatters = [
  FilteringTextInputFormatter.digitsOnly,
  LengthLimitingTextInputFormatter(10),
];

/// Địa chỉ cụ thể (tối đa 250 ký tự)
final List<TextInputFormatter> specificAddressInputFormatters = [
  LengthLimitingTextInputFormatter(250),
]; 