import 'package:sales_app/core/enums/loan_term.dart';
import 'package:sales_app/domain/entities/common/system_config_entity.dart';

/// Utility class để map giữa SystemConfigEntity từ API và LoanTerm enum
/// 
/// Mapping rules dựa trên dữ liệu API trả về:
/// - code: 'DAYS_30' -> LoanTerm.days30
/// - code: 'DAYS_60' -> LoanTerm.days60
/// ...
class LoanTermMapper {
  /// Map từ SystemConfigEntity sang LoanTerm enum
  static LoanTerm? fromSystemConfig(SystemConfigEntity systemConfig) {
    switch (systemConfig.code) {
      case 'DAYS_30':
        return LoanTerm.days30;
      case 'DAYS_60':
        return LoanTerm.days60;
      case 'DAYS_90':
        return LoanTerm.days90;
      case 'DAYS_120':
        return LoanTerm.days120;
      case 'DAYS_150':
        return LoanTerm.days150;
      case 'DAYS_180':
        return LoanTerm.days180;
      case 'DAYS_210':
        return LoanTerm.days210;
      case 'DAYS_240':
        return LoanTerm.days240;
      case 'DAYS_270':
        return LoanTerm.days270;
      default:
        return null;
    }
  }

  /// Map từ LoanTerm enum sang SystemConfigEntity
  static SystemConfigEntity? toSystemConfig(
    LoanTerm loanTerm,
    List<SystemConfigEntity> systemConfigs,
  ) {
    String targetCode;
    switch (loanTerm) {
      case LoanTerm.days30:
        targetCode = 'DAYS_30';
        break;
      case LoanTerm.days60:
        targetCode = 'DAYS_60';
        break;
      case LoanTerm.days90:
        targetCode = 'DAYS_90';
        break;
      case LoanTerm.days120:
        targetCode = 'DAYS_120';
        break;
      case LoanTerm.days150:
        targetCode = 'DAYS_150';
        break;
      case LoanTerm.days180:
        targetCode = 'DAYS_180';
        break;
      case LoanTerm.days210:
        targetCode = 'DAYS_210';
        break;
      case LoanTerm.days240:
        targetCode = 'DAYS_240';
        break;
      case LoanTerm.days270:
        targetCode = 'DAYS_270';
        break;
    }
    try {
      return systemConfigs.firstWhere((config) => config.code == targetCode);
    } catch (e) {
      return null;
    }
  }

  /// Tìm LoanTerm mặc định (DAYS_30) từ danh sách SystemConfigEntity
  static SystemConfigEntity? getDefaultSystemConfig(List<SystemConfigEntity> systemConfigs) {
    try {
      return systemConfigs.firstWhere((config) => config.code == 'DAYS_30');
    } catch (e) {
      return null;
    }
  }
} 