import 'dart:io';
import 'package:image/image.dart' as img;

/// Resize ảnh về chiều rộng tối đa [maxWidth] (mặc định 1024px), gi<PERSON> nguyên tỉ lệ, chất lượ<PERSON> [quality] (mặc định 90).
/// Nếu ảnh đã nhỏ hơn maxWidth thì trả về file gốc.
Future<File> resizeImage(File file, {int maxWidth = 1024, int quality = 90}) async {
  final bytes = await file.readAsBytes();
  final image = img.decodeImage(bytes);
  if (image == null) throw Exception('Cannot decode image');
  if (image.width <= maxWidth) return file;
  final resized = img.copyResize(image, width: maxWidth);
  final resizedFile = File('${file.parent.path}/resized_${file.uri.pathSegments.last}');
  await resizedFile.writeAsBytes(img.encodeJpg(resized, quality: quality));
  return resizedFile;
} 