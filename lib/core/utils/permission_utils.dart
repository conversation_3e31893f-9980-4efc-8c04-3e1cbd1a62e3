import 'package:permission_handler/permission_handler.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';

/// Utility functions để xử lý permission một cách tiện lợi
class PermissionUtils {
  /// Kiểm tra xem permission status có được cấp quyền không
  static bool isGranted(PermissionStatus status) {
    return status.isGranted;
  }

  /// Kiểm tra xem permission status có bị từ chối không
  static bool isDenied(PermissionStatus status) {
    return status.isDenied;
  }

  /// Kiểm tra xem permission status có bị từ chối vĩnh viễn không
  static bool isPermanentlyDenied(PermissionStatus status) {
    return status.isPermanentlyDenied;
  }

  /// Kiểm tra xem permission status có bị hạn chế không (iOS)
  static bool isRestricted(PermissionStatus status) {
    return status.isRestricted;
  }

  /// Kiểm tra xem permission status có bị tạm thời không
  static bool isLimited(PermissionStatus status) {
    return status.isLimited;
  }

  /// Kiểm tra xem permission status có bị tạm thời không
  static bool isProvisional(PermissionStatus status) {
    return status.isProvisional;
  }

  /// Lấy tên hiển thị của permission status
  static String getStatusDisplayName(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Đã cấp quyền';
      case PermissionStatus.denied:
        return 'Bị từ chối';
      case PermissionStatus.permanentlyDenied:
        return 'Bị từ chối vĩnh viễn';
      case PermissionStatus.restricted:
        return 'Bị hạn chế';
      case PermissionStatus.limited:
        return 'Bị giới hạn';
      case PermissionStatus.provisional:
        return 'Tạm thời';
    }
  }

  /// Lấy mô tả chi tiết của permission status
  static String getStatusDescription(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Quyền đã được cấp và có thể sử dụng';
      case PermissionStatus.denied:
        return 'Quyền bị từ chối, có thể yêu cầu lại';
      case PermissionStatus.permanentlyDenied:
        return 'Quyền bị từ chối vĩnh viễn, cần vào cài đặt để cấp quyền';
      case PermissionStatus.restricted:
        return 'Quyền bị hạn chế bởi hệ thống';
      case PermissionStatus.limited:
        return 'Quyền bị giới hạn, chỉ có thể truy cập một phần';
      case PermissionStatus.provisional:
        return 'Quyền được cấp tạm thời';
    }
  }

  /// Lấy icon tương ứng với permission status
  static String getStatusIcon(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return '✅';
      case PermissionStatus.denied:
        return '❌';
      case PermissionStatus.permanentlyDenied:
        return '🚫';
      case PermissionStatus.restricted:
        return '⚠️';
      case PermissionStatus.limited:
        return '📊';
      case PermissionStatus.provisional:
        return '⏳';
    }
  }

  /// Lấy màu tương ứng với permission status
  static String getStatusColor(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return '#4CAF50'; // Green
      case PermissionStatus.denied:
        return '#FF9800'; // Orange
      case PermissionStatus.permanentlyDenied:
        return '#F44336'; // Red
      case PermissionStatus.restricted:
        return '#9C27B0'; // Purple
      case PermissionStatus.limited:
        return '#2196F3'; // Blue
      case PermissionStatus.provisional:
        return '#FFC107'; // Yellow
    }
  }

  /// Kiểm tra xem có cần hiển thị dialog giải thích permission không
  static bool shouldShowPermissionExplanation(PermissionStatus status) {
    return status.isDenied || status.isPermanentlyDenied;
  }

  /// Kiểm tra xem có cần mở cài đặt app không
  static bool shouldOpenAppSettings(PermissionStatus status) {
    return status.isPermanentlyDenied;
  }

  /// Lấy message phù hợp cho permission status
  static String getPermissionMessage(PermissionType permissionType, PermissionStatus status) {
    final permissionName = permissionType.displayName;
    final permissionDescription = permissionType.description;
    
    switch (status) {
      case PermissionStatus.granted:
        return 'Quyền $permissionName đã được cấp';
      case PermissionStatus.denied:
        return 'Cần cấp quyền $permissionName để sử dụng tính năng này. $permissionDescription';
      case PermissionStatus.permanentlyDenied:
        return 'Quyền $permissionName bị từ chối vĩnh viễn. Vui lòng vào Cài đặt > Ứng dụng > $permissionName để cấp quyền.';
      case PermissionStatus.restricted:
        return 'Quyền $permissionName bị hạn chế bởi hệ thống. Vui lòng kiểm tra cài đặt bảo mật.';
      case PermissionStatus.limited:
        return 'Quyền $permissionName bị giới hạn. Một số tính năng có thể không hoạt động đầy đủ.';
      case PermissionStatus.provisional:
        return 'Quyền $permissionName được cấp tạm thời. Có thể cần cấp quyền lại sau này.';
    }
  }

  /// Lấy action text phù hợp cho permission status
  static String getActionText(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Đã cấp quyền';
      case PermissionStatus.denied:
        return 'Cấp quyền';
      case PermissionStatus.permanentlyDenied:
        return 'Vào cài đặt';
      case PermissionStatus.restricted:
        return 'Kiểm tra cài đặt';
      case PermissionStatus.limited:
        return 'Cấp quyền đầy đủ';
      case PermissionStatus.provisional:
        return 'Cấp quyền vĩnh viễn';
    }
  }

  /// Kiểm tra xem có thể request permission lại không
  static bool canRequestAgain(PermissionStatus status) {
    return status.isDenied && !status.isPermanentlyDenied;
  }

  /// Lấy danh sách permission status được sắp xếp theo mức độ ưu tiên
  static List<PermissionStatus> getSortedStatuses(List<PermissionStatus> statuses) {
    // Sắp xếp theo thứ tự ưu tiên: granted > provisional > limited > denied > restricted > permanentlyDenied
    final priorityOrder = {
      PermissionStatus.granted: 0,
      PermissionStatus.provisional: 1,
      PermissionStatus.limited: 2,
      PermissionStatus.denied: 3,
      PermissionStatus.restricted: 4,
      PermissionStatus.permanentlyDenied: 5,
    };
    
    final sorted = List<PermissionStatus>.from(statuses);
    sorted.sort((a, b) => (priorityOrder[a] ?? 6).compareTo(priorityOrder[b] ?? 6));
    
    return sorted;
  }

  /// Lấy permission status có mức độ nghiêm trọng nhất
  static PermissionStatus getMostCriticalStatus(List<PermissionStatus> statuses) {
    if (statuses.isEmpty) return PermissionStatus.denied;
    
    final sorted = getSortedStatuses(statuses);
    return sorted.last; // Lấy status có priority thấp nhất (nghiêm trọng nhất)
  }

  /// Kiểm tra xem có permission nào cần xử lý đặc biệt không
  static bool hasCriticalPermissions(List<PermissionStatus> statuses) {
    return statuses.any((status) => 
      status.isPermanentlyDenied || 
      status.isRestricted
    );
  }

  /// Lấy danh sách permission cần xử lý đặc biệt
  static List<PermissionStatus> getCriticalStatuses(List<PermissionStatus> statuses) {
    return statuses.where((status) => 
      status.isPermanentlyDenied || 
      status.isRestricted
    ).toList();
  }
} 