/// Core Router Barrel Export
/// Centralized access to all routing and navigation components
/// 
/// This file provides a single import point for all routing functionality.
/// Import this file instead of importing individual router files.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/core/router/router.dart';
/// 
/// // Now you can use any routing component:
/// final route = AppRoutes.home;
/// ```
library core_router;

export 'app_routes.dart'; 