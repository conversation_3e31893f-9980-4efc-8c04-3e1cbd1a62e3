/// Core Error Handling Barrel Export
/// Centralized access to all error handling components
/// 
/// This file provides a single import point for all error handling functionality.
/// Import this file instead of importing individual error files.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/core/error/error.dart';
/// 
/// // Now you can use any error handling:
/// return Left(ServerFailure(ServerErrorType.timeout));
/// final errorCode = ErrorCodes.networkTimeout;
/// ```
library core_error;

export 'error_types.dart';
export 'error_codes.dart';
export 'failures.dart'; 