/// Specific error types for different failure categories
/// These enums define the exact type of error that occurred,
/// allowing for precise error message mapping in the presentation layer
library;

/// Server-related error types
enum ServerErrorType {
  /// Generic server error (500, 502, 503, 504)
  internalError,
  
  /// Resource not found (404)
  notFound,
  
  /// Too many requests (429)
  tooManyRequests,
  
  /// Bad request (400)
  badRequest,
  
  /// Unknown server error
  unknown,
}

/// Network-related error types
enum NetworkErrorType {
  /// Connection timeout
  connectionTimeout,
  
  /// Send timeout
  sendTimeout,
  
  /// Receive timeout
  receiveTimeout,
  
  /// No internet connection
  noConnection,
  
  /// Request was cancelled
  requestCancelled,
  
  /// Certificate verification failed
  certificateError,
  
  /// Unknown network error
  unknown,
}

/// Cache-related error types
enum CacheErrorType {
  /// Failed to read from cache
  readError,
  
  /// Failed to write to cache
  writeError,
  
  /// Cache data is corrupted
  dataCorrupted,
  
  /// Cache storage is full
  storageFull,
  
  /// Unknown cache error
  unknown,
}

/// Authentication-related error types
enum AuthErrorType {
  /// Invalid credentials (401)
  invalidCredentials,
  
  /// Access denied/forbidden (403)
  accessDenied,
  
  /// Token expired
  tokenExpired,
  
  /// User not found
  userNotFound,
  
  /// Account locked/disabled
  accountLocked,
  
  /// Unknown auth error
  unknown,
}

/// Validation-related error types
enum ValidationErrorType {
  /// Invalid email format
  invalidEmail,
  
  /// Invalid password format
  invalidPassword,
  
  /// Required field is empty
  requiredField,
  
  /// Field value is too short
  tooShort,
  
  /// Field value is too long
  tooLong,
  
  /// Invalid format (general)
  invalidFormat,
  
  /// Server validation error (422)
  serverValidation,
  
  /// Unknown validation error
  unknown,
}
