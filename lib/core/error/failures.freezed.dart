// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Failure {
  Enum get errorType => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        server,
    required TResult Function(NetworkErrorType errorType) network,
    required TResult Function(CacheErrorType errorType) cache,
    required TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        auth,
    required TResult Function(ValidationErrorType errorType,
            String? serverMessage, String? serverErrorCode)
        validation,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult? Function(NetworkErrorType errorType)? network,
    TResult? Function(CacheErrorType errorType)? cache,
    TResult? Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult? Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult Function(NetworkErrorType errorType)? network,
    TResult Function(CacheErrorType errorType)? cache,
    TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerFailure value) server,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(AuthFailure value) auth,
    required TResult Function(ValidationFailure value) validation,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerFailure value)? server,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(AuthFailure value)? auth,
    TResult? Function(ValidationFailure value)? validation,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerFailure value)? server,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(AuthFailure value)? auth,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FailureCopyWith<$Res> {
  factory $FailureCopyWith(Failure value, $Res Function(Failure) then) =
      _$FailureCopyWithImpl<$Res, Failure>;
}

/// @nodoc
class _$FailureCopyWithImpl<$Res, $Val extends Failure>
    implements $FailureCopyWith<$Res> {
  _$FailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ServerFailureImplCopyWith<$Res> {
  factory _$$ServerFailureImplCopyWith(
          _$ServerFailureImpl value, $Res Function(_$ServerFailureImpl) then) =
      __$$ServerFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {ServerErrorType errorType,
      String? serverMessage,
      String? serverErrorCode});
}

/// @nodoc
class __$$ServerFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$ServerFailureImpl>
    implements _$$ServerFailureImplCopyWith<$Res> {
  __$$ServerFailureImplCopyWithImpl(
      _$ServerFailureImpl _value, $Res Function(_$ServerFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorType = null,
    Object? serverMessage = freezed,
    Object? serverErrorCode = freezed,
  }) {
    return _then(_$ServerFailureImpl(
      null == errorType
          ? _value.errorType
          : errorType // ignore: cast_nullable_to_non_nullable
              as ServerErrorType,
      serverMessage: freezed == serverMessage
          ? _value.serverMessage
          : serverMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      serverErrorCode: freezed == serverErrorCode
          ? _value.serverErrorCode
          : serverErrorCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ServerFailureImpl implements ServerFailure {
  const _$ServerFailureImpl(this.errorType,
      {this.serverMessage, this.serverErrorCode});

  @override
  final ServerErrorType errorType;
  @override
  final String? serverMessage;
  @override
  final String? serverErrorCode;

  @override
  String toString() {
    return 'Failure.server(errorType: $errorType, serverMessage: $serverMessage, serverErrorCode: $serverErrorCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServerFailureImpl &&
            (identical(other.errorType, errorType) ||
                other.errorType == errorType) &&
            (identical(other.serverMessage, serverMessage) ||
                other.serverMessage == serverMessage) &&
            (identical(other.serverErrorCode, serverErrorCode) ||
                other.serverErrorCode == serverErrorCode));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, errorType, serverMessage, serverErrorCode);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ServerFailureImplCopyWith<_$ServerFailureImpl> get copyWith =>
      __$$ServerFailureImplCopyWithImpl<_$ServerFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        server,
    required TResult Function(NetworkErrorType errorType) network,
    required TResult Function(CacheErrorType errorType) cache,
    required TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        auth,
    required TResult Function(ValidationErrorType errorType,
            String? serverMessage, String? serverErrorCode)
        validation,
  }) {
    return server(errorType, serverMessage, serverErrorCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult? Function(NetworkErrorType errorType)? network,
    TResult? Function(CacheErrorType errorType)? cache,
    TResult? Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult? Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
  }) {
    return server?.call(errorType, serverMessage, serverErrorCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult Function(NetworkErrorType errorType)? network,
    TResult Function(CacheErrorType errorType)? cache,
    TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
    required TResult orElse(),
  }) {
    if (server != null) {
      return server(errorType, serverMessage, serverErrorCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerFailure value) server,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(AuthFailure value) auth,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return server(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerFailure value)? server,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(AuthFailure value)? auth,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return server?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerFailure value)? server,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(AuthFailure value)? auth,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (server != null) {
      return server(this);
    }
    return orElse();
  }
}

abstract class ServerFailure implements Failure {
  const factory ServerFailure(final ServerErrorType errorType,
      {final String? serverMessage,
      final String? serverErrorCode}) = _$ServerFailureImpl;

  @override
  ServerErrorType get errorType;
  String? get serverMessage;
  String? get serverErrorCode;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ServerFailureImplCopyWith<_$ServerFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NetworkFailureImplCopyWith<$Res> {
  factory _$$NetworkFailureImplCopyWith(_$NetworkFailureImpl value,
          $Res Function(_$NetworkFailureImpl) then) =
      __$$NetworkFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({NetworkErrorType errorType});
}

/// @nodoc
class __$$NetworkFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$NetworkFailureImpl>
    implements _$$NetworkFailureImplCopyWith<$Res> {
  __$$NetworkFailureImplCopyWithImpl(
      _$NetworkFailureImpl _value, $Res Function(_$NetworkFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorType = null,
  }) {
    return _then(_$NetworkFailureImpl(
      null == errorType
          ? _value.errorType
          : errorType // ignore: cast_nullable_to_non_nullable
              as NetworkErrorType,
    ));
  }
}

/// @nodoc

class _$NetworkFailureImpl implements NetworkFailure {
  const _$NetworkFailureImpl(this.errorType);

  @override
  final NetworkErrorType errorType;

  @override
  String toString() {
    return 'Failure.network(errorType: $errorType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkFailureImpl &&
            (identical(other.errorType, errorType) ||
                other.errorType == errorType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, errorType);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkFailureImplCopyWith<_$NetworkFailureImpl> get copyWith =>
      __$$NetworkFailureImplCopyWithImpl<_$NetworkFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        server,
    required TResult Function(NetworkErrorType errorType) network,
    required TResult Function(CacheErrorType errorType) cache,
    required TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        auth,
    required TResult Function(ValidationErrorType errorType,
            String? serverMessage, String? serverErrorCode)
        validation,
  }) {
    return network(errorType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult? Function(NetworkErrorType errorType)? network,
    TResult? Function(CacheErrorType errorType)? cache,
    TResult? Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult? Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
  }) {
    return network?.call(errorType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult Function(NetworkErrorType errorType)? network,
    TResult Function(CacheErrorType errorType)? cache,
    TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(errorType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerFailure value) server,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(AuthFailure value) auth,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return network(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerFailure value)? server,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(AuthFailure value)? auth,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return network?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerFailure value)? server,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(AuthFailure value)? auth,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(this);
    }
    return orElse();
  }
}

abstract class NetworkFailure implements Failure {
  const factory NetworkFailure(final NetworkErrorType errorType) =
      _$NetworkFailureImpl;

  @override
  NetworkErrorType get errorType;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkFailureImplCopyWith<_$NetworkFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CacheFailureImplCopyWith<$Res> {
  factory _$$CacheFailureImplCopyWith(
          _$CacheFailureImpl value, $Res Function(_$CacheFailureImpl) then) =
      __$$CacheFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CacheErrorType errorType});
}

/// @nodoc
class __$$CacheFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$CacheFailureImpl>
    implements _$$CacheFailureImplCopyWith<$Res> {
  __$$CacheFailureImplCopyWithImpl(
      _$CacheFailureImpl _value, $Res Function(_$CacheFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorType = null,
  }) {
    return _then(_$CacheFailureImpl(
      null == errorType
          ? _value.errorType
          : errorType // ignore: cast_nullable_to_non_nullable
              as CacheErrorType,
    ));
  }
}

/// @nodoc

class _$CacheFailureImpl implements CacheFailure {
  const _$CacheFailureImpl(this.errorType);

  @override
  final CacheErrorType errorType;

  @override
  String toString() {
    return 'Failure.cache(errorType: $errorType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CacheFailureImpl &&
            (identical(other.errorType, errorType) ||
                other.errorType == errorType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, errorType);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CacheFailureImplCopyWith<_$CacheFailureImpl> get copyWith =>
      __$$CacheFailureImplCopyWithImpl<_$CacheFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        server,
    required TResult Function(NetworkErrorType errorType) network,
    required TResult Function(CacheErrorType errorType) cache,
    required TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        auth,
    required TResult Function(ValidationErrorType errorType,
            String? serverMessage, String? serverErrorCode)
        validation,
  }) {
    return cache(errorType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult? Function(NetworkErrorType errorType)? network,
    TResult? Function(CacheErrorType errorType)? cache,
    TResult? Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult? Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
  }) {
    return cache?.call(errorType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult Function(NetworkErrorType errorType)? network,
    TResult Function(CacheErrorType errorType)? cache,
    TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
    required TResult orElse(),
  }) {
    if (cache != null) {
      return cache(errorType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerFailure value) server,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(AuthFailure value) auth,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return cache(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerFailure value)? server,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(AuthFailure value)? auth,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return cache?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerFailure value)? server,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(AuthFailure value)? auth,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (cache != null) {
      return cache(this);
    }
    return orElse();
  }
}

abstract class CacheFailure implements Failure {
  const factory CacheFailure(final CacheErrorType errorType) =
      _$CacheFailureImpl;

  @override
  CacheErrorType get errorType;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CacheFailureImplCopyWith<_$CacheFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AuthFailureImplCopyWith<$Res> {
  factory _$$AuthFailureImplCopyWith(
          _$AuthFailureImpl value, $Res Function(_$AuthFailureImpl) then) =
      __$$AuthFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {AuthErrorType errorType,
      String? serverMessage,
      String? serverErrorCode});
}

/// @nodoc
class __$$AuthFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$AuthFailureImpl>
    implements _$$AuthFailureImplCopyWith<$Res> {
  __$$AuthFailureImplCopyWithImpl(
      _$AuthFailureImpl _value, $Res Function(_$AuthFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorType = null,
    Object? serverMessage = freezed,
    Object? serverErrorCode = freezed,
  }) {
    return _then(_$AuthFailureImpl(
      null == errorType
          ? _value.errorType
          : errorType // ignore: cast_nullable_to_non_nullable
              as AuthErrorType,
      serverMessage: freezed == serverMessage
          ? _value.serverMessage
          : serverMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      serverErrorCode: freezed == serverErrorCode
          ? _value.serverErrorCode
          : serverErrorCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AuthFailureImpl implements AuthFailure {
  const _$AuthFailureImpl(this.errorType,
      {this.serverMessage, this.serverErrorCode});

  @override
  final AuthErrorType errorType;
  @override
  final String? serverMessage;
  @override
  final String? serverErrorCode;

  @override
  String toString() {
    return 'Failure.auth(errorType: $errorType, serverMessage: $serverMessage, serverErrorCode: $serverErrorCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthFailureImpl &&
            (identical(other.errorType, errorType) ||
                other.errorType == errorType) &&
            (identical(other.serverMessage, serverMessage) ||
                other.serverMessage == serverMessage) &&
            (identical(other.serverErrorCode, serverErrorCode) ||
                other.serverErrorCode == serverErrorCode));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, errorType, serverMessage, serverErrorCode);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthFailureImplCopyWith<_$AuthFailureImpl> get copyWith =>
      __$$AuthFailureImplCopyWithImpl<_$AuthFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        server,
    required TResult Function(NetworkErrorType errorType) network,
    required TResult Function(CacheErrorType errorType) cache,
    required TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        auth,
    required TResult Function(ValidationErrorType errorType,
            String? serverMessage, String? serverErrorCode)
        validation,
  }) {
    return auth(errorType, serverMessage, serverErrorCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult? Function(NetworkErrorType errorType)? network,
    TResult? Function(CacheErrorType errorType)? cache,
    TResult? Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult? Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
  }) {
    return auth?.call(errorType, serverMessage, serverErrorCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult Function(NetworkErrorType errorType)? network,
    TResult Function(CacheErrorType errorType)? cache,
    TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
    required TResult orElse(),
  }) {
    if (auth != null) {
      return auth(errorType, serverMessage, serverErrorCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerFailure value) server,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(AuthFailure value) auth,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return auth(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerFailure value)? server,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(AuthFailure value)? auth,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return auth?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerFailure value)? server,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(AuthFailure value)? auth,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (auth != null) {
      return auth(this);
    }
    return orElse();
  }
}

abstract class AuthFailure implements Failure {
  const factory AuthFailure(final AuthErrorType errorType,
      {final String? serverMessage,
      final String? serverErrorCode}) = _$AuthFailureImpl;

  @override
  AuthErrorType get errorType;
  String? get serverMessage;
  String? get serverErrorCode;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthFailureImplCopyWith<_$AuthFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ValidationFailureImplCopyWith<$Res> {
  factory _$$ValidationFailureImplCopyWith(_$ValidationFailureImpl value,
          $Res Function(_$ValidationFailureImpl) then) =
      __$$ValidationFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {ValidationErrorType errorType,
      String? serverMessage,
      String? serverErrorCode});
}

/// @nodoc
class __$$ValidationFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$ValidationFailureImpl>
    implements _$$ValidationFailureImplCopyWith<$Res> {
  __$$ValidationFailureImplCopyWithImpl(_$ValidationFailureImpl _value,
      $Res Function(_$ValidationFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorType = null,
    Object? serverMessage = freezed,
    Object? serverErrorCode = freezed,
  }) {
    return _then(_$ValidationFailureImpl(
      null == errorType
          ? _value.errorType
          : errorType // ignore: cast_nullable_to_non_nullable
              as ValidationErrorType,
      serverMessage: freezed == serverMessage
          ? _value.serverMessage
          : serverMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      serverErrorCode: freezed == serverErrorCode
          ? _value.serverErrorCode
          : serverErrorCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ValidationFailureImpl implements ValidationFailure {
  const _$ValidationFailureImpl(this.errorType,
      {this.serverMessage, this.serverErrorCode});

  @override
  final ValidationErrorType errorType;
  @override
  final String? serverMessage;
  @override
  final String? serverErrorCode;

  @override
  String toString() {
    return 'Failure.validation(errorType: $errorType, serverMessage: $serverMessage, serverErrorCode: $serverErrorCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidationFailureImpl &&
            (identical(other.errorType, errorType) ||
                other.errorType == errorType) &&
            (identical(other.serverMessage, serverMessage) ||
                other.serverMessage == serverMessage) &&
            (identical(other.serverErrorCode, serverErrorCode) ||
                other.serverErrorCode == serverErrorCode));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, errorType, serverMessage, serverErrorCode);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValidationFailureImplCopyWith<_$ValidationFailureImpl> get copyWith =>
      __$$ValidationFailureImplCopyWithImpl<_$ValidationFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        server,
    required TResult Function(NetworkErrorType errorType) network,
    required TResult Function(CacheErrorType errorType) cache,
    required TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)
        auth,
    required TResult Function(ValidationErrorType errorType,
            String? serverMessage, String? serverErrorCode)
        validation,
  }) {
    return validation(errorType, serverMessage, serverErrorCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult? Function(NetworkErrorType errorType)? network,
    TResult? Function(CacheErrorType errorType)? cache,
    TResult? Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult? Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
  }) {
    return validation?.call(errorType, serverMessage, serverErrorCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(ServerErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        server,
    TResult Function(NetworkErrorType errorType)? network,
    TResult Function(CacheErrorType errorType)? cache,
    TResult Function(AuthErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        auth,
    TResult Function(ValidationErrorType errorType, String? serverMessage,
            String? serverErrorCode)?
        validation,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(errorType, serverMessage, serverErrorCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerFailure value) server,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(AuthFailure value) auth,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return validation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerFailure value)? server,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(AuthFailure value)? auth,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return validation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerFailure value)? server,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(AuthFailure value)? auth,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(this);
    }
    return orElse();
  }
}

abstract class ValidationFailure implements Failure {
  const factory ValidationFailure(final ValidationErrorType errorType,
      {final String? serverMessage,
      final String? serverErrorCode}) = _$ValidationFailureImpl;

  @override
  ValidationErrorType get errorType;
  String? get serverMessage;
  String? get serverErrorCode;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValidationFailureImplCopyWith<_$ValidationFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
