import 'error_types.dart';

/// Error codes for different failure types
/// These constants define the error codes that will be displayed to users
/// Format: message (error code)

/// Server error codes (HTTP status codes)
class ServerErrorCodes {
  static const String internalError = '500';
  static const String notFound = '404';
  static const String tooManyRequests = '429';
  static const String badRequest = '400';
  static const String unknown = 'SRV_UNKNOWN';
}

/// Network error codes (custom codes for network issues)
class NetworkErrorCodes {
  static const String connectionTimeout = 'NET_TIMEOUT';
  static const String sendTimeout = 'NET_SEND_TIMEOUT';
  static const String receiveTimeout = 'NET_RECV_TIMEOUT';
  static const String noConnection = 'NET_NO_CONNECTION';
  static const String requestCancelled = 'NET_CANCELLED';
  static const String certificateError = 'NET_CERT_ERROR';
  static const String unknown = 'NET_UNKNOWN';
}

/// Cache error codes (custom codes for cache operations)
class CacheErrorCodes {
  static const String readError = 'CACHE_READ_ERROR';
  static const String writeError = 'CACHE_WRITE_ERROR';
  static const String dataCorrupted = 'CACHE_CORRUPTED';
  static const String storageFull = 'CACHE_FULL';
  static const String unknown = 'CACHE_UNKNOWN';
}

/// Authentication error codes (HTTP status codes + custom codes)
class AuthErrorCodes {
  static const String invalidCredentials = '401';
  static const String accessDenied = '403';
  static const String tokenExpired = 'AUTH_TOKEN_EXPIRED';
  static const String userNotFound = 'AUTH_USER_NOT_FOUND';
  static const String accountLocked = 'AUTH_ACCOUNT_LOCKED';
  static const String unknown = 'AUTH_UNKNOWN';
}

/// Validation error codes (custom codes for validation)
class ValidationErrorCodes {
  static const String invalidEmail = 'VAL_INVALID_EMAIL';
  static const String invalidPassword = 'VAL_INVALID_PASSWORD';
  static const String requiredField = 'VAL_REQUIRED_FIELD';
  static const String tooShort = 'VAL_TOO_SHORT';
  static const String tooLong = 'VAL_TOO_LONG';
  static const String invalidFormat = 'VAL_INVALID_FORMAT';
  static const String serverValidation = '422';
  static const String unknown = 'VAL_UNKNOWN';
}

/// Helper class to get error codes for different error types
class ErrorCodeMapper {
  /// Get error code for server error type
  static String getServerErrorCode(ServerErrorType errorType) {
    switch (errorType) {
      case ServerErrorType.internalError:
        return ServerErrorCodes.internalError;
      case ServerErrorType.notFound:
        return ServerErrorCodes.notFound;
      case ServerErrorType.tooManyRequests:
        return ServerErrorCodes.tooManyRequests;
      case ServerErrorType.badRequest:
        return ServerErrorCodes.badRequest;
      case ServerErrorType.unknown:
        return ServerErrorCodes.unknown;
    }
  }

  /// Get error code for network error type
  static String getNetworkErrorCode(NetworkErrorType errorType) {
    switch (errorType) {
      case NetworkErrorType.connectionTimeout:
        return NetworkErrorCodes.connectionTimeout;
      case NetworkErrorType.sendTimeout:
        return NetworkErrorCodes.sendTimeout;
      case NetworkErrorType.receiveTimeout:
        return NetworkErrorCodes.receiveTimeout;
      case NetworkErrorType.noConnection:
        return NetworkErrorCodes.noConnection;
      case NetworkErrorType.requestCancelled:
        return NetworkErrorCodes.requestCancelled;
      case NetworkErrorType.certificateError:
        return NetworkErrorCodes.certificateError;
      case NetworkErrorType.unknown:
        return NetworkErrorCodes.unknown;
    }
  }

  /// Get error code for cache error type
  static String getCacheErrorCode(CacheErrorType errorType) {
    switch (errorType) {
      case CacheErrorType.readError:
        return CacheErrorCodes.readError;
      case CacheErrorType.writeError:
        return CacheErrorCodes.writeError;
      case CacheErrorType.dataCorrupted:
        return CacheErrorCodes.dataCorrupted;
      case CacheErrorType.storageFull:
        return CacheErrorCodes.storageFull;
      case CacheErrorType.unknown:
        return CacheErrorCodes.unknown;
    }
  }

  /// Get error code for auth error type
  static String getAuthErrorCode(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.invalidCredentials:
        return AuthErrorCodes.invalidCredentials;
      case AuthErrorType.accessDenied:
        return AuthErrorCodes.accessDenied;
      case AuthErrorType.tokenExpired:
        return AuthErrorCodes.tokenExpired;
      case AuthErrorType.userNotFound:
        return AuthErrorCodes.userNotFound;
      case AuthErrorType.accountLocked:
        return AuthErrorCodes.accountLocked;
      case AuthErrorType.unknown:
        return AuthErrorCodes.unknown;
    }
  }

  /// Get error code for validation error type
  static String getValidationErrorCode(ValidationErrorType errorType) {
    switch (errorType) {
      case ValidationErrorType.invalidEmail:
        return ValidationErrorCodes.invalidEmail;
      case ValidationErrorType.invalidPassword:
        return ValidationErrorCodes.invalidPassword;
      case ValidationErrorType.requiredField:
        return ValidationErrorCodes.requiredField;
      case ValidationErrorType.tooShort:
        return ValidationErrorCodes.tooShort;
      case ValidationErrorType.tooLong:
        return ValidationErrorCodes.tooLong;
      case ValidationErrorType.invalidFormat:
        return ValidationErrorCodes.invalidFormat;
      case ValidationErrorType.serverValidation:
        return ValidationErrorCodes.serverValidation;
      case ValidationErrorType.unknown:
        return ValidationErrorCodes.unknown;
    }
  }
}
