import 'package:freezed_annotation/freezed_annotation.dart';
import 'error_types.dart';

part 'failures.freezed.dart';

@freezed
class Failure with _$Failure {
  const factory Failure.server(
    ServerErrorType errorType, {
    String? serverMessage,
    String? serverErrorCode,
  }) = ServerFailure;

  const factory Failure.network(NetworkErrorType errorType) = NetworkFailure;

  const factory Failure.cache(CacheErrorType errorType) = CacheFailure;

  const factory Failure.auth(
    AuthErrorType errorType, {
    String? serverMessage,
    String? serverErrorCode,
  }) = AuthFailure;

  const factory Failure.validation(
    ValidationErrorType errorType, {
    String? serverMessage,
    String? serverErrorCode,
  }) = ValidationFailure;
}