import 'package:flutter/material.dart';

class AppColors {
  // <PERSON><PERSON><PERSON> ch<PERSON>h của ứng dụng
  static const primaryColor = Color(0xFF292663); // <PERSON><PERSON>u xanh đậm KienlongBank
  static const secondaryColor = Color(0xFF1E88E5); // Màu xanh nhạt KienlongBank
  static const buttonColor = Color(0xFF13C7EE); // Màu button

  // Màu gradient
  static const gradientStart = Color(0xFF1E88E5);
  static const gradientEnd = Color(0xFF1565C0);

  // M<PERSON>u chữ
  static const textPrimary = Color(0xFF333333);
  static const textSecondary = Color(0xFF7F8C8D);
  static const textWhite = Colors.white;

  // Màu nền
  static const backgroundColor = Colors.white;
  static const surfaceColor = Color(0xFFF5F6F9);

  // Màu trạng thái
  static const success = Color(0xFF27AE60);
  static const error = Color(0xFFE74C3C);
  static const warning = Color(0xFFF1C40F);
  static const info = Color(0xFF3498DB);

  // <PERSON><PERSON><PERSON> bổ sung
  static const lightGray = Color(0xFFE8E8ED); // Màu xám nhạt cho container
  static const lightGreen = Color(0xFF4CAF50); // Màu xanh lá nhạt
  static const orange = Color(0xFFFF9800); // Màu cam
  static const lightBlue = Color(0xFF2196F3); // Màu xanh dương nhạt

  // Màu cơ bản thay thế Colors.xxx
  static const transparent = Colors.transparent;
  static const black = Colors.black;
  static const white = Colors.white;
  static const red = Colors.red;
  static const green = Colors.green;
  static const blue = Colors.blue;
  static const deepPurple = Colors.deepPurple;
  static const grey100 = Color(0xFFF5F5F5);
  static const grey300 = Color(0xFFE0E0E0);
  static const grey600 = Color(0xFF757575);
}