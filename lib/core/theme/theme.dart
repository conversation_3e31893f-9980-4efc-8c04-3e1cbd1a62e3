/// Core Theme Barrel Export
/// Centralized access to all theme and styling components
/// 
/// This file provides a single import point for all theme functionality.
/// Import this file instead of importing individual theme files.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/core/theme/theme.dart';
/// 
/// // Now you can use any theme component:
/// final primaryColor = AppColors.primaryColor;
/// final theme = AppTheme.lightTheme;
/// ```
library core_theme;

export 'app_theme.dart';
export 'app_color.dart'; 