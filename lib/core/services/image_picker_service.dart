import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/utils/app_logger.dart';

/// Service for handling image picking from camera or gallery
/// This is registered as a singleton because:
/// 1. The underlying ImagePicker is already a singleton
/// 2. Service is stateless and lightweight
/// 3. We want to ensure consistent usage across the app
@singleton
class ImagePickerService {
  // ImagePicker instance is already a singleton internally
  final _picker = ImagePicker();

  Future<String?> pickImage({
    required ImageSource source,
    double? maxWidth,
    double? maxHeight,
    int? quality,
  }) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        imageQuality: quality,
      );
      return image?.path;
    } catch (e) {
      AppLogger.error('Failed to pick image', error: e);
      return null;
    }
  }

  Future<String?> takePhoto({
    double? maxWidth,
    double? maxHeight,
    int? quality,
  }) async {
    return pickImage(
      source: ImageSource.camera,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      quality: quality,
    );
  }

  Future<String?> pickFromGallery({
    double? maxWidth,
    double? maxHeight,
    int? quality,
  }) async {
    return pickImage(
      source: ImageSource.gallery,
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      quality: quality,
    );
  }
} 