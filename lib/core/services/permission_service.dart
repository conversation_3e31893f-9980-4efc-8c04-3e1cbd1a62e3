import 'package:injectable/injectable.dart';
import 'package:permission_handler/permission_handler.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';

/// Service để quản lý permission trong app
@injectable
class PermissionService {
  /// Ki<PERSON>m tra trạng thái của một permission
  Future<PermissionStatus> checkPermission(PermissionType permissionType) async {
    try {
      AppLogger.debug('Checking permission: ${permissionType.key}');
      final status = await permissionType.permission.status;
      AppLogger.debug('Permission status: $status');
      return status;
    } catch (e) {
      AppLogger.error('Error checking permission: ${permissionType.key}', error: e);
      return PermissionStatus.denied;
    }
  }

  /// Kiểm tra trạng thái của nhiều permission
  Future<Map<PermissionType, PermissionStatus>> checkPermissions(
    List<PermissionType> permissionTypes,
  ) async {
    try {
      AppLogger.debug('Checking multiple permissions: ${permissionTypes.map((p) => p.key).join(', ')}');
      
      final results = <PermissionType, PermissionStatus>{};
      
      for (final permissionType in permissionTypes) {
        final status = await checkPermission(permissionType);
        results[permissionType] = status;
      }
      
      AppLogger.debug('Permission check results: $results');
      return results;
    } catch (e) {
      AppLogger.error('Error checking multiple permissions', error: e);
      return {for (final p in permissionTypes) p: PermissionStatus.denied};
    }
  }

  /// Yêu cầu một permission
  Future<PermissionStatus> requestPermission(PermissionType permissionType) async {
    try {
      AppLogger.info('Requesting permission: ${permissionType.key}');
      
      // Kiểm tra trạng thái hiện tại
      final currentStatus = await checkPermission(permissionType);
      
      // Nếu đã được cấp quyền, trả về ngay
      if (currentStatus.isGranted) {
        AppLogger.info('Permission already granted: ${permissionType.key}');
        return currentStatus;
      }
      
      // Nếu bị từ chối vĩnh viễn, không thể request lại
      if (currentStatus.isPermanentlyDenied) {
        AppLogger.warning('Permission permanently denied: ${permissionType.key}');
        return currentStatus;
      }
      
      // Request permission
      final status = await permissionType.permission.request();
      AppLogger.info('Permission request result: $status for ${permissionType.key}');
      
      return status;
    } catch (e) {
      AppLogger.error('Error requesting permission: ${permissionType.key}', error: e);
      return PermissionStatus.denied;
    }
  }

  /// Yêu cầu nhiều permission cùng lúc
  Future<Map<PermissionType, PermissionStatus>> requestPermissions(
    List<PermissionType> permissionTypes,
  ) async {
    try {
      AppLogger.info('Requesting multiple permissions: ${permissionTypes.map((p) => p.key).join(', ')}');
      
      final results = <PermissionType, PermissionStatus>{};
      
      for (final permissionType in permissionTypes) {
        final status = await requestPermission(permissionType);
        results[permissionType] = status;
      }
      
      AppLogger.info('Permission request results: $results');
      return results;
    } catch (e) {
      AppLogger.error('Error requesting multiple permissions', error: e);
      return {for (final p in permissionTypes) p: PermissionStatus.denied};
    }
  }

  /// Kiểm tra xem permission có được cấp quyền không
  Future<bool> isPermissionGranted(PermissionType permissionType) async {
    final status = await checkPermission(permissionType);
    return status.isGranted;
  }

  /// Kiểm tra xem tất cả permission có được cấp quyền không
  Future<bool> areAllPermissionsGranted(List<PermissionType> permissionTypes) async {
    final results = await checkPermissions(permissionTypes);
    return results.values.every((status) => status.isGranted);
  }

  /// Kiểm tra xem có permission nào bị từ chối vĩnh viễn không
  Future<bool> hasPermanentlyDeniedPermissions(List<PermissionType> permissionTypes) async {
    final results = await checkPermissions(permissionTypes);
    return results.values.any((status) => status.isPermanentlyDenied);
  }

  /// Lấy danh sách permission bị từ chối vĩnh viễn
  Future<List<PermissionType>> getPermanentlyDeniedPermissions(
    List<PermissionType> permissionTypes,
  ) async {
    final results = await checkPermissions(permissionTypes);
    return results.entries
        .where((entry) => entry.value.isPermanentlyDenied)
        .map((entry) => entry.key)
        .toList();
  }

  /// Mở cài đặt app để user có thể cấp quyền thủ công
  Future<bool> openSystemAppSettings() async {
    try {
      AppLogger.info('Opening app settings');
      // Gọi global function openAppSettings() của permission_handler
      final opened = await openAppSettings();
      AppLogger.info('App settings opened: $opened');
      return opened;
    } catch (e) {
      AppLogger.error('Error opening app settings', error: e);
      return false;
    }
  }

  /// Mở cài đặt permission cụ thể (chỉ hoạt động trên Android)
  Future<bool> openPermissionSettings(PermissionType permissionType) async {
    try {
      AppLogger.info('Opening permission settings for:  [${permissionType.key}');
      // permission_handler không có method mở settings cho permission cụ thể
      // Sử dụng openSystemAppSettings chung
      final opened = await openSystemAppSettings();
      AppLogger.info('Permission settings opened: $opened');
      return opened;
    } catch (e) {
      AppLogger.error('Error opening permission settings', error: e);
      return false;
    }
  }

  /// Kiểm tra xem permission có cần thiết cho app không
  bool isPermissionRequired(PermissionType permissionType) {
    // Danh sách permission cần thiết cho app
    const requiredPermissions = {
      PermissionType.camera,
      PermissionType.photos,
      PermissionType.photoLibrary,
      PermissionType.microphone,
      PermissionType.location,
      PermissionType.locationWhenInUse,
      PermissionType.storage,
    };
    
    return requiredPermissions.contains(permissionType);
  }

  /// Lấy danh sách permission cần thiết cho một tính năng cụ thể
  List<PermissionType> getRequiredPermissionsForFeature(String feature) {
    switch (feature.toLowerCase()) {
      case 'camera':
      case 'photo_capture':
        return [PermissionType.camera];
      
      case 'gallery':
      case 'photo_picker':
        return [PermissionType.photos, PermissionType.photoLibrary];
      
      case 'audio':
      case 'voice_recording':
        return [PermissionType.microphone];
      
      case 'location':
      case 'gps':
        return [PermissionType.location, PermissionType.locationWhenInUse];
      
      case 'file_storage':
      case 'document_upload':
        return [PermissionType.storage];
      
      case 'qr_scan':
        return [PermissionType.camera];
      
      case 'identity_verification':
        return [
          PermissionType.camera,
          PermissionType.photos,
          PermissionType.photoLibrary,
        ];
      
      default:
        return [];
    }
  }

  /// Kiểm tra và request permission cho một tính năng cụ thể
  Future<Map<PermissionType, PermissionStatus>> requestPermissionsForFeature(
    String feature,
  ) async {
    final requiredPermissions = getRequiredPermissionsForFeature(feature);
    
    if (requiredPermissions.isEmpty) {
      AppLogger.warning('No permissions required for feature: $feature');
      return {};
    }
    
    AppLogger.info('Requesting permissions for feature: $feature');
    return await requestPermissions(requiredPermissions);
  }

  /// Kiểm tra xem tất cả permission cần thiết cho tính năng có được cấp quyền không
  Future<bool> areFeaturePermissionsGranted(String feature) async {
    final requiredPermissions = getRequiredPermissionsForFeature(feature);
    
    if (requiredPermissions.isEmpty) {
      return true;
    }
    
    return await areAllPermissionsGranted(requiredPermissions);
  }
} 