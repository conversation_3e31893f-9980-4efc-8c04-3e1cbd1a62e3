import 'package:file_picker/file_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/utils/app_logger.dart';

/// Service for handling file picking
@singleton
class FilePickerService {
  /// Pick files with specific types and size limit
  Future<List<String>?> pickFiles({
    List<String>? allowedExtensions,
    int maxSizeInMB = 5,
    bool allowMultiple = true,
    FileType type = FileType.custom,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: allowMultiple,
        withData: false, // We only need file paths
      );

      if (result == null || result.files.isEmpty) {
        return null;
      }

      // Check file sizes
      final validFiles = <String>[];
      final maxSizeInBytes = maxSizeInMB * 1024 * 1024;

      for (final file in result.files) {
        if (file.path == null) continue;
        
        if (file.size > maxSizeInBytes) {
          AppLogger.warning('File ${file.name} exceeds size limit of ${maxSizeInMB}MB');
          continue;
        }
        
        validFiles.add(file.path!);
      }

      return validFiles.isEmpty ? null : validFiles;
    } catch (e) {
      AppLogger.error('Failed to pick files', error: e);
      return null;
    }
  }

  /// Pick images and PDFs (for document upload)
  Future<List<String>?> pickDocuments({
    int maxSizeInMB = 5,
    bool allowMultiple = true,
  }) async {
    return pickFiles(
      allowedExtensions: ['pdf', 'png', 'jpg', 'jpeg'],
      maxSizeInMB: maxSizeInMB,
      allowMultiple: allowMultiple,
      type: FileType.custom,
    );
  }

  /// Pick single document
  Future<String?> pickSingleDocument({
    int maxSizeInMB = 5,
  }) async {
    final files = await pickDocuments(
      maxSizeInMB: maxSizeInMB,
      allowMultiple: false,
    );
    return files?.first;
  }

  /// Pick images only
  Future<List<String>?> pickImages({
    int maxSizeInMB = 5,
    bool allowMultiple = true,
  }) async {
    return pickFiles(
      allowedExtensions: ['png', 'jpg', 'jpeg'],
      maxSizeInMB: maxSizeInMB,
      allowMultiple: allowMultiple,
      type: FileType.custom,
    );
  }

  /// Pick PDFs only
  Future<List<String>?> pickPDFs({
    int maxSizeInMB = 5,
    bool allowMultiple = true,
  }) async {
    return pickFiles(
      allowedExtensions: ['pdf'],
      maxSizeInMB: maxSizeInMB,
      allowMultiple: allowMultiple,
      type: FileType.custom,
    );
  }
}
