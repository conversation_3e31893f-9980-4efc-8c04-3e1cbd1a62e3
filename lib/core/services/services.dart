/// Core Services Barrel Export
/// Centralized access to all core service interfaces
/// 
/// This file provides a single import point for all service interfaces.
/// Import this file instead of importing individual service files.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/core/services/services.dart';
/// 
/// // Now you can use any service interface:
/// final imagePicker = getIt<ImagePickerService>();
/// final filePicker = getIt<FilePickerService>();
/// ```
library core_services;

export 'image_picker_service.dart';
export 'file_picker_service.dart';
export 'permission_service.dart'; 