// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_events.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AuthEvent authEvent) auth,
    required TResult Function(NetworkEvent networkEvent) network,
    required TResult Function(UIEvent uiEvent) ui,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AuthEvent authEvent)? auth,
    TResult? Function(NetworkEvent networkEvent)? network,
    TResult? Function(UIEvent uiEvent)? ui,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AuthEvent authEvent)? auth,
    TResult Function(NetworkEvent networkEvent)? network,
    TResult Function(UIEvent uiEvent)? ui,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthEvent value) auth,
    required TResult Function(_NetworkEvent value) network,
    required TResult Function(_UIEvent value) ui,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthEvent value)? auth,
    TResult? Function(_NetworkEvent value)? network,
    TResult? Function(_UIEvent value)? ui,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthEvent value)? auth,
    TResult Function(_NetworkEvent value)? network,
    TResult Function(_UIEvent value)? ui,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppEventCopyWith<$Res> {
  factory $AppEventCopyWith(AppEvent value, $Res Function(AppEvent) then) =
      _$AppEventCopyWithImpl<$Res, AppEvent>;
}

/// @nodoc
class _$AppEventCopyWithImpl<$Res, $Val extends AppEvent>
    implements $AppEventCopyWith<$Res> {
  _$AppEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$AuthEventImplCopyWith<$Res> {
  factory _$$AuthEventImplCopyWith(
          _$AuthEventImpl value, $Res Function(_$AuthEventImpl) then) =
      __$$AuthEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AuthEvent authEvent});

  $AuthEventCopyWith<$Res> get authEvent;
}

/// @nodoc
class __$$AuthEventImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$AuthEventImpl>
    implements _$$AuthEventImplCopyWith<$Res> {
  __$$AuthEventImplCopyWithImpl(
      _$AuthEventImpl _value, $Res Function(_$AuthEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authEvent = null,
  }) {
    return _then(_$AuthEventImpl(
      null == authEvent
          ? _value.authEvent
          : authEvent // ignore: cast_nullable_to_non_nullable
              as AuthEvent,
    ));
  }

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AuthEventCopyWith<$Res> get authEvent {
    return $AuthEventCopyWith<$Res>(_value.authEvent, (value) {
      return _then(_value.copyWith(authEvent: value));
    });
  }
}

/// @nodoc

class _$AuthEventImpl implements _AuthEvent {
  const _$AuthEventImpl(this.authEvent);

  @override
  final AuthEvent authEvent;

  @override
  String toString() {
    return 'AppEvent.auth(authEvent: $authEvent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthEventImpl &&
            (identical(other.authEvent, authEvent) ||
                other.authEvent == authEvent));
  }

  @override
  int get hashCode => Object.hash(runtimeType, authEvent);

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthEventImplCopyWith<_$AuthEventImpl> get copyWith =>
      __$$AuthEventImplCopyWithImpl<_$AuthEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AuthEvent authEvent) auth,
    required TResult Function(NetworkEvent networkEvent) network,
    required TResult Function(UIEvent uiEvent) ui,
  }) {
    return auth(authEvent);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AuthEvent authEvent)? auth,
    TResult? Function(NetworkEvent networkEvent)? network,
    TResult? Function(UIEvent uiEvent)? ui,
  }) {
    return auth?.call(authEvent);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AuthEvent authEvent)? auth,
    TResult Function(NetworkEvent networkEvent)? network,
    TResult Function(UIEvent uiEvent)? ui,
    required TResult orElse(),
  }) {
    if (auth != null) {
      return auth(authEvent);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthEvent value) auth,
    required TResult Function(_NetworkEvent value) network,
    required TResult Function(_UIEvent value) ui,
  }) {
    return auth(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthEvent value)? auth,
    TResult? Function(_NetworkEvent value)? network,
    TResult? Function(_UIEvent value)? ui,
  }) {
    return auth?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthEvent value)? auth,
    TResult Function(_NetworkEvent value)? network,
    TResult Function(_UIEvent value)? ui,
    required TResult orElse(),
  }) {
    if (auth != null) {
      return auth(this);
    }
    return orElse();
  }
}

abstract class _AuthEvent implements AppEvent {
  const factory _AuthEvent(final AuthEvent authEvent) = _$AuthEventImpl;

  AuthEvent get authEvent;

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthEventImplCopyWith<_$AuthEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NetworkEventImplCopyWith<$Res> {
  factory _$$NetworkEventImplCopyWith(
          _$NetworkEventImpl value, $Res Function(_$NetworkEventImpl) then) =
      __$$NetworkEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({NetworkEvent networkEvent});

  $NetworkEventCopyWith<$Res> get networkEvent;
}

/// @nodoc
class __$$NetworkEventImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$NetworkEventImpl>
    implements _$$NetworkEventImplCopyWith<$Res> {
  __$$NetworkEventImplCopyWithImpl(
      _$NetworkEventImpl _value, $Res Function(_$NetworkEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? networkEvent = null,
  }) {
    return _then(_$NetworkEventImpl(
      null == networkEvent
          ? _value.networkEvent
          : networkEvent // ignore: cast_nullable_to_non_nullable
              as NetworkEvent,
    ));
  }

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NetworkEventCopyWith<$Res> get networkEvent {
    return $NetworkEventCopyWith<$Res>(_value.networkEvent, (value) {
      return _then(_value.copyWith(networkEvent: value));
    });
  }
}

/// @nodoc

class _$NetworkEventImpl implements _NetworkEvent {
  const _$NetworkEventImpl(this.networkEvent);

  @override
  final NetworkEvent networkEvent;

  @override
  String toString() {
    return 'AppEvent.network(networkEvent: $networkEvent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkEventImpl &&
            (identical(other.networkEvent, networkEvent) ||
                other.networkEvent == networkEvent));
  }

  @override
  int get hashCode => Object.hash(runtimeType, networkEvent);

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkEventImplCopyWith<_$NetworkEventImpl> get copyWith =>
      __$$NetworkEventImplCopyWithImpl<_$NetworkEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AuthEvent authEvent) auth,
    required TResult Function(NetworkEvent networkEvent) network,
    required TResult Function(UIEvent uiEvent) ui,
  }) {
    return network(networkEvent);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AuthEvent authEvent)? auth,
    TResult? Function(NetworkEvent networkEvent)? network,
    TResult? Function(UIEvent uiEvent)? ui,
  }) {
    return network?.call(networkEvent);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AuthEvent authEvent)? auth,
    TResult Function(NetworkEvent networkEvent)? network,
    TResult Function(UIEvent uiEvent)? ui,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(networkEvent);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthEvent value) auth,
    required TResult Function(_NetworkEvent value) network,
    required TResult Function(_UIEvent value) ui,
  }) {
    return network(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthEvent value)? auth,
    TResult? Function(_NetworkEvent value)? network,
    TResult? Function(_UIEvent value)? ui,
  }) {
    return network?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthEvent value)? auth,
    TResult Function(_NetworkEvent value)? network,
    TResult Function(_UIEvent value)? ui,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(this);
    }
    return orElse();
  }
}

abstract class _NetworkEvent implements AppEvent {
  const factory _NetworkEvent(final NetworkEvent networkEvent) =
      _$NetworkEventImpl;

  NetworkEvent get networkEvent;

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkEventImplCopyWith<_$NetworkEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UIEventImplCopyWith<$Res> {
  factory _$$UIEventImplCopyWith(
          _$UIEventImpl value, $Res Function(_$UIEventImpl) then) =
      __$$UIEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({UIEvent uiEvent});

  $UIEventCopyWith<$Res> get uiEvent;
}

/// @nodoc
class __$$UIEventImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$UIEventImpl>
    implements _$$UIEventImplCopyWith<$Res> {
  __$$UIEventImplCopyWithImpl(
      _$UIEventImpl _value, $Res Function(_$UIEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uiEvent = null,
  }) {
    return _then(_$UIEventImpl(
      null == uiEvent
          ? _value.uiEvent
          : uiEvent // ignore: cast_nullable_to_non_nullable
              as UIEvent,
    ));
  }

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UIEventCopyWith<$Res> get uiEvent {
    return $UIEventCopyWith<$Res>(_value.uiEvent, (value) {
      return _then(_value.copyWith(uiEvent: value));
    });
  }
}

/// @nodoc

class _$UIEventImpl implements _UIEvent {
  const _$UIEventImpl(this.uiEvent);

  @override
  final UIEvent uiEvent;

  @override
  String toString() {
    return 'AppEvent.ui(uiEvent: $uiEvent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UIEventImpl &&
            (identical(other.uiEvent, uiEvent) || other.uiEvent == uiEvent));
  }

  @override
  int get hashCode => Object.hash(runtimeType, uiEvent);

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UIEventImplCopyWith<_$UIEventImpl> get copyWith =>
      __$$UIEventImplCopyWithImpl<_$UIEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AuthEvent authEvent) auth,
    required TResult Function(NetworkEvent networkEvent) network,
    required TResult Function(UIEvent uiEvent) ui,
  }) {
    return ui(uiEvent);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AuthEvent authEvent)? auth,
    TResult? Function(NetworkEvent networkEvent)? network,
    TResult? Function(UIEvent uiEvent)? ui,
  }) {
    return ui?.call(uiEvent);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AuthEvent authEvent)? auth,
    TResult Function(NetworkEvent networkEvent)? network,
    TResult Function(UIEvent uiEvent)? ui,
    required TResult orElse(),
  }) {
    if (ui != null) {
      return ui(uiEvent);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AuthEvent value) auth,
    required TResult Function(_NetworkEvent value) network,
    required TResult Function(_UIEvent value) ui,
  }) {
    return ui(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AuthEvent value)? auth,
    TResult? Function(_NetworkEvent value)? network,
    TResult? Function(_UIEvent value)? ui,
  }) {
    return ui?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AuthEvent value)? auth,
    TResult Function(_NetworkEvent value)? network,
    TResult Function(_UIEvent value)? ui,
    required TResult orElse(),
  }) {
    if (ui != null) {
      return ui(this);
    }
    return orElse();
  }
}

abstract class _UIEvent implements AppEvent {
  const factory _UIEvent(final UIEvent uiEvent) = _$UIEventImpl;

  UIEvent get uiEvent;

  /// Create a copy of AppEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UIEventImplCopyWith<_$UIEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AuthEvent {
  String? get reason => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? reason) sessionExpired,
    required TResult Function(String? reason) forceLogout,
    required TResult Function(String? reason) tokenRefreshFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? reason)? sessionExpired,
    TResult? Function(String? reason)? forceLogout,
    TResult? Function(String? reason)? tokenRefreshFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? reason)? sessionExpired,
    TResult Function(String? reason)? forceLogout,
    TResult Function(String? reason)? tokenRefreshFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SessionExpired value) sessionExpired,
    required TResult Function(_ForceLogout value) forceLogout,
    required TResult Function(_TokenRefreshFailed value) tokenRefreshFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SessionExpired value)? sessionExpired,
    TResult? Function(_ForceLogout value)? forceLogout,
    TResult? Function(_TokenRefreshFailed value)? tokenRefreshFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SessionExpired value)? sessionExpired,
    TResult Function(_ForceLogout value)? forceLogout,
    TResult Function(_TokenRefreshFailed value)? tokenRefreshFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthEventCopyWith<AuthEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthEventCopyWith<$Res> {
  factory $AuthEventCopyWith(AuthEvent value, $Res Function(AuthEvent) then) =
      _$AuthEventCopyWithImpl<$Res, AuthEvent>;
  @useResult
  $Res call({String? reason});
}

/// @nodoc
class _$AuthEventCopyWithImpl<$Res, $Val extends AuthEvent>
    implements $AuthEventCopyWith<$Res> {
  _$AuthEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reason = freezed,
  }) {
    return _then(_value.copyWith(
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionExpiredImplCopyWith<$Res>
    implements $AuthEventCopyWith<$Res> {
  factory _$$SessionExpiredImplCopyWith(_$SessionExpiredImpl value,
          $Res Function(_$SessionExpiredImpl) then) =
      __$$SessionExpiredImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? reason});
}

/// @nodoc
class __$$SessionExpiredImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SessionExpiredImpl>
    implements _$$SessionExpiredImplCopyWith<$Res> {
  __$$SessionExpiredImplCopyWithImpl(
      _$SessionExpiredImpl _value, $Res Function(_$SessionExpiredImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reason = freezed,
  }) {
    return _then(_$SessionExpiredImpl(
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SessionExpiredImpl implements _SessionExpired {
  const _$SessionExpiredImpl({this.reason});

  @override
  final String? reason;

  @override
  String toString() {
    return 'AuthEvent.sessionExpired(reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionExpiredImpl &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, reason);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionExpiredImplCopyWith<_$SessionExpiredImpl> get copyWith =>
      __$$SessionExpiredImplCopyWithImpl<_$SessionExpiredImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? reason) sessionExpired,
    required TResult Function(String? reason) forceLogout,
    required TResult Function(String? reason) tokenRefreshFailed,
  }) {
    return sessionExpired(reason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? reason)? sessionExpired,
    TResult? Function(String? reason)? forceLogout,
    TResult? Function(String? reason)? tokenRefreshFailed,
  }) {
    return sessionExpired?.call(reason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? reason)? sessionExpired,
    TResult Function(String? reason)? forceLogout,
    TResult Function(String? reason)? tokenRefreshFailed,
    required TResult orElse(),
  }) {
    if (sessionExpired != null) {
      return sessionExpired(reason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SessionExpired value) sessionExpired,
    required TResult Function(_ForceLogout value) forceLogout,
    required TResult Function(_TokenRefreshFailed value) tokenRefreshFailed,
  }) {
    return sessionExpired(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SessionExpired value)? sessionExpired,
    TResult? Function(_ForceLogout value)? forceLogout,
    TResult? Function(_TokenRefreshFailed value)? tokenRefreshFailed,
  }) {
    return sessionExpired?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SessionExpired value)? sessionExpired,
    TResult Function(_ForceLogout value)? forceLogout,
    TResult Function(_TokenRefreshFailed value)? tokenRefreshFailed,
    required TResult orElse(),
  }) {
    if (sessionExpired != null) {
      return sessionExpired(this);
    }
    return orElse();
  }
}

abstract class _SessionExpired implements AuthEvent {
  const factory _SessionExpired({final String? reason}) = _$SessionExpiredImpl;

  @override
  String? get reason;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SessionExpiredImplCopyWith<_$SessionExpiredImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ForceLogoutImplCopyWith<$Res>
    implements $AuthEventCopyWith<$Res> {
  factory _$$ForceLogoutImplCopyWith(
          _$ForceLogoutImpl value, $Res Function(_$ForceLogoutImpl) then) =
      __$$ForceLogoutImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? reason});
}

/// @nodoc
class __$$ForceLogoutImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$ForceLogoutImpl>
    implements _$$ForceLogoutImplCopyWith<$Res> {
  __$$ForceLogoutImplCopyWithImpl(
      _$ForceLogoutImpl _value, $Res Function(_$ForceLogoutImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reason = freezed,
  }) {
    return _then(_$ForceLogoutImpl(
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ForceLogoutImpl implements _ForceLogout {
  const _$ForceLogoutImpl({this.reason});

  @override
  final String? reason;

  @override
  String toString() {
    return 'AuthEvent.forceLogout(reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForceLogoutImpl &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, reason);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ForceLogoutImplCopyWith<_$ForceLogoutImpl> get copyWith =>
      __$$ForceLogoutImplCopyWithImpl<_$ForceLogoutImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? reason) sessionExpired,
    required TResult Function(String? reason) forceLogout,
    required TResult Function(String? reason) tokenRefreshFailed,
  }) {
    return forceLogout(reason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? reason)? sessionExpired,
    TResult? Function(String? reason)? forceLogout,
    TResult? Function(String? reason)? tokenRefreshFailed,
  }) {
    return forceLogout?.call(reason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? reason)? sessionExpired,
    TResult Function(String? reason)? forceLogout,
    TResult Function(String? reason)? tokenRefreshFailed,
    required TResult orElse(),
  }) {
    if (forceLogout != null) {
      return forceLogout(reason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SessionExpired value) sessionExpired,
    required TResult Function(_ForceLogout value) forceLogout,
    required TResult Function(_TokenRefreshFailed value) tokenRefreshFailed,
  }) {
    return forceLogout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SessionExpired value)? sessionExpired,
    TResult? Function(_ForceLogout value)? forceLogout,
    TResult? Function(_TokenRefreshFailed value)? tokenRefreshFailed,
  }) {
    return forceLogout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SessionExpired value)? sessionExpired,
    TResult Function(_ForceLogout value)? forceLogout,
    TResult Function(_TokenRefreshFailed value)? tokenRefreshFailed,
    required TResult orElse(),
  }) {
    if (forceLogout != null) {
      return forceLogout(this);
    }
    return orElse();
  }
}

abstract class _ForceLogout implements AuthEvent {
  const factory _ForceLogout({final String? reason}) = _$ForceLogoutImpl;

  @override
  String? get reason;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ForceLogoutImplCopyWith<_$ForceLogoutImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TokenRefreshFailedImplCopyWith<$Res>
    implements $AuthEventCopyWith<$Res> {
  factory _$$TokenRefreshFailedImplCopyWith(_$TokenRefreshFailedImpl value,
          $Res Function(_$TokenRefreshFailedImpl) then) =
      __$$TokenRefreshFailedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? reason});
}

/// @nodoc
class __$$TokenRefreshFailedImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$TokenRefreshFailedImpl>
    implements _$$TokenRefreshFailedImplCopyWith<$Res> {
  __$$TokenRefreshFailedImplCopyWithImpl(_$TokenRefreshFailedImpl _value,
      $Res Function(_$TokenRefreshFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reason = freezed,
  }) {
    return _then(_$TokenRefreshFailedImpl(
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$TokenRefreshFailedImpl implements _TokenRefreshFailed {
  const _$TokenRefreshFailedImpl({this.reason});

  @override
  final String? reason;

  @override
  String toString() {
    return 'AuthEvent.tokenRefreshFailed(reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TokenRefreshFailedImpl &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, reason);

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TokenRefreshFailedImplCopyWith<_$TokenRefreshFailedImpl> get copyWith =>
      __$$TokenRefreshFailedImplCopyWithImpl<_$TokenRefreshFailedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? reason) sessionExpired,
    required TResult Function(String? reason) forceLogout,
    required TResult Function(String? reason) tokenRefreshFailed,
  }) {
    return tokenRefreshFailed(reason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? reason)? sessionExpired,
    TResult? Function(String? reason)? forceLogout,
    TResult? Function(String? reason)? tokenRefreshFailed,
  }) {
    return tokenRefreshFailed?.call(reason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? reason)? sessionExpired,
    TResult Function(String? reason)? forceLogout,
    TResult Function(String? reason)? tokenRefreshFailed,
    required TResult orElse(),
  }) {
    if (tokenRefreshFailed != null) {
      return tokenRefreshFailed(reason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SessionExpired value) sessionExpired,
    required TResult Function(_ForceLogout value) forceLogout,
    required TResult Function(_TokenRefreshFailed value) tokenRefreshFailed,
  }) {
    return tokenRefreshFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SessionExpired value)? sessionExpired,
    TResult? Function(_ForceLogout value)? forceLogout,
    TResult? Function(_TokenRefreshFailed value)? tokenRefreshFailed,
  }) {
    return tokenRefreshFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SessionExpired value)? sessionExpired,
    TResult Function(_ForceLogout value)? forceLogout,
    TResult Function(_TokenRefreshFailed value)? tokenRefreshFailed,
    required TResult orElse(),
  }) {
    if (tokenRefreshFailed != null) {
      return tokenRefreshFailed(this);
    }
    return orElse();
  }
}

abstract class _TokenRefreshFailed implements AuthEvent {
  const factory _TokenRefreshFailed({final String? reason}) =
      _$TokenRefreshFailedImpl;

  @override
  String? get reason;

  /// Create a copy of AuthEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TokenRefreshFailedImplCopyWith<_$TokenRefreshFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$NetworkEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() connectionLost,
    required TResult Function() connectionRestored,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? connectionLost,
    TResult? Function()? connectionRestored,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? connectionLost,
    TResult Function()? connectionRestored,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConnectionLost value) connectionLost,
    required TResult Function(_ConnectionRestored value) connectionRestored,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConnectionLost value)? connectionLost,
    TResult? Function(_ConnectionRestored value)? connectionRestored,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConnectionLost value)? connectionLost,
    TResult Function(_ConnectionRestored value)? connectionRestored,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NetworkEventCopyWith<$Res> {
  factory $NetworkEventCopyWith(
          NetworkEvent value, $Res Function(NetworkEvent) then) =
      _$NetworkEventCopyWithImpl<$Res, NetworkEvent>;
}

/// @nodoc
class _$NetworkEventCopyWithImpl<$Res, $Val extends NetworkEvent>
    implements $NetworkEventCopyWith<$Res> {
  _$NetworkEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NetworkEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ConnectionLostImplCopyWith<$Res> {
  factory _$$ConnectionLostImplCopyWith(_$ConnectionLostImpl value,
          $Res Function(_$ConnectionLostImpl) then) =
      __$$ConnectionLostImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConnectionLostImplCopyWithImpl<$Res>
    extends _$NetworkEventCopyWithImpl<$Res, _$ConnectionLostImpl>
    implements _$$ConnectionLostImplCopyWith<$Res> {
  __$$ConnectionLostImplCopyWithImpl(
      _$ConnectionLostImpl _value, $Res Function(_$ConnectionLostImpl) _then)
      : super(_value, _then);

  /// Create a copy of NetworkEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConnectionLostImpl implements _ConnectionLost {
  const _$ConnectionLostImpl();

  @override
  String toString() {
    return 'NetworkEvent.connectionLost()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ConnectionLostImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() connectionLost,
    required TResult Function() connectionRestored,
  }) {
    return connectionLost();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? connectionLost,
    TResult? Function()? connectionRestored,
  }) {
    return connectionLost?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? connectionLost,
    TResult Function()? connectionRestored,
    required TResult orElse(),
  }) {
    if (connectionLost != null) {
      return connectionLost();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConnectionLost value) connectionLost,
    required TResult Function(_ConnectionRestored value) connectionRestored,
  }) {
    return connectionLost(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConnectionLost value)? connectionLost,
    TResult? Function(_ConnectionRestored value)? connectionRestored,
  }) {
    return connectionLost?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConnectionLost value)? connectionLost,
    TResult Function(_ConnectionRestored value)? connectionRestored,
    required TResult orElse(),
  }) {
    if (connectionLost != null) {
      return connectionLost(this);
    }
    return orElse();
  }
}

abstract class _ConnectionLost implements NetworkEvent {
  const factory _ConnectionLost() = _$ConnectionLostImpl;
}

/// @nodoc
abstract class _$$ConnectionRestoredImplCopyWith<$Res> {
  factory _$$ConnectionRestoredImplCopyWith(_$ConnectionRestoredImpl value,
          $Res Function(_$ConnectionRestoredImpl) then) =
      __$$ConnectionRestoredImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConnectionRestoredImplCopyWithImpl<$Res>
    extends _$NetworkEventCopyWithImpl<$Res, _$ConnectionRestoredImpl>
    implements _$$ConnectionRestoredImplCopyWith<$Res> {
  __$$ConnectionRestoredImplCopyWithImpl(_$ConnectionRestoredImpl _value,
      $Res Function(_$ConnectionRestoredImpl) _then)
      : super(_value, _then);

  /// Create a copy of NetworkEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConnectionRestoredImpl implements _ConnectionRestored {
  const _$ConnectionRestoredImpl();

  @override
  String toString() {
    return 'NetworkEvent.connectionRestored()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ConnectionRestoredImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() connectionLost,
    required TResult Function() connectionRestored,
  }) {
    return connectionRestored();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? connectionLost,
    TResult? Function()? connectionRestored,
  }) {
    return connectionRestored?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? connectionLost,
    TResult Function()? connectionRestored,
    required TResult orElse(),
  }) {
    if (connectionRestored != null) {
      return connectionRestored();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConnectionLost value) connectionLost,
    required TResult Function(_ConnectionRestored value) connectionRestored,
  }) {
    return connectionRestored(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConnectionLost value)? connectionLost,
    TResult? Function(_ConnectionRestored value)? connectionRestored,
  }) {
    return connectionRestored?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConnectionLost value)? connectionLost,
    TResult Function(_ConnectionRestored value)? connectionRestored,
    required TResult orElse(),
  }) {
    if (connectionRestored != null) {
      return connectionRestored(this);
    }
    return orElse();
  }
}

abstract class _ConnectionRestored implements NetworkEvent {
  const factory _ConnectionRestored() = _$ConnectionRestoredImpl;
}

/// @nodoc
mixin _$UIEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) showMessage,
    required TResult Function() hideLoading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? showMessage,
    TResult? Function()? hideLoading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? showMessage,
    TResult Function()? hideLoading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ShowMessage value) showMessage,
    required TResult Function(_HideLoading value) hideLoading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ShowMessage value)? showMessage,
    TResult? Function(_HideLoading value)? hideLoading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ShowMessage value)? showMessage,
    TResult Function(_HideLoading value)? hideLoading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UIEventCopyWith<$Res> {
  factory $UIEventCopyWith(UIEvent value, $Res Function(UIEvent) then) =
      _$UIEventCopyWithImpl<$Res, UIEvent>;
}

/// @nodoc
class _$UIEventCopyWithImpl<$Res, $Val extends UIEvent>
    implements $UIEventCopyWith<$Res> {
  _$UIEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UIEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ShowMessageImplCopyWith<$Res> {
  factory _$$ShowMessageImplCopyWith(
          _$ShowMessageImpl value, $Res Function(_$ShowMessageImpl) then) =
      __$$ShowMessageImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ShowMessageImplCopyWithImpl<$Res>
    extends _$UIEventCopyWithImpl<$Res, _$ShowMessageImpl>
    implements _$$ShowMessageImplCopyWith<$Res> {
  __$$ShowMessageImplCopyWithImpl(
      _$ShowMessageImpl _value, $Res Function(_$ShowMessageImpl) _then)
      : super(_value, _then);

  /// Create a copy of UIEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ShowMessageImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ShowMessageImpl implements _ShowMessage {
  const _$ShowMessageImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'UIEvent.showMessage(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShowMessageImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of UIEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShowMessageImplCopyWith<_$ShowMessageImpl> get copyWith =>
      __$$ShowMessageImplCopyWithImpl<_$ShowMessageImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) showMessage,
    required TResult Function() hideLoading,
  }) {
    return showMessage(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? showMessage,
    TResult? Function()? hideLoading,
  }) {
    return showMessage?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? showMessage,
    TResult Function()? hideLoading,
    required TResult orElse(),
  }) {
    if (showMessage != null) {
      return showMessage(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ShowMessage value) showMessage,
    required TResult Function(_HideLoading value) hideLoading,
  }) {
    return showMessage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ShowMessage value)? showMessage,
    TResult? Function(_HideLoading value)? hideLoading,
  }) {
    return showMessage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ShowMessage value)? showMessage,
    TResult Function(_HideLoading value)? hideLoading,
    required TResult orElse(),
  }) {
    if (showMessage != null) {
      return showMessage(this);
    }
    return orElse();
  }
}

abstract class _ShowMessage implements UIEvent {
  const factory _ShowMessage(final String message) = _$ShowMessageImpl;

  String get message;

  /// Create a copy of UIEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShowMessageImplCopyWith<_$ShowMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$HideLoadingImplCopyWith<$Res> {
  factory _$$HideLoadingImplCopyWith(
          _$HideLoadingImpl value, $Res Function(_$HideLoadingImpl) then) =
      __$$HideLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HideLoadingImplCopyWithImpl<$Res>
    extends _$UIEventCopyWithImpl<$Res, _$HideLoadingImpl>
    implements _$$HideLoadingImplCopyWith<$Res> {
  __$$HideLoadingImplCopyWithImpl(
      _$HideLoadingImpl _value, $Res Function(_$HideLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of UIEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$HideLoadingImpl implements _HideLoading {
  const _$HideLoadingImpl();

  @override
  String toString() {
    return 'UIEvent.hideLoading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$HideLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) showMessage,
    required TResult Function() hideLoading,
  }) {
    return hideLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? showMessage,
    TResult? Function()? hideLoading,
  }) {
    return hideLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? showMessage,
    TResult Function()? hideLoading,
    required TResult orElse(),
  }) {
    if (hideLoading != null) {
      return hideLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ShowMessage value) showMessage,
    required TResult Function(_HideLoading value) hideLoading,
  }) {
    return hideLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ShowMessage value)? showMessage,
    TResult? Function(_HideLoading value)? hideLoading,
  }) {
    return hideLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ShowMessage value)? showMessage,
    TResult Function(_HideLoading value)? hideLoading,
    required TResult orElse(),
  }) {
    if (hideLoading != null) {
      return hideLoading(this);
    }
    return orElse();
  }
}

abstract class _HideLoading implements UIEvent {
  const factory _HideLoading() = _$HideLoadingImpl;
}
