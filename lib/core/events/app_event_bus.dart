import 'dart:async';
import 'package:injectable/injectable.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';

/// Global Event Bus for application-wide event communication
/// 
/// This service provides a centralized way to emit and listen to events
/// across different layers of the application while maintaining Clean Architecture.
/// 
/// Features:
/// - Type-safe event system using sealed classes
/// - Broadcast streams for multiple listeners
/// - Automatic logging of events
/// - Memory leak prevention with proper disposal
/// 
/// Usage:
/// ```dart
/// // Emit an event
/// eventBus.emit(AppEvent.auth(AuthEvent.sessionExpired()));
/// 
/// // Listen to events
/// eventBus.events.listen((event) {
///   event.when(
///     auth: (authEvent) => handleAuthEvent(authEvent),
///     network: (networkEvent) => handleNetworkEvent(networkEvent),
///     ui: (uiEvent) => handleUIEvent(uiEvent),
///   );
/// });
/// 
/// // Listen to specific event types
/// eventBus.authEvents.listen((authEvent) {
///   authEvent.when(
///     sessionExpired: (reason) => handleSessionExpired(reason),
///     forceLogout: (reason) => handleForceLogout(reason),
///     tokenRefreshFailed: (reason) => handleTokenRefreshFailed(reason),
///   );
/// });
/// ```
@lazySingleton
class AppEventBus {
  // Main event stream controller
  final StreamController<AppEvent> _eventController = 
      StreamController<AppEvent>.broadcast();

  /// Stream of all application events
  Stream<AppEvent> get events => _eventController.stream;

  /// Stream of authentication events only
  Stream<AuthEvent> get authEvents => events
      .where((event) => event.maybeWhen(
            auth: (_) => true,
            orElse: () => false,
          ))
      .map((event) => event.when(
            auth: (authEvent) => authEvent,
            network: (_) => throw StateError('Should not reach here'),
            ui: (_) => throw StateError('Should not reach here'),
          ));

  /// Stream of network events only
  Stream<NetworkEvent> get networkEvents => events
      .where((event) => event.maybeWhen(
            network: (_) => true,
            orElse: () => false,
          ))
      .map((event) => event.when(
            auth: (_) => throw StateError('Should not reach here'),
            network: (networkEvent) => networkEvent,
            ui: (_) => throw StateError('Should not reach here'),
          ));

  /// Stream of UI events only
  Stream<UIEvent> get uiEvents => events
      .where((event) => event.maybeWhen(
            ui: (_) => true,
            orElse: () => false,
          ))
      .map((event) => event.when(
            auth: (_) => throw StateError('Should not reach here'),
            network: (_) => throw StateError('Should not reach here'),
            ui: (uiEvent) => uiEvent,
          ));

  /// Emit an event to all listeners
  void emit(AppEvent event) {
    if (_eventController.isClosed) {
      AppLogger.warning('AppEventBus: Attempted to emit event on closed controller');
      return;
    }

    AppLogger.event('AppEventBus: Event emitted', data: {
      'eventType': event.runtimeType.toString(),
      'event': event.toString(),
    });

    _eventController.add(event);
  }

  /// Emit an authentication event
  void emitAuthEvent(AuthEvent authEvent) {
    emit(AppEvent.auth(authEvent));
  }

  /// Emit a network event
  void emitNetworkEvent(NetworkEvent networkEvent) {
    emit(AppEvent.network(networkEvent));
  }

  /// Emit a UI event
  void emitUIEvent(UIEvent uiEvent) {
    emit(AppEvent.ui(uiEvent));
  }

  /// Dispose the event bus and close all streams
  void dispose() {
    AppLogger.info('AppEventBus: Disposing event bus');
    _eventController.close();
  }

  /// Check if the event bus is active
  bool get isActive => !_eventController.isClosed;

  /// Get the number of listeners (for debugging)
  bool get hasListeners => _eventController.hasListener;
}
