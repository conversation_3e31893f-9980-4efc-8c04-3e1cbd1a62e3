/// Events Layer Barrel Export
/// Single import point for all event-related functionality
/// 
/// This file provides centralized access to:
/// - Event definitions (AppEvent, AuthEvent, NetworkEvent, UIEvent)
/// - Event bus service (AppEventBus)
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/core/events/events.dart';
/// 
/// // Emit events
/// eventBus.emitAuthEvent(AuthEvent.sessionExpired());
/// 
/// // Listen to events
/// eventBus.authEvents.listen((authEvent) {
///   // Handle auth events
/// });
/// ```

library events;

// Event definitions
export 'app_events.dart';

// Event bus service
export 'app_event_bus.dart';
