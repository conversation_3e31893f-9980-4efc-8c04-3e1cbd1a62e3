import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_events.freezed.dart';

/// Base class for all application events
/// Used with AppEventBus for global event communication
@freezed
class AppEvent with _$AppEvent {
  /// Authentication related events
  const factory AppEvent.auth(AuthEvent authEvent) = _AuthEvent;
  
  /// Network related events (for future use)
  const factory AppEvent.network(NetworkEvent networkEvent) = _NetworkEvent;
  
  /// UI related events (for future use)
  const factory AppEvent.ui(UIEvent uiEvent) = _UIEvent;
}

/// Authentication events
@freezed
class AuthEvent with _$AuthEvent {
  /// User session has expired (refresh token invalid)
  /// Should trigger logout and show session expired dialog
  const factory AuthEvent.sessionExpired({
    String? reason,
  }) = _SessionExpired;
  
  /// Force logout event (for manual logout or security reasons)
  /// Should trigger logout without showing session expired dialog
  const factory AuthEvent.forceLogout({
    String? reason,
  }) = _ForceLogout;
  
  /// Token refresh failed event (for analytics/logging)
  const factory AuthEvent.tokenRefreshFailed({
    String? reason,
  }) = _TokenRefreshFailed;
}

/// Network events (for future expansion)
@freezed
class NetworkEvent with _$NetworkEvent {
  const factory NetworkEvent.connectionLost() = _ConnectionLost;
  const factory NetworkEvent.connectionRestored() = _ConnectionRestored;
}

/// UI events (for future expansion)
@freezed
class UIEvent with _$UIEvent {
  const factory UIEvent.showMessage(String message) = _ShowMessage;
  const factory UIEvent.hideLoading() = _HideLoading;
}
