/// Core Config Barrel Export
/// Centralized access to all configuration components
/// 
/// This file provides a single import point for all configuration functionality.
/// Import this file instead of importing individual config files.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/core/config/config.dart';
/// 
/// // Now you can use any config component:
/// final config = AppConfig.instance;
/// ```
library core_config;

export 'app_config.dart'; 