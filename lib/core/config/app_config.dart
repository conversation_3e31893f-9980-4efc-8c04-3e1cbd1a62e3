enum Flavor {
  development,
  staging,
  production,
}

class AppConfig {
  static Flavor? appFlavor;

  static String get name => appFlavor?.name ?? '';

  static String get title {
    switch (appFlavor) {
      case Flavor.development:
        return 'Sales App Dev';
      case Flavor.staging:
        return 'Sales App Staging';
      case Flavor.production:
        return 'Sales App';
      default:
        return 'title';
    }
  }

  static String get baseUrl {
    switch (appFlavor) {
      case Flavor.development:
        return 'https://api-staging.kienlongbank.co';
      case Flavor.staging:
        return 'https://api-staging.kienlongbank.co';
      case Flavor.production:
        return 'https://api.kienlongbank.com';
      default:
        return 'https://api.kienlongbank.com';
    }
  }

  static String get databaseName {
    switch (appFlavor) {
      case Flavor.development:
        return 'sales_app_dev.db';
      case Flavor.staging:
        return 'sales_app_staging.db';
      case Flavor.production:
        return 'sales_app.db';
      default:
        return 'sales_app.db';
    }
  }

  static bool get enableLogging {
    switch (appFlavor) {
      case Flavor.development:
      case Flavor.staging:
        return true;
      case Flavor.production:
        return false;
      default:
        return false;
    }
  }

  static bool get enableDebugMode {
    switch (appFlavor) {
      case Flavor.development:
        return true;
      case Flavor.staging:
      case Flavor.production:
        return false;
      default:
        return false;
    }
  }

  static String get bundleId {
    switch (appFlavor) {
      case Flavor.development:
        return 'com.kienlongbank.sales_app.dev';
      case Flavor.staging:
        return 'com.kienlongbank.sales_app.staging';
      case Flavor.production:
        return 'com.kienlongbank.sales_app';
      default:
        return 'com.kienlongbank.sales_app';
    }
  }
} 