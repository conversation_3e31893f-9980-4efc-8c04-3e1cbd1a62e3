import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';
// Presentation widgets and extensions
import 'package:sales_app/presentation/widgets/common/common_dialog.dart';
import 'package:sales_app/presentation/router/navigation_extensions.dart';

/// Global navigator key for accessing navigator context from anywhere in the app
/// This is useful for showing dialogs, snackbars, or navigation from non-widget contexts
class GlobalNavigator {
  /// Global navigator key
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  /// Get current navigator context
  static BuildContext? get currentContext => navigatorKey.currentContext;
  
  /// Get current navigator state
  static NavigatorState? get currentState => navigatorKey.currentState;
  
  /// Show snackbar globally
  static void showSnackBar(SnackBar snackBar) {
    final context = currentContext;
    if (context != null) {
      AppLogger.debug('GlobalNavigator: Showing snackbar');
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    } else {
      AppLogger.warning('GlobalNavigator: No context available for snackbar');
    }
  }
  
  /// Show confirm dialog globally using CommonDialog
  static Future<void> showConfirmDialog({
    required String message,
    String? title,
    String? confirmText,
    String? cancelText,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    bool isDangerous = false,
  }) async {
    final context = currentContext;
    if (context != null) {
      AppLogger.debug('GlobalNavigator: Showing confirm dialog', data: {
        'title': title,
        'isDangerous': isDangerous,
      });
      return CommonDialog.showConfirmDialog(
        context: context,
        message: message,
        title: title,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm,
        onCancel: onCancel,
        isDangerous: isDangerous,
      );
    } else {
      AppLogger.warning('GlobalNavigator: No context available for confirm dialog');
    }
  }
  
  /// Navigate to login using navigation extensions
  static void goToLogin() {
    final context = currentContext;
    if (context != null) {
      try {
        AppLogger.info('GlobalNavigator: Navigating to login using extensions');
        // Use navigation extensions for consistent navigation
        context.goToLogin();
      } catch (e) {
        AppLogger.warning('GlobalNavigator: Navigation extension failed, using fallback', data: {'error': e.toString()});
        // Fallback to GoRouter directly
        GoRouter.of(context).go('/login');
      }
    } else {
      AppLogger.warning('GlobalNavigator: No context available for navigation to login');
    }
  }

  /// Show session expired dialog and navigate to login immediately
  /// 
  /// [message] - Custom message to show. If empty, will use default localized message.
  static void showSessionExpiredMessage(String message) {
    final context = currentContext;
    if (context != null) {
      AppLogger.info('GlobalNavigator: Showing session expired message', data: {
        'message': message,
        'hasCustomMessage': message.isNotEmpty,
      });
      
      // Navigate to login immediately
      goToLogin();

      // Show info dialog after navigation to inform user
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currentCtx = currentContext;
        if (currentCtx != null && currentCtx.mounted) {
          // Use localized strings from generated localization
          final s = S.of(currentCtx);
          CommonDialog.showInfoDialog(
            context: currentCtx,
            title: s.session_expired_title,
            message: message.isNotEmpty ? message : s.session_expired_message,
            buttonText: s.agree,
          );
        } else {
          AppLogger.warning('GlobalNavigator: Context not available for session expired dialog');
        }
      });
    } else {
      AppLogger.warning('GlobalNavigator: No context available for session expired message');
    }
  }

  /// Show session expired dialog with default localized message and navigate to login immediately
  static void showSessionExpiredMessageWithDefault() {
    showSessionExpiredMessage('');
  }
}
