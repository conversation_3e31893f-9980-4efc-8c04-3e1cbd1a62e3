// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'base_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BaseState<T> {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure, T? previousData) error,
    required TResult Function(T? previousData) submitting,
    required TResult Function(T? data) submitted,
    required TResult Function() empty,
    required TResult Function(T data) refreshing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure, T? previousData)? error,
    TResult? Function(T? previousData)? submitting,
    TResult? Function(T? data)? submitted,
    TResult? Function()? empty,
    TResult? Function(T data)? refreshing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure, T? previousData)? error,
    TResult Function(T? previousData)? submitting,
    TResult Function(T? data)? submitted,
    TResult Function()? empty,
    TResult Function(T data)? refreshing,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial<T> value) initial,
    required TResult Function(_Loading<T> value) loading,
    required TResult Function(_Success<T> value) success,
    required TResult Function(_Error<T> value) error,
    required TResult Function(_Submitting<T> value) submitting,
    required TResult Function(_Submitted<T> value) submitted,
    required TResult Function(_Empty<T> value) empty,
    required TResult Function(_Refreshing<T> value) refreshing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial<T> value)? initial,
    TResult? Function(_Loading<T> value)? loading,
    TResult? Function(_Success<T> value)? success,
    TResult? Function(_Error<T> value)? error,
    TResult? Function(_Submitting<T> value)? submitting,
    TResult? Function(_Submitted<T> value)? submitted,
    TResult? Function(_Empty<T> value)? empty,
    TResult? Function(_Refreshing<T> value)? refreshing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial<T> value)? initial,
    TResult Function(_Loading<T> value)? loading,
    TResult Function(_Success<T> value)? success,
    TResult Function(_Error<T> value)? error,
    TResult Function(_Submitting<T> value)? submitting,
    TResult Function(_Submitted<T> value)? submitted,
    TResult Function(_Empty<T> value)? empty,
    TResult Function(_Refreshing<T> value)? refreshing,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BaseStateCopyWith<T, $Res> {
  factory $BaseStateCopyWith(
          BaseState<T> value, $Res Function(BaseState<T>) then) =
      _$BaseStateCopyWithImpl<T, $Res, BaseState<T>>;
}

/// @nodoc
class _$BaseStateCopyWithImpl<T, $Res, $Val extends BaseState<T>>
    implements $BaseStateCopyWith<T, $Res> {
  _$BaseStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<T, $Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl<T> value, $Res Function(_$InitialImpl<T>) then) =
      __$$InitialImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$InitialImpl<T>>
    implements _$$InitialImplCopyWith<T, $Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl<T> _value, $Res Function(_$InitialImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl<T> implements _Initial<T> {
  const _$InitialImpl();

  @override
  String toString() {
    return 'BaseState<$T>.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure, T? previousData) error,
    required TResult Function(T? previousData) submitting,
    required TResult Function(T? data) submitted,
    required TResult Function() empty,
    required TResult Function(T data) refreshing,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure, T? previousData)? error,
    TResult? Function(T? previousData)? submitting,
    TResult? Function(T? data)? submitted,
    TResult? Function()? empty,
    TResult? Function(T data)? refreshing,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure, T? previousData)? error,
    TResult Function(T? previousData)? submitting,
    TResult Function(T? data)? submitted,
    TResult Function()? empty,
    TResult Function(T data)? refreshing,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial<T> value) initial,
    required TResult Function(_Loading<T> value) loading,
    required TResult Function(_Success<T> value) success,
    required TResult Function(_Error<T> value) error,
    required TResult Function(_Submitting<T> value) submitting,
    required TResult Function(_Submitted<T> value) submitted,
    required TResult Function(_Empty<T> value) empty,
    required TResult Function(_Refreshing<T> value) refreshing,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial<T> value)? initial,
    TResult? Function(_Loading<T> value)? loading,
    TResult? Function(_Success<T> value)? success,
    TResult? Function(_Error<T> value)? error,
    TResult? Function(_Submitting<T> value)? submitting,
    TResult? Function(_Submitted<T> value)? submitted,
    TResult? Function(_Empty<T> value)? empty,
    TResult? Function(_Refreshing<T> value)? refreshing,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial<T> value)? initial,
    TResult Function(_Loading<T> value)? loading,
    TResult Function(_Success<T> value)? success,
    TResult Function(_Error<T> value)? error,
    TResult Function(_Submitting<T> value)? submitting,
    TResult Function(_Submitted<T> value)? submitted,
    TResult Function(_Empty<T> value)? empty,
    TResult Function(_Refreshing<T> value)? refreshing,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial<T> implements BaseState<T> {
  const factory _Initial() = _$InitialImpl<T>;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<T, $Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl<T> value, $Res Function(_$LoadingImpl<T>) then) =
      __$$LoadingImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$LoadingImpl<T>>
    implements _$$LoadingImplCopyWith<T, $Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl<T> _value, $Res Function(_$LoadingImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl<T> implements _Loading<T> {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'BaseState<$T>.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure, T? previousData) error,
    required TResult Function(T? previousData) submitting,
    required TResult Function(T? data) submitted,
    required TResult Function() empty,
    required TResult Function(T data) refreshing,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure, T? previousData)? error,
    TResult? Function(T? previousData)? submitting,
    TResult? Function(T? data)? submitted,
    TResult? Function()? empty,
    TResult? Function(T data)? refreshing,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure, T? previousData)? error,
    TResult Function(T? previousData)? submitting,
    TResult Function(T? data)? submitted,
    TResult Function()? empty,
    TResult Function(T data)? refreshing,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial<T> value) initial,
    required TResult Function(_Loading<T> value) loading,
    required TResult Function(_Success<T> value) success,
    required TResult Function(_Error<T> value) error,
    required TResult Function(_Submitting<T> value) submitting,
    required TResult Function(_Submitted<T> value) submitted,
    required TResult Function(_Empty<T> value) empty,
    required TResult Function(_Refreshing<T> value) refreshing,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial<T> value)? initial,
    TResult? Function(_Loading<T> value)? loading,
    TResult? Function(_Success<T> value)? success,
    TResult? Function(_Error<T> value)? error,
    TResult? Function(_Submitting<T> value)? submitting,
    TResult? Function(_Submitted<T> value)? submitted,
    TResult? Function(_Empty<T> value)? empty,
    TResult? Function(_Refreshing<T> value)? refreshing,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial<T> value)? initial,
    TResult Function(_Loading<T> value)? loading,
    TResult Function(_Success<T> value)? success,
    TResult Function(_Error<T> value)? error,
    TResult Function(_Submitting<T> value)? submitting,
    TResult Function(_Submitted<T> value)? submitted,
    TResult Function(_Empty<T> value)? empty,
    TResult Function(_Refreshing<T> value)? refreshing,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading<T> implements BaseState<T> {
  const factory _Loading() = _$LoadingImpl<T>;
}

/// @nodoc
abstract class _$$SuccessImplCopyWith<T, $Res> {
  factory _$$SuccessImplCopyWith(
          _$SuccessImpl<T> value, $Res Function(_$SuccessImpl<T>) then) =
      __$$SuccessImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T data});
}

/// @nodoc
class __$$SuccessImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$SuccessImpl<T>>
    implements _$$SuccessImplCopyWith<T, $Res> {
  __$$SuccessImplCopyWithImpl(
      _$SuccessImpl<T> _value, $Res Function(_$SuccessImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_$SuccessImpl<T>(
      freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$SuccessImpl<T> implements _Success<T> {
  const _$SuccessImpl(this.data);

  @override
  final T data;

  @override
  String toString() {
    return 'BaseState<$T>.success(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessImpl<T> &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessImplCopyWith<T, _$SuccessImpl<T>> get copyWith =>
      __$$SuccessImplCopyWithImpl<T, _$SuccessImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure, T? previousData) error,
    required TResult Function(T? previousData) submitting,
    required TResult Function(T? data) submitted,
    required TResult Function() empty,
    required TResult Function(T data) refreshing,
  }) {
    return success(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure, T? previousData)? error,
    TResult? Function(T? previousData)? submitting,
    TResult? Function(T? data)? submitted,
    TResult? Function()? empty,
    TResult? Function(T data)? refreshing,
  }) {
    return success?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure, T? previousData)? error,
    TResult Function(T? previousData)? submitting,
    TResult Function(T? data)? submitted,
    TResult Function()? empty,
    TResult Function(T data)? refreshing,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial<T> value) initial,
    required TResult Function(_Loading<T> value) loading,
    required TResult Function(_Success<T> value) success,
    required TResult Function(_Error<T> value) error,
    required TResult Function(_Submitting<T> value) submitting,
    required TResult Function(_Submitted<T> value) submitted,
    required TResult Function(_Empty<T> value) empty,
    required TResult Function(_Refreshing<T> value) refreshing,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial<T> value)? initial,
    TResult? Function(_Loading<T> value)? loading,
    TResult? Function(_Success<T> value)? success,
    TResult? Function(_Error<T> value)? error,
    TResult? Function(_Submitting<T> value)? submitting,
    TResult? Function(_Submitted<T> value)? submitted,
    TResult? Function(_Empty<T> value)? empty,
    TResult? Function(_Refreshing<T> value)? refreshing,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial<T> value)? initial,
    TResult Function(_Loading<T> value)? loading,
    TResult Function(_Success<T> value)? success,
    TResult Function(_Error<T> value)? error,
    TResult Function(_Submitting<T> value)? submitting,
    TResult Function(_Submitted<T> value)? submitted,
    TResult Function(_Empty<T> value)? empty,
    TResult Function(_Refreshing<T> value)? refreshing,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class _Success<T> implements BaseState<T> {
  const factory _Success(final T data) = _$SuccessImpl<T>;

  T get data;

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuccessImplCopyWith<T, _$SuccessImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<T, $Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl<T> value, $Res Function(_$ErrorImpl<T>) then) =
      __$$ErrorImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({Failure failure, T? previousData});

  $FailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$ErrorImpl<T>>
    implements _$$ErrorImplCopyWith<T, $Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl<T> _value, $Res Function(_$ErrorImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
    Object? previousData = freezed,
  }) {
    return _then(_$ErrorImpl<T>(
      null == failure
          ? _value.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as Failure,
      previousData: freezed == previousData
          ? _value.previousData
          : previousData // ignore: cast_nullable_to_non_nullable
              as T?,
    ));
  }

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FailureCopyWith<$Res> get failure {
    return $FailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$ErrorImpl<T> implements _Error<T> {
  const _$ErrorImpl(this.failure, {this.previousData});

  @override
  final Failure failure;
  @override
  final T? previousData;

  @override
  String toString() {
    return 'BaseState<$T>.error(failure: $failure, previousData: $previousData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl<T> &&
            (identical(other.failure, failure) || other.failure == failure) &&
            const DeepCollectionEquality()
                .equals(other.previousData, previousData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, failure, const DeepCollectionEquality().hash(previousData));

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<T, _$ErrorImpl<T>> get copyWith =>
      __$$ErrorImplCopyWithImpl<T, _$ErrorImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure, T? previousData) error,
    required TResult Function(T? previousData) submitting,
    required TResult Function(T? data) submitted,
    required TResult Function() empty,
    required TResult Function(T data) refreshing,
  }) {
    return error(failure, previousData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure, T? previousData)? error,
    TResult? Function(T? previousData)? submitting,
    TResult? Function(T? data)? submitted,
    TResult? Function()? empty,
    TResult? Function(T data)? refreshing,
  }) {
    return error?.call(failure, previousData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure, T? previousData)? error,
    TResult Function(T? previousData)? submitting,
    TResult Function(T? data)? submitted,
    TResult Function()? empty,
    TResult Function(T data)? refreshing,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(failure, previousData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial<T> value) initial,
    required TResult Function(_Loading<T> value) loading,
    required TResult Function(_Success<T> value) success,
    required TResult Function(_Error<T> value) error,
    required TResult Function(_Submitting<T> value) submitting,
    required TResult Function(_Submitted<T> value) submitted,
    required TResult Function(_Empty<T> value) empty,
    required TResult Function(_Refreshing<T> value) refreshing,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial<T> value)? initial,
    TResult? Function(_Loading<T> value)? loading,
    TResult? Function(_Success<T> value)? success,
    TResult? Function(_Error<T> value)? error,
    TResult? Function(_Submitting<T> value)? submitting,
    TResult? Function(_Submitted<T> value)? submitted,
    TResult? Function(_Empty<T> value)? empty,
    TResult? Function(_Refreshing<T> value)? refreshing,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial<T> value)? initial,
    TResult Function(_Loading<T> value)? loading,
    TResult Function(_Success<T> value)? success,
    TResult Function(_Error<T> value)? error,
    TResult Function(_Submitting<T> value)? submitting,
    TResult Function(_Submitted<T> value)? submitted,
    TResult Function(_Empty<T> value)? empty,
    TResult Function(_Refreshing<T> value)? refreshing,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error<T> implements BaseState<T> {
  const factory _Error(final Failure failure, {final T? previousData}) =
      _$ErrorImpl<T>;

  Failure get failure;
  T? get previousData;

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<T, _$ErrorImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmittingImplCopyWith<T, $Res> {
  factory _$$SubmittingImplCopyWith(
          _$SubmittingImpl<T> value, $Res Function(_$SubmittingImpl<T>) then) =
      __$$SubmittingImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T? previousData});
}

/// @nodoc
class __$$SubmittingImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$SubmittingImpl<T>>
    implements _$$SubmittingImplCopyWith<T, $Res> {
  __$$SubmittingImplCopyWithImpl(
      _$SubmittingImpl<T> _value, $Res Function(_$SubmittingImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? previousData = freezed,
  }) {
    return _then(_$SubmittingImpl<T>(
      previousData: freezed == previousData
          ? _value.previousData
          : previousData // ignore: cast_nullable_to_non_nullable
              as T?,
    ));
  }
}

/// @nodoc

class _$SubmittingImpl<T> implements _Submitting<T> {
  const _$SubmittingImpl({this.previousData});

  @override
  final T? previousData;

  @override
  String toString() {
    return 'BaseState<$T>.submitting(previousData: $previousData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubmittingImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.previousData, previousData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(previousData));

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubmittingImplCopyWith<T, _$SubmittingImpl<T>> get copyWith =>
      __$$SubmittingImplCopyWithImpl<T, _$SubmittingImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure, T? previousData) error,
    required TResult Function(T? previousData) submitting,
    required TResult Function(T? data) submitted,
    required TResult Function() empty,
    required TResult Function(T data) refreshing,
  }) {
    return submitting(previousData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure, T? previousData)? error,
    TResult? Function(T? previousData)? submitting,
    TResult? Function(T? data)? submitted,
    TResult? Function()? empty,
    TResult? Function(T data)? refreshing,
  }) {
    return submitting?.call(previousData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure, T? previousData)? error,
    TResult Function(T? previousData)? submitting,
    TResult Function(T? data)? submitted,
    TResult Function()? empty,
    TResult Function(T data)? refreshing,
    required TResult orElse(),
  }) {
    if (submitting != null) {
      return submitting(previousData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial<T> value) initial,
    required TResult Function(_Loading<T> value) loading,
    required TResult Function(_Success<T> value) success,
    required TResult Function(_Error<T> value) error,
    required TResult Function(_Submitting<T> value) submitting,
    required TResult Function(_Submitted<T> value) submitted,
    required TResult Function(_Empty<T> value) empty,
    required TResult Function(_Refreshing<T> value) refreshing,
  }) {
    return submitting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial<T> value)? initial,
    TResult? Function(_Loading<T> value)? loading,
    TResult? Function(_Success<T> value)? success,
    TResult? Function(_Error<T> value)? error,
    TResult? Function(_Submitting<T> value)? submitting,
    TResult? Function(_Submitted<T> value)? submitted,
    TResult? Function(_Empty<T> value)? empty,
    TResult? Function(_Refreshing<T> value)? refreshing,
  }) {
    return submitting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial<T> value)? initial,
    TResult Function(_Loading<T> value)? loading,
    TResult Function(_Success<T> value)? success,
    TResult Function(_Error<T> value)? error,
    TResult Function(_Submitting<T> value)? submitting,
    TResult Function(_Submitted<T> value)? submitted,
    TResult Function(_Empty<T> value)? empty,
    TResult Function(_Refreshing<T> value)? refreshing,
    required TResult orElse(),
  }) {
    if (submitting != null) {
      return submitting(this);
    }
    return orElse();
  }
}

abstract class _Submitting<T> implements BaseState<T> {
  const factory _Submitting({final T? previousData}) = _$SubmittingImpl<T>;

  T? get previousData;

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubmittingImplCopyWith<T, _$SubmittingImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmittedImplCopyWith<T, $Res> {
  factory _$$SubmittedImplCopyWith(
          _$SubmittedImpl<T> value, $Res Function(_$SubmittedImpl<T>) then) =
      __$$SubmittedImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T? data});
}

/// @nodoc
class __$$SubmittedImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$SubmittedImpl<T>>
    implements _$$SubmittedImplCopyWith<T, $Res> {
  __$$SubmittedImplCopyWithImpl(
      _$SubmittedImpl<T> _value, $Res Function(_$SubmittedImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_$SubmittedImpl<T>(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as T?,
    ));
  }
}

/// @nodoc

class _$SubmittedImpl<T> implements _Submitted<T> {
  const _$SubmittedImpl({this.data});

  @override
  final T? data;

  @override
  String toString() {
    return 'BaseState<$T>.submitted(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubmittedImpl<T> &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubmittedImplCopyWith<T, _$SubmittedImpl<T>> get copyWith =>
      __$$SubmittedImplCopyWithImpl<T, _$SubmittedImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure, T? previousData) error,
    required TResult Function(T? previousData) submitting,
    required TResult Function(T? data) submitted,
    required TResult Function() empty,
    required TResult Function(T data) refreshing,
  }) {
    return submitted(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure, T? previousData)? error,
    TResult? Function(T? previousData)? submitting,
    TResult? Function(T? data)? submitted,
    TResult? Function()? empty,
    TResult? Function(T data)? refreshing,
  }) {
    return submitted?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure, T? previousData)? error,
    TResult Function(T? previousData)? submitting,
    TResult Function(T? data)? submitted,
    TResult Function()? empty,
    TResult Function(T data)? refreshing,
    required TResult orElse(),
  }) {
    if (submitted != null) {
      return submitted(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial<T> value) initial,
    required TResult Function(_Loading<T> value) loading,
    required TResult Function(_Success<T> value) success,
    required TResult Function(_Error<T> value) error,
    required TResult Function(_Submitting<T> value) submitting,
    required TResult Function(_Submitted<T> value) submitted,
    required TResult Function(_Empty<T> value) empty,
    required TResult Function(_Refreshing<T> value) refreshing,
  }) {
    return submitted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial<T> value)? initial,
    TResult? Function(_Loading<T> value)? loading,
    TResult? Function(_Success<T> value)? success,
    TResult? Function(_Error<T> value)? error,
    TResult? Function(_Submitting<T> value)? submitting,
    TResult? Function(_Submitted<T> value)? submitted,
    TResult? Function(_Empty<T> value)? empty,
    TResult? Function(_Refreshing<T> value)? refreshing,
  }) {
    return submitted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial<T> value)? initial,
    TResult Function(_Loading<T> value)? loading,
    TResult Function(_Success<T> value)? success,
    TResult Function(_Error<T> value)? error,
    TResult Function(_Submitting<T> value)? submitting,
    TResult Function(_Submitted<T> value)? submitted,
    TResult Function(_Empty<T> value)? empty,
    TResult Function(_Refreshing<T> value)? refreshing,
    required TResult orElse(),
  }) {
    if (submitted != null) {
      return submitted(this);
    }
    return orElse();
  }
}

abstract class _Submitted<T> implements BaseState<T> {
  const factory _Submitted({final T? data}) = _$SubmittedImpl<T>;

  T? get data;

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubmittedImplCopyWith<T, _$SubmittedImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EmptyImplCopyWith<T, $Res> {
  factory _$$EmptyImplCopyWith(
          _$EmptyImpl<T> value, $Res Function(_$EmptyImpl<T>) then) =
      __$$EmptyImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$EmptyImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$EmptyImpl<T>>
    implements _$$EmptyImplCopyWith<T, $Res> {
  __$$EmptyImplCopyWithImpl(
      _$EmptyImpl<T> _value, $Res Function(_$EmptyImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EmptyImpl<T> implements _Empty<T> {
  const _$EmptyImpl();

  @override
  String toString() {
    return 'BaseState<$T>.empty()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EmptyImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure, T? previousData) error,
    required TResult Function(T? previousData) submitting,
    required TResult Function(T? data) submitted,
    required TResult Function() empty,
    required TResult Function(T data) refreshing,
  }) {
    return empty();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure, T? previousData)? error,
    TResult? Function(T? previousData)? submitting,
    TResult? Function(T? data)? submitted,
    TResult? Function()? empty,
    TResult? Function(T data)? refreshing,
  }) {
    return empty?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure, T? previousData)? error,
    TResult Function(T? previousData)? submitting,
    TResult Function(T? data)? submitted,
    TResult Function()? empty,
    TResult Function(T data)? refreshing,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial<T> value) initial,
    required TResult Function(_Loading<T> value) loading,
    required TResult Function(_Success<T> value) success,
    required TResult Function(_Error<T> value) error,
    required TResult Function(_Submitting<T> value) submitting,
    required TResult Function(_Submitted<T> value) submitted,
    required TResult Function(_Empty<T> value) empty,
    required TResult Function(_Refreshing<T> value) refreshing,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial<T> value)? initial,
    TResult? Function(_Loading<T> value)? loading,
    TResult? Function(_Success<T> value)? success,
    TResult? Function(_Error<T> value)? error,
    TResult? Function(_Submitting<T> value)? submitting,
    TResult? Function(_Submitted<T> value)? submitted,
    TResult? Function(_Empty<T> value)? empty,
    TResult? Function(_Refreshing<T> value)? refreshing,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial<T> value)? initial,
    TResult Function(_Loading<T> value)? loading,
    TResult Function(_Success<T> value)? success,
    TResult Function(_Error<T> value)? error,
    TResult Function(_Submitting<T> value)? submitting,
    TResult Function(_Submitted<T> value)? submitted,
    TResult Function(_Empty<T> value)? empty,
    TResult Function(_Refreshing<T> value)? refreshing,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class _Empty<T> implements BaseState<T> {
  const factory _Empty() = _$EmptyImpl<T>;
}

/// @nodoc
abstract class _$$RefreshingImplCopyWith<T, $Res> {
  factory _$$RefreshingImplCopyWith(
          _$RefreshingImpl<T> value, $Res Function(_$RefreshingImpl<T>) then) =
      __$$RefreshingImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T data});
}

/// @nodoc
class __$$RefreshingImplCopyWithImpl<T, $Res>
    extends _$BaseStateCopyWithImpl<T, $Res, _$RefreshingImpl<T>>
    implements _$$RefreshingImplCopyWith<T, $Res> {
  __$$RefreshingImplCopyWithImpl(
      _$RefreshingImpl<T> _value, $Res Function(_$RefreshingImpl<T>) _then)
      : super(_value, _then);

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_$RefreshingImpl<T>(
      freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$RefreshingImpl<T> implements _Refreshing<T> {
  const _$RefreshingImpl(this.data);

  @override
  final T data;

  @override
  String toString() {
    return 'BaseState<$T>.refreshing(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefreshingImpl<T> &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RefreshingImplCopyWith<T, _$RefreshingImpl<T>> get copyWith =>
      __$$RefreshingImplCopyWithImpl<T, _$RefreshingImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(T data) success,
    required TResult Function(Failure failure, T? previousData) error,
    required TResult Function(T? previousData) submitting,
    required TResult Function(T? data) submitted,
    required TResult Function() empty,
    required TResult Function(T data) refreshing,
  }) {
    return refreshing(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(T data)? success,
    TResult? Function(Failure failure, T? previousData)? error,
    TResult? Function(T? previousData)? submitting,
    TResult? Function(T? data)? submitted,
    TResult? Function()? empty,
    TResult? Function(T data)? refreshing,
  }) {
    return refreshing?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(T data)? success,
    TResult Function(Failure failure, T? previousData)? error,
    TResult Function(T? previousData)? submitting,
    TResult Function(T? data)? submitted,
    TResult Function()? empty,
    TResult Function(T data)? refreshing,
    required TResult orElse(),
  }) {
    if (refreshing != null) {
      return refreshing(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial<T> value) initial,
    required TResult Function(_Loading<T> value) loading,
    required TResult Function(_Success<T> value) success,
    required TResult Function(_Error<T> value) error,
    required TResult Function(_Submitting<T> value) submitting,
    required TResult Function(_Submitted<T> value) submitted,
    required TResult Function(_Empty<T> value) empty,
    required TResult Function(_Refreshing<T> value) refreshing,
  }) {
    return refreshing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial<T> value)? initial,
    TResult? Function(_Loading<T> value)? loading,
    TResult? Function(_Success<T> value)? success,
    TResult? Function(_Error<T> value)? error,
    TResult? Function(_Submitting<T> value)? submitting,
    TResult? Function(_Submitted<T> value)? submitted,
    TResult? Function(_Empty<T> value)? empty,
    TResult? Function(_Refreshing<T> value)? refreshing,
  }) {
    return refreshing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial<T> value)? initial,
    TResult Function(_Loading<T> value)? loading,
    TResult Function(_Success<T> value)? success,
    TResult Function(_Error<T> value)? error,
    TResult Function(_Submitting<T> value)? submitting,
    TResult Function(_Submitted<T> value)? submitted,
    TResult Function(_Empty<T> value)? empty,
    TResult Function(_Refreshing<T> value)? refreshing,
    required TResult orElse(),
  }) {
    if (refreshing != null) {
      return refreshing(this);
    }
    return orElse();
  }
}

abstract class _Refreshing<T> implements BaseState<T> {
  const factory _Refreshing(final T data) = _$RefreshingImpl<T>;

  T get data;

  /// Create a copy of BaseState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RefreshingImplCopyWith<T, _$RefreshingImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}
