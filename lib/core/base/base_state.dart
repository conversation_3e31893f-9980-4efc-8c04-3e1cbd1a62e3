import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/core/error/failures.dart';

part 'base_state.freezed.dart';

/// Base state để quản lý các trạng thái API call
@freezed
class BaseState<T> with _$BaseState<T> {
  /// Initial state - no data loaded yet
  const factory BaseState.initial() = _Initial<T>;

  /// Loading state - data is being fetched
  const factory BaseState.loading() = _Loading<T>;

  /// Success state - data loaded successfully
  const factory BaseState.success(T data) = _Success<T>;

  /// Error state - something went wrong (keeps previous data for better UX)
  const factory BaseState.error(Failure failure, {T? previousData}) = _Error<T>;

  /// Submitting state - form is being submitted (keeps previous data)
  const factory BaseState.submitting({T? previousData}) = _Submitting<T>;

  /// Submitted state - đã gửi thành công, chờ phản hồi hoặc chuyển bước
  const factory BaseState.submitted({T? data}) = _Submitted<T>;

  /// Empty state - no data available
  const factory BaseState.empty() = _Empty<T>;

  /// Refreshing state - refreshing existing data
  const factory BaseState.refreshing(T data) = _Refreshing<T>;
}

/// Extension methods để dễ dàng check trạng thái
extension BaseStateX<T> on BaseState<T> {
  /// Basic state checks
  bool get isInitial => this is _Initial<T>;
  bool get isLoading => this is _Loading<T>;
  bool get isSuccess => this is _Success<T>;
  bool get isError => this is _Error<T>;
  bool get isSubmitting => this is _Submitting<T>;
  bool get isSubmitted => this is _Submitted<T>;
  bool get isEmpty => this is _Empty<T>;
  bool get isRefreshing => this is _Refreshing<T>;

  /// Lấy data nếu có (bao gồm cả data từ submitted/submitting/error state)
  T? get data => when(
        initial: () => null,
        loading: () => null,
        success: (data) => data,
        error: (failure, previousData) => previousData,
        submitting: (previousData) => previousData,
        submitted: (data) => data,
        empty: () => null,
        refreshing: (data) => data,
      );

  /// Lấy failure nếu có
  Failure? get failure => when(
        initial: () => null,
        loading: () => null,
        success: (_) => null,
        error: (failure, _) => failure,
        submitting: (_) => null,
        submitted: (_) => null,
        empty: () => null,
        refreshing: (_) => null,
      );

  /// Lấy error message nếu có
  String? get errorMessage => when(
        initial: () => null,
        loading: () => null,
        success: (_) => null,
        error: (failure, _) => failure.when(
          server: (errorType, serverMessage, serverErrorCode) => serverMessage,
          network: (errorType) => null,
          cache: (errorType) => null,
          auth: (errorType, serverMessage, serverErrorCode) => serverMessage,
          validation: (errorType, serverMessage, serverErrorCode) => serverMessage,
        ),
        submitting: (_) => null,
        submitted: (_) => null,
        empty: () => null,
        refreshing: (_) => null,
      );

  /// Lấy error code nếu có
  String? get errorCode => when(
        initial: () => null,
        loading: () => null,
        success: (_) => null,
        error: (failure, _) => failure.when(
          server: (errorType, serverMessage, serverErrorCode) => serverErrorCode,
          network: (errorType) => null,
          cache: (errorType) => null,
          auth: (errorType, serverMessage, serverErrorCode) => serverErrorCode,
          validation: (errorType, serverMessage, serverErrorCode) => serverErrorCode,
        ),
        submitting: (_) => null,
        submitted: (_) => null,
        empty: () => null,
        refreshing: (_) => null,
      );

  /// Check xem có đang thực hiện action nào không (loading hoặc submitting)
  bool get isProcessing => isLoading || isSubmitting;

  /// Check xem submitting có data hay không
  bool get isSubmittingWithData => when(
        initial: () => false,
        loading: () => false,
        success: (_) => false,
        error: (_, __) => false,
        submitting: (previousData) => previousData != null,
        submitted: (_) => false,
        empty: () => false,
        refreshing: (_) => false,
      );

  /// Check xem submitted có data hay không
  bool get isSubmittedWithData => when(
        initial: () => false,
        loading: () => false,
        success: (_) => false,
        error: (_, __) => false,
        submitting: (_) => false,
        submitted: (data) => data != null,
        empty: () => false,
        refreshing: (_) => false,
      );

  /// Check xem error có data hay không
  bool get isErrorWithData => when(
        initial: () => false,
        loading: () => false,
        success: (_) => false,
        error: (_, previousData) => previousData != null,
        submitting: (_) => false,
        submitted: (_) => false,
        empty: () => false,
        refreshing: (_) => false,
      );

  /// Check xem có thể retry không (error state)
  bool get canRetry => isError;

  /// Check xem có thể refresh không (success, submitted hoặc error with data)
  bool get canRefresh => isSuccess || isSubmittedWithData || isErrorWithData;

  /// Check xem có thể submit không (success hoặc submitted state)
  bool get canSubmit => isSuccess || isSubmitted;
} 