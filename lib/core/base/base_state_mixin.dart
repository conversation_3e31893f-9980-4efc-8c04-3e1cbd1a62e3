import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/base/base_state.dart';
import 'package:sales_app/core/error/failures.dart';
import 'package:sales_app/core/error/error_types.dart';
import 'package:sales_app/domain/services/network_info.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:dartz/dartz.dart';

/// Mixin cung cấp logic state management cho BaseState
/// Dành cho Notifier thường (non-auto-dispose)
mixin BaseStateMixin<T> on Notifier<BaseState<T>> {
  /// Network info cho connectivity check
  NetworkInfo get networkInfo;

  /// Load data với automatic error handling
  Future<void> loadData(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isProcessing) return; // Prevent multiple calls

    state = const BaseState.loading();

    try {
      // Network connectivity và retry logic đều do ErrorInterceptor và SimpleRetryInterceptor xử lý
      final result = await dataCall();

      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = data != null
            ? BaseState.success(data)
            : const BaseState.empty(),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in loadData', error: e);
      // Transform unexpected errors to domain failures
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }

  /// Submit data với automatic error handling
  Future<void> submitData<R>(
    R data,
    Future<Either<Failure, T>> Function(R data) submitCall,
  ) async {
    if (state.isSubmitting) return; // Prevent multiple submissions

    // Keep previous data if available for better UX
    final previousData = state.data;
    state = BaseState.submitting(previousData: previousData);

    try {
      // Network connectivity và retry logic đều do ErrorInterceptor và SimpleRetryInterceptor xử lý
      final result = await submitCall(data);

      result.fold(
        (failure) => state = BaseState.error(failure, previousData: previousData),
        (response) => state = BaseState.success(response),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in submitData', error: e);
      // Transform unexpected errors to domain failures
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
        previousData: previousData,
      );
    }
  }

  /// Refresh data while keeping existing data visible
  Future<void> refreshData(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isProcessing) return;

    final currentData = state.data;
    if (currentData == null) {
      await loadData(dataCall);
      return;
    }

    state = BaseState.refreshing(currentData);

    try {
      // Network connectivity và retry logic đều do ErrorInterceptor và SimpleRetryInterceptor xử lý
      final result = await dataCall();

      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = data != null
            ? BaseState.success(data)
            : const BaseState.empty(),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in refreshData', error: e);
      // Transform unexpected errors to domain failures
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }

  /// Retry last operation
  Future<void> retry(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.canRetry) {
      await loadData(dataCall);
    }
  }

  /// Reset to initial state
  void reset() {
    state = const BaseState.initial();
  }

  /// Set custom state
  void setState(BaseState<T> newState) {
    state = newState;
  }

  /// Check if currently processing (loading or submitting)
  bool get isProcessing => state.isProcessing;

  /// Check if can retry (error state)
  bool get canRetry => state.canRetry;

  /// Check if can refresh (success or error with data)
  bool get canRefresh => state.canRefresh;

  /// Check if can submit (success state)
  bool get canSubmit => state.canSubmit;
}

/// Mixin cung cấp logic state management cho BaseState
/// Dành cho AutoDisposeNotifier (@riverpod)
mixin AutoDisposeBaseStateMixin<T> on AutoDisposeNotifier<BaseState<T>> {
  /// Network info cho connectivity check
  NetworkInfo get networkInfo;

  /// Load data với automatic error handling
  Future<void> loadData(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isProcessing) return; // Prevent multiple calls

    state = const BaseState.loading();

    try {
      // Network connectivity và retry logic đều do ErrorInterceptor và SimpleRetryInterceptor xử lý
      final result = await dataCall();

      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = data != null
            ? BaseState.success(data)
            : const BaseState.empty(),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in loadData', error: e);
      // Transform unexpected errors to domain failures
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }

  /// Submit data với automatic error handling
  Future<void> submitData<R>(
    R data,
    Future<Either<Failure, T>> Function(R data) submitCall,
  ) async {
    if (state.isSubmitting) return; // Prevent multiple submissions

    // Keep previous data if available for better UX
    final previousData = state.data;
    state = BaseState.submitting(previousData: previousData);

    try {
      // Network connectivity và retry logic đều do ErrorInterceptor và SimpleRetryInterceptor xử lý
      final result = await submitCall(data);

      result.fold(
        (failure) => state = BaseState.error(failure, previousData: previousData),
        (response) => state = BaseState.success(response),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in submitData', error: e);
      // Transform unexpected errors to domain failures
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
        previousData: previousData,
      );
    }
  }

  /// Refresh data while keeping existing data visible
  Future<void> refreshData(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.isProcessing) return;

    final currentData = state.data;
    if (currentData == null) {
      await loadData(dataCall);
      return;
    }

    state = BaseState.refreshing(currentData);

    try {
      // Network connectivity và retry logic đều do ErrorInterceptor và SimpleRetryInterceptor xử lý
      final result = await dataCall();

      result.fold(
        (failure) => state = BaseState.error(failure),
        (data) => state = data != null
            ? BaseState.success(data)
            : const BaseState.empty(),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in refreshData', error: e);
      // Transform unexpected errors to domain failures
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }

  /// Retry last operation
  Future<void> retry(Future<Either<Failure, T?>> Function() dataCall) async {
    if (state.canRetry) {
      await loadData(dataCall);
    }
  }

  /// Reset to initial state
  void reset() {
    state = const BaseState.initial();
  }

  /// Set custom state
  void setState(BaseState<T> newState) {
    state = newState;
  }

  /// Check if currently processing (loading or submitting)
  bool get isProcessing => state.isProcessing;

  /// Check if can retry (error state)
  bool get canRetry => state.canRetry;

  /// Check if can refresh (success or error with data)
  bool get canRefresh => state.canRefresh;

  /// Check if can submit (success state)
  bool get canSubmit => state.canSubmit;
} 