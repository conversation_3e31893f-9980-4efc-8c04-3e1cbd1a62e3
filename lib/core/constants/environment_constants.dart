import '../config/app_config.dart';
import 'api_constants.dart';

/// Environment configuration constants
/// Centralized place for all environment-specific URLs and settings
class EnvironmentConstants {
  
  // ✅ Base URLs for different environments - now use AppConfig for flavor support
  static String get devBaseUrl => AppConfig.appFlavor == Flavor.development 
      ? AppConfig.baseUrl 
      : 'https://api-staging.kienlongbank.co';
  static String get stagingBaseUrl => AppConfig.appFlavor == Flavor.staging 
      ? AppConfig.baseUrl 
      : 'https://api-staging.kienlongbank.co';
  static String get prodBaseUrl => AppConfig.appFlavor == Flavor.production 
      ? AppConfig.baseUrl 
      : 'https://api.kienlongbank.com';
  
  // ✅ API configuration
  static const String apiVersion = 'v1';
  // Use ApiConstants.defaultTimeoutDuration instead of duplicating
  
  // ✅ Environment-specific settings - now use AppConfig for flavor support
  static bool get devLoggingEnabled => AppConfig.appFlavor == Flavor.development 
      ? AppConfig.enableLogging 
      : true;
  static bool get stagingLoggingEnabled => AppConfig.appFlavor == Flavor.staging 
      ? AppConfig.enableLogging 
      : true;
  static bool get prodLoggingEnabled => AppConfig.appFlavor == Flavor.production 
      ? AppConfig.enableLogging 
      : false;
  
  static const bool devMockApiEnabled = true;
  static const bool stagingMockApiEnabled = false;
  static const bool prodMockApiEnabled = false;
  
  // ✅ Additional environment-specific configurations
  // Use ApiConstants.defaultTimeoutDuration for all environments
  static int get devTimeoutDuration => ApiConstants.defaultTimeoutDuration;
  static int get stagingTimeoutDuration => ApiConstants.defaultTimeoutDuration;
  static int get prodTimeoutDuration => ApiConstants.defaultTimeoutDuration;
  
  // ✅ Feature flags per environment (for future use)
  static const Map<String, bool> devFeatureFlags = {
    'debug_menu': true,
    'mock_payments': true,
    'environment_selector': true,
    'crash_reporting': false,
  };
  
  static const Map<String, bool> stagingFeatureFlags = {
    'debug_menu': true,
    'mock_payments': false,
    'environment_selector': true,
    'crash_reporting': true,
  };
  
  static const Map<String, bool> prodFeatureFlags = {
    'debug_menu': false,
    'mock_payments': false,
    'environment_selector': false,
    'crash_reporting': true,
  };
  
  // ✅ Helper methods to get full API URLs
  static String getFullApiUrl(String baseUrl) {
    return '$baseUrl/api/$apiVersion';
  }
  
  static String get devFullApiUrl => getFullApiUrl(devBaseUrl);
  static String get stagingFullApiUrl => getFullApiUrl(stagingBaseUrl);
  static String get prodFullApiUrl => getFullApiUrl(prodBaseUrl);
  
  // ✅ Validation helpers
  static bool isValidEnvironmentUrl(String url) {
    return url.startsWith('https://') && (url.contains('kienlongbank.com') || url.contains('kienlongbank.co'));
  }
  
  // ✅ Environment detection helpers (for future use)
  static bool isDevUrl(String url) => url.contains('dev-api');
  static bool isStagingUrl(String url) => url.contains('staging-api');
  static bool isProdUrl(String url) => url == prodBaseUrl;
}
