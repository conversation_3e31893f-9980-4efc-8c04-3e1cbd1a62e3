/// Core enum representing network connection status
/// Shared across all layers without violating clean architecture
enum NetworkConnectionStatus {
  /// Devi<PERSON> is connected to internet
  connected,
  
  /// Devi<PERSON> has no internet connection
  disconnected,
  
  /// Connection is unstable (frequent disconnections)
  unstable,
  
  /// Currently checking connection status
  checking,
}

/// Extension methods for NetworkConnectionStatus
extension NetworkConnectionStatusExtension on NetworkConnectionStatus {
  /// Check if currently connected
  bool get isConnected => this == NetworkConnectionStatus.connected;
  
  /// Check if currently disconnected
  bool get isDisconnected => this == NetworkConnectionStatus.disconnected;
  
  /// Check if connection is unstable
  bool get isUnstable => this == NetworkConnectionStatus.unstable;
  
  /// Check if checking connection
  bool get isChecking => this == NetworkConnectionStatus.checking;
  
  /// Get status icon (language-independent)
  String get statusIcon {
    switch (this) {
      case NetworkConnectionStatus.connected:
        return '✅';
      case NetworkConnectionStatus.disconnected:
        return '❌';
      case NetworkConnectionStatus.unstable:
        return '⚠️';
      case NetworkConnectionStatus.checking:
        return '🔄';
    }
  }
}
