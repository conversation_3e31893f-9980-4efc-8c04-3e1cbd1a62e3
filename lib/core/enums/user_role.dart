/// Enum định nghĩa các vai trò người dùng trong hệ thống App Sale
enum UserRole {
  /// Cộng tác viên
  ctv,
  
  /// Tổ trưởng
  toTruong,
  
  /// Tổ phó
  toPho,
  
  /// Nhóm trưởng
  nhomTruong,
  
  /// C<PERSON> bộ bán hàng
  canBoBanHang,
  
  /// Quản lý
  quanLy,
  
  /// Back-office
  backOffice,
}

/// Extension để thêm các methods tiện ích cho UserRole
extension UserRoleExtension on UserRole {
  /// Tên hiển thị của vai trò
  String get displayName {
    switch (this) {
      case UserRole.ctv:
        return 'Cộng tác viên';
      case UserRole.toTruong:
        return 'Tổ trưởng';
      case UserRole.toPho:
        return 'Tổ phó';
      case UserRole.nhomTruong:
        return 'Nhóm trưởng';
      case UserRole.canBoBanHang:
        return '<PERSON><PERSON> bộ bán hàng';
      case UserRole.quanLy:
        return 'Quản lý';
      case UserRole.backOffice:
        return 'Back-office';
    }
  }

  /// Tên viết tắt của vai trò
  String get shortName {
    switch (this) {
      case UserRole.ctv:
        return 'CTV';
      case UserRole.toTruong:
        return 'Tổ trưởng';
      case UserRole.toPho:
        return 'Tổ phó';
      case UserRole.nhomTruong:
        return 'Nhóm trưởng';
      case UserRole.canBoBanHang:
        return 'CB Bán hàng';
      case UserRole.quanLy:
        return 'Quản lý';
      case UserRole.backOffice:
        return 'Back-office';
    }
  }

  /// Kiểm tra có hiển thị mục "Hồ sơ chưa hoàn tất" không
  /// Theo SRS: chỉ hiển thị cho CTV, Tổ trưởng, Tổ phó, Nhóm trưởng
  bool get shouldShowIncompleteRecords {
    switch (this) {
      case UserRole.ctv:
      case UserRole.toTruong:
      case UserRole.toPho:
      case UserRole.nhomTruong:
        return true;
      case UserRole.canBoBanHang:
      case UserRole.quanLy:
      case UserRole.backOffice:
        return false;
    }
  }

  /// Kiểm tra có hiển thị mục "Đã xem gần đây" không
  /// Theo SRS: chỉ hiển thị cho CTV, Tổ trưởng, Tổ phó, Nhóm trưởng
  bool get shouldShowRecentViewed {
    switch (this) {
      case UserRole.ctv:
      case UserRole.toTruong:
      case UserRole.toPho:
      case UserRole.nhomTruong:
        return true;
      case UserRole.canBoBanHang:
      case UserRole.quanLy:
      case UserRole.backOffice:
        return false;
    }
  }

  /// Kiểm tra có hiển thị "Banner sản phẩm" không
  /// Theo SRS: chỉ hiển thị cho các role KHÔNG phải CTV, Tổ trưởng, Tổ phó, Nhóm trưởng
  bool get shouldShowProductBanners {
    switch (this) {
      case UserRole.ctv:
      case UserRole.toTruong:
      case UserRole.toPho:
      case UserRole.nhomTruong:
        return false;
      case UserRole.canBoBanHang:
      case UserRole.quanLy:
      case UserRole.backOffice:
        return true;
    }
  }

  /// Kiểm tra có quyền truy cập báo cáo chi tiết không
  bool get hasReportAccess {
    switch (this) {
      case UserRole.toTruong:
      case UserRole.nhomTruong:
      case UserRole.quanLy:
      case UserRole.backOffice:
        return true;
      case UserRole.ctv:
      case UserRole.toPho:
      case UserRole.canBoBanHang:
        return false;
    }
  }

  /// Kiểm tra có quyền quản lý người dùng không
  bool get hasUserManagement {
    switch (this) {
      case UserRole.toTruong:
      case UserRole.nhomTruong:
      case UserRole.quanLy:
      case UserRole.backOffice:
        return true;
      case UserRole.ctv:
      case UserRole.toPho:
      case UserRole.canBoBanHang:
        return false;
    }
  }

  /// Kiểm tra có quyền tạo khoản vay không
  bool get canCreateLoan {
    switch (this) {
      case UserRole.ctv:
      case UserRole.toTruong:
      case UserRole.toPho:
      case UserRole.nhomTruong:
      case UserRole.canBoBanHang:
        return true;
      case UserRole.quanLy:
      case UserRole.backOffice:
        return false;
    }
  }

  /// Mức độ ưu tiên thông báo (1 = cao nhất, 5 = thấp nhất)
  int get notificationPriority {
    switch (this) {
      case UserRole.quanLy:
        return 1;
      case UserRole.nhomTruong:
        return 2;
      case UserRole.toTruong:
        return 3;
      case UserRole.backOffice:
        return 3;
      case UserRole.toPho:
        return 4;
      case UserRole.canBoBanHang:
        return 4;
      case UserRole.ctv:
        return 5;
    }
  }
} 