/// Enum cho các loại file được hỗ trợ
enum FileType {
  avatar,
  idCard,
  liveness,
  icon,
  collocation,
  proposal,
  statement,
  sign,
  custom,
  other,
}

extension FileTypeExtension on FileType {
  String get value {
    switch (this) {
      case FileType.avatar:
        return 'AVATAR';
      case FileType.idCard:
        return 'IDCARD';
      case FileType.liveness:
        return 'LIVENESS';
      case FileType.icon:
        return 'ICON';
      case FileType.collocation:
        return 'COLLOCATION';
      case FileType.proposal:
        return 'PROPOSAL';
      case FileType.statement:
        return 'STATEMENT';
      case FileType.sign:
        return 'SIGN';
      case FileType.custom:
        return 'CUSTOM';
      case FileType.other:
        return 'OTHER';
    }
  }
  
  String get displayName {
    switch (this) {
      case FileType.avatar:
        return 'Ảnh đại diện';
      case FileType.idCard:
        return 'Giấy tờ tùy thân';
      case FileType.liveness:
        return '<PERSON><PERSON><PERSON> thực khuôn mặt';
      case FileType.icon:
        return 'Biểu tượng';
      case FileType.collocation:
        return 'Đồng vị trí';
      case FileType.proposal:
        return 'Đề xuất';
      case FileType.statement:
        return 'Báo cáo';
      case FileType.sign:
        return 'Chữ ký';
      case FileType.custom:
        return 'Tùy chỉnh';
      case FileType.other:
        return 'Khác';
    }
  }
} 