import 'package:permission_handler/permission_handler.dart';

/// Enum định nghĩa các loại permission cần thiết cho app
enum PermissionType {
  /// Quyền truy cập camera
  camera(Permission.camera, 'camera'),
  
  /// Quyền truy cập thư viện ảnh
  photos(Permission.photos, 'photos'),
  
  /// Quyền truy cập thư viện <PERSON>nh (iOS)
  photoLibrary(Permission.photos, 'photoLibrary'),
  
  /// Quyền truy cập microphone
  microphone(Permission.microphone, 'microphone'),
  
  /// Quyền truy cập vị trí
  location(Permission.location, 'location'),
  
  /// Quyền truy cập vị trí khi app đang chạy
  locationWhenInUse(Permission.locationWhenInUse, 'locationWhenInUse'),
  
  /// Quyền truy cập vị trí luôn luôn
  locationAlways(Permission.locationAlways, 'locationAlways'),
  
  /// Quyền ghi file
  storage(Permission.storage, 'storage'),
  
  /// Quyền truy cập video
  videos(Permission.videos, 'videos'),
  
  /// Quyền truy cập âm thanh
  audio(Permission.audio, 'audio'),
  
  /// Quyền quản lý bộ nhớ ngoài
  manageExternalStorage(Permission.manageExternalStorage, 'manageExternalStorage'),
  
  /// Quyền truy cập vị trí media
  accessMediaLocation(Permission.accessMediaLocation, 'accessMediaLocation'),
  
  /// Quyền nhận diện hoạt động
  activityRecognition(Permission.activityRecognition, 'activityRecognition'),
  
  /// Quyền truy cập Bluetooth
  bluetooth(Permission.bluetooth, 'bluetooth'),
  
  /// Quyền kết nối Bluetooth
  bluetoothConnect(Permission.bluetoothConnect, 'bluetoothConnect'),
  
  /// Quyền quét Bluetooth
  bluetoothScan(Permission.bluetoothScan, 'bluetoothScan'),
  
  /// Quyền quảng cáo Bluetooth
  bluetoothAdvertise(Permission.bluetoothAdvertise, 'bluetoothAdvertise'),
  
  /// Quyền truy cập lịch
  calendar(Permission.calendarFullAccess, 'calendar'),
  
  /// Quyền truy cập danh bạ
  contacts(Permission.contacts, 'contacts'),
  
  /// Quyền truy cập điện thoại
  phone(Permission.phone, 'phone'),
  
  /// Quyền truy cập cảm biến
  sensors(Permission.sensors, 'sensors'),
  
  /// Quyền truy cập SMS
  sms(Permission.sms, 'sms'),
  
  /// Quyền nhận diện giọng nói
  speech(Permission.speech, 'speech'),
  
  /// Quyền gửi thông báo
  notification(Permission.notification, 'notification'),
  
  /// Quyền theo dõi quảng cáo
  appTrackingTransparency(Permission.appTrackingTransparency, 'appTrackingTransparency'),
  
  /// Quyền gửi cảnh báo quan trọng
  criticalAlerts(Permission.criticalAlerts, 'criticalAlerts'),
  
  /// Quyền truy cập thư viện media
  mediaLibrary(Permission.mediaLibrary, 'mediaLibrary'),
  
  /// Quyền truy cập thiết bị WiFi gần đó
  nearbyWifiDevices(Permission.nearbyWifiDevices, 'nearbyWifiDevices'),
  
  /// Quyền hiển thị cửa sổ cảnh báo hệ thống
  systemAlertWindow(Permission.systemAlertWindow, 'systemAlertWindow'),
  
  /// Quyền bỏ qua tối ưu pin
  ignoreBatteryOptimizations(Permission.ignoreBatteryOptimizations, 'ignoreBatteryOptimizations'),
  
  /// Quyền truy cập chính sách thông báo
  accessNotificationPolicy(Permission.accessNotificationPolicy, 'accessNotificationPolicy');

  const PermissionType(this.permission, this.key);
  
  final Permission permission;
  final String key;
  
  /// Lấy tên hiển thị của permission
  String get displayName {
    switch (this) {
      case PermissionType.camera:
        return 'Camera';
      case PermissionType.photos:
        return 'Thư viện ảnh';
      case PermissionType.photoLibrary:
        return 'Thư viện ảnh';
      case PermissionType.microphone:
        return 'Microphone';
      case PermissionType.location:
        return 'Vị trí';
      case PermissionType.locationWhenInUse:
        return 'Vị trí khi sử dụng';
      case PermissionType.locationAlways:
        return 'Vị trí luôn luôn';
      case PermissionType.storage:
        return 'Bộ nhớ';
      case PermissionType.videos:
        return 'Video';
      case PermissionType.audio:
        return 'Âm thanh';
      case PermissionType.manageExternalStorage:
        return 'Quản lý bộ nhớ ngoài';
      case PermissionType.accessMediaLocation:
        return 'Truy cập vị trí media';
      case PermissionType.activityRecognition:
        return 'Nhận diện hoạt động';
      case PermissionType.bluetooth:
        return 'Bluetooth';
      case PermissionType.bluetoothConnect:
        return 'Kết nối Bluetooth';
      case PermissionType.bluetoothScan:
        return 'Quét Bluetooth';
      case PermissionType.bluetoothAdvertise:
        return 'Quảng cáo Bluetooth';
      case PermissionType.calendar:
        return 'Lịch';
      case PermissionType.contacts:
        return 'Danh bạ';
      case PermissionType.phone:
        return 'Điện thoại';
      case PermissionType.sensors:
        return 'Cảm biến';
      case PermissionType.sms:
        return 'SMS';
      case PermissionType.speech:
        return 'Nhận diện giọng nói';
      case PermissionType.notification:
        return 'Thông báo';
      case PermissionType.appTrackingTransparency:
        return 'Theo dõi quảng cáo';
      case PermissionType.criticalAlerts:
        return 'Cảnh báo quan trọng';
      case PermissionType.mediaLibrary:
        return 'Thư viện media';
      case PermissionType.nearbyWifiDevices:
        return 'Thiết bị WiFi gần đó';
      case PermissionType.systemAlertWindow:
        return 'Cửa sổ cảnh báo hệ thống';
      case PermissionType.ignoreBatteryOptimizations:
        return 'Bỏ qua tối ưu pin';
      case PermissionType.accessNotificationPolicy:
        return 'Truy cập chính sách thông báo';
    }
  }
  
  /// Lấy mô tả chi tiết của permission
  String get description {
    switch (this) {
      case PermissionType.camera:
        return 'Ứng dụng cần quyền truy cập camera để chụp ảnh giấy tờ tùy thân';
      case PermissionType.photos:
      case PermissionType.photoLibrary:
        return 'Ứng dụng cần quyền truy cập thư viện ảnh để chọn ảnh giấy tờ tùy thân';
      case PermissionType.microphone:
        return 'Ứng dụng cần quyền truy cập microphone để ghi âm';
      case PermissionType.location:
      case PermissionType.locationWhenInUse:
      case PermissionType.locationAlways:
        return 'Ứng dụng cần quyền truy cập vị trí để xác định địa điểm';
      case PermissionType.storage:
        return 'Ứng dụng cần quyền truy cập bộ nhớ để lưu trữ dữ liệu';
      case PermissionType.videos:
        return 'Ứng dụng cần quyền truy cập video';
      case PermissionType.audio:
        return 'Ứng dụng cần quyền truy cập âm thanh';
      case PermissionType.manageExternalStorage:
        return 'Ứng dụng cần quyền quản lý bộ nhớ ngoài';
      case PermissionType.accessMediaLocation:
        return 'Ứng dụng cần quyền truy cập vị trí media';
      case PermissionType.activityRecognition:
        return 'Ứng dụng cần quyền nhận diện hoạt động';
      case PermissionType.bluetooth:
      case PermissionType.bluetoothConnect:
      case PermissionType.bluetoothScan:
      case PermissionType.bluetoothAdvertise:
        return 'Ứng dụng cần quyền truy cập Bluetooth';
      case PermissionType.calendar:
        return 'Ứng dụng cần quyền truy cập lịch';
      case PermissionType.contacts:
        return 'Ứng dụng cần quyền truy cập danh bạ';
      case PermissionType.phone:
        return 'Ứng dụng cần quyền truy cập điện thoại';
      case PermissionType.sensors:
        return 'Ứng dụng cần quyền truy cập cảm biến';
      case PermissionType.sms:
        return 'Ứng dụng cần quyền truy cập SMS';
      case PermissionType.speech:
        return 'Ứng dụng cần quyền nhận diện giọng nói';
      case PermissionType.notification:
        return 'Ứng dụng cần quyền gửi thông báo';
      case PermissionType.appTrackingTransparency:
        return 'Ứng dụng cần quyền theo dõi quảng cáo';
      case PermissionType.criticalAlerts:
        return 'Ứng dụng cần quyền gửi cảnh báo quan trọng';
      case PermissionType.mediaLibrary:
        return 'Ứng dụng cần quyền truy cập thư viện media';
      case PermissionType.nearbyWifiDevices:
        return 'Ứng dụng cần quyền truy cập thiết bị WiFi gần đó';
      case PermissionType.systemAlertWindow:
        return 'Ứng dụng cần quyền hiển thị cửa sổ cảnh báo hệ thống';
      case PermissionType.ignoreBatteryOptimizations:
        return 'Ứng dụng cần quyền bỏ qua tối ưu pin';
      case PermissionType.accessNotificationPolicy:
        return 'Ứng dụng cần quyền truy cập chính sách thông báo';
    }
  }
} 