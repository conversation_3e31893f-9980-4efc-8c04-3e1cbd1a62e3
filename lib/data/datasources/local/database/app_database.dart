import 'dart:async';
import 'package:floor/floor.dart';
import 'package:sqflite/sqflite.dart' as sqflite;

// Data barrel export
import 'package:sales_app/data/data.dart';

// Local imports for DAO
import '../dao/user_dao.dart';

// Floor code generation sẽ tạo file này
part 'app_database.g.dart';

/// Main Database class cho Sales App
/// Sử dụng Floor ORM để quản lý SQLite database
@Database(
  version: 1,
  entities: [
    UserLocalModel,
  ],
)
abstract class AppDatabase extends FloorDatabase {
  
  // ==================== DAO GETTERS ====================
  
  /// User DAO để thao tác với users table
  UserDao get userDao;
  
  // ==================== DATABASE INSTANCE ====================
  
  /// Singleton instance của database
  static AppDatabase? _instance;
  
  /// Get database instance (singleton pattern)
  static Future<AppDatabase> getInstance() async {
    _instance ??= await _createDatabase();
    return _instance!;
  }
  
  /// Create database instance
  static Future<AppDatabase> _createDatabase() async {
    return await $FloorAppDatabase
        .databaseBuilder('sales_app_database.db')
        .addMigrations([
          // Migrations sẽ được thêm ở đây khi cần
        ])
        .build();
  }
  
  // ==================== DATABASE UTILITIES ====================
  
  /// Close database connection
  static Future<void> closeDatabase() async {
    if (_instance != null) {
      await _instance!.close();
      _instance = null;
    }
  }
  
  /// Clear all data (for testing hoặc logout)
  Future<void> clearAllData() async {
    await userDao.deleteAllUsers();
  }
  
  /// Get database info
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    final userCount = await userDao.getTotalUsersCount();
    
    return {
      'database_name': 'sales_app_database.db',
      'version': 1,
      'tables': ['users'],
      'user_count': userCount ?? 0,
    };
  }
}

// ==================== MIGRATION EXAMPLES ====================
// Khi cần update database schema, thêm migrations ở đây

/// Migration từ version 1 sang 2 (example)
/// Uncomment khi cần sử dụng
/*
final migration1to2 = Migration(1, 2, (database) async {
  // Example: Add new column to users table
  await database.execute('ALTER TABLE users ADD COLUMN department TEXT');
});
*/

/// Migration từ version 2 sang 3 (example)
/*
final migration2to3 = Migration(2, 3, (database) async {
  // Example: Create new table
  await database.execute('''
    CREATE TABLE customers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      email TEXT,
      phone TEXT,
      createdAt INTEGER NOT NULL,
      updatedAt INTEGER NOT NULL,
      syncStatus TEXT NOT NULL DEFAULT 'synced'
    )
  ''');
});
*/
