// Domain barrel export
import 'package:sales_app/domain/domain.dart';

/// Abstract Local Data Source cho User operations
/// <PERSON><PERSON><PERSON> nghĩa interface cho local database operations
/// Theo Clean Architecture, data sources là abstract interfaces
abstract class UserLocalDataSource {
  
  // ==================== BASIC CRUD OPERATIONS ====================
  
  /// Save user to local storage
  Future<void> saveUser(User user);
  
  /// Get user by ID from local storage
  Future<User?> getUserById(String id);
  
  /// Get user by email (for login)
  Future<User?> getUserByEmail(String email);
  
  /// Get all users from local storage
  Future<List<User>> getAllUsers();
  
  /// Delete user by ID
  Future<void> deleteUser(String id);
  
  /// Delete all users (for logout/clear data)
  Future<void> deleteAllUsers();
  
  /// Check if user exists
  Future<bool> userExists(String id);
  
  /// Get total users count
  Future<int> getUsersCount();
  
  // ==================== SYNC OPERATIONS ====================
  
  /// Save user for offline sync (mark as pending)
  Future<void> saveUserOffline(User user);
  
  /// Get users that need to be synced to server
  Future<List<User>> getPendingSyncUsers();
  
  /// Get users with failed sync attempts
  Future<List<User>> getFailedSyncUsers();
  
  /// Mark user as successfully synced
  Future<void> markUserAsSynced(String userId, {int? serverTimestamp});
  
  /// Mark user sync as failed
  Future<void> markUserSyncFailed(String userId);
  
  // ==================== UTILITY OPERATIONS ====================
  
  /// Get users created after timestamp
  Future<List<User>> getUsersCreatedAfter(int timestamp);
  
  /// Get users updated after timestamp
  Future<List<User>> getUsersUpdatedAfter(int timestamp);
  
  /// Get database info for debugging
  Future<Map<String, dynamic>> getDatabaseInfo();
}
