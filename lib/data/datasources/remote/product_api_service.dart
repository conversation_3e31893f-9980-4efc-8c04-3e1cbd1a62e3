import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/core/error/failures.dart';
import 'package:sales_app/data/network/base_api_service.dart';

/// Example Product API Service using default ApiClient
/// This demonstrates how regular API services use all interceptors including auth
@injectable
class ProductApiService extends BaseApiService {
  ProductApiService(@Named('default') super.apiClient);

  /// Get all products
  Future<Either<Failure, List<Map<String, dynamic>>>> getProducts() async {
    return handleApiCallList(
      () => dio.get('/products'),
      (data) => data as Map<String, dynamic>,
    );
  }

  /// Get product by ID
  Future<Either<Failure, Map<String, dynamic>>> getProduct(String id) async {
    return handleApiCall(
      () => dio.get('/products/$id'),
      (data) => data as Map<String, dynamic>,
    );
  }

  /// Create new product
  Future<Either<Failure, Map<String, dynamic>>> createProduct(
    Map<String, dynamic> productData,
  ) async {
    return handleApiCall(
      () => dio.post(
        '/products',
        data: createRequestData(productData),
      ),
      (data) => data as Map<String, dynamic>,
    );
  }

  /// Update product
  Future<Either<Failure, Map<String, dynamic>>> updateProduct(
    String id,
    Map<String, dynamic> productData,
  ) async {
    return handleApiCall(
      () => dio.put(
        '/products/$id',
        data: createRequestData(productData),
      ),
      (data) => data as Map<String, dynamic>,
    );
  }

  /// Delete product
  Future<Either<Failure, void>> deleteProduct(String id) async {
    return handleApiCallVoid(
      () => dio.delete('/products/$id'),
    );
  }
}
