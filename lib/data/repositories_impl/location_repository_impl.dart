import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Core và Domain barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/domain.dart';
// Data barrel export
import 'package:sales_app/data/data.dart';
import 'package:sales_app/domain/entities/location/ward_entity.dart';

/// Location Repository Implementation
/// Handles province and branch data with caching support
/// 
/// Features:
/// - Province and branch data retrieval
/// - Local caching with configurable duration
/// - Serialization/deserialization for cache storage
@LazySingleton(as: LocationRepository)
class LocationRepositoryImpl implements LocationRepository {
  final OpenApiClient _openApiClient;
  final StorageService _storageService;
  static const cacheDuration = Duration(hours: 24);

  LocationRepositoryImpl(this._openApiClient, this._storageService);

  @override
  Future<Either<Failure, List<ProvinceEntity>>> getProvinces() async {
    try {
      // Kiểm tra cache trước
      final cacheTime = await _storageService.getProvincesCacheTime();
      final now = DateTime.now();
      
      if (cacheTime != null && now.difference(cacheTime) < cacheDuration) {
        final cached = await _storageService.getProvincesCache();
        if (cached != null) {
          AppLogger.info('Using cached provinces data');
          final provinces = _deserializeProvinces(cached);
          return Right(provinces);
        }
      }

      // Nếu không có cache hoặc cache hết hạn, gọi API
      AppLogger.info('Fetching provinces from API');
      final response = await _openApiClient.provinceApi.list1();
      
      if (response.data?.data?.provinces != null) {
        final provincesDto = response.data!.data!.provinces!.toList();
        final provinces = provincesDto.map((dto) => ProvinceEntity(
          id: dto.id ?? '',
          name: dto.name ?? '',
        )).toList();
        
        // Cache kết quả
        if (provinces.isNotEmpty) {
          final provincesJson = _serializeProvinces(provinces);
          await _storageService.saveProvincesCache(provincesJson, now);
          AppLogger.info('Cached ${provinces.length} provinces');
        }
        
        return Right(provinces);
      } else {
        AppLogger.error('Provinces API returned null data');
        return const Left(ServerFailure(ServerErrorType.notFound));
      }
    } on DioException catch (e) {
      AppLogger.error('Provinces API DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      AppLogger.error('Provinces API unexpected exception', error: e);
      
      // Handle unexpected exceptions (not DioException)
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, List<BranchEntity>>> getBranches(String provinceId) async {
    try {
      // Kiểm tra cache trước
      final cacheTime = await _storageService.getBranchesCacheTime(provinceId);
      final now = DateTime.now();
      
      if (cacheTime != null && now.difference(cacheTime) < cacheDuration) {
        final cached = await _storageService.getBranchesCache(provinceId);
        if (cached != null) {
          AppLogger.info('Using cached branches data for province: $provinceId');
          final branches = _deserializeBranches(cached);
          return Right(branches);
        }
      }

      // Nếu không có cache hoặc cache hết hạn, gọi API
      AppLogger.info('Fetching branches from API for province: $provinceId');
      final response = await _openApiClient.branchApi.list2(provinceId: provinceId);
      
      if (response.data?.data?.branches != null) {
        final branchesDto = response.data!.data!.branches!.toList();
        final branches = branchesDto.map((dto) => BranchEntity(
          id: dto.id ?? '',
          code: dto.code ?? '',
          name: dto.name ?? '',
          address: dto.address,
          provinceId: dto.provinceId,
        )).toList();
        
        // Cache kết quả
        if (branches.isNotEmpty) {
          final branchesJson = _serializeBranches(branches);
          await _storageService.saveBranchesCache(provinceId, branchesJson, now);
          AppLogger.info('Cached ${branches.length} branches for province: $provinceId');
        }
        
        return Right(branches);
      } else {
        AppLogger.error('Branches API returned null data for province: $provinceId');
        return const Left(ServerFailure(ServerErrorType.notFound));
      }
    } on DioException catch (e) {
      AppLogger.error('Branches API DioException for province: $provinceId', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      AppLogger.error('Branches API unexpected exception for province: $provinceId', error: e);
      
      // Handle unexpected exceptions (not DioException)
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, List<WardEntity>>> getWards(String provinceId) async {
    try {
      // Kiểm tra cache trước
      final cacheTime = await _storageService.getWardsCacheTime(provinceId);
      final now = DateTime.now();
      
      if (cacheTime != null && now.difference(cacheTime) < cacheDuration) {
        final cached = await _storageService.getWardsCache(provinceId);
        if (cached != null) {
          AppLogger.info('Using cached wards data for province: $provinceId');
          final wards = _deserializeWards(cached);
          return Right(wards);
        }
      }

      // Nếu không có cache hoặc cache hết hạn, gọi API
      AppLogger.info('Fetching wards from API for province: $provinceId');
      final response = await _openApiClient.wardApi.list(provinceId: provinceId);
      if (response.data?.data?.wards != null) {
        final wardsDto = response.data!.data!.wards!.toList();
        final wards = wardsDto.map((dto) => WardEntity(
          id: dto.id ?? '',
          name: dto.name ?? '',
        )).toList();
        // Cache kết quả
        if (wards.isNotEmpty) {
          final wardsJson = _serializeWards(wards);
          await _storageService.saveWardsCache(provinceId, wardsJson, now);
          AppLogger.info('Cached  [1m${wards.length} [0m wards for province: $provinceId');
        }
        return Right(wards);
      } else {
        AppLogger.error('Wards API returned null data for province: $provinceId');
        return const Left(ServerFailure(ServerErrorType.notFound));
      }
    } on DioException catch (e) {
      AppLogger.error('Wards API DioException for province: $provinceId', error: e);
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối:  [1m${e.message} [0m',
      ));
    } catch (e) {
      AppLogger.error('Wards API unexpected exception for province: $provinceId', error: e);
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định:  [1m${e.toString()} [0m',
      ));
    }
  }

  // Helper methods để serialize/deserialize entities
  List<dynamic> _serializeProvinces(List<ProvinceEntity> provinces) {
    return provinces.map((province) => {
      'id': province.id,
      'name': province.name,
    }).toList();
  }

  List<ProvinceEntity> _deserializeProvinces(List<dynamic> jsonList) {
    return jsonList.map((json) => ProvinceEntity(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
    )).toList();
  }

  List<dynamic> _serializeBranches(List<BranchEntity> branches) {
    return branches.map((branch) => {
      'id': branch.id,
      'code': branch.code,
      'name': branch.name,
      'address': branch.address,
      'provinceId': branch.provinceId,
    }).toList();
  }

  List<BranchEntity> _deserializeBranches(List<dynamic> jsonList) {
    return jsonList.map((json) => BranchEntity(
      id: json['id'] as String? ?? '',
      code: json['code'] as String? ?? '',
      name: json['name'] as String? ?? '',
      address: json['address'] as String?,
      provinceId: json['provinceId'] as String?,
    )).toList();
  }

  List<dynamic> _serializeWards(List<WardEntity> wards) {
    return wards.map((ward) => {
      'id': ward.id,
      'name': ward.name,
    }).toList();
  }

  List<WardEntity> _deserializeWards(List<dynamic> jsonList) {
    return jsonList.map((json) => WardEntity(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
    )).toList();
  }
} 