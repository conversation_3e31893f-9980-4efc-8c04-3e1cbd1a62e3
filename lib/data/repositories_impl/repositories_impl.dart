/// Repository Implementations Barrel Export
/// Concrete implementations of domain repository interfaces
/// 
/// This file provides a single import point for all repository implementations.
/// These implementations handle data access logic and coordinate between
/// remote and local data sources.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/data/repositories_impl/repositories_impl.dart';
/// 
/// // Repository implementations are typically registered in DI:
/// @module
/// abstract class RepositoryModule {
///   @Injectable(as: AuthRepository)
///   AuthRepositoryImpl authRepository(
///     OpenApiClient apiClient,
///     StorageService storageService,
///     DatabaseService databaseService,
///     NetworkInfo networkInfo,
///   ) => AuthRepositoryImpl(apiClient, storageService, databaseService, networkInfo);
/// }
/// ```
/// 
/// ## Repository Implementation Categories:
/// - **Auth Repositories**: Authentication and user management implementations
/// - **Location Repositories**: Province and branch data management implementations
/// - **Media Repositories**: File upload, download, and media operations
/// - **Article Repositories**: Content and article management
/// - **Referrer Repositories**: Referrer data retrieval
/// - **CTV Registration Repositories**: Collaborator registration process
/// - **System Config Repositories**: System configuration data management
library repositories_impl;

// Auth repositories - authentication and user management
export 'auth_repository_impl.dart';

// Location repositories - province and branch data
export 'location_repository_impl.dart';

// Media repositories - file upload and media operations
export 'media_repository_impl.dart';

// Article repositories - content management
export 'article_repository_impl.dart';

// Referrer repositories - referrer data
export 'referrer_repository_impl.dart';

// CTV Registration repositories - collaborator registration
export 'ctv_registration_repository_impl.dart';

// Loan repositories - loan creation and management
export 'loan_repository_impl.dart';

// System Config repositories - system configuration data
export 'system_config_repository_impl.dart';
