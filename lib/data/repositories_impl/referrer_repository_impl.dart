import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Core và Domain barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/domain.dart';
// Data barrel export
import 'package:sales_app/data/data.dart';

/// Referrer Repository Implementation
/// Handles referrer data retrieval from API
@Injectable(as: ReferrerRepository)
class ReferrerRepositoryImpl implements ReferrerRepository {
  final OpenApiClient _openApiClient;

  const ReferrerRepositoryImpl(this._openApiClient);

  @override
  Future<Either<Failure, ReferrerEntity>> getReferrerByCode(String referrerCode) async {
    try {
      final response = await _openApiClient.usersApi.getReferrerByCode(
        referrerCode: referrerCode,
      );

      if (response.data?.data != null) {
        final dto = response.data!.data!;
        final entity = ReferrerEntity(
          referrerCode: dto.referrerCode ?? '',
          referrerName: dto.referrerName ?? '',
        );
        return Right(entity);
      } else {
        return const Left(ServerFailure(ServerErrorType.notFound));
      }
    } on DioException catch (e) {
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }
} 