import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app_api/sales_app_api.dart' hide User;

// Core và Domain barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/domain.dart';
// Data barrel export
import 'package:sales_app/data/data.dart';

/// Authentication Repository Implementation
/// Handles user authentication, login/logout, and token management
/// 
/// Features:
/// - Online/offline login support
/// - Token refresh and management
/// - Local user data persistence
/// - Network connectivity awareness
@Injectable(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  final OpenApiClient _openApiClient;
  final StorageService _storageService;
  final DatabaseService _databaseService;
  final NetworkInfo _networkInfo;

  AuthRepositoryImpl(
    this._openApiClient,
    this._storageService,
    this._databaseService,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
  }) async {
    AppLogger.info('Attempting login', data: {'email': email});

    // Check network connectivity
    final isConnected = await _networkInfo.isConnected;

    if (isConnected) {
      // Online login
      return await _loginOnline(email, password);
    } else {
      // Offline login - check local database
      return await _loginOffline(email, password);
    }
  }

  /// Online login with API
  Future<Either<Failure, User>> _loginOnline(
      String email, String password) async {
    try {
      // Create login request
      final request = AppLoginRequest((b) => b
        ..username = email
        ..password = password);

      AppLogger.debug('Calling login API', data: {'email': email});

      // Call authentication API
      final response = await _openApiClient.authenticationApi.login(
        appLoginRequest: request,
      );

      // Check if API call was successful
      if (response.data?.success != true || response.data?.data == null) {
        AppLogger.error('Login API returned error', data: {
          'success': response.data?.success,
          'code': response.data?.code,
          'message': response.data?.message,
        });
        return Left(AuthFailure(
          AuthErrorType.invalidCredentials,
          serverMessage: response.data?.message ?? 'Đăng nhập thất bại',
        ));
      }

      final loginData = response.data!.data!;

      // Create User entity from response data
      final user = User(
        id: loginData.cifNo ?? '',
        email: loginData.email ?? email,
        name: loginData.fullName ?? '',
        phone: loginData.phoneNumber,
      );

      // Store authentication tokens
      await _storeAuthTokens(loginData);

      // ✅ Lưu user vào storage để _checkInitialLoginStatus có thể lấy
      await _storageService.saveUserInfo(user);

      // Save user to local database for offline access
      await _databaseService.saveUser(user);

      AppLogger.info('Online login successful', data: {
        'userId': user.id,
        'email': user.email,
        'fullName': user.name,
      });

      return Right(user);
    } on DioException catch (e) {
      AppLogger.error('Online login DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return Left(AuthFailure(
        AuthErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      AppLogger.error('Online login unexpected exception', error: e);
      
      // Handle unexpected exceptions (not DioException)
      return Left(AuthFailure(
        AuthErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  /// Offline login using local database
  Future<Either<Failure, User>> _loginOffline(
      String email, String password) async {
    try {
      // Check if user exists in local database
      final localUser = await _databaseService.getUserByEmail(email);

      if (localUser == null) {
        AppLogger.warning('Offline login failed - user not found',
            data: {'email': email});
        return const Left(AuthFailure(AuthErrorType.userNotFound));
      }

      // Note: In real app, you should store password hash for offline verification
      // For now, we'll allow offline login if user exists locally
      AppLogger.info('Offline login successful', data: {
        'userId': localUser.id,
        'email': localUser.email,
      });

      return Right(localUser);
    } catch (e) {
      AppLogger.error('Offline login exception', error: e);
      return Left(AuthFailure(
        AuthErrorType.unknown,
        serverMessage: 'Lỗi đăng nhập offline: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    AppLogger.info('Attempting logout');

    try {
      // 1. Lấy tokens hiện tại để gọi API logout
      final accessToken = await _storageService.getAccessToken();
      final refreshToken = await _storageService.getRefreshToken();

      // 2. Gọi API logout nếu có tokens
      if (accessToken != null && refreshToken != null) {
        try {
          AppLogger.info('Calling logout API: /system/api/v1/auth/logout');
          
          // Tạo logout request
          final logoutRequest = AppLogoutRequest((b) => b
            ..accessToken = accessToken
            ..refreshToken = refreshToken);

          // Gọi API logout
          final response = await _openApiClient.authenticationApi.logout(
            appLogoutRequest: logoutRequest,
          );

          AppLogger.info('Logout API response received', data: {
            'statusCode': response.statusCode,
            'success': response.data?.success,
            'code': response.data?.code,
            'message': response.data?.message,
            'logoutAt': response.data?.data?.logoutAt?.toIso8601String(),
          });

          // Kiểm tra response
          if (response.data?.success != true) {
            AppLogger.warning('Logout API returned error', data: {
              'success': response.data?.success,
              'code': response.data?.code,
              'message': response.data?.message,
            });
            // Vẫn tiếp tục xóa dữ liệu local ngay cả khi API thất bại
          } else {
            AppLogger.info('Logout API call successful');
          }
        } on DioException catch (e) {
          AppLogger.error('Logout API DioException', error: e);
          // Vẫn tiếp tục xóa dữ liệu local ngay cả khi API thất bại
        } catch (e) {
          AppLogger.error('Logout API unexpected exception', error: e);
          // Vẫn tiếp tục xóa dữ liệu local ngay cả khi API thất bại
        }
      } else {
        AppLogger.info('No tokens found, skipping API logout call');
      }

      // 3. Xóa tất cả dữ liệu local
      await _clearAllAuthData();
      
      AppLogger.info('Logout completed successfully');
      return const Right(null);
    } catch (e) {
      AppLogger.error('Logout failed with exception', error: e);
      return Left(AuthFailure(
        AuthErrorType.unknown,
        serverMessage: 'Lỗi đăng xuất: ${e.toString()}',
      ));
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    // Check both token and local user data
    final token = await _storageService.getAccessToken();
    final hasToken = token != null && token.isNotEmpty;

    // Also check if we have user data locally (for offline support)
    // final usersCount = await _databaseService.getUsersCount();
    // final hasLocalUser = usersCount > 0;

    return hasToken /*|| hasLocalUser*/;
  }

  @override
  Future<Either<Failure, User>> getCurrentUser() async {
    try {
      // 1. Check if we have valid authentication first
      final hasValidAuth = await isLoggedIn();
      
      if (!hasValidAuth) {
        AppLogger.warning('No valid authentication found');
        return const Left(AuthFailure(AuthErrorType.userNotFound));
      }
      
      // 2. Try to get user from storage first (most reliable)
      final userFromStorage = await _storageService.getUserInfo();
      
      if (userFromStorage != null) {
        AppLogger.info('Retrieved current user from storage', data: {
          'userId': userFromStorage.id,
          'email': userFromStorage.email,
        });
        return Right(userFromStorage);
      }
      
      // 3. Fallback to database if storage is empty
      final users = await _databaseService.getAllUsers();
      
      if (users.isEmpty) {
        AppLogger.warning('No users found in local database');
        return const Left(AuthFailure(AuthErrorType.userNotFound));
      }

      // For now, return the first user (in real app, you might want to store current user ID)
      final currentUser = users.first;
      
      AppLogger.info('Retrieved current user from database', data: {
        'userId': currentUser.id,
        'email': currentUser.email,
      });
      
      return Right(currentUser);
    } catch (e) {
      AppLogger.error('Failed to get current user', error: e);
      return Left(AuthFailure(
        AuthErrorType.unknown,
        serverMessage: 'Lỗi lấy thông tin user: ${e.toString()}',
      ));
    }
  }

  /// Store authentication tokens from login response
  Future<void> _storeAuthTokens(AppLoginResponse loginData) async {
    try {
      // Store access token
      if (loginData.accessToken != null && loginData.accessToken!.isNotEmpty) {
        await _storageService.saveAccessToken(loginData.accessToken!);
      }

      // Store refresh token
      if (loginData.refreshToken != null && loginData.refreshToken!.isNotEmpty) {
        await _storageService.saveRefreshToken(loginData.refreshToken!);
      }

      // Calculate and store token expiry time
      if (loginData.expiresIn != null) {
        final expiryTime = DateTime.now().add(Duration(seconds: loginData.expiresIn!));
        await _storageService.saveTokenExpiryTime(expiryTime);
      }

      AppLogger.debug('Auth tokens stored successfully');
    } catch (e) {
      AppLogger.error('Failed to store auth tokens', error: e);
      // Don't throw here - token storage failure shouldn't break login
    }
  }

  /// Clear all authentication data
  Future<void> _clearAllAuthData() async {
    try {
      // Clear tokens
      await _storageService.clearTokens();

      // Clear local user data
      // await _databaseService.clearAllUsers();

      AppLogger.debug('All auth data cleared successfully');
    } catch (e) {
      AppLogger.error('Failed to clear auth data', error: e);
      // Don't throw here - cleanup failure shouldn't break logout
    }
  }

  @override
  Future<Either<Failure, User>> getProfile() async {
    try {
      AppLogger.info('🔍 Fetching user profile from API: system/api/v1/users/profile');
      
      // Call profile API directly - network check is handled by interceptor
      final response = await _openApiClient.usersApi.getProfile();
      
      AppLogger.info('📥 Profile API response received', data: {
        'statusCode': response.statusCode,
        'success': response.data?.success,
        'code': response.data?.code,
        'message': response.data?.message,
        'hasData': response.data?.data != null,
      });
      
      if (response.data?.success == true && response.data?.data != null) {
        final profileData = response.data!.data!;
        
                      AppLogger.info('✅ Profile data extracted', data: {
                'fullName': profileData.fullName,
                'phoneNumber': profileData.phoneNumber,
                'code': profileData.code,
                'cifNo': profileData.cifNo,
                'email': profileData.email,
                'branch': {
                  'id': profileData.branch?.id,
                  'code': profileData.branch?.code,
                  'name': profileData.branch?.name,
                  'address': profileData.branch?.address,
                  'provinceId': profileData.branch?.provinceId,
                },
              });
        
        // Get current user to update with profile data
        final currentUserResult = await getCurrentUser();
        return currentUserResult.fold(
          (failure) => Left(failure),
          (currentUser) async {
            // Map AppBranchDto to BranchEntity
            BranchEntity? branchEntity;
            if (profileData.branch != null) {
              branchEntity = BranchEntity(
                id: profileData.branch!.id ?? '',
                code: profileData.branch!.code ?? '',
                name: profileData.branch!.name ?? '',
                address: profileData.branch!.address,
                provinceId: profileData.branch!.provinceId,
              );
            }
            
            // Update user with profile data
            final updatedUser = currentUser.copyWith(
              name: profileData.fullName ?? currentUser.name,
              phone: profileData.phoneNumber ?? currentUser.phone,
              branch: branchEntity,
              // Có thể thêm các field khác từ profile nếu cần
            );
            
            // Save updated user to storage and database
            await _storageService.saveUserInfo(updatedUser);
            await _databaseService.saveUser(updatedUser);
            
            AppLogger.info('🔄 User profile updated and saved', data: {
              'userId': updatedUser.id,
              'email': updatedUser.email,
              'fullName': updatedUser.name,
              'phone': updatedUser.phone,
              'originalName': currentUser.name,
              'originalPhone': currentUser.phone,
              'branch': {
                'id': updatedUser.branch?.id,
                'code': updatedUser.branch?.code,
                'name': updatedUser.branch?.name,
                'address': updatedUser.branch?.address,
                'provinceId': updatedUser.branch?.provinceId,
              },
            });
            
            return Right(updatedUser);
          },
        );
      } else {
        AppLogger.warning('⚠️ Profile API returned error', data: {
          'success': response.data?.success,
          'code': response.data?.code,
          'message': response.data?.message,
          'statusCode': response.statusCode,
        });
        
        return Left(ServerFailure(
          ServerErrorType.unknown,
          serverMessage: response.data?.message ?? 'Lỗi lấy thông tin profile',
        ));
      }
    } on DioException catch (e) {
      AppLogger.error('❌ Profile API DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối profile API: ${e.message}',
      ));
    } catch (e) {
      AppLogger.error('❌ Profile API unexpected exception', error: e);
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }
}
