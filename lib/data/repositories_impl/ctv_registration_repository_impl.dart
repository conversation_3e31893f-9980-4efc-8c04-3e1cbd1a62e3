import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app_api/sales_app_api.dart';

// Core và Domain barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/domain.dart';
// Data barrel export
import 'package:sales_app/data/data.dart';

/// CTV Registration Repository Implementation
/// <PERSON>les collaborator registration process with media upload support
@LazySingleton(as: CTVRegistrationRepository)
class CTVRegistrationRepositoryImpl implements CTVRegistrationRepository {
  final LocationRepository _locationRepository;
  final ReferrerRepository _referrerRepository;
  final OpenApiClient _openApiClient;
  final MediaRepository _mediaRepository;

  CTVRegistrationRepositoryImpl(
    this._locationRepository,
    this._referrerRepository,
    this._openApiClient,
    this._mediaRepository,
  );

  @override
  Future<Either<Failure, List<ProvinceEntity>>> getProvinces() async {
    // Delegate to LocationRepository
    return await _locationRepository.getProvinces();
  }

  @override
  Future<Either<Failure, List<BranchEntity>>> getBranches(String provinceId) async {
    // Delegate to LocationRepository
    return await _locationRepository.getBranches(provinceId);
  }

  @override
  Future<Either<Failure, ReferrerEntity>> getReferrerByCode(String referrerCode) async {
    // Delegate to ReferrerRepository
    return await _referrerRepository.getReferrerByCode(referrerCode);
  }

  @override
  Future<Either<Failure, CTVRegistrationResult>> registerCTV({
    required String fullName,
    required DocumentType? documentType,
    required String idCardNo,
    required String phoneNumber,
    required String permanentAddress,
    required String provinceId,
    required String branchId,
    required String frontCardUrl,
    required String backCardUrl,
    String? referrerCode,
    String? email,
  }) async {
    try {
      AppLogger.info('Calling CTV registration API', data: {
        'fullName': fullName,
        'documentType': documentType?.name,
        'phoneNumber': phoneNumber,
        'provinceId': provinceId,
        'branchId': branchId,
        'referrerCode': referrerCode,
      });

      // Tạo request object từ OpenAPI
      final request = AppRegisterCollaboratorRequest(
        (b) => b
          ..fullName = fullName
          ..idCardType = _mapIdCardType(documentType)
          ..idCardNo = idCardNo
          ..phoneNumber = phoneNumber
          ..permanentAddress = permanentAddress
          // ..provinceId = provinceId
          ..branchId = branchId
          ..frontCardUrl = frontCardUrl
          ..backCardUrl = backCardUrl
          ..referrerCode = referrerCode
          ..email = email,
      );

      // Call API đăng ký CTV qua OpenApiClient
      final response = await _openApiClient.registersApi.register(
        appRegisterCollaboratorRequest: request,
      );

      // Kiểm tra response
      if (response.data?.success != true) {
        AppLogger.error('CTV registration API returned error', data: {
          'success': response.data?.success,
          'code': response.data?.code,
          'message': response.data?.message,
        });
        return Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: response.data?.message ?? 'Đăng ký CTV thất bại',
        ));
      }

      // Map response data thành entity
      final responseData = response.data?.data;
      if (responseData == null) {
        AppLogger.error('CTV registration API returned null data');
        return const Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: 'Dữ liệu phản hồi không hợp lệ',
        ));
      }

      final result = CTVRegistrationResult(
        fullName: responseData.fullName,
        branchName: responseData.branchName,
        positionName: responseData.positionName,
      );

      AppLogger.info('CTV registration successful', data: {
        'fullName': result.fullName,
        'branchName': result.branchName,
        'positionName': result.positionName,
      });

      return Right(result);
    } on DioException catch (e) {
      AppLogger.error('CTV registration DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      AppLogger.error('CTV registration unexpected exception', error: e);
      
      // Handle unexpected exceptions (not DioException)
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  /// Upload identity documents và trả về URLs
  @override
  Future<Either<Failure, Map<String, String>>> uploadIdentityDocuments({
    required File frontFile,
    required File backFile,
  }) async {
    try {
      AppLogger.info('Starting identity documents upload', data: {
        'frontFile': frontFile.path,
        'backFile': backFile.path,
      });

      // Upload cả 2 files cùng lúc
      final uploadResult = await _mediaRepository.uploadMultipleFiles(
        files: [frontFile, backFile],
        fileType: FileType.idCard,
      );

      if (uploadResult.isLeft()) {
        final failure = uploadResult
            .swap()
            .getOrElse(() => const ServerFailure(ServerErrorType.unknown));
        AppLogger.error('Identity documents upload failed',
            data: {'failure': failure.toString()});
        return Left(failure);
      }

      final urls = uploadResult.getOrElse(() => []);
      if (urls.length != 2) {
        AppLogger.error('Expected 2 URLs but got ${urls.length}');
        return Left(ServerFailure(
          ServerErrorType.unknown,
          serverMessage: 'Số lượng URL không đúng: ${urls.length}',
        ));
      }

      final frontUrl = urls[0];
      final backUrl = urls[1];

      AppLogger.info('Identity documents uploaded successfully', data: {
        'frontUrl': frontUrl,
        'backUrl': backUrl,
      });

      return Right({
        'frontUrl': frontUrl,
        'backUrl': backUrl,
      });
    } catch (e) {
      AppLogger.error('Error uploading identity documents', error: e);
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  /// Map DocumentType sang String value
  String _mapDocumentTypeToString(DocumentType? documentType) {
    switch (documentType) {
      case DocumentType.cccd:
      case DocumentType.theCanCuoc:
        return 'CHIP_ID';
      case DocumentType.passport:
        return 'PASSPORT';
      default:
        return 'CHIP_ID';
    }
  }

  /// Map idCardType string sang enum của OpenAPI cho registration
  AppRegisterCollaboratorRequestIdCardTypeEnum _mapIdCardType(DocumentType? documentType) {
    final typeString = _mapDocumentTypeToString(documentType);
    return AppRegisterCollaboratorRequestIdCardTypeEnum.valueOf(typeString);
  }

  /// Map idCardType string sang enum của OpenAPI cho referral
  AppReferralRegisterCollabRequestIdCardTypeEnum _mapReferralIdCardType(DocumentType? documentType) {
    final typeString = _mapDocumentTypeToString(documentType);
    return AppReferralRegisterCollabRequestIdCardTypeEnum.valueOf(typeString);
  }

  @override
  Future<Either<Failure, CTVReferralResult>> referCTV({
    required String fullName,
    required DocumentType? documentType,
    required String idCardNo,
    required String phoneNumber,
    required String permanentAddress,
    required String provinceId,
    required String branchId,
    required String frontCardUrl,
    required String backCardUrl,
    String? email,
  }) async {
    try {
      AppLogger.info('Calling CTV referral API', data: {
        'fullName': fullName,
        'documentType': documentType?.name,
        'phoneNumber': phoneNumber,
        'provinceId': provinceId,
        'branchId': branchId,
      });

      // Tạo request object từ OpenAPI
      final request = AppReferralRegisterCollabRequest(
        (b) => b
          ..fullName = fullName
          ..idCardType = _mapReferralIdCardType(documentType)
          ..idCardNo = idCardNo
          ..phoneNumber = phoneNumber
          ..permanentAddress = permanentAddress
          ..branchId = branchId
          ..frontCardUrl = frontCardUrl
          ..backCardUrl = backCardUrl
          ..email = email,
      );

      // Call API giới thiệu CTV qua OpenApiClient
      final response = await _openApiClient.registersApi.registerReferral(
        appReferralRegisterCollabRequest: request,
      );

      // Kiểm tra response
      if (response.data?.success != true) {
        AppLogger.error('CTV referral API returned error', data: {
          'success': response.data?.success,
          'code': response.data?.code,
          'message': response.data?.message,
        });
        return Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: response.data?.message ?? 'Giới thiệu CTV thất bại',
        ));
      }

      // Map response data thành entity
      final responseData = response.data?.data;
      if (responseData == null) {
        AppLogger.error('CTV referral API returned null data');
        return const Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: 'Dữ liệu phản hồi không hợp lệ',
        ));
      }

      final result = CTVReferralResult(
        fullName: responseData.fullName,
        branchName: responseData.branchName,
        positionName: responseData.positionName,
        referrerCode: responseData.referrerCode,
        referrerName: responseData.referrerName,
      );

      AppLogger.info('CTV referral successful', data: {
        'fullName': result.fullName,
        'branchName': result.branchName,
        'positionName': result.positionName,
        'referrerCode': result.referrerCode,
        'referrerName': result.referrerName,
      });

      return Right(result);
    } on DioException catch (e) {
      AppLogger.error('CTV referral DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      AppLogger.error('CTV referral unexpected exception', error: e);
      
      // Handle unexpected exceptions (not DioException)
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

} 