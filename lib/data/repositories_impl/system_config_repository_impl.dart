import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Core và Domain barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/domain.dart';
// Data barrel export
import 'package:sales_app/data/data.dart';

/// System Config Repository Implementation
/// Handles system configuration data retrieval using OpenApiClient
/// 
/// Features:
/// - System config retrieval by group code
/// - Marital status and gender data from API
/// - Error handling with proper failure types
@LazySingleton(as: SystemConfigRepository)
class SystemConfigRepositoryImpl implements SystemConfigRepository {
  final OpenApiClient _openApiClient;

  SystemConfigRepositoryImpl(this._openApiClient);

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getSystemConfigsByGroupCode(
    String groupCode,
  ) async {
    try {
      AppLogger.info('Fetching system configs for group code: $groupCode');
      final response = await _openApiClient.systemConfigApi.getSystemConfigsByGroupCode(
        groupCode: groupCode,
      );
      
      if (response.data?.data?.configs != null) {
        final configsDto = response.data!.data!.configs!.toList();
        final configs = configsDto.map((dto) => SystemConfigEntity(
          id: dto.id ?? '',
          code: dto.code ?? '',
          label: dto.label ?? '',
        )).toList();
        
        AppLogger.info('Retrieved ${configs.length} configs for group: $groupCode');
        return Right(configs);
      } else {
        AppLogger.error('System config API returned null data for group: $groupCode');
        return const Left(ServerFailure(ServerErrorType.notFound));
      }
    } on DioException catch (e) {
      AppLogger.error('System config API DioException for group: $groupCode', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return const Left(const ServerFailure(
        ServerErrorType.unknown,
      ));
    } catch (e) {
      AppLogger.error('System config API unexpected exception for group: $groupCode', error: e);
      
      // Handle unexpected exceptions (not DioException)
      return const Left(const ServerFailure(
        ServerErrorType.unknown,
      ));
    }
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getMaritalStatuses() async {
    // Gọi API với groupCode cho tình trạng hôn nhân
    // Có thể cần điều chỉnh groupCode theo backend
    return await getSystemConfigsByGroupCode('MARITAL_STATUS');
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getGenders() async {
    // Gọi API với groupCode cho giới tính
    // Có thể cần điều chỉnh groupCode theo backend
    return await getSystemConfigsByGroupCode('GENDER');
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getLoanPurposes() async {
    // Gọi API với groupCode cho mục đích sử dụng vốn
    return await getSystemConfigsByGroupCode('PURPOSE_OF_LOAN');
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getLoanTerms() async {
    // Gọi API với groupCode cho thời hạn vay
    return await getSystemConfigsByGroupCode('LOAN_TERM');
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getIncomeSources() async {
    // Gọi API với groupCode cho nguồn thu (INCOME_SOURCE)
    return await getSystemConfigsByGroupCode('INCOME_SOURCE');
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getAssetTypes() async {
    // Gọi API với groupCode cho loại tài sản bảo đảm
    return await getSystemConfigsByGroupCode('ASSET_TYPE');
  }
}