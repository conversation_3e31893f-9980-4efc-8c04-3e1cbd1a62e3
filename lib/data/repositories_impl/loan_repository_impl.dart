import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Core và Domain barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/domain.dart';
import 'package:sales_app/data/network/openapi_client.dart';
import 'package:sales_app_api/sales_app_api.dart';

// Data barrel export

/// Loan Repository Implementation
/// Handles loan creation and document upload operations
/// 
/// Features:
/// - Identity document upload for loan creation
/// - Co-borrower document upload
/// - Additional document upload
/// - Loan creation and management
/// - Location data delegation
/// - System config data delegation
@LazySingleton(as: LoanRepository)
class LoanRepositoryImpl implements LoanRepository {
  final MediaRepository _mediaRepository;
  final LocationRepository _locationRepository;
  final SystemConfigRepository _systemConfigRepository;
  final OpenApiClient _openApiClient;

  LoanRepositoryImpl(
    this._mediaRepository,
    this._locationRepository,
    this._systemConfigRepository,
    this._openApiClient,
  );

  @override
  Future<Either<Failure, Map<String, String>>> uploadIdentityDocuments({
    required File frontFile,
    File? backFile,
  }) async {
    try {
      AppLogger.info('Uploading identity documents for loan', data: {
        'frontFile': frontFile.path,
        'backFile': backFile?.path ?? 'null',
        'isPassport': backFile == null,
      });

      // Xử lý khác nhau cho passport và CCCD
      final files = backFile != null ? [frontFile, backFile] : [frontFile];
      
      final uploadResult = await _mediaRepository.uploadMultipleFiles(
        files: files,
        fileType: FileType.idCard,
      );

      return uploadResult.fold(
        (failure) {
          AppLogger.error('Failed to upload identity documents', error: failure);
          return Left(failure);
        },
        (urls) {
          // Passport chỉ có 1 ảnh, CCCD có 2 ảnh
          if (backFile != null && urls.length != 2) {
            AppLogger.error('Expected 2 URLs for CCCD but got ${urls.length}');
            return const Left(ServerFailure(ServerErrorType.unknown));
          }
          
          if (backFile == null && urls.length != 1) {
            AppLogger.error('Expected 1 URL for passport but got ${urls.length}');
            return const Left(ServerFailure(ServerErrorType.unknown));
          }

          final result = <String, String>{
            'frontUrl': urls[0],
          };
          
          if (backFile != null) {
            result['backUrl'] = urls[1];
          }

          AppLogger.info('Identity documents uploaded successfully', data: result);
          return Right(result);
        },
      );
    } on DioException catch (e) {
      AppLogger.error('Identity documents upload DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return const Left(ServerFailure(
        ServerErrorType.unknown,
      ));
    } catch (e) {
      AppLogger.error('Error uploading identity documents', error: e);
      return const Left(ServerFailure(
        ServerErrorType.unknown,
      ));
    }
  }

  @override
  Future<Either<Failure, Map<String, String>>> uploadCoBorrowerDocuments({
    required File frontFile,
    File? backFile,
  }) async {
    try {
      AppLogger.info('Uploading co-borrower documents for loan', data: {
        'frontFile': frontFile.path,
        'backFile': backFile?.path ?? 'null',
        'isPassport': backFile == null,
      });

      // Xử lý khác nhau cho passport và CCCD
      final files = backFile != null ? [frontFile, backFile] : [frontFile];
      final uploadResult = await _mediaRepository.uploadMultipleFiles(
        files: files,
        fileType: FileType.idCard,
        bucketName: 'salt',
      );

      return uploadResult.fold(
        (failure) {
          AppLogger.error('Failed to upload co-borrower documents', error: failure);
          return Left(failure);
        },
        (urls) {
          // Passport chỉ có 1 ảnh, CCCD có 2 ảnh
          if (backFile != null && urls.length != 2) {
            AppLogger.error('Expected 2 URLs for CCCD but got  [urls.length]');
            return const Left(ServerFailure(ServerErrorType.unknown));
          }
          if (backFile == null && urls.length != 1) {
            AppLogger.error('Expected 1 URL for passport but got  [urls.length]');
            return const Left(ServerFailure(ServerErrorType.unknown));
          }

          final result = <String, String>{
            'frontUrl': urls[0],
          };
          if (backFile != null) {
            result['backUrl'] = urls[1];
          }

          AppLogger.info('Co-borrower documents uploaded successfully', data: result);
          return Right(result);
        },
      );
    } on DioException catch (e) {
      AppLogger.error('Co-borrower documents upload DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return const Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối',
      ));
    } catch (e) {
      AppLogger.error('Error uploading co-borrower documents', error: e);
      return const Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định',
      ));
    }
  }

  @override
  Future<Either<Failure, List<String>>> uploadAdditionalDocuments({
    required List<File> files,
    required String documentType,
  }) async {
    try {
      AppLogger.info('Uploading additional documents for loan', data: {
        'documentType': documentType,
        'fileCount': files.length,
      });

      // Upload tất cả files cùng lúc
      final uploadResult = await _mediaRepository.uploadMultipleFiles(
        files: files,
        fileType: FileType.other,
        bucketName: 'salt',
      );

      return uploadResult.fold(
        (failure) {
          AppLogger.error('Failed to upload additional documents', error: failure);
          return Left(failure);
        },
        (urls) {
          AppLogger.info('Additional documents uploaded successfully', data: {
            'documentType': documentType,
            'urlCount': urls.length,
          });
          return Right(urls);
        },
      );
    } on DioException catch (e) {
      AppLogger.error('Additional documents upload DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return const Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối',
      ));
    } catch (e) {
      AppLogger.error('Error uploading additional documents', error: e);
      return const Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định',
      ));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> createLoan({
    required Map<String, dynamic> loanData,
  }) async {
    try {
      AppLogger.info('Creating loan', data: {
        'hasIdentityDocuments': loanData.containsKey('identityDocuments'),
        'hasBorrowerInfo': loanData.containsKey('borrowerInfo'),
        'hasLoanRequest': loanData.containsKey('loanRequest'),
      });

      // TODO: Implement loan creation API call
      // final result = await _loanApiService.createLoan(loanData);
      
      // Tạm thời return mock data để test flow
      final mockLoanData = {
        'id': 'loan_${DateTime.now().millisecondsSinceEpoch}',
        'status': 'pending',
        'createdAt': DateTime.now().toIso8601String(),
        ...loanData,
      };

      AppLogger.info('Loan created successfully', data: mockLoanData);
      return Right(mockLoanData);
    } on DioException catch (e) {
      AppLogger.error('Loan creation DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return const Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối',
      ));
    } catch (e) {
      AppLogger.error('Error creating loan', error: e);
      return const Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định',
      ));
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getLoans() async {
    try {
      AppLogger.info('Getting loans list');

      // TODO: Implement get loans API call
      // final result = await _loanApiService.getLoans();
      
      // Tạm thời return mock data
      final mockLoans = [
        {
          'id': 'loan_1',
          'status': 'pending',
          'amount': 10000000,
          'createdAt': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        },
        {
          'id': 'loan_2',
          'status': 'approved',
          'amount': 5000000,
          'createdAt': DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
        },
      ];

      AppLogger.info('Got loans list successfully', data: {'count': mockLoans.length});
      return Right(mockLoans);
    } on DioException catch (e) {
      AppLogger.error('Get loans DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      // Extract the failure from the error object
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      // Fallback if ErrorInterceptor didn't work properly
      return const Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối',
      ));
    } catch (e) {
      AppLogger.error('Error getting loans', error: e);
      return const Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định',
      ));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getLoanById(String loanId) async {
    try {
      AppLogger.info('Getting loan by ID', data: {'loanId': loanId});

      // TODO: Implement get loan by ID API call
      // final result = await _loanApiService.getLoanById(loanId);
      
      // Tạm thời return mock data
      final mockLoan = {
        'id': loanId,
        'status': 'pending',
        'amount': 10000000,
        'borrowerInfo': {
          'fullName': 'Nguyễn Văn A',
          'phoneNumber': '**********',
        },
        'createdAt': DateTime.now().toIso8601String(),
      };

      AppLogger.info('Retrieved loan detail for: $loanId');
      return Right(mockLoan);
    } catch (e) {
      AppLogger.error('Failed to fetch loan detail: $loanId', error: e);
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi lấy chi tiết khoản vay: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, List<PaymentAccountEntity>>> getPaymentAccounts(String idNumber) async {
    try {
      AppLogger.info('Fetching payment accounts for idNumber', data: {'idNumber': idNumber});
      final request = AppGetListPaymentAccountRequest((b) => b..idCardNo = idNumber);
      final response = await _openApiClient.accountApi.getPaymentAccount(
        appGetListPaymentAccountRequest: request,
      );
      final accounts = response.data?.data?.accounts;
      if (accounts == null) {
        AppLogger.error('No payment accounts found');
        return const Right([]);
      }
      final entities = accounts
          .where((a) => a.accountNo != null && a.accountName != null)
          .map((a) => PaymentAccountEntity(
                accountNumber: a.accountNo!,
                accountName: a.accountName!,
              ))
          .toList();
      AppLogger.info('Fetched payment accounts', data: {'count': entities.length});
      return Right(entities);
    } on DioException catch (e) {
      AppLogger.error('getPaymentAccounts DioException', error: e);
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      return const Left(ServerFailure(ServerErrorType.unknown));
    } catch (e) {
      AppLogger.error('Error fetching payment accounts', error: e);
      return const Left(ServerFailure(ServerErrorType.unknown));
    }
  }

  // ==================== LOCATION DATA DELEGATION ====================
  
  @override
  Future<Either<Failure, List<ProvinceEntity>>> getProvinces() async {
    // Delegate to LocationRepository
    return await _locationRepository.getProvinces();
  }

  @override
  Future<Either<Failure, List<WardEntity>>> getWards(String provinceId) async {
    // Delegate to LocationRepository
    return await _locationRepository.getWards(provinceId);
  }

  // ==================== SYSTEM CONFIG DATA DELEGATION ====================
  
  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getMaritalStatuses() async {
    // Delegate to SystemConfigRepository
    return await _systemConfigRepository.getMaritalStatuses();
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getGenders() async {
    // Delegate to SystemConfigRepository
    return await _systemConfigRepository.getGenders();
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getLoanPurposes() async {
    // Delegate to SystemConfigRepository
    return await _systemConfigRepository.getLoanPurposes();
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getLoanTerms() async {
    // Delegate to SystemConfigRepository
    return await _systemConfigRepository.getLoanTerms();
  }

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getIncomeSources() async {
    // Delegate to SystemConfigRepository
    return await _systemConfigRepository.getIncomeSources();
  } 

  @override
  Future<Either<Failure, List<SystemConfigEntity>>> getAssetTypes() async {
    // Delegate to SystemConfigRepository
    return await _systemConfigRepository.getAssetTypes();
  }
} 