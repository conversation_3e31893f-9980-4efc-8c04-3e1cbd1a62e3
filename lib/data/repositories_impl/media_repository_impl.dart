import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Core và Domain barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/domain.dart';
// Data barrel export
import 'package:sales_app/data/data.dart';

/// Media Repository Implementation
/// Handles file upload, download, and media operations via Media API
@LazySingleton(as: MediaRepository)
class MediaRepositoryImpl implements MediaRepository {
  final OpenApiClient _openApiClient;

  MediaRepositoryImpl(this._openApiClient);

  @override
  Future<Either<Failure, String>> uploadFile({
    required File file,
    required FileType fileType,
    String? bucketName,
    void Function(int sent, int total)? onProgress,
  }) async {
    try {
      AppLogger.info('Starting file upload', data: {
        'filePath': file.path,
        'fileType': fileType.value,
        'bucketName': bucketName ?? 'SaleApp',
      });

      // Tạo MultipartFile từ File
      final multipartFile = await MultipartFile.fromFile(
        file.path,
        filename: file.path.split('/').last,
      );

      // Call API upload file qua minioControllerApi
      final response = await _openApiClient.minioControllerApi.upload(
        file: multipartFile,
        fileType: fileType.value,
        bucketName: bucketName ?? 'SaleApp',
        onSendProgress: onProgress,
      );

      // Kiểm tra response
      if (response.statusCode != 200) {
        AppLogger.error('File upload API returned error status', data: {
          'statusCode': response.statusCode,
          'statusMessage': response.statusMessage,
        });
        return Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: 'Upload file thất bại: ${response.statusMessage}',
        ));
      }

      final fileUrl = response.data?.previewUrl;
      if (fileUrl == null || fileUrl.isEmpty) {
        AppLogger.error('File upload API returned empty URL');
        return const Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: 'URL file không hợp lệ',
        ));
      }

      AppLogger.info('File upload successful', data: {
        'fileUrl': fileUrl,
        'fileType': fileType.value,
      });

      return Right(fileUrl);
    } on DioException catch (e) {
      AppLogger.error('File upload DioException', error: e);
      
      // ErrorInterceptor has already transformed DioException to Failure
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      AppLogger.error('File upload unexpected exception', error: e);
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, List<String>>> uploadMultipleFiles({
    required List<File> files,
    required FileType fileType,
    String? bucketName,
    void Function(int fileIndex, int sent, int total)? onProgress,
  }) async {
    try {
      AppLogger.info('Starting multiple files upload', data: {
        'fileCount': files.length,
        'fileType': fileType.value,
        'bucketName': bucketName ?? 'SaleApp',
      });

      final uploadedUrls = <String>[];

      for (int i = 0; i < files.length; i++) {
        final file = files[i];
        
        // Upload từng file với progress callback
        final result = await uploadFile(
          file: file,
          fileType: fileType,
          bucketName: bucketName ?? 'SaleApp',
          onProgress: onProgress != null 
            ? (sent, total) => onProgress(i, sent, total)
            : null,
        );

        if (result.isLeft()) {
          final failure = result
              .swap()
              .getOrElse(() => const ServerFailure(ServerErrorType.unknown));
          AppLogger.error('Multiple files upload failed at file $i',
              data: {'failure': failure.toString()});
          return Left(failure);
        }

        final fileUrl = result.getOrElse(() => '');
        uploadedUrls.add(fileUrl);
      }

      AppLogger.info('Multiple files upload successful', data: {
        'uploadedCount': uploadedUrls.length,
        'fileType': fileType.value,
      });

      return Right(uploadedUrls);
    } catch (e) {
      AppLogger.error('Error uploading multiple files', error: e);
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, void>> deleteFile({
    required String bucketName,
    required FileType fileType,
    required String objectName,
  }) async {
    try {
      AppLogger.info('Starting file deletion', data: {
        'bucketName': bucketName.isEmpty ? 'SaleApp' : bucketName,
        'fileType': fileType.value,
        'objectName': objectName,
      });

      // Call API xóa file qua minioControllerApi
      final response = await _openApiClient.minioControllerApi.delete(
        bucketName: bucketName.isEmpty ? 'SaleApp' : bucketName,
        fileType: fileType.value,
        objectName: objectName,
      );

      // Kiểm tra response
      if (response.statusCode != 200) {
        AppLogger.error('File deletion API returned error status', data: {
          'statusCode': response.statusCode,
          'statusMessage': response.statusMessage,
        });
        return Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: 'Xóa file thất bại: ${response.statusMessage}',
        ));
      }

      AppLogger.info('File deletion successful', data: {
        'bucketName': bucketName,
        'objectName': objectName,
      });

      return const Right(null);
    } on DioException catch (e) {
      AppLogger.error('File deletion DioException', error: e);
      
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      AppLogger.error('File deletion unexpected exception', error: e);
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  @override
  Future<Either<Failure, String>> getPreviewUrl({
    required String bucketName,
    required FileType fileType,
    required String objectName,
    bool isPrivate = false,
  }) async {
    try {
      AppLogger.info('Getting preview URL', data: {
        'bucketName': bucketName.isEmpty ? 'SaleApp' : bucketName,
        'fileType': fileType.value,
        'objectName': objectName,
        'isPrivate': isPrivate,
      });

      // Vì previewImage và previewPrivatePicture trả về Response<void>,
      // chúng ta sẽ tạo URL trực tiếp từ thông tin đã có
      String previewUrl;
      
      if (isPrivate) {
        // Tạo private preview URL
        previewUrl = '/private/preview/${bucketName.isEmpty ? 'SaleApp' : bucketName}/private/${fileType.value}/$objectName';
      } else {
        // Tạo public preview URL
        previewUrl = '/preview/${bucketName.isEmpty ? 'SaleApp' : bucketName}/${fileType.value}/$objectName';
      }

      AppLogger.info('Get preview URL successful', data: {
        'previewUrl': previewUrl,
        'bucketName': bucketName,
        'objectName': objectName,
      });

      return Right(previewUrl);
    } on DioException catch (e) {
      AppLogger.error('Get preview URL DioException', error: e);
      
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      AppLogger.error('Get preview URL unexpected exception', error: e);
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }
} 