import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Core và Domain barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/domain.dart';
// Data barrel export
import 'package:sales_app/data/data.dart';

/// Article Repository Implementation
/// Handles article data retrieval from API
@Injectable(as: ArticleRepository)
class ArticleRepositoryImpl implements ArticleRepository {
  final OpenApiClient _openApiClient;

  ArticleRepositoryImpl(this._openApiClient);

  @override
  Future<Either<Failure, ArticleEntity>> getArticleByCode(String code) async {
    try {
      final response = await _openApiClient.articleApi.getArticle(code: code);
      
      if (response.data?.data != null) {
        final articleData = response.data!.data!;
        final article = ArticleEntity(
          title: articleData.title ?? '',
          content: articleData.content ?? '',
          code: articleData.code ?? code,
        );
        return Right(article);
      } else {
        return const Left(ServerFailure(ServerErrorType.notFound));
      }
    } on DioException catch (e) {
      if (e.error is Failure) {
        return Left(e.error as Failure);
      }
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.message}',
      ));
    } catch (e) {
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }
} 