/// Data Layer Master Barrel Export
/// Single import point for all data layer components
/// 
/// This file provides centralized access to all data layer components:
/// - Models (DTOs, local models, API models)
/// - Network layer (API clients, services, interceptors)
/// - Repository implementations (concrete data access)
/// - Service implementations (concrete business logic)
/// - Data sources (remote and local data access)
/// 
/// The data layer handles all external data access including APIs,
/// databases, and external services.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/data/data.dart';
/// 
/// // Access any data component:
/// 
/// // Models
/// final userModel = UserModel.fromJson(json);
/// final userLocal = UserLocalModel(id: 1, userId: '123');
/// 
/// // Network layer
/// final apiClient = ApiClient(interceptors: [], config: config);
/// 
/// // Repository implementations
/// final authRepo = AuthRepositoryImpl(apiService, localDataSource);
/// 
/// // Service implementations
/// final logger = LoggerServiceImpl();
/// final networkInfo = NetworkInfoImpl(connectivity);
/// ```
/// 
/// ## Data Layer Organization:
/// - **models/**: Data models, DTOs, and local storage models
/// - **network/**: API clients, interceptors, and network configuration
/// - **repositories_impl/**: Concrete implementations of repository interfaces
/// - **services_impl/**: Concrete implementations of service interfaces
/// - **datasources/**: Remote and local data source implementations
library data;

// Models - DTOs, local models, API models
export 'models/models.dart';

// Network layer - API clients, services, interceptors
export 'network/network.dart';

// Repository implementations - concrete data access
export 'repositories_impl/repositories_impl.dart';

// Service implementations - concrete business logic
export 'services_impl/services_impl.dart';

// Data sources - remote and local data access
export 'datasources/datasources.dart';
