import 'dart:async';
import 'package:injectable/injectable.dart' hide Environment;

// Core barrel export
import 'package:sales_app/core/core.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';

/// Environment Service Implementation
/// Concrete implementation of EnvironmentService with reactive environment management
/// 
/// Features:
/// - Multi-environment support (dev, staging, prod)
/// - Reactive environment changes via Stream
/// - Persistent environment selection
/// - Environment-specific configuration
/// - Automatic fallback to default environment
/// 
/// Usage:
/// ```dart
/// class ConfigUseCase {
///   final EnvironmentService _env;
///   
///   ConfigUseCase(this._env);
///   
///   Future<void> setupApi() async {
///     final baseUrl = _env.baseUrl;
///     final timeout = _env.timeoutDuration;
///     // Configure API client
///   }
///   
///   void listenToEnvironmentChanges() {
///     _env.environmentChanges.listen((config) {
///       print('Environment changed to: ${config.environment.displayName}');
///     });
///   }
/// }
/// ```
@LazySingleton(as: EnvironmentService)
class EnvironmentServiceImpl implements EnvironmentService {
  final StorageService _storageService;
  EnvironmentEntity _currentConfig = _getDefaultConfig();

  // Stream controller for reactive UI updates
  final StreamController<EnvironmentEntity> _environmentController =
      StreamController<EnvironmentEntity>.broadcast();

  EnvironmentServiceImpl(this._storageService);

  @override
  Stream<EnvironmentEntity> get environmentChanges => _environmentController.stream;

  // ✅ Static environment configurations - created once, reused many times
  static Map<Environment, EnvironmentEntity> get _environmentConfigs => {
    Environment.dev: EnvironmentEntity(
      environment: Environment.dev,
      baseUrl: EnvironmentConstants.devBaseUrl,
      apiVersion: EnvironmentConstants.apiVersion,
      enableLogging: EnvironmentConstants.devLoggingEnabled,
      enableMockApi: EnvironmentConstants.devMockApiEnabled,
      timeoutDuration: EnvironmentConstants.devTimeoutDuration,
    ),
    Environment.staging: EnvironmentEntity(
      environment: Environment.staging,
      baseUrl: EnvironmentConstants.stagingBaseUrl,
      apiVersion: EnvironmentConstants.apiVersion,
      enableLogging: EnvironmentConstants.stagingLoggingEnabled,
      enableMockApi: EnvironmentConstants.stagingMockApiEnabled,
      timeoutDuration: EnvironmentConstants.stagingTimeoutDuration,
    ),
    Environment.prod: EnvironmentEntity(
      environment: Environment.prod,
      baseUrl: EnvironmentConstants.prodBaseUrl,
      apiVersion: EnvironmentConstants.apiVersion,
      enableLogging: EnvironmentConstants.prodLoggingEnabled,
      enableMockApi: EnvironmentConstants.prodMockApiEnabled,
      timeoutDuration: EnvironmentConstants.prodTimeoutDuration,
    ),
  };

  /// Get default environment configuration (Dev)
  static EnvironmentEntity _getDefaultConfig() {
    return _environmentConfigs[Environment.dev]!;
  }

  /// Get configuration for specific environment - O(1) lookup
  static EnvironmentEntity _getConfigForEnvironment(Environment environment) {
    return _environmentConfigs[environment]!;
  }

  @override
  EnvironmentEntity get currentConfig => _currentConfig;

  @override
  Environment get currentEnvironment => _currentConfig.environment;

  @override
  List<Environment> get availableEnvironments => Environment.values;

  @override
  Future<void> initialize() async {
    await _loadSavedEnvironment();
  }

  /// Load saved environment from storage
  Future<void> _loadSavedEnvironment() async {
    try {
      final savedEnvKey = await _storageService.getSelectedEnvironment();
      if (savedEnvKey != null) {
        final environment = Environment.fromKey(savedEnvKey);
        _currentConfig = _getConfigForEnvironment(environment);
        AppLogger.info('Loaded saved environment: ${_currentConfig.environment.displayName}');
      } else {
        AppLogger.info('No saved environment found, using default: ${_currentConfig.environment.displayName}');
      }

      // Notify listeners about the loaded environment
      _environmentController.add(_currentConfig);
    } catch (e) {
      AppLogger.error('Error loading saved environment: $e');
      _currentConfig = _getDefaultConfig(); // Fallback to dev
      _environmentController.add(_currentConfig);
    }
  }

  @override
  Future<void> switchEnvironment(Environment environment) async {
    try {
      final newConfig = _getConfigForEnvironment(environment);

      // Save to storage
      await _storageService.saveSelectedEnvironment(environment.key);

      // Update current config
      _currentConfig = newConfig;

      // Notify listeners about the environment change
      _environmentController.add(_currentConfig);

      AppLogger.info('Switched to environment: ${environment.displayName}');
      AppLogger.info('New base URL: ${newConfig.baseUrl}');

    } catch (e) {
      AppLogger.error('Error switching environment: $e');
      throw Exception('Failed to switch environment: $e');
    }
  }

  @override
  String get baseUrl => _currentConfig.baseUrl;

  @override
  String get salesApiUrl => '${_currentConfig.baseUrl}/sales-app';

  @override
  String get mediaApiUrl => '${_currentConfig.baseUrl}/media';

  @override
  String get fullApiUrl => _currentConfig.fullApiUrl;

  @override
  bool get isLoggingEnabled => _currentConfig.enableLogging;

  @override
  bool get isMockApiEnabled => _currentConfig.enableMockApi;

  @override
  int get timeoutDuration => _currentConfig.timeoutDuration;

  @override
  Future<void> resetToDefault() async {
    await switchEnvironment(Environment.dev);
  }

  @override
  String get environmentDisplayInfo {
    final environment = _currentConfig.environment;
    return '${environment.displayName} (${_currentConfig.baseUrl})';
  }

  @override
  bool get isProduction => _currentConfig.environment == Environment.prod;

  @override
  bool get isDevelopment => _currentConfig.environment == Environment.dev;

  @override
  bool get isStaging => _currentConfig.environment == Environment.staging;
}
