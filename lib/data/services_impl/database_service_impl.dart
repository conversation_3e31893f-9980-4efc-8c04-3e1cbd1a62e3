import 'package:injectable/injectable.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';
// Data barrel export
import 'package:sales_app/data/data.dart';

/// Database Service Implementation
/// Concrete implementation of DatabaseService using UserLocalDataSource
/// 
/// Features:
/// - User CRUD operations (Create, Read, Update, Delete)
/// - Offline data synchronization support
/// - Database utility operations
/// - Adapter pattern implementation
/// 
/// Usage:
/// ```dart
/// class UserUseCase {
///   final DatabaseService _database;
///   
///   UserUseCase(this._database);
///   
///   Future<void> saveUserData(User user) async {
///     await _database.saveUser(user);
///   }
///   
///   Future<List<User>> getPendingSync() async {
///     return await _database.getPendingSyncUsers();
///   }
/// }
/// ```
@LazySingleton(as: DatabaseService)
class DatabaseServiceImpl implements DatabaseService {
  final UserLocalDataSource _userLocalDataSource;

  DatabaseServiceImpl(this._userLocalDataSource);

  // ==================== USER OPERATIONS ====================

  @override
  Future<void> saveUser(User user) async {
    return await _userLocalDataSource.saveUser(user);
  }

  @override
  Future<User?> getUserById(String id) async {
    return await _userLocalDataSource.getUserById(id);
  }

  @override
  Future<User?> getUserByEmail(String email) async {
    return await _userLocalDataSource.getUserByEmail(email);
  }

  @override
  Future<List<User>> getAllUsers() async {
    return await _userLocalDataSource.getAllUsers();
  }

  @override
  Future<void> deleteUser(String id) async {
    return await _userLocalDataSource.deleteUser(id);
  }

  @override
  Future<bool> userExists(String id) async {
    return await _userLocalDataSource.userExists(id);
  }

  // ==================== OFFLINE OPERATIONS ====================

  @override
  Future<void> saveUserOffline(User user) async {
    return await _userLocalDataSource.saveUserOffline(user);
  }

  @override
  Future<List<User>> getPendingSyncUsers() async {
    return await _userLocalDataSource.getPendingSyncUsers();
  }

  @override
  Future<List<User>> getFailedSyncUsers() async {
    return await _userLocalDataSource.getFailedSyncUsers();
  }

  @override
  Future<void> markUserAsSynced(String userId, {int? serverTimestamp}) async {
    return await _userLocalDataSource.markUserAsSynced(userId, serverTimestamp: serverTimestamp);
  }

  @override
  Future<void> markUserSyncFailed(String userId) async {
    return await _userLocalDataSource.markUserSyncFailed(userId);
  }

  // ==================== UTILITY OPERATIONS ====================

  @override
  Future<void> clearAllUsers() async {
    return await _userLocalDataSource.deleteAllUsers();
  }

  @override
  Future<int> getUsersCount() async {
    return await _userLocalDataSource.getUsersCount();
  }

  @override
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    return await _userLocalDataSource.getDatabaseInfo();
  }

  @override
  Future<void> closeDatabase() async {
    // Note: UserLocalDataSource doesn't expose closeDatabase
    // This is handled by AppDatabase singleton
    AppLogger.info('Database close requested - handled by AppDatabase singleton');
  }
}
