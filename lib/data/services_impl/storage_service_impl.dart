import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';

/// Storage Service Implementation
/// Concrete implementation of StorageService using SharedPreferences and FlutterSecureStorage
/// 
/// Features:
/// - Secure token storage (access token, refresh token)
/// - User information caching
/// - Environment configuration persistence
/// - Location data caching (provinces, branches)
/// - Token expiry management
/// 
/// Usage:
/// ```dart
/// class AuthUseCase {
///   final StorageService _storage;
///   
///   AuthUseCase(this._storage);
///   
///   Future<void> saveUserSession(User user, String token) async {
///     await _storage.saveUserInfo(user);
///     await _storage.saveAccessToken(token);
///   }
/// }
/// ```
@LazySingleton(as: StorageService)
class StorageServiceImpl implements StorageService {
  final SharedPreferences _prefs;
  final FlutterSecureStorage _secureStorage;

  StorageServiceImpl(this._prefs, this._secureStorage);

  // Auth token methods
  @override
  Future<void> saveAccessToken(String token) async {
    await _secureStorage.write(key: StorageKeys.accessToken, value: token);
  }

  @override
  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: StorageKeys.accessToken);
  }

  @override
  Future<void> saveRefreshToken(String token) async {
    await _secureStorage.write(key: StorageKeys.refreshToken, value: token);
  }

  @override
  Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: StorageKeys.refreshToken);
  }

  @override
  Future<void> saveTokenExpiryTime(DateTime expiryTime) async {
    await _prefs.setInt(StorageKeys.tokenExpiryTime, expiryTime.millisecondsSinceEpoch);
  }

  @override
  Future<DateTime?> getTokenExpiryTime() async {
    final timestamp = _prefs.getInt(StorageKeys.tokenExpiryTime);
    return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
  }

  @override
  Future<bool> isTokenExpired() async {
    final expiryTime = await getTokenExpiryTime();
    if (expiryTime == null) return true;

    // Add 30 seconds buffer to refresh before actual expiry
    final bufferTime = expiryTime.subtract(const Duration(seconds: 30));
    return DateTime.now().isAfter(bufferTime);
  }

  @override
  Future<void> saveUserInfo(User user) async {
    await _prefs.setString(StorageKeys.userInfo, jsonEncode(user.toJson()));
  }

  @override
  Future<User?> getUserInfo() async {
    final userStr = _prefs.getString(StorageKeys.userInfo);
    if (userStr == null) return null;

    return User.fromJson(jsonDecode(userStr));
  }

  // Environment configuration methods
  @override
  Future<void> saveSelectedEnvironment(String environmentKey) async {
    await _prefs.setString(StorageKeys.selectedEnvironment, environmentKey);
  }

  @override
  Future<String?> getSelectedEnvironment() async {
    return _prefs.getString(StorageKeys.selectedEnvironment);
  }

  // Location cache methods
  @override
  Future<void> saveProvincesCache(List<dynamic> provincesJson, DateTime cachedAt) async {
    await _prefs.setString(StorageKeys.provincesCache, jsonEncode(provincesJson));
    await _prefs.setInt(StorageKeys.provincesCacheTime, cachedAt.millisecondsSinceEpoch);
  }

  @override
  Future<List<dynamic>?> getProvincesCache() async {
    final jsonStr = _prefs.getString(StorageKeys.provincesCache);
    if (jsonStr == null) return null;
    return jsonDecode(jsonStr) as List<dynamic>;
  }

  @override
  Future<DateTime?> getProvincesCacheTime() async {
    final ts = _prefs.getInt(StorageKeys.provincesCacheTime);
    if (ts == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(ts);
  }

  @override
  Future<void> clearProvincesCache() async {
    await _prefs.remove(StorageKeys.provincesCache);
    await _prefs.remove(StorageKeys.provincesCacheTime);
  }

  @override
  Future<void> saveBranchesCache(String provinceId, List<dynamic> branchesJson, DateTime cachedAt) async {
    await _prefs.setString('${StorageKeys.branchesCachePrefix}$provinceId', jsonEncode(branchesJson));
    await _prefs.setInt('${StorageKeys.branchesCacheTimePrefix}$provinceId', cachedAt.millisecondsSinceEpoch);
  }

  @override
  Future<List<dynamic>?> getBranchesCache(String provinceId) async {
    final jsonStr = _prefs.getString('${StorageKeys.branchesCachePrefix}$provinceId');
    if (jsonStr == null) return null;
    return jsonDecode(jsonStr) as List<dynamic>;
  }

  @override
  Future<DateTime?> getBranchesCacheTime(String provinceId) async {
    final ts = _prefs.getInt('${StorageKeys.branchesCacheTimePrefix}$provinceId');
    if (ts == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(ts);
  }

  @override
  Future<void> clearBranchesCache(String provinceId) async {
    await _prefs.remove('${StorageKeys.branchesCachePrefix}$provinceId');
    await _prefs.remove('${StorageKeys.branchesCacheTimePrefix}$provinceId');
  }

  static const String wardsCachePrefix = 'wards_cache_';
  static const String wardsCacheTimePrefix = 'wards_cache_time_';
  @override
  Future<void> saveWardsCache(String provinceId, List<dynamic> wardsJson, DateTime cachedAt) async {
    await _prefs.setString('${wardsCachePrefix}$provinceId', jsonEncode(wardsJson));
    await _prefs.setInt('${wardsCacheTimePrefix}$provinceId', cachedAt.millisecondsSinceEpoch);
  }

  @override
  Future<List<dynamic>?> getWardsCache(String provinceId) async {
    final jsonStr = _prefs.getString('${wardsCachePrefix}$provinceId');
    if (jsonStr == null) return null;
    return jsonDecode(jsonStr) as List<dynamic>;
  }

  @override
  Future<DateTime?> getWardsCacheTime(String provinceId) async {
    final ts = _prefs.getInt('${wardsCacheTimePrefix}$provinceId');
    if (ts == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(ts);
  }

  @override
  Future<void> clearWardsCache(String provinceId) async {
    await _prefs.remove('${wardsCachePrefix}$provinceId');
    await _prefs.remove('${wardsCacheTimePrefix}$provinceId');
  }

  @override
  Future<void> clearAll() async {
    await Future.wait([
      _secureStorage.deleteAll(),
      _prefs.clear(),
    ]);
  }

  @override
  Future<void> clearTokens() async {
    await Future.wait([
      _secureStorage.delete(key: StorageKeys.accessToken),
      _secureStorage.delete(key: StorageKeys.refreshToken),
      _prefs.remove(StorageKeys.tokenExpiryTime),
    ]);
  }
}