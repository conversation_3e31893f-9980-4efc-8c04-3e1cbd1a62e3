import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:injectable/injectable.dart';

// Domain barrel export
import 'package:sales_app/domain/domain.dart';

/// Network Info Implementation
/// Concrete implementation of NetworkInfo using connectivity_plus package
/// 
/// Features:
/// - Real-time network connectivity monitoring
/// - Stream-based connectivity status updates
/// - Automatic disposal of resources
/// 
/// Usage:
/// ```dart
/// class NetworkUseCase {
///   final NetworkInfo _networkInfo;
///   
///   NetworkUseCase(this._networkInfo);
///   
///   Future<void> checkConnection() async {
///     if (await _networkInfo.isConnected) {
///       // Proceed with network operation
///     }
///   }
///   
///   void listenToConnectivity() {
///     _networkInfo.connectivityStream.listen((isConnected) {
///       print('Network status: $isConnected');
///     });
///   }
/// }
/// ```
@Injectable(as: NetworkInfo)
class NetworkInfoImpl implements NetworkInfo {
  final Connectivity _connectivity;
  StreamController<bool>? _connectivityController;

  NetworkInfoImpl(this._connectivity);

  @override
  Future<bool> get isConnected async {
    final result = await _connectivity.checkConnectivity();
    // checkConnectivity() returns a single ConnectivityResult in newer versions
    return result != ConnectivityResult.none;
  }

  @override
  Stream<bool> get connectivityStream {
    _connectivityController ??= StreamController<bool>.broadcast();

    // Listen to connectivity changes
    _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
      final isConnected = result != ConnectivityResult.none;
      _connectivityController?.add(isConnected);
    });

    return _connectivityController!.stream;
  }

  /// Dispose resources to prevent memory leaks
  void dispose() {
    _connectivityController?.close();
    _connectivityController = null;
  }
}
