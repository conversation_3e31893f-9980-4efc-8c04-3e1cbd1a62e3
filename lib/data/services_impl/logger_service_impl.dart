import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:logger/logger.dart';

// Domain barrel export
import 'package:sales_app/domain/domain.dart';

/// Logger Service Implementation
/// Concrete implementation of LoggerService using package logger
/// 
/// Features:
/// - Debug mode only logging (disabled in release)
/// - Custom pretty printer with timestamps
/// - Network request/response logging
/// - Event tracking
/// - Structured data logging
/// 
/// Usage:
/// ```dart
/// class SomeUseCase {
///   final LoggerService _logger;
///   
///   SomeUseCase(this._logger);
///   
///   void doSomething() {
///     _logger.logInfo('Starting operation', data: {'param': 'value'});
///     // ... business logic
///     _logger.logEvent('operation_completed');
///   }
/// }
/// ```
@LazySingleton(as: LoggerService)
class LoggerServiceImpl implements LoggerService {
  late final Logger _logger;

  LoggerServiceImpl() {
    _logger = Logger(
      printer: _AppPrettyPrinter(),
      level: kDebugMode ? Level.debug : Level.off,
    );
  }

  @override
  void logInfo(String message, {Map<String, dynamic>? data}) {
    if (!kDebugMode) return;
    _logger.i(_formatMessage(message, data));
  }

  @override
  void logDebug(String message, {Map<String, dynamic>? data}) {
    if (!kDebugMode) return;
    _logger.d(_formatMessage(message, data));
  }

  @override
  void logWarning(String message, {Map<String, dynamic>? data}) {
    if (!kDebugMode) return;
    _logger.w(_formatMessage(message, data));
  }

  @override
  void logError(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    if (!kDebugMode) return;
    _logger.e(
      _formatMessage(message, data),
      error: error,
      stackTrace: stackTrace,
    );
  }

  @override
  void logEvent(String event, {Map<String, dynamic>? data}) {
    if (!kDebugMode) return;
    final eventMessage = '🎯 EVENT: $event';
    _logger.i(_formatMessage(eventMessage, data));
  }

  @override
  void logNetworkRequest(
    String method,
    String url, {
    Map<String, dynamic>? headers,
    Map<String, dynamic>? body,
  }) {
    if (!kDebugMode) return;
    final message = '🌐 REQUEST [$method] $url';
    final requestData = <String, dynamic>{
      if (headers != null) 'headers': headers,
      if (body != null) 'body': body,
    };
    _logger.d(_formatMessage(message, requestData.isNotEmpty ? requestData : null));
  }

  @override
  void logNetworkResponse(
    String method,
    String url,
    int statusCode, {
    Map<String, dynamic>? response,
    Duration? duration,
  }) {
    if (!kDebugMode) return;
    final emoji = statusCode >= 200 && statusCode < 300 ? '✅' : '❌';
    final message = '$emoji RESPONSE [$method] $url ($statusCode)';
    final responseData = <String, dynamic>{
      'statusCode': statusCode,
      if (duration != null) 'duration': '${duration.inMilliseconds}ms',
      if (response != null) 'response': response,
    };
    _logger.i(_formatMessage(message, responseData));
  }

  /// Format message với data đi kèm
  String _formatMessage(String message, Map<String, dynamic>? data) {
    if (data == null || data.isEmpty) return message;
    return '$message\nData: $data';
  }
}

/// Custom PrettyPrinter cho logger
/// Cung cấp format đẹp với timestamp và emoji
class _AppPrettyPrinter extends PrettyPrinter {
  _AppPrettyPrinter()
      : super(
          methodCount: 0,
          errorMethodCount: 5,
          lineLength: 120,
          colors: true,
          printEmojis: true,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
          stackTraceBeginIndex: 1,
        );

  @override
  List<String> log(LogEvent event) {
    final color = PrettyPrinter.defaultLevelColors[event.level];
    final emoji = PrettyPrinter.defaultLevelEmojis[event.level];
    final timestamp = DateTime.now().toIso8601String();
    
    final messageStr = stringifyMessage(event.message);
    final errorStr = event.error?.toString();
    final stackTraceStr = formatStackTrace(event.stackTrace, methodCount);

    final result = <String>[];
    
    // Header với timestamp và level
    result.add(color!('$emoji [$timestamp] [${event.level.name.toUpperCase()}]'));
    
    // Message
    result.add(color(messageStr));
    
    // Error nếu có
    if (errorStr != null) {
      result.add(color('ERROR: $errorStr'));
    }
    
    // Stack trace nếu có
    if (stackTraceStr != null) {
      result.addAll(stackTraceStr.split('\n').map((line) => color(line)));
    }
    
    // Thêm separator
    result.add(color('─' * 80));
    
    return result;
  }
} 