import 'package:dio/dio.dart';
import 'package:sales_app/data/network/dio_builder.dart';
import 'package:sales_app/core/constants/api_constants.dart';

/// Configuration class for creating Dio instances
class DioConfig {
  final String baseUrl;
  final List<Interceptor> interceptors;
  final String? proxy;
  final int? sendTimeout;
  final int? connectTimeout;
  final int? receiveTimeout;
  final Map<String, dynamic>? headers;

  const DioConfig({
    required this.baseUrl,
    this.interceptors = const [],
    this.proxy,
    this.sendTimeout,
    this.connectTimeout,
    this.receiveTimeout,
    this.headers,
  });

  /// Create default configuration for main API
  /// Note: Environment-specific values are injected at runtime
  static DioConfig createDefaultConfig(String baseUrl, [int? timeoutDuration]) => DioConfig(
    baseUrl: baseUrl,
    sendTimeout: timeoutDuration != null ? timeoutDuration ~/ 1000 : null,
    connectTimeout: timeoutDuration != null ? timeoutDuration ~/ 1000 : null,
    receiveTimeout: timeoutDuration != null ? timeoutDuration ~/ 1000 : null,
  );

  /// Create configuration for authentication APIs (might have different timeout)
  static DioConfig createAuthConfig(String baseUrl) => DioConfig(
    baseUrl: baseUrl,
    sendTimeout: 60, // Longer timeout for auth
    connectTimeout: 60,
    receiveTimeout: 60,
  );

  /// Create configuration for file upload APIs
  static DioConfig createUploadConfig(String baseUrl) => DioConfig(
    baseUrl: baseUrl,
    sendTimeout: 300, // 5 minutes for uploads
    connectTimeout: 30,
    receiveTimeout: 300,
  );
}

/// Simple API client that wraps Dio with specific interceptors
/// Each instance is configured for a specific purpose (default, auth, public, etc.)
class ApiClient {
  final Dio _dio;

  ApiClient({
    required List<Interceptor> interceptors,
    required DioConfig config,
  }) : _dio = _createDio(config, interceptors);

  /// Get the configured Dio instance
  Dio get dio => _dio;

  /// Create Dio instance with given config and interceptors
  static Dio _createDio(DioConfig config, List<Interceptor> interceptors) {
    return DioBuilder.createDio(
      config.baseUrl,
      interceptors: interceptors,
      proxy: config.proxy,
      sendTimeout: config.sendTimeout ?? (ApiConstants.defaultTimeoutDuration ~/ 1000),
      connectTimeout: config.connectTimeout ?? (ApiConstants.defaultTimeoutDuration ~/ 1000),
      receiveTimeout: config.receiveTimeout ?? (ApiConstants.defaultTimeoutDuration ~/ 1000),
      headers: config.headers,
    );
  }


}
