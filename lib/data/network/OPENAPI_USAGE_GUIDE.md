# OpenApiClient Usage Guide

## Tổng quan

`OpenApiClient` là wrapper cho các API đã được generate từ OpenAPI specifications. Nó cung cấp cách tiếp cận type-safe và tự động cho việc gọi API với các Dio instances được cấu hình khác nhau cho từng loại API.

## Cấu trúc OpenApiClient

```dart
@singleton
class OpenApiClient {
  final ApiClient _defaultApiClient;    // Cho các API thông thường
  final ApiClient _authApiClient;       // Cho authentication APIs
  final ApiClient _uploadApiClient;     // Cho file upload APIs

  // Generated API instances
  late final AuthenticationApi _authenticationApi;
  late final UsersApi _usersApi;
  late final RegistersApi _registersApi;
  late final ProvinceApi _provinceApi;
  late final BranchApi _branchApi;
  late final ArticleApi _articleApi;
  late final media_api.MinioControllerApi _minioControllerApi;
}
```

## Các API có sẵn

### 1. Authentication APIs (Sử dụng authDio)
```dart
// Login
final response = await openApiClient.authenticationApi.login(
  appLoginRequest: AppLoginRequest(
    (b) => b
      ..username = '<EMAIL>'
      ..password = 'password123',
  ),
);

// Logout
final response = await openApiClient.authenticationApi.logout(
  body: JsonObject({}),
);

// Refresh Token
final response = await openApiClient.authenticationApi.refreshToken(
  appRefreshTokenRequest: AppRefreshTokenRequest(
    (b) => b..refreshToken = 'refresh_token_here',
  ),
);
```

### 2. Users APIs (Sử dụng defaultDio)
```dart
// Get referrer by code
final response = await openApiClient.usersApi.getReferrerByCode(
  referrerCode: 'REF123',
);
```

### 3. Registers APIs (Sử dụng defaultDio)
```dart
// Các API đăng ký người dùng
// (Xem documentation chi tiết trong generated code)
```

### 4. Province APIs (Sử dụng defaultDio)
```dart
// Các API quản lý tỉnh/thành phố
// (Xem documentation chi tiết trong generated code)
```

### 5. Branch APIs (Sử dụng defaultDio)
```dart
// Các API quản lý chi nhánh
// (Xem documentation chi tiết trong generated code)
```

### 6. Article APIs (Sử dụng defaultDio)
```dart
// Các API quản lý bài viết
// (Xem documentation chi tiết trong generated code)
```

### 7. Media APIs - MinioController (Sử dụng uploadDio)
```dart
// Upload file
final response = await openApiClient.minioControllerApi.uploadFile(
  bucketName: 'user-uploads',
  fileType: 'image',
  file: fileBytes,
);

// Download file
final response = await openApiClient.minioControllerApi.downloadFile(
  bucketName: 'user-uploads',
  fileType: 'image',
  objectName: 'filename.jpg',
);

// Delete file
final response = await openApiClient.minioControllerApi.delete(
  bucketName: 'user-uploads',
  fileType: 'image',
  objectName: 'filename.jpg',
);
```

## Cách sử dụng trong Repository

### Ví dụ: AuthRepositoryImpl

```dart
@injectable
class AuthRepositoryImpl implements AuthRepository {
  final OpenApiClient _openApiClient;
  final StorageService _storageService;

  AuthRepositoryImpl(this._openApiClient, this._storageService);

  @override
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
  }) async {
    try {
      final request = AppLoginRequest(
        (b) => b
          ..username = email
          ..password = password,
      );

      final response = await _openApiClient.authenticationApi.login(
        appLoginRequest: request,
      );

      if (response.data?.success == true) {
        final userData = response.data!.data!;
        
        // Save tokens
        await _storageService.saveAccessToken(userData.accessToken);
        await _storageService.saveRefreshToken(userData.refreshToken);
        
        // Convert to domain entity
        final user = User(
          id: userData.user?.id ?? '',
          email: userData.user?.email ?? '',
          name: userData.user?.name ?? '',
        );
        
        return Right(user);
      } else {
        return Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: response.data?.message ?? 'Đăng nhập thất bại',
        ));
      }
    } catch (e) {
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.toString()}',
      ));
    }
  }
}
```

## Cách sử dụng trong Service

### Ví dụ: UserService

```dart
@injectable
class UserService {
  final OpenApiClient _openApiClient;

  UserService(this._openApiClient);

  Future<Either<Failure, Map<String, dynamic>>> getReferrerInfo(String code) async {
    try {
      final response = await _openApiClient.usersApi.getReferrerByCode(
        referrerCode: code,
      );

      if (response.data?.success == true) {
        return Right(response.data?.data?.toJson() ?? {});
      } else {
        return Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: response.data?.message ?? 'Không tìm thấy thông tin',
        ));
      }
    } catch (e) {
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.toString()}',
      ));
    }
  }
}
```

## Cách sử dụng trong Controller

### Ví dụ: AuthController

```dart
@riverpod
class AuthController extends _$AuthController {
  late final OpenApiClient _openApiClient;
  late final StorageService _storageService;

  @override
  AuthState build() {
    _openApiClient = getIt<OpenApiClient>();
    _storageService = getIt<StorageService>();
    return const AuthState.initial();
  }

  Future<void> login(String email, String password) async {
    state = const AuthState.loading();

    try {
      final request = AppLoginRequest(
        (b) => b
          ..username = email
          ..password = password,
      );

      final response = await _openApiClient.authenticationApi.login(
        appLoginRequest: request,
      );

      if (response.data?.success == true) {
        final userData = response.data!.data!;
        await _storageService.saveAccessToken(userData.accessToken);
        
        final user = User(
          id: userData.user?.id ?? '',
          email: userData.user?.email ?? '',
          name: userData.user?.name ?? '',
        );
        
        state = AuthState.authenticated(user);
      } else {
        state = AuthState.error(Failure.server(
          ServerErrorType.unknown,
          serverMessage: response.data?.message ?? 'Đăng nhập thất bại',
        ));
      }
    } catch (e) {
      state = AuthState.error(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.toString()}',
      ));
    }
  }
}
```

## Truy cập Dio Instances

### 1. Dio Instances có sẵn
```dart
// Default API calls (timeout: 30s)
final dio = openApiClient.dio;

// Authentication APIs (timeout: 60s)
final authDio = openApiClient.authDio;

// File uploads (timeout: 300s)
final uploadDio = openApiClient.uploadDio;
```

### 2. Tạo Dio Instance tùy chỉnh
```dart
// Custom Dio instance
final customDio = openApiClient.createCustomDio(
  baseUrl: 'https://different-api.com',
  sendTimeout: 120,
  connectTimeout: 60,
  receiveTimeout: 120,
  headers: {'Custom-Header': 'value'},
  interceptors: [customInterceptor],
);
```

## Lợi ích của OpenApiClient

### 1. Type Safety
- Tất cả request/response models đều được generate tự động
- Compile-time checking cho API calls
- IntelliSense support trong IDE

### 2. Consistency
- Tất cả API calls sử dụng cùng một pattern
- Error handling nhất quán
- Serialization/deserialization tự động

### 3. Maintainability
- Khi API spec thay đổi, chỉ cần regenerate code
- Không cần maintain manual API calls
- Documentation tự động từ OpenAPI spec

### 4. Multiple Dio Instances
- Mỗi loại API có timeout và cấu hình phù hợp
- Authentication APIs có timeout dài hơn
- Upload APIs có timeout rất dài cho file lớn

## Migration từ Manual APIs

### Trước (Manual API)
```dart
class AuthApiService extends BaseApiService {
  Future<Either<Failure, LoginResponse>> login(LoginRequest request) async {
    return handleApiCall(
      () => dio.post(
        ApiPaths.authLogin,
        data: createRequestData(request.toJson()),
      ),
      (data) => LoginResponse.fromJson(data),
    );
  }
}
```

### Sau (Generated API)
```dart
// Sử dụng trực tiếp OpenApiClient trong Repository
@injectable
class AuthRepositoryImpl implements AuthRepository {
  final OpenApiClient _openApiClient;
  
  AuthRepositoryImpl(this._openApiClient);

  @override
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
  }) async {
    try {
      final request = AppLoginRequest(
        (b) => b
          ..username = email
          ..password = password,
      );

      final response = await _openApiClient.authenticationApi.login(
        appLoginRequest: request,
      );

      if (response.data?.success == true) {
        // Handle success
        return Right(User.fromGeneratedResponse(response.data!));
      } else {
        return Left(Failure.server(
          ServerErrorType.unknown,
          serverMessage: response.data?.message ?? 'Đăng nhập thất bại',
        ));
      }
    } catch (e) {
      return Left(Failure.server(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi kết nối: ${e.toString()}',
      ));
    }
  }
}
```

## Best Practices

### 1. Error Handling
```dart
try {
  final response = await _openApiClient.authenticationApi.login(request);
  
  if (response.data?.success == true) {
    // Handle success
    return Right(result);
  } else {
    // Handle API error
    return Left(Failure.server(
      ServerErrorType.unknown,
      serverMessage: response.data?.message,
    ));
  }
} catch (e) {
  // Handle network/exception error
  return Left(Failure.server(
    ServerErrorType.unknown,
    serverMessage: e.toString(),
  ));
}
```

### 2. Model Conversion
```dart
// Convert domain model to generated model
AppLoginRequest toGeneratedRequest() => AppLoginRequest(
  (b) => b
    ..username = email
    ..password = password,
);

// Convert generated model to domain model
factory User.fromGeneratedResponse(BaseResponseAppLoginResponse response) {
  final data = response.data;
  return User(
    id: data?.user?.id ?? '',
    email: data?.user?.email ?? '',
    name: data?.user?.name ?? '',
  );
}
```

### 3. Dependency Injection
```dart
// Đã được cấu hình trong di/module.dart
@module
abstract class NetworkModule {
  @singleton
  OpenApiClient openApiClient(
    @Named('default') ApiClient defaultApiClient,
    @Named('auth') ApiClient authApiClient,
    @Named('upload') ApiClient uploadApiClient,
  ) => OpenApiClient(defaultApiClient, authApiClient, uploadApiClient);
}

// Sử dụng trong repository/service
@injectable
class AuthRepositoryImpl implements AuthRepository {
  final OpenApiClient _openApiClient;
  
  AuthRepositoryImpl(this._openApiClient);
}
```

## Troubleshooting

### 1. Import Issues
```dart
// Correct imports - sử dụng package names
import 'package:sales_app_api/sales_app_api.dart';
import 'package:media_api/media_api.dart' as media_api;
```

### 2. Serialization Issues
```dart
// Make sure to use proper JsonObject for logout
final response = await _openApiClient.authenticationApi.logout(
  body: JsonObject({}), // Not just {}
);
```

### 3. Model Building
```dart
// Use builder pattern for generated models
AppLoginRequest request = AppLoginRequest(
  (b) => b
    ..username = email
    ..password = password,
);
```

### 4. Timeout Configuration
```dart
// Các timeout được cấu hình trong environment_constants.dart
// - default: 30s
// - auth: 60s  
// - upload: 300s
```

## Cấu hình Timeout

### Timeout cho từng loại API
```dart
// Trong environment_constants.dart
const int defaultTimeoutDuration = 30000; // 30s
const int authTimeoutDuration = 60000;    // 60s
const int uploadTimeoutDuration = 300000; // 300s (5 phút)
```

### Cách thay đổi timeout
```dart
// Tạo Dio instance tùy chỉnh với timeout khác
final customDio = openApiClient.createCustomDio(
  baseUrl: 'https://api.example.com',
  sendTimeout: 120000,    // 2 phút
  connectTimeout: 60000,  // 1 phút
  receiveTimeout: 120000, // 2 phút
);
```

## Kết luận

OpenApiClient cung cấp cách tiếp cận hiện đại và type-safe cho việc gọi API với:

1. **Type Safety**: Tất cả models được generate tự động
2. **Multiple Dio Instances**: Mỗi loại API có cấu hình timeout phù hợp
3. **Consistency**: Pattern nhất quán cho tất cả API calls
4. **Maintainability**: Dễ dàng update khi API spec thay đổi

Để sử dụng hiệu quả:

1. Sử dụng đúng Dio instance cho từng loại API
2. Handle errors một cách nhất quán
3. Convert models giữa generated và domain entities
4. Test thoroughly với real API endpoints 