import 'package:dio/dio.dart';
import 'package:dartz/dartz.dart';
import 'package:sales_app/core/error/error_types.dart';
import 'package:sales_app/core/error/failures.dart';
import 'package:sales_app/data/network/api_client.dart';

/// Base class for manual API services
/// Provides common functionality for API calls
abstract class BaseApiService {
  final ApiClient _apiClient;

  BaseApiService(this._apiClient);

  /// Get the configured Dio instance from ApiClient
  Dio get dio => _apiClient.dio;

  /// Generic method for handling API calls with error handling
  Future<Either<Failure, T>> handleApiCall<T>(
    Future<Response> Function() apiCall,
    T Function(dynamic data) fromJson,
  ) async {
    try {
      final response = await apiCall();
      final data = fromJson(response.data);
      return Right(data);
    } on DioException catch (e) {
      return Left(_handleDioException(e));
    } catch (e) {
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Unexpected error: $e',
      ));
    }
  }

  /// Generic method for handling API calls that return lists
  Future<Either<Failure, List<T>>> handleApiCallList<T>(
    Future<Response> Function() apiCall,
    T Function(dynamic data) fromJson,
  ) async {
    try {
      final response = await apiCall();
      final List<dynamic> dataList = response.data as List<dynamic>;
      final List<T> result = dataList.map((item) => fromJson(item)).toList();
      return Right(result);
    } on DioException catch (e) {
      return Left(_handleDioException(e));
    } catch (e) {
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Unexpected error: $e',
      ));
    }
  }

  /// Generic method for handling API calls that don't return data (like delete)
  Future<Either<Failure, void>> handleApiCallVoid(
    Future<Response> Function() apiCall,
  ) async {
    try {
      await apiCall();
      return const Right(null);
    } on DioException catch (e) {
      return Left(_handleDioException(e));
    } catch (e) {
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Unexpected error: $e',
      ));
    }
  }

  /// Handle DioException and extract custom failure from error interceptor
  Failure _handleDioException(DioException e) {
    // The error interceptor should have already transformed the error
    if (e.error is Failure) {
      return e.error as Failure;
    }

    // Fallback error handling
    return ServerFailure(
      ServerErrorType.unknown,
      serverMessage: e.message ?? 'An error occurred',
    );
  }

  /// Helper method for creating request data
  Map<String, dynamic> createRequestData(Map<String, dynamic> data) {
    // Remove null values
    data.removeWhere((key, value) => value == null);
    return data;
  }

  /// Helper method for creating query parameters
  Map<String, dynamic> createQueryParams(Map<String, dynamic> params) {
    // Remove null values and convert to string
    final cleanParams = <String, dynamic>{};
    params.forEach((key, value) {
      if (value != null) {
        cleanParams[key] = value.toString();
      }
    });
    return cleanParams;
  }
}
