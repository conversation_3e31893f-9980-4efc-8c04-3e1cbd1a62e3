# Timeout Configuration Guide

## Tổng quan

Hệ thống timeout đã được cập nhật để đồng nhất và sử dụng `ApiConstants.defaultTimeoutDuration` làm giá trị mặc định. Tất cả các timeout parameters giờ đây đều là nullable và sẽ sử dụng giá trị mặc định nếu không được cung cấp.

## Cấu trúc mới

### 1. ApiConstants (Nguồn gốc của timeout mặc định)
```dart
// lib/core/constants/api_constants.dart
class ApiConstants {
  static const int defaultTimeoutDuration = 30000; // 30 seconds
}
```

### 2. DioConfig (Nullable timeout parameters)
```dart
class DioConfig {
  final String baseUrl;
  final List<Interceptor> interceptors;
  final String? proxy;
  final int? sendTimeout;      // Nullable - sử dụng default nếu null
  final int? connectTimeout;   // Nullable - sử dụng default nếu null
  final int? receiveTimeout;   // Nullable - sử dụng default nếu null
  final Map<String, dynamic>? headers;

  const DioConfig({
    required this.baseUrl,
    this.interceptors = const [],
    this.proxy,
    this.sendTimeout,      // Không có default value
    this.connectTimeout,   // Không có default value
    this.receiveTimeout,   // Không có default value
    this.headers,
  });
}
```

### 3. DioBuilder (Tự động sử dụng default)
```dart
static Dio createDio(
  String baseUrl, {
  List<Interceptor> interceptors = const [],
  String? proxy,
  int? sendTimeout,      // Nullable
  int? connectTimeout,   // Nullable
  int? receiveTimeout,   // Nullable
  Map<String, dynamic>? headers,
}) {
  // Tự động sử dụng ApiConstants.defaultTimeoutDuration nếu null
  const defaultTimeoutSeconds = ApiConstants.defaultTimeoutDuration ~/ 1000;
  
  BaseOptions options = BaseOptions(
    baseUrl: baseUrl,
    sendTimeout: Duration(seconds: sendTimeout ?? defaultTimeoutSeconds),
    connectTimeout: Duration(seconds: connectTimeout ?? defaultTimeoutSeconds),
    receiveTimeout: Duration(seconds: receiveTimeout ?? defaultTimeoutSeconds),
    // ...
  );
}
```

## Cách sử dụng

### 1. Sử dụng timeout mặc định
```dart
// Tự động sử dụng ApiConstants.defaultTimeoutDuration
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  // Không cần chỉ định timeout - sẽ dùng default
);

final apiClient = ApiClient(
  interceptors: [],
  config: config,
);
```

### 2. Chỉ định timeout tùy chỉnh
```dart
// Override timeout cho trường hợp đặc biệt
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  sendTimeout: 60,      // 60 seconds
  connectTimeout: 30,   // 30 seconds
  receiveTimeout: 120,  // 2 minutes
);
```

### 3. Chỉ định một số timeout
```dart
// Chỉ override một số timeout, các cái khác dùng default
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  sendTimeout: 60,      // Custom
  // connectTimeout và receiveTimeout sẽ dùng default
);
```

## Factory Methods

### 1. createDefaultConfig
```dart
// Sử dụng timeout mặc định
final config = DioConfig.createDefaultConfig('https://api.example.com');

// Hoặc chỉ định timeout tùy chỉnh
final config = DioConfig.createDefaultConfig(
  'https://api.example.com',
  60000, // 60 seconds
);
```

### 2. createAuthConfig
```dart
// Timeout dài hơn cho authentication
final config = DioConfig.createAuthConfig('https://api.example.com');
// sendTimeout: 60, connectTimeout: 60, receiveTimeout: 60
```

### 3. createUploadConfig
```dart
// Timeout rất dài cho file upload
final config = DioConfig.createUploadConfig('https://api.example.com');
// sendTimeout: 300, connectTimeout: 30, receiveTimeout: 300
```

## OpenApiClient

### 1. Sử dụng timeout mặc định
```dart
final customDio = openApiClient.createCustomDio(
  baseUrl: 'https://api.example.com',
  // Không chỉ định timeout - sẽ dùng default
);
```

### 2. Chỉ định timeout tùy chỉnh
```dart
final customDio = openApiClient.createCustomDio(
  baseUrl: 'https://api.example.com',
  sendTimeout: 60,
  connectTimeout: 30,
  receiveTimeout: 120,
);
```

## Environment Service Integration

Environment Service vẫn sử dụng timeout theo environment nhưng giờ đây nó lấy từ `ApiConstants`:

```dart
// lib/core/constants/environment_constants.dart
class EnvironmentConstants {
  // Sử dụng ApiConstants.defaultTimeoutDuration cho tất cả environments
  static int get devTimeoutDuration => ApiConstants.defaultTimeoutDuration;
  static int get stagingTimeoutDuration => ApiConstants.defaultTimeoutDuration;
  static int get prodTimeoutDuration => ApiConstants.defaultTimeoutDuration;
}
```

## Migration từ cũ sang mới

### Trước (Hard-coded timeout)
```dart
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  sendTimeout: 30,      // Hard-coded
  connectTimeout: 30,   // Hard-coded
  receiveTimeout: 30,   // Hard-coded
);
```

### Sau (Sử dụng default)
```dart
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  // Không cần chỉ định timeout - tự động dùng default
);
```

### Hoặc chỉ định khi cần
```dart
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  sendTimeout: 60,      // Chỉ override khi cần
  // Các timeout khác dùng default
);
```

## Lợi ích

### 1. Đồng nhất
- Tất cả timeout đều lấy từ một nguồn duy nhất (`ApiConstants`)
- Không có hard-coded values scattered trong codebase

### 2. Linh hoạt
- Có thể override timeout khi cần thiết
- Mặc định sử dụng giá trị chuẩn

### 3. Maintainable
- Chỉ cần thay đổi `ApiConstants.defaultTimeoutDuration` để update tất cả
- Không cần tìm và sửa từng chỗ

### 4. Type-safe
- Nullable parameters cho phép kiểm tra compile-time
- IDE support tốt hơn

## Best Practices

### 1. Sử dụng default khi có thể
```dart
// ✅ Tốt - sử dụng default
final config = DioConfig(
  baseUrl: 'https://api.example.com',
);

// ❌ Không cần thiết - trừ khi thực sự cần custom
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  sendTimeout: 30,      // Giống default
  connectTimeout: 30,   // Giống default
  receiveTimeout: 30,   // Giống default
);
```

### 2. Chỉ override khi cần thiết
```dart
// ✅ Chỉ override timeout cần thiết
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  sendTimeout: 60,      // Upload cần timeout dài
  // connectTimeout và receiveTimeout dùng default
);
```

### 3. Sử dụng factory methods
```dart
// ✅ Sử dụng factory methods có sẵn
final authConfig = DioConfig.createAuthConfig('https://api.example.com');
final uploadConfig = DioConfig.createUploadConfig('https://api.example.com');
```

### 4. Document custom timeout
```dart
// ✅ Comment giải thích lý do override
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  sendTimeout: 120,     // File upload cần timeout dài
  receiveTimeout: 120,  // Download cũng cần timeout dài
);
```

## Troubleshooting

### 1. Timeout quá ngắn
```dart
// Nếu gặp timeout errors, tăng timeout
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  sendTimeout: 60,      // Tăng từ 30 lên 60
  connectTimeout: 60,   // Tăng từ 30 lên 60
);
```

### 2. Timeout quá dài
```dart
// Nếu muốn fail fast, giảm timeout
final config = DioConfig(
  baseUrl: 'https://api.example.com',
  sendTimeout: 10,      // Giảm từ 30 xuống 10
  connectTimeout: 10,   // Giảm từ 30 xuống 10
);
```

### 3. Thay đổi timeout global
```dart
// Thay đổi trong ApiConstants
class ApiConstants {
  static const int defaultTimeoutDuration = 45000; // Tăng từ 30s lên 45s
}
```

## Kết luận

Hệ thống timeout mới cung cấp:
- **Đồng nhất**: Tất cả timeout đều lấy từ `ApiConstants`
- **Linh hoạt**: Có thể override khi cần thiết
- **Maintainable**: Dễ dàng thay đổi timeout global
- **Type-safe**: Nullable parameters với compile-time checking

Sử dụng timeout mặc định khi có thể và chỉ override khi thực sự cần thiết! 