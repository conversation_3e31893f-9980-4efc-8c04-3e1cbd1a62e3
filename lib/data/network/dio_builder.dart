import 'package:dio/dio.dart';
import 'package:sales_app/core/constants/api_constants.dart';

class DioBuilder {
  const DioBuilder._();

  static Dio createDio(
    String baseUrl, {
    List<Interceptor> interceptors = const [],
    String? proxy,
    int? sendTimeout,
    int? connectTimeout,
    int? receiveTimeout,
    Map<String, dynamic>? headers,
  }) {
    // Use default timeout from ApiConstants if not provided
    const defaultTimeoutSeconds = ApiConstants.defaultTimeoutDuration ~/ 1000;
    
    BaseOptions options = BaseOptions(
      baseUrl: baseUrl,
      sendTimeout: Duration(seconds: sendTimeout ?? defaultTimeoutSeconds),
      connectTimeout: Duration(seconds: connectTimeout ?? defaultTimeoutSeconds),
      receiveTimeout: Duration(seconds: receiveTimeout ?? defaultTimeoutSeconds),
      headers: headers ?? {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    final dio = Dio(options);

    // Add proxy if provided
    if (proxy != null) {
      // Note: Proxy configuration would need platform-specific implementation
      // For now, we'll skip proxy configuration
      // TODO: Implement proxy configuration when needed
    }

    // Add interceptors
    if (interceptors.isNotEmpty) {
      dio.interceptors.addAll(interceptors);
    }

    return dio;
  }
}
