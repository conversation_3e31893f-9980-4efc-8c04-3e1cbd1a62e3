/// Network Layer Barrel Export
/// Tổng hợp các API client, base service, builder, interceptors, openapi config
///
/// Sử dụng:
/// ```dart
/// import 'package:sales_app/data/network/network.dart';
/// ```
///
/// - API client: cấu hình và tạo Dio instance
/// - BaseApiService: base cho các service thủ công
/// - Interceptors: logging, error, retry, token refresh...
/// - OpenAPI config: config cho các API codegen
library network;

export 'api_client.dart';
export 'base_api_service.dart';
export 'dio_builder.dart';
export 'openapi_client.dart';

// Barrel export cho interceptors
export 'interceptors/interceptors.dart';
// Barrel export cho openapi config
export 'openapi/openapi.dart';