# OpenAPI Configuration

Th<PERSON> mục này chứa các file cấu hình OpenAPI Generator cho việc tự động generate API client code.

## 📁 Files

### `sales_api_config.dart`
- **Purpose**: C<PERSON>u hình generate Sales App API client
- **Input**: `openapi/json-specs/sale-app-api-spec.json`
- **Output**: `lib/generated/api/sales/`
- **APIs**: Authentication, users, registrations, etc.

### `media_api_config.dart`
- **Purpose**: Cấu hình generate Media API client  
- **Input**: `openapi/json-specs/media-spec.json`
- **Output**: `lib/generated/api/media/`
- **APIs**: File upload/download, image processing

## 🔧 Usage

### Generate API Clients
```bash
# Generate tất cả APIs
./scripts/generate_api.sh

# Generate riêng lẻ
./scripts/generate_api.sh sales   # Chỉ Sales App API
./scripts/generate_api.sh media   # Chỉ Media API
```

### Cấu hình mới
```dart
// Thêm cấu hình mới cho API khác
@Openapi(
  additionalProperties: AdditionalProperties(
    pubName: 'new_api_client',
    pubAuthor: 'KienlongBank Sales Team',
  ),
  inputSpec: InputSpec(path: 'openapi/json-specs/new-api-spec.json'),
  generatorName: Generator.dio,
  outputDirectory: 'lib/generated/api/new-api',
)
class NewApiConfig {}
```

## 📋 Workflow

1. **Export JSON** từ Swagger UI của backend
2. **Đặt file** vào `openapi/json-specs/`
3. **Tạo config** trong thư mục này
4. **Generate** với `./scripts/generate_api.sh`
5. **Integrate** generated clients vào app

## 🏗️ Architecture Compliance

- ✅ **Clean Architecture**: Configs nằm trong `data/network/` layer
- ✅ **Separation of Concerns**: Mỗi API có config riêng biệt
- ✅ **Maintainability**: Dễ dàng thêm/sửa/xóa API configs
- ✅ **Scalability**: Hỗ trợ nhiều APIs khác nhau 