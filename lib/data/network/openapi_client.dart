import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sales_app/data/network/api_client.dart';

// Import generated APIs using package names
// import 'package:sales_app_api/sales_app_api.dart';
import 'package:media_api/media_api.dart' as media_api;
import 'package:sales_app_api/sales_app_api.dart';
import 'package:sales_app_api/src/api/ward_api.dart';
import 'package:sales_app_api/src/api/account_api.dart';

/// OpenAPI Client wrapper for generated APIs
/// This provides access to all generated APIs from OpenAPI specifications
/// 
/// Base URL Configuration:
/// - Sales API (default, auth, public): base_url/sales-app
/// - Media API (upload): base_url/media
/// Where base_url = https://api-staging.kienlongbank.co
@singleton
class OpenApiClient {
  final ApiClient _defaultApiClient;  // Sales API client
  final ApiClient _authApiClient;     // Sales API auth client  
  final ApiClient _uploadApiClient;   // Media API client

  // Generated API instances
  late final AuthenticationApi _authenticationApi;
  late final UsersApi _usersApi;
  late final RegistersApi _registersApi;
  late final ProvinceApi _provinceApi;
  late final BranchApi _branchApi;
  late final ArticleApi _articleApi;
  late final WardApi _wardApi;
  late final SystemConfigApi _systemConfigApi;
  late final media_api.MinioControllerApi _minioControllerApi;
  late final AccountApi _accountApi;

  OpenApiClient(
    @Named('default') this._defaultApiClient,   // Sales API (base_url/sales-app)
    @Named('auth') this._authApiClient,         // Sales API auth (base_url/sales-app)
    @Named('upload') this._uploadApiClient,     // Media API (base_url/media)
  ) {
    // Initialize Sales API clients (base_url/sales-app)
    // Sử dụng standardSerializers để serialize thành JSON object thay vì array
    _authenticationApi = AuthenticationApi(_authApiClient.dio, standardSerializers);
    _usersApi = UsersApi(_defaultApiClient.dio, standardSerializers);
    _registersApi = RegistersApi(_defaultApiClient.dio, standardSerializers);
    _provinceApi = ProvinceApi(_defaultApiClient.dio, standardSerializers);
    _branchApi = BranchApi(_defaultApiClient.dio, standardSerializers);
    _articleApi = ArticleApi(_defaultApiClient.dio, standardSerializers);
    _wardApi = WardApi(_defaultApiClient.dio, standardSerializers);
    _systemConfigApi = SystemConfigApi(_defaultApiClient.dio, standardSerializers);
    _accountApi = AccountApi(_defaultApiClient.dio, standardSerializers);
    
    // Initialize Media API clients (base_url/media)
    _minioControllerApi = media_api.MinioControllerApi(_uploadApiClient.dio, media_api.standardSerializers);
  }

  /// Get configured Dio instances for manual API creation
  /// This is useful during the transition period
  Dio get dio => _defaultApiClient.dio;
  Dio get authDio => _authApiClient.dio;
  Dio get uploadDio => _uploadApiClient.dio;

  // Sales API getters (base_url/sales-app)
  AuthenticationApi get authenticationApi => _authenticationApi;
  UsersApi get usersApi => _usersApi;
  RegistersApi get registersApi => _registersApi;
  ProvinceApi get provinceApi => _provinceApi;
  BranchApi get branchApi => _branchApi;
  ArticleApi get articleApi => _articleApi;
  WardApi get wardApi => _wardApi;
  SystemConfigApi get systemConfigApi => _systemConfigApi;
  AccountApi get accountApi => _accountApi;
  
  // Media API getters (base_url/media)
  media_api.MinioControllerApi get minioControllerApi => _minioControllerApi;

  /// Create a custom Dio instance for specific needs
  Dio createCustomDio({
    required String baseUrl,
    List<Interceptor>? interceptors,
    String? proxy,
    int? sendTimeout,
    int? connectTimeout,
    int? receiveTimeout,
    Map<String, dynamic>? headers,
  }) {
    return ApiClient(
      interceptors: interceptors ?? [],
      config: DioConfig(
        baseUrl: baseUrl,
        proxy: proxy,
        sendTimeout: sendTimeout,
        connectTimeout: connectTimeout,
        receiveTimeout: receiveTimeout,
        headers: headers,
      ),
    ).dio;
  }
}

/// Future implementation when we have generated APIs:
///
/// @singleton
/// class OpenApiClient {
///   final ApiClient _apiClient;
///   late final Openapi _openapi;
///
///   OpenApiClient(this._apiClient) {
///     _openapi = Openapi(dio: _apiClient.defaultDio);
///   }
///
///   AuthApi get authApi => _openapi.getAuthApi();
///   UserApi get userApi => _openapi.getUserApi();
///   ProductApi get productApi => _openapi.getProductApi();
/// }
