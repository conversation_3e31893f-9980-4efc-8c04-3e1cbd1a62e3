import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Domain barrel export
import 'package:sales_app/domain/domain.dart';

@injectable
class LoggingInterceptor extends Interceptor {
  final LoggerService _logger;

  LoggingInterceptor(this._logger);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _logger.logInfo('🚀 REQUEST: ${options.method} ${options.uri}');
    _logger.logDebug('Headers: ${options.headers}');

    if (options.data != null) {
      _logger.logDebug('Body: ${options.data}');
    }

    if (options.queryParameters.isNotEmpty) {
      _logger.logDebug('Query Parameters: ${options.queryParameters}');
    }

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _logger.logInfo(
      '✅ RESPONSE: ${response.statusCode} ${response.requestOptions.method} ${response.requestOptions.uri}'
    );
    _logger.logDebug('Response Data: ${response.data}');

    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    _logger.logError(
      '❌ ERROR: ${err.response?.statusCode} ${err.requestOptions.method} ${err.requestOptions.uri}',
      error: err,
    );
    _logger.logError('Error Message: ${err.message}', error: err);

    if (err.response?.data != null) {
      _logger.logError('Error Response: ${err.response?.data}', error: err);
    }

    handler.next(err);
  }
}
