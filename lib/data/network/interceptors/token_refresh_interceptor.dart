import 'dart:async';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Core và Domain barrel exports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/domain/domain.dart';
// Data barrel export
import 'package:sales_app/data/data.dart';
// Generated API imports
import 'package:sales_app_api/sales_app_api.dart';

/// Token Refresh Interceptor
/// Handles automatic token refresh when access token expires (401 errors)
/// Ensures thread-safety for concurrent requests during token refresh
/// Uses generated API: /system/api/v1/auth/refreshToken
@injectable
class TokenRefreshInterceptor extends Interceptor {
  final StorageService _storageService;
  final ApiClient _refreshApiClient; // Use dedicated ApiClient for refresh

  // Mutex to prevent concurrent refresh attempts
  static Completer<void>? _currentRefreshCompleter;

  // Generated API instance for refresh token
  late final AuthenticationApi _authenticationApi;

  TokenRefreshInterceptor(
    this._storageService,
    @Named('refresh') this._refreshApiClient, // Inject dedicated client
  ) {
    // Initialize generated API instance similar to openapi_client.dart
    _authenticationApi = AuthenticationApi(_refreshApiClient.dio, standardSerializers);
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Check if token is about to expire before making request
    try {
      final isExpired = await _storageService.isTokenExpired();
      if (isExpired && !_isAuthEndpoint(options.path)) {
        AppLogger.info('TokenRefresh: Token about to expire, proactive refresh');

        final refreshSuccess = await _refreshTokenWithMutex();
        if (refreshSuccess) {
          // Update request with new token
          final newToken = await _storageService.getAccessToken();
          if (newToken != null && newToken.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $newToken';
            AppLogger.debug('TokenRefresh: Request updated with new token');
          }
        } else {
          AppLogger.warning('TokenRefresh: Proactive refresh failed, will rely on reactive refresh');
        }
      }
    } catch (e) {
      AppLogger.error('TokenRefresh: Error in proactive refresh', error: e);
      // Continue with original request even if proactive refresh fails
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Log all errors for debugging
    AppLogger.debug('TokenRefresh: onError called', data: {
      'statusCode': err.response?.statusCode,
      'path': err.requestOptions.path,
      'method': err.requestOptions.method,
    });

    // Only handle 401 Unauthorized errors
    if (err.response?.statusCode != 401) {
      AppLogger.debug('TokenRefresh: Not a 401 error, passing to next handler');
      handler.next(err);
      return;
    }

    // Skip refresh for auth endpoints to prevent infinite loops
    if (_isAuthEndpoint(err.requestOptions.path)) {
      AppLogger.info('TokenRefresh: 401 on auth endpoint, skipping refresh to prevent loop');
      await _handleRefreshFailure();
      handler.next(err);
      return;
    }

    AppLogger.info('TokenRefresh: 401 detected, attempting reactive token refresh', data: {
      'path': err.requestOptions.path,
      'method': err.requestOptions.method,
    });

    try {
      // Attempt to refresh token
      AppLogger.info('TokenRefresh: Starting refresh token process');
      final refreshSuccess = await _refreshTokenWithMutex();

      AppLogger.info('TokenRefresh: Refresh result', data: {
        'success': refreshSuccess,
      });

      if (refreshSuccess) {
        // Retry the original request with new token
        AppLogger.info('TokenRefresh: Retrying original request with new token');
        final retryResponse = await _retryRequest(err.requestOptions);
        handler.resolve(retryResponse);
      } else {
        // Refresh failed, proceed with original error
        AppLogger.warning('TokenRefresh: Refresh failed, handling failure');
        await _handleRefreshFailure();
        handler.next(err);
      }
    } catch (e) {
      AppLogger.error('TokenRefresh: Unexpected error during refresh', error: e);
      await _handleRefreshFailure();
      handler.next(err);
    }
  }

  /// Refresh token with mutex to handle concurrent requests
  Future<bool> _refreshTokenWithMutex() async {
    AppLogger.info('TokenRefresh: _refreshTokenWithMutex called');

    // If refresh is already in progress, wait for it
    if (_currentRefreshCompleter != null) {
      AppLogger.debug('TokenRefresh: Waiting for ongoing refresh');
      await _currentRefreshCompleter!.future;
      
      AppLogger.info('TokenRefresh: Ongoing refresh completed, checking token');
      // Check if refresh was successful by verifying new token
      final newToken = await _storageService.getAccessToken();
      return newToken != null && newToken.isNotEmpty;
    }

    // Start new refresh process
    AppLogger.info('TokenRefresh: Starting new refresh process');
    _currentRefreshCompleter = Completer<void>();

    try {
      AppLogger.info('TokenRefresh: About to call _performTokenRefresh');
      final success = await _performTokenRefresh();
      AppLogger.info('TokenRefresh: _performTokenRefresh completed', data: {
        'success': success,
      });
      _currentRefreshCompleter!.complete();
      return success;
    } catch (e) {
      AppLogger.error('TokenRefresh: Exception in _performTokenRefresh', error: e);
      _currentRefreshCompleter!.completeError(e);
      rethrow;
    } finally {
      AppLogger.info('TokenRefresh: Cleaning up refresh completer');
      _currentRefreshCompleter = null;
    }
  }

  /// Perform the actual token refresh using generated API
  /// Calls: POST /system/api/v1/auth/refreshToken
  Future<bool> _performTokenRefresh() async {
    try {
      AppLogger.info('TokenRefresh: Starting _performTokenRefresh');
      final refreshToken = await _storageService.getRefreshToken();

      if (refreshToken == null || refreshToken.isEmpty) {
        AppLogger.warning('TokenRefresh: No refresh token available');
        return false;
      }

      AppLogger.info('TokenRefresh: Calling refresh API: /system/api/v1/auth/refreshToken', data: {
        'refreshTokenLength': refreshToken.length,
        'refreshTokenPrefix': refreshToken.substring(0, 10),
      });
      
      // Create request using generated API models (similar to openapi_client.dart)
      final request = AppRefreshTokenRequest((b) => b
        ..refreshToken = refreshToken);

      // Call generated API using AuthenticationApi instance
      AppLogger.info('TokenRefresh: Making API call to refresh token');
      final response = await _authenticationApi.refreshToken(
        appRefreshTokenRequest: request,
      );

      AppLogger.info('TokenRefresh: Received response', data: {
        'statusCode': response.statusCode,
        'success': response.data?.success,
        'hasData': response.data?.data != null,
      });

      // Check if API call was successful
      if (response.data?.success != true) {
        AppLogger.error('TokenRefresh: API returned error', data: {
          'success': response.data?.success,
          'code': response.data?.code,
          'message': response.data?.message,
        });
        return false;
      }

      final refreshData = response.data?.data;
      if (refreshData == null) {
        AppLogger.error('TokenRefresh: No data in response');
        return false;
      }

      // Save new tokens
      if (refreshData.accessToken != null && refreshData.accessToken!.isNotEmpty) {
        await _storageService.saveAccessToken(refreshData.accessToken!);
      }

      if (refreshData.refreshToken != null && refreshData.refreshToken!.isNotEmpty) {
        await _storageService.saveRefreshToken(refreshData.refreshToken!);
      }

      // Calculate and save new token expiry time
      if (refreshData.expiresIn != null) {
        final expiryTime = DateTime.now().add(Duration(seconds: refreshData.expiresIn!));
        await _storageService.saveTokenExpiryTime(expiryTime);
      }

      AppLogger.info('TokenRefresh: Tokens refreshed successfully', data: {
        'expiresIn': refreshData.expiresIn,
        'tokenType': refreshData.tokenType,
        'refreshExpiresIn': refreshData.refreshExpiresIn,
      });
      
      return true;
    } catch (e) {
      AppLogger.error('TokenRefresh: Exception during refresh', error: e);
      return false;
    }
  }

  /// Retry the original request with new access token
  Future<Response> _retryRequest(RequestOptions requestOptions) async {
    try {
      // Get the new access token
      final newToken = await _storageService.getAccessToken();
      
      // Update authorization header
      if (newToken != null && newToken.isNotEmpty) {
        requestOptions.headers['Authorization'] = 'Bearer $newToken';
        AppLogger.debug('TokenRefresh: Retrying request with new token');
      } else {
        AppLogger.warning('TokenRefresh: No new token available for retry');
      }

      AppLogger.debug('TokenRefresh: Retrying original request');
      
      // Create new Dio instance to avoid interceptor loops
      final dio = Dio();
      return await dio.fetch(requestOptions);
    } catch (e) {
      AppLogger.error('TokenRefresh: Failed to retry request', error: e);
      rethrow;
    }
  }

  /// Handle refresh failure by clearing tokens
  /// ErrorInterceptor will handle the 401 error and emit session expired event
  Future<void> _handleRefreshFailure() async {
    try {
      AppLogger.info('TokenRefresh: Clearing tokens due to refresh failure');
      await _storageService.clearTokens();
      AppLogger.info('TokenRefresh: Tokens cleared, ErrorInterceptor will handle session expiry');
    } catch (e) {
      AppLogger.error('TokenRefresh: Error clearing tokens', error: e);
    }
  }

  /// Check if the request is to an auth endpoint
  ///
  /// This is used to prevent infinite loops when refresh token API itself returns 401.
  /// When refresh token expires, the refresh API call will return 401, and we need to
  /// detect this scenario to trigger session expiry instead of attempting another refresh.
  bool _isAuthEndpoint(String path) {
    return ApiPaths.isAuthPath(path);
  }
}
