import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Direct import for events to avoid naming conflicts
import 'package:sales_app/core/events/app_events.dart' as events;

@injectable
class ErrorInterceptor extends Interceptor {
  final AppEventBus _eventBus;

  ErrorInterceptor(this._eventBus);

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Log error details for debugging
    AppLogger.debug('ErrorInterceptor: Processing ${err.type} error for ${err.requestOptions.uri}');
    if (err.response?.statusCode != null) {
      AppLogger.debug('ErrorInterceptor: Status code ${err.response?.statusCode}');
    }

    // Check for 401 Unauthorized - potential session expiry
    if (err.response?.statusCode == 401) {
      _handleUnauthorizedError(err);
    }

    // Transform DioException to custom failure types
    final failure = _mapDioExceptionToFailure(err);

    AppLogger.debug('ErrorInterceptor: Mapped to ${failure.runtimeType}');

    // Create a new DioException with custom failure information
    final customError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: failure,
      message: 'Error occurred', // Generic message, actual message will be handled in presentation layer
    );

    handler.next(customError);
  }

  /// Handle 401 Unauthorized errors - session expiry after failed refresh
  void _handleUnauthorizedError(DioException err) {
    AppLogger.warning('ErrorInterceptor: 401 Unauthorized detected after refresh attempt', data: {
      'path': err.requestOptions.path,
      'method': err.requestOptions.method,
      'url': err.requestOptions.uri.toString(),
    });

    // 401 errors that reach ErrorInterceptor means:
    // 1. TokenRefreshInterceptor already attempted refresh and failed
    // 2. Refresh token is expired/invalid
    // 3. Session is truly expired - emit force logout event

    AppLogger.info('ErrorInterceptor: Session expired detected, emitting force logout event');
    _eventBus.emitAuthEvent(const events.AuthEvent.forceLogout(
      reason: 'Phiên đăng nhập hết hạn, vui lòng đăng nhập lại',
    ));
  }

  Failure _mapDioExceptionToFailure(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return const NetworkFailure(NetworkErrorType.connectionTimeout);

      case DioExceptionType.sendTimeout:
        return const NetworkFailure(NetworkErrorType.sendTimeout);

      case DioExceptionType.receiveTimeout:
        return const NetworkFailure(NetworkErrorType.receiveTimeout);

      case DioExceptionType.badResponse:
        return _handleBadResponse(error);

      case DioExceptionType.cancel:
        return const NetworkFailure(NetworkErrorType.requestCancelled);

      case DioExceptionType.connectionError:
        return const NetworkFailure(NetworkErrorType.noConnection);

      case DioExceptionType.badCertificate:
        return const NetworkFailure(NetworkErrorType.certificateError);

      case DioExceptionType.unknown:
        return const ServerFailure(ServerErrorType.unknown);
    }
  }

  Failure _handleBadResponse(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    switch (statusCode) {
      case 400:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ValidationFailure(
          ValidationErrorType.serverValidation,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      // case 401:
      //   final serverMessage = _extractErrorMessage(responseData);
      //   final serverErrorCode = _extractErrorCode(responseData);
      //   // 401 errors that reach ErrorInterceptor are session expired scenarios
      //   // (after TokenRefreshInterceptor failed to refresh)
      //   return AuthFailure(
      //     AuthErrorType.tokenExpired,
      //     serverMessage: serverMessage,
      //     serverErrorCode: serverErrorCode,
      //   );

      case 403:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return AuthFailure(
          AuthErrorType.accessDenied,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 404:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ServerFailure(
          ServerErrorType.notFound,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 422:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ValidationFailure(
          ValidationErrorType.serverValidation,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 429:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ServerFailure(
          ServerErrorType.tooManyRequests,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        
        // Log detailed information for server errors
        AppLogger.warning('Server error detected', data: {
          'statusCode': statusCode,
          'serverMessage': serverMessage,
          'serverErrorCode': serverErrorCode,
          'responseData': responseData?.toString().substring(0, responseData.toString().length.clamp(0, 200)),
        });
        
        // Log detailed information for server errors
        AppLogger.warning('Server error detected', data: {
          'statusCode': statusCode,
          'serverMessage': serverMessage,
          'serverErrorCode': serverErrorCode,
          'responseData': responseData?.toString().substring(0, responseData.toString().length.clamp(0, 200)),
        });
        
        return ServerFailure(
          ServerErrorType.internalError,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode ?? statusCode.toString(),
        );

      default:
        final serverMessage = _extractErrorMessage(responseData);
        final serverErrorCode = _extractErrorCode(responseData);
        return ServerFailure(
          ServerErrorType.unknown,
          serverMessage: serverMessage,
          serverErrorCode: serverErrorCode,
        );
    }
  }

  String? _extractErrorMessage(dynamic responseData) {
    if (responseData == null) return null;

    try {
      if (responseData is Map<String, dynamic>) {
        // Try common error message fields
        return responseData['message'] ??
               responseData['error'] ??
               responseData['detail'] ??
               responseData['errors']?.toString();
      }

      if (responseData is String) {
        // Check if it's HTML content (common for server errors like 503)
        if (responseData.trim().toLowerCase().startsWith('<!doctype html>') ||
            responseData.trim().toLowerCase().startsWith('<html>') ||
            responseData.contains('<title>') && responseData.contains('</title>')) {
          
          // Extract title from HTML if possible
          final titleMatch = RegExp(r'<title[^>]*>(.*?)</title>', caseSensitive: false).firstMatch(responseData);
          if (titleMatch != null) {
            return titleMatch.group(1)?.trim();
          }
          
          // If no title, return a generic message for HTML responses
          return 'Server temporarily unavailable';
        }
        
        // Regular string response
        return responseData;
      }
    } catch (e) {
      AppLogger.warning('ErrorInterceptor: Failed to extract error message from response data', data: {
        'responseData': responseData.toString(),
        'error': e.toString(),
      });
    }

    return null;
  }

  String? _extractErrorCode(dynamic responseData) {
    if (responseData == null) return null;

    try {
      if (responseData is Map<String, dynamic>) {
        // Try common error code fields and convert to string
        final errorCode = responseData['error_code'] ??
                         responseData['errorCode'] ??
                         responseData['code'] ??
                         responseData['status_code'];

        // Convert to string if not null
        return errorCode?.toString();
      }

      if (responseData is String) {
        // For HTML responses, try to extract status code from content
        if (responseData.contains('<title>') && responseData.contains('</title>')) {
          // Look for common status codes in HTML title
          final statusMatch = RegExp(r'(\d{3})\s+[A-Za-z\s]+').firstMatch(responseData);
          if (statusMatch != null) {
            return statusMatch.group(1);
          }
        }
      }
    } catch (e) {
      AppLogger.warning('ErrorInterceptor: Failed to extract error code from response data', data: {
        'responseData': responseData.toString(),
        'error': e.toString(),
      });
    }

    return null;
  }
}
