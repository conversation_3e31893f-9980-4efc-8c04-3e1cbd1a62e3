# Simple Retry System Documentation

## 🔄 Overview

The simple retry system provides automatic retry functionality for failed HTTP requests in the Flutter application. It's designed with simplicity in mind while handling transient network errors gracefully and maintaining clean architecture principles.

## 🏗️ Architecture

### Core Components

```
lib/data/network/interceptors/
├── simple_retry_interceptor.dart  # Simple retry interceptor implementation
└── README_RETRY.md               # This documentation
```

**Key Design Principles:**
- **Simplicity First**: One file, minimal configuration
- **Sensible Defaults**: Works out of the box for 90% of use cases
- **Easy to Understand**: Clear, readable code
- **OpenAPI Compatible**: Ready for future generated clients

### Integration Points

- **NetworkModule**: Creates retry interceptors directly in API client configuration
- **ApiClient**: Integrates retry interceptors into the interceptor chain
- **BaseApiService**: Works seamlessly with retry logic through Either pattern
- **Error Handling**: Coordinates with ErrorInterceptor for comprehensive error management

## 🎯 Retry Strategy

### Simple Exponential Backoff

The system uses a single, proven retry strategy:

- **Exponential Backoff**: Exponentially increasing delays with reasonable limits
- **Max Delay Cap**: Prevents excessive wait times (30 seconds max)
- **Sensible Defaults**: 3 retries, 1 second initial delay
- **Environment Aware**: More aggressive in development, conservative in production

## ⚙️ Configuration

### Pre-configured Retry Policies

#### Default Configuration
```dart
SimpleRetryInterceptor()
// - maxRetries: 3
// - initialDelay: 1000ms
// - enableLogging: true
// - strategy: exponential backoff with 30s max delay
```

#### Development Environment
```dart
SimpleRetryInterceptor.development()
// - maxRetries: 5
// - initialDelay: 500ms
// - enableLogging: true
// - More aggressive for testing
```

#### Authentication Endpoints
```dart
SimpleRetryInterceptor.auth()
// - maxRetries: 2
// - initialDelay: 1500ms
// - enableLogging: true
// - Conservative for auth endpoints
```

#### Custom Configuration
```dart
SimpleRetryInterceptor(
  maxRetries: 10,
  initialDelay: Duration(milliseconds: 2000),
  enableLogging: false,
)
```

### Retryable Conditions

#### Retryable Network Issues
- Connection timeout
- Send timeout
- Receive timeout
- Connection errors

#### Retryable Server Errors
- All 5xx status codes (500, 502, 503, 504, etc.)

#### Non-Retryable Conditions
- Cancelled requests
- Client errors (4xx status codes)
- Authentication failures
- Request validation errors

## 🔧 Usage

### Automatic Integration

Retry interceptors are automatically integrated into API clients:

```dart
// Default API Client (environment-based retry)
@Named('default') ApiClient defaultApiClient;

// Auth API Client (conservative retry)
@Named('auth') ApiClient authApiClient;

// Public API Client (standard retry)
@Named('public') ApiClient publicApiClient;

// Upload API Client (no retry for large files)
@Named('upload') ApiClient uploadApiClient;
```

### Manual Usage

```dart
// Create simple retry interceptor
final retryInterceptor = SimpleRetryInterceptor();

// Or with custom configuration
final customRetryInterceptor = SimpleRetryInterceptor(
  maxRetries: 5,
  initialDelay: Duration(milliseconds: 2000),
  enableLogging: false,
);

// Add to Dio instance
dio.interceptors.add(retryInterceptor);
```

### Monitoring Retry Attempts

Retry attempts are automatically logged when logging is enabled:

```
SimpleRetryInterceptor: Retrying attempt 1/3 after 1000ms
SimpleRetryInterceptor: Retry successful on attempt 1
SimpleRetryInterceptor: Max retries exceeded: 3/3
```

## 🚀 OpenAPI Compatibility

The retry system is designed to work seamlessly with OpenAPI generated clients:

### Current Manual API Services
```dart
class AuthApiService extends BaseApiService {
  // Retry logic handled automatically by interceptor
  Future<Either<Failure, LoginResponse>> login(LoginRequest request) async {
    return handleApiCall(
      () => dio.post('/auth/login', data: request.toJson()),
      (data) => LoginResponse.fromJson(data),
    );
  }
}
```

### Future OpenAPI Generated Clients
```dart
// Generated API client will automatically inherit retry behavior
final apiClient = ApiClient(
  interceptors: [
    loggingInterceptor,
    retryInterceptor, // Same retry logic
    authInterceptor,
    errorInterceptor,
  ],
);

final generatedApi = DefaultApi(apiClient.dio);
```

## 🎛️ API Client Types

### Default API Client
- **Purpose**: Most authenticated API requests
- **Retry Policy**: Environment-based (development/production)
- **Interceptors**: Logging → Retry → Mock → Auth → Error

### Auth API Client
- **Purpose**: Authentication endpoints (login, logout)
- **Retry Policy**: Conservative (2 retries, fixed delay)
- **Interceptors**: Logging → Retry → Mock → Error

### Public API Client
- **Purpose**: Public endpoints without authentication
- **Retry Policy**: Standard (3 retries, exponential backoff)
- **Interceptors**: Logging → Retry → Error

### Upload API Client
- **Purpose**: File upload operations
- **Retry Policy**: No retry (large files can be time-consuming)
- **Interceptors**: Logging → Mock → Auth → Error

## 🔍 Best Practices

### 1. Retry Strategy Selection
- **Development**: Use aggressive retry for testing (`SimpleRetryInterceptor.development()`)
- **Production**: Use conservative retry to avoid server overload (`SimpleRetryInterceptor()`)
- **Auth Endpoints**: Use minimal retry to prevent account lockouts (`SimpleRetryInterceptor.auth()`)
- **File Uploads**: Disable retry for large files (no retry interceptor)

### 2. Custom Retry Logic
```dart
// Simple approach - use existing configurations
final customInterceptor = SimpleRetryInterceptor(
  maxRetries: 5,
  initialDelay: Duration(milliseconds: 2000),
  enableLogging: false,
);

// For complex custom logic, extend SimpleRetryInterceptor
class CustomRetryInterceptor extends SimpleRetryInterceptor {
  @override
  bool _shouldRetry(DioException err) {
    // Custom retry logic
    final statusCode = err.response?.statusCode;
    if (statusCode != null && statusCode >= 400 && statusCode < 500) {
      return false; // Don't retry client errors
    }
    return super._shouldRetry(err);
  }
}
```

### 3. Monitoring and Debugging
- Enable logging in development environments (`enableLogging: true`)
- Monitor retry patterns in production logs
- SimpleRetryInterceptor automatically caps delays at 30 seconds
- Exponential backoff with reasonable limits prevents server overload

### 4. Error Handling Integration
```dart
// Retry works seamlessly with existing error handling
final result = await authRepository.login(email, password);

result.fold(
  (failure) {
    // Handle final failure after all retries exhausted
    if (failure is NetworkFailure) {
      showNetworkError();
    }
  },
  (user) {
    // Handle success (may have succeeded after retries)
    navigateToHome(user);
  },
);
```

## 🧪 Testing

### Unit Testing Retry Logic
```dart
test('should retry on network timeout', () async {
  // Mock network timeout
  when(mockDio.post(any)).thenThrow(
    DioException(
      requestOptions: RequestOptions(path: '/test'),
      type: DioExceptionType.connectionTimeout,
    ),
  );

  // Verify retry attempts
  await retryInterceptor.onError(exception, handler);
  
  verify(mockDio.post(any)).called(3); // maxRetries + 1
});
```

### Integration Testing
```dart
test('should eventually succeed after retries', () async {
  // Mock first two calls to fail, third to succeed
  when(mockApiService.getData())
    .thenThrow(NetworkException())
    .thenThrow(NetworkException())
    .thenAnswer((_) async => Right(testData));

  final result = await repository.getData();
  
  expect(result.isRight(), true);
});
```

## 📊 Performance Considerations

### Memory Usage
- SimpleRetryInterceptor has minimal memory footprint
- No persistent state between requests
- Lightweight configuration with sensible defaults

### Network Impact
- Exponential backoff prevents server overload
- 30-second max delay prevents excessive waits
- Conservative retry policies for auth endpoints
- No retry for upload operations to avoid bandwidth waste

### User Experience
- Transparent retry handling
- No user intervention required
- Appropriate timeouts prevent long waits

## 🔧 Troubleshooting

### Common Issues

1. **Infinite Retry Loops**
   - Check `maxRetries` configuration
   - Verify `shouldRetry` logic
   - Ensure proper error conditions

2. **Retry Not Working**
   - Verify interceptor order in API client
   - Check if error type is retryable
   - Enable logging for debugging

3. **Performance Issues**
   - Reduce `maxRetries` in production
   - Implement proper `maxDelay` limits
   - Use appropriate retry strategies

### Debug Logging
```dart
// Enable detailed retry logging
final interceptor = SimpleRetryInterceptor(
  enableLogging: true,
  maxRetries: 3,
  initialDelay: Duration(milliseconds: 1000),
);
```

## 🚀 Future Enhancements

### Planned Features
- Circuit breaker pattern integration
- Retry metrics and monitoring
- Dynamic retry configuration
- Request-specific retry policies
- Retry queue management

### Migration Path
The retry system is designed to be forward-compatible with:
- OpenAPI generator updates
- New Dio versions
- Additional retry strategies
- Enhanced monitoring capabilities
