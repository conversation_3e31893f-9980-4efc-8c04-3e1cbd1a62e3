import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
import 'base_mock_interceptor.dart';

/// Mock interceptor specifically for authentication APIs
/// Handles: /auth/login, /auth/logout, /auth/register, etc.
@injectable
class MockAuthInterceptor extends BaseMockInterceptor {
  
  /// Mock users database
  static final List<Map<String, dynamic>> _mockUsers = [
    {
      'id': '1',
      'email': '<EMAIL>',
      'password': '123456',
      'name': 'Admin User',
      'avatar': 'https://via.placeholder.com/150',
      'phone': '+84123456789',
      'role': 'admin',
    },
    {
      'id': '2',
      'email': '<EMAIL>',
      'password': 'password',
      'name': 'Test User',
      'avatar': 'https://via.placeholder.com/150',
      'phone': '+84987654321',
      'role': 'user',
    },
    {
      'id': '3',
      'email': '<EMAIL>',
      'password': 'demo123',
      'name': 'Demo User',
      'avatar': null,
      'phone': '+84555666777',
      'role': 'demo',
    },
  ];

  @override
  bool shouldHandle(RequestOptions options) {
    // Handle all auth-related endpoints
    return ApiPaths.isAuthPath(options.path);
  }

  @override
  void handleMockRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      // Simulate network delay
      await simulateNetworkDelay();
      
      // Optional: Simulate random errors (uncomment to enable)
      // simulateRandomError(options);
      
      // Route to specific handlers based on endpoint
      if (options.path.contains(ApiPaths.authLogin) && options.method == 'POST') {
        _handleLogin(options, handler);
      } else if (options.path.contains(ApiPaths.authLogout) && options.method == 'POST') {
        _handleLogout(options, handler);
      } else if (options.path.contains(ApiPaths.authRegister) && options.method == 'POST') {
        _handleRegister(options, handler);
      } else if (options.path.contains(ApiPaths.authRefresh) && options.method == 'POST') {
        _handleRefreshToken(options, handler);
      } else {
        // Unknown auth endpoint
        AppLogger.warning('Mock Auth: Unknown endpoint ${options.path}');
        handler.resolve(createErrorResponse(options, 404, 'Endpoint not found'));
      }
    } catch (e) {
      AppLogger.error('Mock Auth: Error handling request', error: e);
      handler.reject(DioException(
        requestOptions: options,
        message: 'Mock auth error: $e',
      ));
    }
  }

  void _handleLogin(RequestOptions options, RequestInterceptorHandler handler) {
    try {
      final requestData = options.data;
      final email = requestData['email'] ?? '';
      final password = requestData['password'] ?? '';

      AppLogger.info('Mock Auth: Login attempt', data: {'email': email});

      // Validate credentials
      final mockUser = _validateCredentials(email, password);
      
      if (mockUser != null) {
        // Success response
        final responseData = {
          'access_token': 'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
          'refresh_token': 'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
          'user': {
            'id': mockUser['id'],
            'email': mockUser['email'],
            'name': mockUser['name'],
            'avatar': mockUser['avatar'],
            'phone': mockUser['phone'],
          },
          'expires_in': 3600,
          'token_type': 'Bearer',
        };

        AppLogger.info('Mock Auth: Login successful', data: {'userId': mockUser['id']});
        handler.resolve(createSuccessResponse(options, responseData));
      } else {
        // Invalid credentials
        AppLogger.warning('Mock Auth: Login failed - Invalid credentials');
        handler.resolve(createErrorResponse(options, 401, 'Invalid email or password'));
      }
    } catch (e) {
      AppLogger.error('Mock Auth: Login error', error: e);
      handler.resolve(createErrorResponse(options, 500, 'Internal server error'));
    }
  }

  void _handleLogout(RequestOptions options, RequestInterceptorHandler handler) {
    AppLogger.info('Mock Auth: Logout attempt');

    // Logout always succeeds in mock
    final responseData = {
      'message': 'Logged out successfully',
      'success': true,
    };

    AppLogger.info('Mock Auth: Logout successful');
    handler.resolve(createSuccessResponse(options, responseData));
  }

  void _handleRegister(RequestOptions options, RequestInterceptorHandler handler) {
    AppLogger.info('Mock Auth: Register attempt');
    
    // For now, just return success (can be expanded later)
    final responseData = {
      'message': 'Registration successful',
      'success': true,
      'user': {
        'id': 'new_user_${DateTime.now().millisecondsSinceEpoch}',
        'email': options.data['email'] ?? '<EMAIL>',
        'name': options.data['name'] ?? 'New User',
      }
    };

    handler.resolve(createSuccessResponse(options, responseData));
  }

  void _handleRefreshToken(RequestOptions options, RequestInterceptorHandler handler) {
    AppLogger.info('Mock Auth: Refresh token attempt');

    try {
      // Extract refresh token from request body
      final requestData = options.data;
      String? refreshToken;

      if (requestData is Map<String, dynamic>) {
        refreshToken = requestData['refresh_token'];
      } else if (requestData is String) {
        // Handle JSON string
        final Map<String, dynamic> jsonData = jsonDecode(requestData);
        refreshToken = jsonData['refresh_token'];
      }

      if (refreshToken == null || refreshToken.isEmpty) {
        AppLogger.warning('Mock Auth: No refresh token provided');
        handler.resolve(createErrorResponse(options, 400, 'Refresh token is required'));
        return;
      }

      // Validate refresh token format (mock validation)
      if (!refreshToken.startsWith('mock_refresh_token_') && !refreshToken.startsWith('mock_new_refresh_token_')) {
        AppLogger.warning('Mock Auth: Invalid refresh token format');
        handler.resolve(createErrorResponse(options, 401, 'Invalid refresh token'));
        return;
      }

      // Generate new tokens
      final responseData = {
        'access_token': 'mock_new_access_token_${DateTime.now().millisecondsSinceEpoch}',
        'refresh_token': 'mock_new_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      AppLogger.info('Mock Auth: Token refresh successful');
      handler.resolve(createSuccessResponse(options, responseData));
    } catch (e) {
      AppLogger.error('Mock Auth: Error processing refresh token', error: e);
      handler.resolve(createErrorResponse(options, 500, 'Internal server error'));
    }
  }

  Map<String, dynamic>? _validateCredentials(String email, String password) {
    try {
      return _mockUsers.firstWhere(
        (user) => user['email'] == email && user['password'] == password,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get all mock users (for testing purposes)
  static List<Map<String, dynamic>> get mockUsers => List.from(_mockUsers);
  
  /// Add a new mock user (for testing purposes)
  static void addMockUser(Map<String, dynamic> user) {
    _mockUsers.add(user);
  }
}
