// Core barrel export
import 'package:sales_app/core/core.dart';
import 'mock_auth_interceptor.dart';

/// Test function to demonstrate mock interceptors functionality
void testMockInterceptors() {
  AppLogger.debug('=== Modular Mock Interceptors Test ===');

  AppLogger.info('Current API Mode: ${AppConstants.isMockMode ? "Mock" : "Real"}');

  // Test Auth Interceptor
  AppLogger.debug('\n=== Mock Auth Interceptor ===');
  final authInterceptor = MockAuthInterceptor();

  // Test shouldHandle method
  AppLogger.debug('Should handle ${ApiPaths.authLogin}: ${authInterceptor.shouldHandle(_createOptions(ApiPaths.authLogin, 'POST'))}');
  AppLogger.debug('Should handle /users/profile: ${authInterceptor.shouldHandle(_createOptions('/users/profile', 'GET'))}');
  AppLogger.debug('Should handle ${ApiPaths.authLogout}: ${authInterceptor.shouldHandle(_createOptions(ApiPaths.authLogout, 'POST'))}');

  // Test API paths constants
  AppLogger.debug('\n=== API Paths Constants ===');
  AppLogger.debug('Auth Login: ${ApiPaths.authLogin}');
  AppLogger.debug('Auth Logout: ${ApiPaths.authLogout}');
  AppLogger.debug('Auth Register: ${ApiPaths.authRegister}');
  AppLogger.debug('Auth Refresh: ${ApiPaths.authRefresh}');

  // Test mock users
  AppLogger.debug('\n=== Available Mock Users ===');
  final mockUsers = MockAuthInterceptor.mockUsers;
  for (var user in mockUsers) {
    AppLogger.debug('- ${user['email']} / ${user['password']} (${user['name']}) [${user['role']}]');
  }

  // Test API mode
  AppLogger.debug('\n=== API Mode Testing ===');
  AppLogger.info('Current Mode: ${AppConstants.isMockMode ? "Mock" : "Real"}');
  AppLogger.debug('To switch modes: Change AppConstants.isMockMode in app_constants.dart');

  AppLogger.debug('\n=== Interceptor Architecture ===');
  AppLogger.debug('Request Flow:');
  AppLogger.debug('1. LoggingInterceptor (logs request)');
  AppLogger.debug('2. MockAuthInterceptor (catches /auth/*)');
  AppLogger.debug('3. AuthInterceptor (adds auth headers if not mocked)');
  AppLogger.debug('4. ErrorInterceptor (handles errors)');
  AppLogger.debug('5. Real API call (if not intercepted)');

  AppLogger.debug('\n=== Usage Instructions ===');
  AppLogger.info('1. App is currently in Mock Mode');
  AppLogger.info('2. Login with: <EMAIL> / 123456');
  AppLogger.debug('3. Check console for "Mock Interceptor" logs');
  AppLogger.info('4. To switch to Real API: Change AppConstants.isMockMode to false');

  AppLogger.debug('\n=== Test Complete ===');
}

// Helper function to create RequestOptions for testing
dynamic _createOptions(String path, String method) {
  // This is a simplified version for testing
  // In real usage, this would be a proper RequestOptions object
  return MockRequestOptions(path: path, method: method);
}

// Mock class for testing
class MockRequestOptions {
  final String path;
  final String method;
  
  MockRequestOptions({required this.path, required this.method});
}
