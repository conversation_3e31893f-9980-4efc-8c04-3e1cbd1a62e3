import 'package:dio/dio.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';

/// Base class for all mock interceptors
/// Provides common functionality for mock API simulation
abstract class BaseMockInterceptor extends Interceptor {
  
  /// Check if this interceptor should handle the request
  bool shouldHandle(RequestOptions options);
  
  /// Handle the mock request
  void handleMockRequest(RequestOptions options, RequestInterceptorHandler handler);
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Skip if not in mock mode
    if (!AppConstants.isMockMode) {
      return handler.next(options);
    }

    // Check if this interceptor should handle this request
    if (!shouldHandle(options)) {
      return handler.next(options);
    }

    AppLogger.info('Mock Interceptor [$runtimeType]: Intercepting ${options.method} ${options.path}');
    
    // Handle the mock request
    handleMockRequest(options, handler);
  }
  
  /// Simulate realistic network delay
  Future<void> simulateNetworkDelay() async {
    if (AppConstants.isMockMode) {
      final delay = 500 + (DateTime.now().millisecondsSinceEpoch % 1500);
      await Future.delayed(Duration(milliseconds: delay));
    }
  }
  
  /// Create a successful mock response
  Response createSuccessResponse(RequestOptions options, Map<String, dynamic> data) {
    return Response(
      requestOptions: options,
      data: data,
      statusCode: 200,
      statusMessage: 'OK',
    );
  }
  
  /// Create an error mock response
  Response createErrorResponse(RequestOptions options, int statusCode, String message, {Map<String, dynamic>? errorData}) {
    return Response(
      requestOptions: options,
      data: errorData ?? {
        'error': 'mock_error',
        'message': message,
      },
      statusCode: statusCode,
      statusMessage: _getStatusMessage(statusCode),
    );
  }
  
  /// Simulate random network errors (10% chance)
  void simulateRandomError(RequestOptions options) {
    if (AppConstants.isMockMode) {
      final random = DateTime.now().millisecondsSinceEpoch % 100;
      if (random < 10) { // 10% chance of error
        throw DioException(
          requestOptions: options,
          message: 'Mock network error: Connection timeout',
          type: DioExceptionType.connectionTimeout,
        );
      }
    }
  }
  
  String _getStatusMessage(int statusCode) {
    switch (statusCode) {
      case 200: return 'OK';
      case 201: return 'Created';
      case 400: return 'Bad Request';
      case 401: return 'Unauthorized';
      case 403: return 'Forbidden';
      case 404: return 'Not Found';
      case 500: return 'Internal Server Error';
      default: return 'Unknown';
    }
  }
}
