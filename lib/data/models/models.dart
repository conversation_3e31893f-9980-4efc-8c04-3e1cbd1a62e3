/// Data Models Master Barrel Export
/// All data models and DTOs for the application
/// 
/// This file provides a single import point for all data models including:
/// - Remote API models (DTOs)
/// - Local storage models
/// - Generated models (Freezed, JSON serializable)
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/data/models/models.dart';
/// 
/// // Access any data model:
/// 
/// // Remote API models
/// final userModel = UserModel(
///   id: '123',
///   name: '<PERSON>',
///   email: '<EMAIL>',
/// );
/// 
/// // Local storage models
/// final userLocal = UserLocalModel.fromDomain(userEntity);
/// 
/// // Convert between models and entities
/// final userEntity = userModel.toEntity();
/// final modelFromEntity = UserModel.fromEntity(userEntity);
/// ```
/// 
/// ## Model Categories:
/// - **Remote Models**: API response/request models (DTOs)
/// - **Local Models**: Database and local storage models
library models;

// Remote models - API DTOs và response models
export 'user_model.dart';

// Local models - database và local storage models
export 'local/local.dart';
