import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/domain/entities/entities.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

/// Remote API model cho User (DTO)
/// <PERSON><PERSON><PERSON> nghĩa cấu trúc dữ liệu nhận từ API
/// 
/// Usage:
/// ```dart
/// // Tạo từ JSON response
/// final userModel = UserModel.fromJson(jsonResponse);
/// 
/// // Tạo từ domain entity
/// final userModel = UserModel.fromEntity(userEntity);
/// 
/// // Convert sang domain entity
/// final userEntity = userModel.toEntity();
/// 
/// // Convert sang JSON
/// final json = userModel.toJson();
/// ```
@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    required String id,
    required String email,
    required String name,
    String? avatar,
    String? phone,
  }) = _UserModel;

  /// Tạo UserModel từ JSON response của API
  /// 
  /// [json] - JSON object từ API response
  /// 
  /// Example:
  /// ```dart
  /// final json = {
  ///   'id': '123',
  ///   'email': '<EMAIL>',
  ///   'name': 'John Doe',
  ///   'avatar': 'https://example.com/avatar.jpg',
  ///   'phone': '+1234567890',
  /// };
  /// final userModel = UserModel.fromJson(json);
  /// ```
  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
      
  /// Tạo UserModel từ Domain Entity
  /// 
  /// [user] - Domain User entity
  /// 
  /// Example:
  /// ```dart
  /// final userEntity = User(
  ///   id: '123',
  ///   email: '<EMAIL>',
  ///   name: 'John Doe',
  /// );
  /// final userModel = UserModel.fromEntity(userEntity);
  /// ```
  factory UserModel.fromEntity(User user) => UserModel(
    id: user.id,
    email: user.email,
    name: user.name,
    avatar: user.avatar,
    phone: user.phone,
  );
  
  const UserModel._();
  
  /// Convert UserModel sang Domain Entity
  /// 
  /// Returns: User - Domain entity với business logic
  /// 
  /// Example:
  /// ```dart
  /// final userEntity = userModel.toEntity();
  /// print(userEntity.getGreeting()); // "Chào buổi sáng 👋"
  /// ```
  User toEntity() => User(
    id: id,
    email: email,
    name: name,
    avatar: avatar,
    phone: phone,
  );
} 