{"@@locale": "en", "appName": "KienlongBank Sales App", "home": "Home", "password": "Password", "login": "<PERSON><PERSON>", "introduction": "Introduction", "register": "Register", "register_account": "Register account", "documentVerificationGuide": "Document Verification Guide", "notesWhenTakingDocuments": "Notes when taking documents:", "avoidUsingImages": "Avoid using images like:", "checkFrontSide": "Check front side of ID", "checkBackSide": "Check back side of ID", "pleaseCheckPhoto": "Please check the photo", "placeDocumentInFrame": "Please place the document in the frame for verification", "clickToCapture": "<PERSON>lick to capture", "personalInfoConfirmation": "Personal Information Confirmation", "personal_info_confirmation_title": "Personal Information Confirmation", "personal_info_confirmation_header": "Confirm Your Information", "personal_info_confirmation_subtitle": "Please review and confirm your personal information before proceeding", "infoFromId": "Information from ID", "additionalInfo": "Additional Information", "phoneNumber": "Phone Number", "enterPhoneNumber": "Enter phone number", "position": "Position", "province": "Province/City", "province_selection_label": "Select Province/City", "selectProvince": "Select province/city", "select_province_hint": "Choose your province or city", "branch": "Branch", "branch_selection_label": "Select Branch", "selectBranch": "Select branch", "select_branch_hint": "Choose your branch", "branchAddress": "Branch Address", "referralCode": "Referral Code (if any)", "enterReferralCode": "Enter referral code", "registrationSuccess": "Registration Successful", "bankWillContact": "KienlongBank will contact you soon to inform the result!", "close": "Close", "agree": "Agree", "fullName": "Full Name", "idNumber": "ID Number", "residentialAddress": "Residential Address", "newCTV": "New CTV", "dailyInstallmentLoan": "Daily Installment Loan", "benefitsAsCTV": "Benefits as CTV", "stepsToBecomeCTV": "Steps to become a CTV", "dailyInstallmentLoanDesc": "Daily Installment Loan is a loan product in the form of daily installments (principal, interest) to serve living needs and business activities, other activities.", "suitableFor": "Suitable for individuals with sudden, short-term spending needs; small businesses and individual businesses; customers with stable income and good credit history.", "flexibleWorkingHours": "Flexible working hours", "monthlyServiceFee": "Monthly service fee", "unlimitedIncome": "Unlimited income", "workUniform": "Work uniform provided", "healthInsurance": "Health insurance and accident insurance provided by Kienlongbank when meeting outstanding balance conditions", "holidayBonus": "Holiday bonus for 30/04, National Day 02/9, ...", "requirements": "Requirements:", "vietnameseCitizen": "Vietnamese citizen with legal capacity, civil act capacity and civil liability according to law", "goodBackground": "Good background and character; No criminal record", "ageRequirement": "Age from 18 to 60, graduated from secondary school or higher", "healthRequirement": "Good health, clear mind", "assetRequirement": "Minimum collateral of 150 million VND and commitment to comply with Kienlongbank's minimum average outstanding balance regulations", "areaKnowledge": "Understanding of working area", "noOtherBank": "Not working for other credit institutions or finance companies", "continueText": "Continue", "continue_button": "Continue", "retake": "Retake", "confirm": "Confirm", "idCard": "ID Card", "passport": "Passport", "no_provinces_available": "No provinces available", "no_branches_available": "No branches available for selected province", "errorServerInternal": "Server error. Please try again later.", "errorServerNotFound": "The requested resource was not found.", "errorServerTooManyRequests": "Too many requests. Please try again later.", "errorServerBadRequest": "Invalid request data.", "errorServerUnknown": "An error occurred. Please try again.", "errorNetworkConnectionTimeout": "Connection timeout. Please check your internet connection.", "errorNetworkSendTimeout": "Request timeout. Please try again.", "errorNetworkReceiveTimeout": "Response timeout. Please try again.", "errorNetworkNoConnection": "No internet connection. Please check your network settings.", "errorNetworkRequestCancelled": "Request was cancelled.", "errorNetworkCertificate": "Certificate verification failed.", "errorNetworkUnknown": "Network error. Please try again.", "errorCacheRead": "Failed to read cached data.", "errorCacheWrite": "Failed to save data to cache.", "errorCacheDataCorrupted": "Cached data is corrupted.", "errorCacheStorageFull": "Storage is full.", "errorCacheUnknown": "Cache error occurred.", "errorAuthInvalidCredentials": "Authentication failed. Please login again.", "errorAuthAccessDenied": "Access denied. You don't have permission to perform this action.", "errorAuthTokenExpired": "Session expired. Please login again.", "errorAuthUserNotFound": "User not found.", "errorAuthAccountLocked": "Account is locked or disabled.", "errorAuthUnknown": "Authentication error occurred.", "errorValidationInvalidEmail": "Please enter a valid email address.", "errorValidationInvalidPassword": "Please enter a valid password.", "errorValidationRequiredField": "This field is required.", "errorValidationTooShort": "Input is too short.", "errorValidationTooLong": "Input is too long.", "errorValidationInvalidFormat": "Invalid format.", "errorValidationServer": "Validation failed.", "errorValidationUnknown": "Validation error occurred.", "networkConnected": "Connected", "networkDisconnected": "No internet connection", "networkUnstable": "Unstable connection", "networkChecking": "Checking connection...", "networkReconnectedSuccess": "Successfully reconnected", "networkUnstableWarning": "Unstable connection", "networkUnstableDescription": "Network is unstable - Some features may be affected", "networkCheckingConnection": "Checking connection...", "networkDisconnectedDescription": "Please check your internet connection and try again", "networkUnstableConnectionDescription": "Network connection is unstable. Some features may be affected", "networkCheckingConnectionDescription": "Checking connection status...", "networkRetry": "Retry", "networkRetryChecking": "Checking...", "networkContinue": "Continue", "networkCheck": "Check", "createNewLoan": "Create New Loan", "loanStep1Title": "Provide Identity Documents", "loanStep2Title": "Confirm Primary Borrower Information", "loanStep3Title": "Provide Identity Documents (Co-borrower)", "loanStep4Title": "Confirm Co-borrower Information", "loanStep5Title": "Provide Loan Request Information", "loanStep6Title": "Provide Financial Information", "loanStep7Title": "Provide Collateral Information", "loanStep8Title": "Detailed Collateral Information", "loanStep9Title": "Provide Document List", "loanStep10Title": "Confirm Loan Information", "loanStep11Title": "Loan Creation Successful", "inDevelopment": "In Development...", "step": "Bước", "noData": "No data available", "submitting_login": "Logging in...", "submitting_register": "Registering...", "submitting_save": "Saving...", "submitting_delete": "Deleting...", "submitting_update": "Updating...", "submitting_form": "Submitting form...", "cvt_policy_title": "CTV Policy", "captureFrontSide": "Capture Front Side", "captureBackSide": "Capture Back Side", "validationEmailRequired": "Please enter email", "validationEmailInvalid": "Invalid email format", "validationPhoneRequired": "Please enter phone number", "validationPhoneStartWithZero": "Phone number must start with 0", "validationPhoneLength": "Phone number must have 10 digits", "validationPhoneInvalid": "Invalid phone number", "validationIdNumberRequired": "Please enter ID number", "validationIdNumberLength": "ID number must have 12 digits", "validationIdNumberNumeric": "ID number must contain only digits", "validationFullNameRequired": "Please enter full name", "validationFullNameMinLength": "Full name must have at least 2 characters", "validationFullNameMaxLength": "Full name cannot exceed 100 characters", "validationFullNameCharacters": "Full name can only contain letters and spaces", "validationAddressRequired": "Please enter address", "validationAddressMinLength": "Address must have at least 10 characters", "validationAddressMaxLength": "Address cannot exceed 200 characters", "validationAddressCharacters": "Address must not contain special characters", "validationRequired": "Please enter {fieldName}", "validationMinLength": "{fieldName} must have at least {minLength} characters", "validationMaxLength": "{fieldName} cannot exceed {maxLength} characters", "validationNumericOnly": "{fieldName} must contain only digits", "validationPasswordRequired": "Please enter password", "validationPasswordMinLength": "Password must have at least 8 characters", "validationPasswordMaxLength": "Password cannot exceed 50 characters", "validationPasswordStrength": "Password must contain uppercase, lowercase and numbers", "validationConfirmPasswordRequired": "Please enter confirm password", "validationConfirmPasswordMismatch": "Confirm password does not match", "validationDateOfBirthRequired": "Please select date of birth", "validationDateOfBirthAge": "You must be at least 18 years old", "validationDateOfBirthInvalid": "Invalid date of birth", "validationReferrerCodeMinLength": "Referrer code must have at least 3 characters", "validationReferrerCodeMaxLength": "Referrer code cannot exceed 20 characters", "validationReferrerCodeCharacters": "Referrer code can only contain letters, numbers and hyphens", "validationAmountRequired": "Please enter amount", "validationAmountFormat": "Amount can only contain digits and commas", "validationAmountPositive": "Amount must be greater than 0", "validationAmountTooLarge": "Amount is too large", "validationUrlRequired": "Please enter URL", "validationUrlInvalid": "Invalid URL format", "validationOtpRequired": "Please enter OTP code", "validationOtpLength": "OTP code must have {length} digits", "validationOtpNumeric": "OTP code must contain only digits", "validationBankAccountRequired": "Please enter bank account number", "validationBankAccountLength": "Bank account number must have 8-20 digits", "validationBankAccountNumeric": "Bank account number must contain only digits", "validationTaxCodeRequired": "Please enter tax code", "validationTaxCodeLength": "Tax code must have 10 or 13 digits", "validationTaxCodeNumeric": "Tax code must contain only digits", "validationIssueDateRequired": "Please select issue date", "validationExpiryDateRequired": "Please select expiry date", "validationBirthDateRequired": "Please select birth date", "step1_identity_document_description": "Please select the type of identity document of the borrower to verify information", "document_type_label": "Document Type", "document_front_side_label": "Front Side of Document", "document_back_side_label": "Back Side of Document", "success_ctv_message": "CTV registration successful", "account": "Account", "dvkd_address": "Business Unit Address", "profile": "Profile", "logout": "Logout", "logout_title": "Logout", "logout_message": "Are you sure you want to logout?", "logout_confirm": "Logout", "logout_cancel": "Cancel", "logout_error": "An error occurred while logging out", "session_expired_title": "Session Expired", "session_expired_message": "Your session has expired. Please login again to continue using the app.", "session_expired_button": "Login Again", "ctv_referral_title": "CTV Referral", "info_from_id_documents": "Information from ID documents", "additional_information": "Additional Information", "registration_branch": "Registration Branch", "referred_person_email": "Referred person email (optional)", "collaborator": "Collaborator", "province_city": "Province/City", "select_province_city": "Select province/city", "business_unit": "Business Unit", "business_unit_registration": "Business Unit Registration", "no_province_data": "No province data available", "loading_branches": "Loading branches...", "no_branches_for_province": "No branches available for selected province", "try_again": "Try again", "referral_code_if_any": "Referral code (if any)", "enter_referral_code": "Enter referral code", "referral_code": "Referral code", "searching_referrer": "Searching for referrer...", "referrer": "<PERSON><PERSON><PERSON>", "id_document_number": "ID document number", "email_optional": "Email (if any)", "name_seller": "Referrer Name", "code_seller": "Referrer Code", "qrScanTitle": "Scan QR code", "skip": "<PERSON><PERSON>", "qr_scan_invalid_data": "Cannot read information from QR code. Please try again or select an image from the gallery.", "has_co_borrower": "Has co-borrower", "identity_document_info": "Identity document information", "full_name": "Full name", "document_number": "Document number", "issue_date": "Issue date", "expiry_date": "Expiry date", "issue_place": "Issue place", "birth_date": "Birth date", "gender": "Gender", "permanent_address": "Permanent address", "personal_info": "Personal information", "marital_status": "Marital status", "phone_number": "Phone number", "same_as_permanent_address": "Current address is the same as permanent address", "district": "District", "ward": "Ward", "specific_address": "Specific address", "error": "Error", "retry": "Retry", "camera_not_found": "No camera found on device", "camera_init_error": "Cannot initialize camera", "camera_permission_error": "Camera permission error", "cannot_decode_image": "Cannot decode image", "no_ward_data": "No wards available for the selected province/district.", "document_info_label": "Document Information", "capturePassportGuidanceTitle": "Capture document", "previewPassportGuidanceTitle": "Check passport photo", "passportGuidanceSubtitle": "Please make sure the photo is clear, not blurry or obscured", "loan_purpose": "Loan purpose", "loan_term": "Loan term", "loading_loan_purpose": "Loading loan purposes...", "no_loan_purpose_data": "No loan purpose data available", "loading_loan_term": "Loading loan terms...", "no_loan_term_data": "No loan term data available", "loan_amount": "Requested loan amount", "total_need": "Total need", "own_capital": "Own capital", "branch_code": "Branch code", "loan_method": "Loan method", "custom_purpose_name": "Custom purpose name", "loan_type": "Loan type", "loan_plan": "Loan plan", "repayment_method": "Repayment method", "disbursement_method": "Disbursement method", "recipient_account_number": "Recipient account number", "daily_repayment": "Daily repayment of principal and interest", "collateral_yes": "With collateral", "collateral_no": "Without collateral", "installment_loan": "Installment loan", "cash": "Cash", "transfer": "Transfer", "loading": "Loading...", "loading_payment_accounts": "Loading payment accounts...", "no_payment_account_data": "No payment accounts found for this ID number.", "validationPassportNumberRequired": "Please enter passport number", "validationPassportNumberMaxLength": "Passport number cannot exceed 50 characters", "validationPassportNumberAlphanumeric": "Passport number can only contain letters and numbers (no special characters)", "step3_identity_document_description": "Please select the type of identity document for the borrower to verify information", "validationOwnCapitalRequired": "Please enter your own capital", "validationOwnCapitalInvalid": "Own capital must be a valid number", "validationOwnCapitalTooLarge": "Own capital cannot exceed 1,000,000,000", "validationLoanAmountRequired": "Please enter the loan amount", "validationLoanAmountTooSmall": "Loan amount must be at least 1,000,000", "validationLoanAmountTooLarge": "Loan amount cannot exceed 1,000,000,000", "validationIssuePlaceRequired": "Please enter issue place", "validationIssuePlaceMaxLength": "Issue place cannot exceed 50 characters", "validationPassportNumberCharacters": "Passport number can only contain letters and numbers", "income_source": "Income source", "daily_revenue": "Average daily revenue", "daily_income": "Average daily income", "financial_info_title": "Financial Information", "business_location_title": "Business Location"}