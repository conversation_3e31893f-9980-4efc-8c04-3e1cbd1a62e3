{"@@locale": "vi", "appName": "Ứng dụng KienlongBank Sales", "home": "Trang chủ", "password": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "introduction": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "register": "<PERSON><PERSON><PERSON> ký", "register_account": "<PERSON><PERSON><PERSON> ký tài k<PERSON>n", "documentVerificationGuide": "Hướng dẫn xác minh giấy tờ", "notesWhenTakingDocuments": "<PERSON><PERSON><PERSON> khi chụp giấy tờ:", "avoidUsingImages": "<PERSON><PERSON><PERSON>h sử dụng hình <PERSON>nh <PERSON>:", "checkFrontSide": "<PERSON><PERSON><PERSON> tra mặt trước GTTT", "checkBackSide": "<PERSON><PERSON><PERSON> tra mặt sau GTTT", "pleaseCheckPhoto": "<PERSON><PERSON> lòng kiểm tra lại ảnh đã chụp", "placeDocumentInFrame": "<PERSON><PERSON> lòng đặt giấy tờ vào khung để xác minh", "clickToCapture": "<PERSON><PERSON><PERSON><PERSON> để ch<PERSON>p", "personalInfoConfirmation": "<PERSON><PERSON><PERSON> nhận thông tin cá nhân", "personal_info_confirmation_title": "<PERSON><PERSON><PERSON> nhận thông tin cá nhân", "personal_info_confirmation_header": "<PERSON><PERSON><PERSON> n<PERSON>ận thông tin của bạn", "personal_info_confirmation_subtitle": "<PERSON>ui lòng xem lại và xác nhận thông tin cá nhân trước khi tiếp tục", "infoFromId": "Thông tin từ GTTT", "additionalInfo": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "enterPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "position": "<PERSON><PERSON><PERSON> v<PERSON>", "province": "Tỉnh/Thành phố", "province_selection_label": "Chọn Tỉnh/Thành phố", "selectProvince": "Chọn tỉnh/thành phố", "select_province_hint": "Chọn tỉnh hoặc thành phố của bạn", "branch": "<PERSON> n<PERSON>h", "branch_selection_label": "<PERSON><PERSON><PERSON>", "selectBranch": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "select_branch_hint": "<PERSON><PERSON><PERSON> chi nh<PERSON>h của bạn", "branchAddress": "Địa chỉ chi nh<PERSON>h", "referralCode": "<PERSON><PERSON> gi<PERSON>i thi<PERSON> (nế<PERSON> c<PERSON>)", "enterReferralCode": "<PERSON><PERSON><PERSON><PERSON> mã giới thiệu", "registrationSuccess": "<PERSON><PERSON><PERSON> ký thành công", "bankWillContact": "KienlongBank sẽ liên hệ với bạn trong thời gian sớm nhất để thông báo kết quả!", "close": "Đ<PERSON><PERSON>", "agree": "Đồng ý", "fullName": "Họ và tên", "idNumber": "Số CMND", "residentialAddress": "Địa chỉ thường trú", "newCTV": "CTV mới", "dailyInstallmentLoan": "<PERSON>ay tr<PERSON> góp ng<PERSON>y", "benefitsAsCTV": "<PERSON><PERSON><PERSON><PERSON> lợi khi làm CTV", "stepsToBecomeCTV": "<PERSON><PERSON><PERSON> bước trở thành CTV", "dailyInstallmentLoanDesc": "Vay trả góp ngày là sản phẩm cho vay dư<PERSON><PERSON> hình thức trả góp theo <PERSON> (gốc, lãi) để phục vụ nhu cầu sinh hoạt và hoạt động kinh doanh, các hoạt động kh<PERSON>c.", "suitableFor": "<PERSON><PERSON> hợp với cá nhân có nhu cầu chi tiêu đột xuất, ng<PERSON>n hạn; hộ <PERSON>h doanh và doanh nghiệp cá thể; kh<PERSON><PERSON> hàng có thu nhập ổn định và lịch sử tín dụng tốt.", "flexibleWorkingHours": "<PERSON><PERSON><PERSON><PERSON> gian làm vi<PERSON><PERSON> linh ho<PERSON>t", "monthlyServiceFee": "<PERSON><PERSON> d<PERSON> v<PERSON> hàng tháng", "unlimitedIncome": "<PERSON><PERSON> <PERSON>h<PERSON><PERSON> không gi<PERSON>i hạn", "workUniform": "<PERSON><PERSON><PERSON><PERSON> cấp đồng phục làm việc", "healthInsurance": "<PERSON><PERSON><PERSON><PERSON> Ki<PERSON>bank cấp bảo hiểm sức khỏe và bảo hiểm tai nạn khi đạt điều kiện dư nợ", "holidayBonus": "Thưởng l<PERSON> 30/04, <PERSON><PERSON><PERSON><PERSON> 02/9, ...", "requirements": "<PERSON><PERSON><PERSON> c<PERSON>:", "vietnameseCitizen": "Là công dân Việt Nam có năng lực pháp luật dân sự, năng lực hành vi dân sự và chịu trách nhiệm dân sự theo quy định của pháp luật", "goodBackground": "<PERSON><PERSON> lý lịch rõ <PERSON>, ph<PERSON><PERSON> chất đạo đức tốt; <PERSON><PERSON><PERSON><PERSON> có tiền án, tiền sự", "ageRequirement": "<PERSON><PERSON> tuổi từ 18 đến 60, t<PERSON><PERSON> ng<PERSON><PERSON><PERSON> THCS trở lên", "healthRequirement": "<PERSON><PERSON> sức khỏe tốt, minh mẫn", "assetRequirement": "<PERSON><PERSON> tài sản đảm bảo tối thiểu 150 triệu đồng và cam kết tuân thủ quy định về dư nợ bình quân tối thiểu của Kienlongbank", "areaKnowledge": "<PERSON><PERSON><PERSON> biết về khu vực hoạt động", "noOtherBank": "<PERSON>hông làm việc cho các tổ chức tín dụng hoặc công ty tài chính khác", "continueText": "<PERSON><PERSON><PERSON><PERSON>", "continue_button": "<PERSON><PERSON><PERSON><PERSON>", "retake": "<PERSON><PERSON><PERSON> l<PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "idCard": "CMND/CCCD", "passport": "<PERSON><PERSON> ch<PERSON>", "no_provinces_available": "<PERSON><PERSON><PERSON>ng có tỉnh thành nào", "no_branches_available": "<PERSON><PERSON><PERSON><PERSON> có chi nhánh nào cho tỉnh đã chọn", "errorServerInternal": "Lỗi máy chủ. <PERSON><PERSON> lòng thử lại sau.", "errorServerNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên đư<PERSON><PERSON> yêu cầu.", "errorServerTooManyRequests": "<PERSON><PERSON><PERSON> nhiều yêu cầu. <PERSON><PERSON> lòng thử lại sau.", "errorServerBadRequest": "<PERSON><PERSON> liệu yêu cầu không hợp lệ.", "errorServerUnknown": "<PERSON><PERSON> xảy ra lỗi. <PERSON><PERSON> lòng thử lại.", "errorNetworkConnectionTimeout": "<PERSON>ết thời gian kết nối. Vui lòng kiểm tra kết nối internet.", "errorNetworkSendTimeout": "<PERSON><PERSON><PERSON> thời gian gửi yêu cầu. <PERSON><PERSON> lòng thử lại.", "errorNetworkReceiveTimeout": "<PERSON><PERSON><PERSON> thời gian nhận phản hồi. <PERSON><PERSON> lòng thử lại.", "errorNetworkNoConnection": "<PERSON><PERSON><PERSON><PERSON> có kết nối internet. Vui lòng kiểm tra cài đặt mạng.", "errorNetworkRequestCancelled": "<PERSON><PERSON><PERSON> c<PERSON>u đã bị hủy.", "errorNetworkCertificate": "<PERSON><PERSON><PERSON> minh chứng chỉ thất bại.", "errorNetworkUnknown": "Lỗi mạng. <PERSON><PERSON> lòng thử lại.", "errorCacheRead": "<PERSON><PERSON><PERSON><PERSON> thể đọc dữ liệu đã lưu.", "errorCacheWrite": "<PERSON><PERSON><PERSON><PERSON> thể lưu dữ liệu vào bộ nhớ đệm.", "errorCacheDataCorrupted": "<PERSON><PERSON> liệu đã lưu bị hỏng.", "errorCacheStorageFull": "Bộ nhớ đã đầy.", "errorCacheUnknown": "<PERSON><PERSON> xảy ra lỗi bộ nhớ đệm.", "errorAuthInvalidCredentials": "<PERSON><PERSON><PERSON> thực thất bại. <PERSON><PERSON> lòng đăng nhập lại.", "errorAuthAccessDenied": "<PERSON><PERSON><PERSON> cập bị từ chối. <PERSON><PERSON><PERSON> không có quyền thực hiện hành động này.", "errorAuthTokenExpired": "<PERSON><PERSON><PERSON> đăng nhập đã hết hạn. <PERSON><PERSON> lòng đăng nhập lại.", "errorAuthUserNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng.", "errorAuthAccountLocked": "<PERSON><PERSON><PERSON> khoản đã bị khóa hoặc vô hiệu hóa.", "errorAuthUnknown": "<PERSON><PERSON> xảy ra lỗi xác thực.", "errorValidationInvalidEmail": "<PERSON><PERSON> lòng nhập địa chỉ email hợp lệ.", "errorValidationInvalidPassword": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u hợp lệ.", "errorValidationRequiredField": "<PERSON>rư<PERSON><PERSON> này là b<PERSON> bu<PERSON>.", "errorValidationTooShort": "<PERSON><PERSON> liệu nhập quá <PERSON>n.", "errorValidationTooLong": "<PERSON><PERSON> liệu nhập quá dài.", "errorValidationInvalidFormat": "<PERSON><PERSON><PERSON> dạng kh<PERSON>ng hợp l<PERSON>.", "errorValidationServer": "<PERSON><PERSON><PERSON> thực thất bại.", "errorValidationUnknown": "<PERSON><PERSON> xảy ra lỗi xác thực.", "networkConnected": "<PERSON><PERSON> kết nối", "networkDisconnected": "<PERSON><PERSON><PERSON> kết n<PERSON>i mạng", "networkUnstable": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> k<PERSON>ông <PERSON>n đ<PERSON>nh", "networkChecking": "<PERSON><PERSON> kiểm tra kết nối...", "networkReconnectedSuccess": "<PERSON><PERSON> kết nối lại thành công", "networkUnstableWarning": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> k<PERSON>ông <PERSON>n đ<PERSON>nh", "networkUnstableDescription": "<PERSON><PERSON>ng không ổn định - <PERSON><PERSON>t số tính năng có thể bị ảnh hưởng", "networkCheckingConnection": "<PERSON><PERSON> kiểm tra kết nối...", "networkDisconnectedDescription": "<PERSON><PERSON> lòng kiểm tra kết nối internet và thử lại", "networkUnstableConnectionDescription": "Kết nối mạng không ổn định. Một số tính năng có thể bị ảnh hưởng", "networkCheckingConnectionDescription": "<PERSON><PERSON> kiểm tra trạng thái kết nối...", "networkRetry": "<PERSON><PERSON><PERSON> lại", "networkRetryChecking": "<PERSON><PERSON> kiểm tra...", "networkContinue": "<PERSON><PERSON><PERSON><PERSON>", "networkCheck": "<PERSON><PERSON><PERSON> tra", "createNewLoan": "<PERSON><PERSON><PERSON>n vay mới", "loanStep1Title": "<PERSON><PERSON> cấp gi<PERSON>y tờ tùy thân", "loanStep2Title": "<PERSON><PERSON><PERSON>n thông tin người vay ch<PERSON>h", "loanStep3Title": "<PERSON><PERSON> cấp gi<PERSON>y tờ tùy thân (người đồng vay)", "loanStep4Title": "<PERSON><PERSON><PERSON>n thông tin người đồng vay", "loanStep5Title": "<PERSON><PERSON> cấp đề nghị và phương án vay vốn", "loanStep6Title": "<PERSON><PERSON> cấp thông tin tài ch<PERSON>h", "loanStep7Title": "<PERSON><PERSON> cấp thông tin tài sản bảo đảm", "loanStep8Title": "<PERSON> tiết thông tin tài sản bảo đảm", "loanStep9Title": "<PERSON><PERSON> cấp danh mục chứng từ", "loanStep10Title": "<PERSON><PERSON><PERSON> n<PERSON>n thông tin khoản vay", "loanStep11Title": "Khởi tạo kho<PERSON>n vay thành công", "inDevelopment": "<PERSON><PERSON> ph<PERSON>t triển...", "step": "Bước", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "submitting_login": "<PERSON><PERSON> đăng nhập...", "submitting_register": "<PERSON><PERSON> đăng ký...", "submitting_save": "<PERSON><PERSON> l<PERSON>...", "submitting_delete": "Đang xóa...", "submitting_update": "<PERSON><PERSON> cập nhật...", "submitting_form": "<PERSON><PERSON> gửi biểu mẫu...", "cvt_policy_title": "Chính sách CTV", "captureFrontSide": "Chụp mặt trước", "captureBackSide": "Chụp mặt sau", "validationEmailRequired": "<PERSON><PERSON> lòng nhập email", "validationEmailInvalid": "<PERSON><PERSON> h<PERSON> l<PERSON>", "validationPhoneRequired": "<PERSON><PERSON> lòng nhập số điện thoại", "validationPhoneStartWithZero": "Số điện tho<PERSON>i phải bắt đầu bằng số 0", "validationPhoneLength": "Số điện thoại phải có 10 chữ số", "validationPhoneInvalid": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ", "validationIdNumberRequired": "<PERSON><PERSON> lòng nhập số giấy tờ tùy thân", "validationIdNumberLength": "Số giấy tờ tùy thân phải có 12 chữ số", "validationIdNumberNumeric": "Số giấy tờ tùy thân chỉ được chứa chữ số", "validationFullNameRequired": "<PERSON><PERSON> lòng nhập họ tên", "validationFullNameMinLength": "<PERSON><PERSON> tên ph<PERSON>i có ít nhất 2 ký tự", "validationFullNameMaxLength": "<PERSON><PERSON> tên không đư<PERSON><PERSON> quá 100 ký tự", "validationFullNameCharacters": "<PERSON>ọ tên chỉ được chứa chữ cái và dấu cách", "validationAddressRequired": "<PERSON><PERSON> lòng nhập địa chỉ", "validationAddressMinLength": "Địa chỉ phải có ít nhất 10 ký tự", "validationAddressMaxLength": "Địa chỉ không được quá 200 ký tự", "validationAddressCharacters": "Địa chỉ không được chứa kí tự đặc biệt", "validationRequired": "<PERSON><PERSON> lòng nhập {fieldName}", "validationMinLength": "{fieldName} ph<PERSON>i có ít nhất {minLength} ký tự", "validationMaxLength": "{fieldName} kh<PERSON><PERSON> đ<PERSON><PERSON> quá {maxLength} ký tự", "validationNumericOnly": "{fieldName} chỉ đ<PERSON><PERSON><PERSON> chứa chữ số", "validationPasswordRequired": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "validationPasswordMinLength": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "validationPasswordMaxLength": "<PERSON><PERSON><PERSON> khẩu không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "validationPasswordStrength": "<PERSON><PERSON><PERSON> kh<PERSON>u ph<PERSON>i chứa chữ hoa, chữ thư<PERSON>ng và số", "validationConfirmPasswordRequired": "<PERSON><PERSON> lòng nhập lại mật khẩu", "validationConfirmPasswordMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u xác nhận không khớp", "validationDateOfBirthRequired": "<PERSON><PERSON> lòng chọn ngà<PERSON> sinh", "validationDateOfBirthAge": "Bạn phải đủ 18 tuổi", "validationDateOfBirthInvalid": "<PERSON><PERSON><PERSON> sinh không h<PERSON>p lệ", "validationReferrerCodeMinLength": "Mã giới thiệu phải có ít nhất 3 ký tự", "validationReferrerCodeMaxLength": "Mã giới thiệu không được quá 20 ký tự", "validationReferrerCodeCharacters": "<PERSON>ã giới thiệu chỉ được chứa chữ cái, số và dấu gạch ngang", "validationAmountRequired": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "validationAmountFormat": "<PERSON>ố tiền chỉ được chứa chữ số và dấu phẩy", "validationAmountPositive": "Số tiền phải lớn hơn 0", "validationAmountTooLarge": "Số tiền quá lớn", "validationUrlRequired": "<PERSON><PERSON> lòng nhập URL", "validationUrlInvalid": "URL không hợp lệ", "validationOtpRequired": "<PERSON><PERSON> lòng nh<PERSON>p mã OTP", "validationOtpLength": "Mã OTP phải có {length} chữ số", "validationOtpNumeric": "Mã OTP chỉ được chứa chữ số", "validationBankAccountRequired": "<PERSON><PERSON> lòng nhập số tài khoản ngân hàng", "validationBankAccountLength": "Số tài k<PERSON>n phải có từ 8-20 chữ số", "validationBankAccountNumeric": "S<PERSON> tài k<PERSON>n chỉ được chứa chữ số", "validationTaxCodeRequired": "<PERSON><PERSON> lòng nhập mã số thuế", "validationTaxCodeLength": "Mã số thuế phải có 10 hoặc 13 chữ số", "validationTaxCodeNumeric": "<PERSON>ã số thuế chỉ được chứa chữ số", "validationIssueDateRequired": "<PERSON><PERSON> lòng chọn ng<PERSON>y cấp", "validationExpiryDateRequired": "<PERSON><PERSON> lòng chọn ngày hết hạn", "validationBirthDateRequired": "<PERSON><PERSON> lòng chọn ngà<PERSON> sinh", "step1_identity_document_description": "<PERSON><PERSON> lòng chọn loại giấy tờ tùy thân của người vay để xác thực thông tin", "document_type_label": "<PERSON><PERSON><PERSON> gi<PERSON>y tờ", "document_front_side_label": "Mặt trước gi<PERSON>y tờ", "document_back_side_label": "Mặt sau giấy tờ", "success_ctv_message": "Đăng ký CTV thành công", "account": "<PERSON><PERSON><PERSON>", "dvkd_address": "Địa chỉ ĐVKD", "profile": "Cá nhân", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "logout_title": "<PERSON><PERSON><PERSON> xu<PERSON>", "logout_message": "Bạn có chắc chắn muốn đăng xuất?", "logout_confirm": "<PERSON><PERSON><PERSON> xu<PERSON>", "logout_cancel": "<PERSON><PERSON><PERSON>", "logout_error": "<PERSON><PERSON> xảy ra lỗi khi đăng xuất", "session_expired_title": "<PERSON><PERSON><PERSON> đ<PERSON> nh<PERSON>p hết hạn", "session_expired_message": "<PERSON><PERSON><PERSON> đăng nhập của bạn đã hết hạn. <PERSON><PERSON> lòng đăng nhập lại để tiếp tục sử dụng ứng dụng.", "session_expired_button": "<PERSON><PERSON><PERSON> lại", "ctv_referral_title": "G<PERSON><PERSON>i thiệu CTV", "info_from_id_documents": "Thông tin từ giấy tờ tùy thân", "additional_information": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung", "registration_branch": "<PERSON> nh<PERSON>h đăng ký", "referred_person_email": "<PERSON><PERSON> ng<PERSON><PERSON>i đ<PERSON><PERSON> giớ<PERSON> thi<PERSON> (t<PERSON><PERSON>)", "collaborator": "<PERSON><PERSON><PERSON> tác viên", "province_city": "Tỉnh/Thành phố", "select_province_city": "Chọn tỉnh/thành phố", "business_unit": "ĐVKD", "business_unit_registration": "ĐVKD đăng ký", "no_province_data": "<PERSON><PERSON><PERSON>ng có dữ liệu tỉnh/thành", "loading_branches": "<PERSON><PERSON> tải chi nh<PERSON>h...", "no_branches_for_province": "<PERSON><PERSON><PERSON><PERSON> có chi nhánh cho tỉnh đã chọn", "try_again": "<PERSON><PERSON><PERSON> lại", "referral_code_if_any": "<PERSON><PERSON> người giới thi<PERSON> (nếu c<PERSON>)", "enter_referral_code": "<PERSON><PERSON><PERSON><PERSON> mã giới thiệu", "referral_code": "<PERSON>ã giới thiệu", "searching_referrer": "<PERSON><PERSON> tìm kiếm người giới thiệu...", "referrer": "<PERSON><PERSON><PERSON><PERSON> giới thiệu", "id_document_number": "Số giấy tờ tuỳ thân", "email_optional": "<PERSON><PERSON> (n<PERSON>u có)", "name_seller": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i giới thiệu", "code_seller": "<PERSON>ã người giới thiệu", "qrScanTitle": "Quét mã QR", "skip": "Bỏ qua", "error": "Lỗi", "retry": "<PERSON><PERSON><PERSON> lại", "camera_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy camera trên thiết bị", "camera_init_error": "<PERSON><PERSON><PERSON><PERSON> thể khởi tạo camera", "camera_permission_error": "Lỗi quyền truy cập camera", "cannot_decode_image": "<PERSON><PERSON><PERSON><PERSON> thể giải mã <PERSON>nh", "qr_scan_invalid_data": "<PERSON><PERSON>ông thể đọc thông tin từ mã QR. <PERSON><PERSON> lòng thử lại hoặc chọn ảnh từ thư viện.", "has_co_borrower": "<PERSON><PERSON>ời đồng vay", "identity_document_info": "Thông tin nhận dạng giấy tờ", "full_name": "Họ và tên", "document_number": "Số gi<PERSON>y tờ", "issue_date": "<PERSON><PERSON><PERSON> c<PERSON>", "expiry_date": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "issue_place": "<PERSON><PERSON><PERSON> c<PERSON>p", "birth_date": "<PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "permanent_address": "Địa chỉ thường trú", "personal_info": "Thông tin cá nhân", "marital_status": "Tình trạng hôn nhân", "phone_number": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "same_as_permanent_address": "Địa chỉ hiện tại trùng với địa chỉ thường trú", "district": "Quận/Huyện", "ward": "Phường/Xã", "specific_address": "Địa chỉ cụ thể", "no_ward_data": "<PERSON><PERSON><PERSON>ng có phường/xã cho tỉnh đã chọn.", "document_info_label": "Thông tin giấy tờ", "capturePassportGuidanceTitle": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> tờ", "previewPassportGuidanceTitle": "<PERSON><PERSON><PERSON> tra <PERSON>nh hộ chiếu", "passportGuidanceSubtitle": "<PERSON><PERSON> lòng đảm b<PERSON>o <PERSON>nh <PERSON> n<PERSON>, không bị mờ hoặc che khuất thông tin", "loan_purpose": "<PERSON><PERSON><PERSON> đ<PERSON>ch sử dụng vốn", "loan_term": "<PERSON><PERSON><PERSON><PERSON> hạn vay", "loading_loan_purpose": "<PERSON><PERSON> tải mục đích sử dụng vốn...", "no_loan_purpose_data": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu mục đích sử dụng vốn", "loading_loan_term": "<PERSON><PERSON> tải thời hạn vay...", "no_loan_term_data": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu thời hạn vay", "loan_amount": "<PERSON><PERSON> tiền đề nghị vay", "total_need": "<PERSON><PERSON><PERSON> nhu cầu", "own_capital": "<PERSON><PERSON><PERSON> tự có", "branch_code": "CN/PGD", "loan_method": "<PERSON><PERSON><PERSON><PERSON> thức vay", "custom_purpose_name": "<PERSON><PERSON><PERSON> m<PERSON>", "loan_type": "<PERSON><PERSON><PERSON> thức vay vốn", "loan_plan": "Phương án vay vốn", "repayment_method": "<PERSON><PERSON><PERSON> thức tr<PERSON> nợ", "disbursement_method": "<PERSON><PERSON><PERSON><PERSON> thức g<PERSON><PERSON> ngân", "recipient_account_number": "<PERSON><PERSON> tài k<PERSON>n nhận tiền", "daily_repayment": "<PERSON><PERSON>ả góp nợ gốc và lãi tiền vay hàng ngày", "collateral_yes": "Có TSĐB", "collateral_no": "Không có TSĐB", "installment_loan": "<PERSON>ay trả góp", "cash": "Tiền mặt", "transfer": "<PERSON><PERSON><PERSON><PERSON>", "loading": "<PERSON><PERSON> tả<PERSON>...", "loading_payment_accounts": "<PERSON><PERSON> tải danh s<PERSON>ch tài <PERSON>...", "no_payment_account_data": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài k<PERSON>n thanh toán nào cho số giấy tờ này.", "validationPassportNumberAlphanumeric": "<PERSON><PERSON> hộ chiếu chỉ được chứa chữ và số, không chứa ký tự đặc biệt", "step3_identity_document_description": "<PERSON><PERSON> lòng chọn loại giấy tờ tùy thân của người vay để xác thực thông tin", "validationOwnCapitalRequired": "<PERSON><PERSON> lòng nhập vốn tự có", "validationOwnCapitalInvalid": "Vốn tự có phải là số hợp lệ", "validationOwnCapitalTooLarge": "Vốn tự có không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá *************", "validationLoanAmountRequired": "<PERSON><PERSON> lòng nhập số tiền đề nghị vay", "validationLoanAmountTooSmall": "S<PERSON> tiền đề nghị vay tối thiểu là 1.000.000", "validationLoanAmountTooLarge": "<PERSON><PERSON> tiền đề nghị vay không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá *************", "validationIssuePlaceRequired": "<PERSON><PERSON> lòng nh<PERSON>p n<PERSON> cấp", "validationIssuePlaceMaxLength": "<PERSON><PERSON><PERSON> cấp không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "validationPassportNumberRequired": "<PERSON><PERSON> lòng nh<PERSON>p số hộ chiếu", "validationPassportNumberMaxLength": "<PERSON><PERSON> hộ chiếu không đ<PERSON><PERSON><PERSON> quá 50 ký tự", "validationPassportNumberCharacters": "<PERSON><PERSON> hộ chiếu chỉ được chứa chữ và số", "income_source": "<PERSON><PERSON><PERSON><PERSON> thu", "daily_revenue": "<PERSON><PERSON><PERSON> số bán bình quân/ngày", "daily_income": "<PERSON><PERSON> nh<PERSON>p bình quân/ngày", "financial_info_title": "<PERSON><PERSON><PERSON> hình tài ch<PERSON>h", "business_location_title": "<PERSON><PERSON><PERSON> điểm sản xuất - <PERSON>h do<PERSON>h"}