import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sales_app/di/injection.dart';
import 'package:sales_app/core/theme/app_theme.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/domain/services/environment_service.dart';
import 'package:sales_app/core/config/app_config.dart';

// Router imports (Clean Architecture compliant)
import 'presentation/router/app_router.dart';

import 'presentation/widgets/widgets.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Set default flavor if not already set (when running main.dart directly)
  AppConfig.appFlavor ??= Flavor.development;
  
  await configureDependencies();

  // Initialize EnvironmentService to load saved environment
  final environmentService = getIt<EnvironmentService>();
  await environmentService.initialize();

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get router instance from provider
    final router = ref.watch(routerProvider);

    // Design size cho iPhone 12 Pro
    return KeyboardDismisser(
      child: ScreenUtilInit(
        designSize: const Size(390, 844),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return MaterialApp.router(
            title: AppConfig.title,
            debugShowCheckedModeBanner: AppConfig.enableDebugMode,
            theme: AppTheme.lightTheme,
            routerConfig: router,
            builder: AppBuilder.build,
            localizationsDelegates: const [
              S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: S.delegate.supportedLocales,
          );
        },
      ),
    );
  }
}