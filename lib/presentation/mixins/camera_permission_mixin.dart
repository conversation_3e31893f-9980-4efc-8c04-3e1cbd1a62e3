import 'package:permission_handler/permission_handler.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';
// Controllers barrel export
import 'package:sales_app/presentation/controllers/controllers.dart';
// Widgets barrel export
import 'package:sales_app/presentation/widgets/widgets.dart';

/// Mixin để xử lý camera permission một cách chung nhất
/// C<PERSON> thể được sử dụng trong các ConsumerStatefulWidget cần camera permission
/// 
/// **Vị trí**: Presentation Layer - vì phụ thuộc vào UI và state management
/// **Yêu cầu**: Widget phải extend ConsumerStatefulWidget và State phải extend ConsumerState
mixin CameraPermissionMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  
  /// Kiểm tra và request camera permission với retry logic
  /// 
  /// [maxRetries]: Số lần thử lại tối đa (mặc định: 1)
  /// [showDialogOnPermanentDenial]: <PERSON><PERSON> hiển thị dialog khi bị từ chối vĩnh viễn không
  /// [showDialogOnTemporaryDenial]: <PERSON><PERSON> hiển thị dialog khi bị từ chối tạm thời không
  /// [onGranted]: Callback khi được cấp quyền
  /// [onDenied]: Callback khi bị từ chối
  /// [onPermanentlyDenied]: Callback khi bị từ chối vĩnh viễn
  /// [onError]: Callback khi có lỗi
  /// [onBack]: Callback khi user muốn back về màn hình trước đó
  Future<CameraPermissionResult> requestCameraPermission({
    int maxRetries = 1,
    bool showDialogOnPermanentDenial = true,
    bool showDialogOnTemporaryDenial = true,
    VoidCallback? onGranted,
    VoidCallback? onDenied,
    VoidCallback? onPermanentlyDenied,
    Function(String)? onError,
    VoidCallback? onBack,
  }) async {
    try {
      AppLogger.info('CameraPermissionMixin: Requesting camera permission');
      
      // Lần 1: request quyền
      var permissionResult = await _getPermissionController()
          .checkAndRequestPermission(PermissionType.camera);
      
      // Xử lý kết quả lần đầu
      final firstResult = _processPermissionResult(permissionResult);
      
      // Nếu đã được cấp quyền ngay lần đầu
      if (firstResult.isGranted) {
        AppLogger.info('CameraPermissionMixin: Camera permission granted on first try');
        onGranted?.call();
        return firstResult;
      }
      
      // Nếu bị từ chối vĩnh viễn ngay lần đầu
      if (firstResult.isPermanentlyDenied) {
        AppLogger.warning('CameraPermissionMixin: Camera permission permanently denied on first try');
        if (showDialogOnPermanentDenial) {
          _showPermissionDeniedDialog(true, onBack: onBack);
        }
        onPermanentlyDenied?.call();
        return firstResult;
      }
      
      // Nếu bị từ chối tạm thời và có thể retry
      if (firstResult.isDenied && maxRetries > 0) {
        AppLogger.info('CameraPermissionMixin: Camera permission denied temporarily, retrying...');
        
        // Thử request lại
        for (int i = 0; i < maxRetries; i++) {
          AppLogger.info('CameraPermissionMixin: Retry attempt ${i + 1}/$maxRetries');
          
          permissionResult = await _getPermissionController()
              .checkAndRequestPermission(PermissionType.camera);
          
          final retryResult = _processPermissionResult(permissionResult);
          
          // Nếu được cấp quyền sau retry
          if (retryResult.isGranted) {
            AppLogger.info('CameraPermissionMixin: Camera permission granted after retry');
            onGranted?.call();
            return retryResult;
          }
          
          // Nếu bị từ chối vĩnh viễn sau retry
          if (retryResult.isPermanentlyDenied) {
            AppLogger.warning('CameraPermissionMixin: Camera permission permanently denied after retry');
            if (showDialogOnPermanentDenial) {
              _showPermissionDeniedDialog(true, onBack: onBack);
            }
            onPermanentlyDenied?.call();
            return retryResult;
          }
          
          // Nếu vẫn bị từ chối tạm thời và còn retry
          if (retryResult.isDenied && i < maxRetries - 1) {
            AppLogger.info('CameraPermissionMixin: Still denied, continuing retry...');
            continue;
          }
          
          // Hết retry hoặc bị từ chối
          AppLogger.warning('CameraPermissionMixin: Camera permission denied after all retries');
          if (showDialogOnTemporaryDenial) {
            _showPermissionDeniedDialog(false, onBack: onBack);
          }
          onDenied?.call();
          return retryResult;
        }
      }
      
      // Xử lý trường hợp bị từ chối tạm thời sau lần đầu
      if (firstResult.isDenied) {
        AppLogger.warning('CameraPermissionMixin: Camera permission denied after first try');
        if (showDialogOnTemporaryDenial) {
          _showPermissionDeniedDialog(false, onBack: onBack);
        }
        onDenied?.call();
        return firstResult;
      }
      
      // Trường hợp khác
      AppLogger.warning('CameraPermissionMixin: Unexpected permission result');
      onDenied?.call();
      return firstResult;
      
    } catch (e) {
      AppLogger.error('CameraPermissionMixin: Error requesting camera permission', error: e);
      final errorMessage = 'Lỗi quyền truy cập camera: ${e.toString()}';
      onError?.call(errorMessage);
      return CameraPermissionResult.error(errorMessage);
    }
  }
  
  /// Kiểm tra camera permission mà không request
  Future<CameraPermissionResult> checkCameraPermission() async {
    try {
      AppLogger.info('CameraPermissionMixin: Checking camera permission');
      
      final permissionResult = await _getPermissionController()
          .checkAndRequestPermission(PermissionType.camera);
      
      return _processPermissionResult(permissionResult);
    } catch (e) {
      AppLogger.error('CameraPermissionMixin: Error checking camera permission', error: e);
      return CameraPermissionResult.error('Lỗi kiểm tra quyền truy cập camera: ${e.toString()}');
    }
  }
  
  /// Xử lý kết quả permission từ controller
  CameraPermissionResult _processPermissionResult(PermissionFeatureResult permissionResult) {
    return permissionResult.maybeWhen(
      granted: () => const CameraPermissionResult.granted(),
      denied: (permissions, feature) {
        final status = permissions[PermissionType.camera];
        if (PermissionUtils.isPermanentlyDenied(status!)) {
          return CameraPermissionResult.permanentlyDenied(status);
        } else {
          return CameraPermissionResult.denied(status);
        }
      },
      permanentlyDenied: (permissions, feature) {
        final status = permissions[PermissionType.camera];
        return CameraPermissionResult.permanentlyDenied(status!);
      },
      error: (message, feature) => CameraPermissionResult.error(message),
      orElse: () => const CameraPermissionResult.denied(PermissionStatus.denied),
    );
  }
  
  /// Hiển thị dialog khi permission bị từ chối
  void _showPermissionDeniedDialog(bool permanentlyDenied, {VoidCallback? onBack}) {
    if (!mounted) return;
    
    final message = permanentlyDenied
        ? 'Quyền truy cập camera đã bị từ chối vĩnh viễn. Vui lòng vào cài đặt để cấp lại quyền.'
        : 'Quyền truy cập camera đã bị từ chối. Vui lòng thử lại hoặc vào cài đặt để cấp quyền.';
    
    CommonDialog.showConfirmDialogWithResult(
      context: context,
      title: 'Yêu cầu quyền truy cập',
      message: message,
      confirmText: 'Mở cài đặt',
      cancelText: 'Đóng',
    ).then((goToSetting) {
      if (goToSetting) {
        _getPermissionController().openSystemAppSettings();
        // Sau khi mở settings, back về màn hình trước đó
        onBack?.call();
      } else {
        // User nhấn "Bỏ qua", back về màn hình trước đó
        onBack?.call();
      }
    });
  }
  
  /// Lấy permission controller từ Riverpod thông qua ref
  PermissionController _getPermissionController() {
    return ref.read(permissionControllerProvider.notifier);
  }
}

/// Kết quả của việc request camera permission
class CameraPermissionResult {
  final bool isGranted;
  final bool isDenied;
  final bool isPermanentlyDenied;
  final bool isError;
  final PermissionStatus? status;
  final String? errorMessage;
  
  const CameraPermissionResult._({
    required this.isGranted,
    required this.isDenied,
    required this.isPermanentlyDenied,
    required this.isError,
    this.status,
    this.errorMessage,
  });
  
  /// Được cấp quyền
  const CameraPermissionResult.granted() : this._(
    isGranted: true,
    isDenied: false,
    isPermanentlyDenied: false,
    isError: false,
  );
  
  /// Bị từ chối tạm thời
  const CameraPermissionResult.denied(PermissionStatus status) : this._(
    isGranted: false,
    isDenied: true,
    isPermanentlyDenied: false,
    isError: false,
    status: status,
  );
  
  /// Bị từ chối vĩnh viễn
  const CameraPermissionResult.permanentlyDenied(PermissionStatus status) : this._(
    isGranted: false,
    isDenied: false,
    isPermanentlyDenied: true,
    isError: false,
    status: status,
  );
  
  /// Có lỗi
  const CameraPermissionResult.error(String message) : this._(
    isGranted: false,
    isDenied: false,
    isPermanentlyDenied: false,
    isError: true,
    errorMessage: message,
  );
  
  @override
  String toString() {
    if (isGranted) return 'CameraPermissionResult.granted';
    if (isDenied) return 'CameraPermissionResult.denied($status)';
    if (isPermanentlyDenied) return 'CameraPermissionResult.permanentlyDenied($status)';
    if (isError) return 'CameraPermissionResult.error($errorMessage)';
    return 'CameraPermissionResult.unknown';
  }
} 