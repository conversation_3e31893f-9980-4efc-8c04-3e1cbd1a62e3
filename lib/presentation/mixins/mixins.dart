/// Presentation Mixins Barrel Export
/// Centralized access to all mixin classes in the presentation layer
/// 
/// This file provides a single import point for all mixin classes used in presentation layer.
/// These mixins are UI-dependent and should only be used in presentation layer.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/mixins/mixins.dart';
/// 
/// // Use camera permission mixin:
/// class MyWidget extends StatefulWidget {
///   @override
///   State<MyWidget> createState() => _MyWidgetState();
/// }
/// 
/// class _MyWidgetState extends State<MyWidget> with CameraPermissionMixin {
///   @override
///   Widget build(BuildContext context) {
///     return ElevatedButton(
///       onPressed: () async {
///         final result = await requestCameraPermission();
///         if (result.isGranted) {
///           // Camera permission granted
///         }
///       },
///       child: const Text('Request Camera Permission'),
///     );
///   }
/// }
/// ```
/// 
/// ## Mixin Organization:
/// - **camera_permission_mixin.dart**: Camera permission handling with UI integration
/// - **gallery_permission_mixin.dart**: Gallery permission handling (future)
/// - **microphone_permission_mixin.dart**: Microphone permission handling (future)
library presentation_mixins;

export 'camera_permission_mixin.dart'; 