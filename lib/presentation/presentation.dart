/// Presentation Layer Master Barrel Export
/// Single import point for all presentation layer components
/// 
/// This file provides centralized access to all presentation layer components:
/// - Controllers (Riverpod state management)
/// - Screens (UI screens with co-located widgets)
/// - Widgets (reusable UI components)
/// - Mixins (UI-dependent mixins)
/// - Router (navigation system)
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/presentation.dart';
/// 
/// // Access any presentation component:
/// 
/// // Controllers
/// final authController = ref.watch(authControllerProvider);
/// 
/// // Screens
/// class MyScreen extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return Scaffold(
///       body: Center(child: Text('My Screen')),
///     );
///   }
/// }
/// 
/// // Widgets
/// CommonButton(text: 'Submit', onPressed: () {})
/// 
/// // Mixins
/// class MyWidget extends StatefulWidget {
///   @override
///   State<MyWidget> createState() => _MyWidgetState();
/// }
/// 
/// class _MyWidgetState extends State<MyWidget> with CameraPermissionMixin {
///   // Use camera permission mixin
/// }
/// ```
/// 
/// ## Presentation Layer Organization:
/// - **controllers/**: Riverpod controllers for state management
/// - **screens/**: UI screens with co-located widgets (Hybrid Structure)
/// - **widgets/**: Reusable UI components (common, shared, loading, network)
/// - **mixins/**: UI-dependent mixins for common functionality
/// - **router/**: Navigation system with GoRouter
library presentation;

// Controllers - Riverpod state management
export 'controllers/controllers.dart';

// Screens - UI screens with co-located widgets
export 'screens/screens.dart';

// Widgets - reusable UI components
export 'widgets/widgets.dart';

// Mixins - UI-dependent mixins
export 'mixins/mixins.dart';

// Router - navigation system
export 'router/router.dart'; 