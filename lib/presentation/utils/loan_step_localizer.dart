import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';

/// Utility class to handle localization for loan steps
/// This belongs to presentation layer and handles UI concerns
class LoanStepLocalizer {
  /// Get localized title for a loan step
  static String getStepTitle(BuildContext context, LoanStep step) {
    final s = S.of(context);
    switch (step) {
      case LoanStep.identityDocument:
        return s.loanStep1Title;
      case LoanStep.borrowerInfo:
        return s.loanStep2Title;
      case LoanStep.coBorrowerDocument:
        return s.loanStep3Title;
      case LoanStep.coBorrowerInfo:
        return s.loanStep4Title;
      case LoanStep.loanRequest:
        return s.loanStep5Title;
      case LoanStep.financialInfo:
        return s.loanStep6Title;
      case LoanStep.collateralInfo:
        return s.loanStep7Title;
      case LoanStep.collateralDetail:
        return s.loanStep8Title;
      case LoanStep.documentList:
        return s.loanStep9Title;
      case LoanStep.loanConfirmation:
        return s.loanStep10Title;
      case LoanStep.success:
        return s.loanStep11Title;
    }
  }

  /// Get localized title by step number (for backward compatibility)
  static String getStepTitleByNumber(BuildContext context, int stepNumber) {
    final step = LoanStep.fromNumber(stepNumber);
    return getStepTitle(context, step);
  }
}
