import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';

// Generated localization
import 'package:sales_app/generated/l10n.dart';

// Common widgets
import 'package:sales_app/presentation/widgets/common/common.dart';

/// Helper class for QR parsing with error handling
class QRParseHelper {
  /// Parse QR data and show error dialog if parsing fails
  /// 
  /// Example usage:
  /// ```dart
  /// final cccdInfo = QRParseHelper.parseWithErrorDialog<CCCDInfoEntity>(
  ///   context: context,
  ///   qrData: qrData,
  ///   parseFunction: QRParser.parseCccdInfoFromQr,
  ///   errorMessage: 'Không thể đọc thông tin từ mã QR. Vui lòng thử lại.',
  /// );
  /// 
  /// if (cccdInfo != null) {
  ///   // Handle success
  /// }
  /// ```
  static T? parseWithErrorDialog<T>({
    required BuildContext context,
    required String qrData,
    required T? Function(String) parseFunction,
    String? errorMessage,
    String? dialogTitle,
    String? dialogContent,
    String? dialogButtonText,
  }) {
    try {
      final result = parseFunction(qrData);
      
      if (result == null) {
        _showErrorDialog(
          context: context,
          title: dialogTitle,
          content: dialogContent ?? errorMessage,
          buttonText: dialogButtonText,
        );
        return null;
      }
      
      return result;
    } catch (e) {
      AppLogger.error('QR Parse Error', error: e, data: {
        'qrData': qrData,
        'parseFunction': parseFunction.toString(),
      });
      
      _showErrorDialog(
        context: context,
        title: dialogTitle,
        content: dialogContent ?? errorMessage,
        buttonText: dialogButtonText,
      );
      return null;
    }
  }

  /// Parse QR data and show error dialog with custom error handling
  /// 
  /// Example usage:
  /// ```dart
  /// final cccdInfo = QRParseHelper.parseWithCustomError<T>(
  ///   context: context,
  ///   qrData: qrData,
  ///   parseFunction: QRParser.parseCccdInfoFromQr,
  ///   onError: (error) {
  ///     // Custom error handling
  ///     ScaffoldMessenger.of(context).showSnackBar(
  ///       SnackBar(content: Text('Custom error: $error')),
  ///     );
  ///   },
  /// );
  /// ```
  static T? parseWithCustomError<T>({
    required BuildContext context,
    required String qrData,
    required T? Function(String) parseFunction,
    required void Function(String error) onError,
  }) {
    try {
      final result = parseFunction(qrData);
      
      if (result == null) {
        final s = S.of(context);
        onError(s.qr_scan_invalid_data);
        return null;
      }
      
      return result;
    } catch (e) {
      AppLogger.error('QR Parse Error', error: e, data: {
        'qrData': qrData,
        'parseFunction': parseFunction.toString(),
      });
      
      final s = S.of(context);
      onError(s.qr_scan_invalid_data);
      return null;
    }
  }

  /// Show error dialog using CommonDialog
  static void _showErrorDialog({
    required BuildContext context,
    String? title,
    String? content,
    String? buttonText,
  }) {
    final s = S.of(context);
    
    CommonDialog.showErrorDialog(
      context: context,
      title: title,
      message: content ?? s.qr_scan_invalid_data,
      buttonText: buttonText,
    );
  }

  /// Parse QR data and return result with error message
  /// Useful when you want to handle error yourself
  /// 
  /// Returns: (result, errorMessage)
  /// If result is null, errorMessage will contain the error
  static (T?, String?) parseWithError<T>({
    required String qrData,
    required T? Function(String) parseFunction,
  }) {
    try {
      final result = parseFunction(qrData);
      
      if (result == null) {
        return (null, 'Không thể đọc thông tin từ mã QR. Vui lòng thử lại.');
      }
      
      return (result, null);
    } catch (e) {
      AppLogger.error('QR Parse Error', error: e, data: {
        'qrData': qrData,
        'parseFunction': parseFunction.toString(),
      });
      
      return (null, 'Có lỗi xảy ra khi xử lý mã QR. Vui lòng thử lại.');
    }
  }
} 