import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';

/// Helper class for localizing network connection status messages
/// Presentation layer utility for UI-specific localization
/// Works directly with NetworkConnectionStatus enum - simple and clean
class NetworkStateLocalizer {
  const NetworkStateLocalizer._();

  /// Get localized status message for network connection status
  static String getStatusMessage(BuildContext context, NetworkConnectionStatus status) {
    final localizations = S.of(context);

    switch (status) {
      case NetworkConnectionStatus.connected:
        return localizations.networkConnected;
      case NetworkConnectionStatus.disconnected:
        return localizations.networkDisconnected;
      case NetworkConnectionStatus.unstable:
        return localizations.networkUnstable;
      case NetworkConnectionStatus.checking:
        return localizations.networkChecking;
    }
  }

  /// Get localized description for network connection status
  static String getDescription(BuildContext context, NetworkConnectionStatus status) {
    final localizations = S.of(context);

    switch (status) {
      case NetworkConnectionStatus.connected:
        return '';
      case NetworkConnectionStatus.disconnected:
        return localizations.networkDisconnectedDescription;
      case NetworkConnectionStatus.unstable:
        return localizations.networkUnstableConnectionDescription;
      case NetworkConnectionStatus.checking:
        return localizations.networkCheckingConnectionDescription;
    }
  }

  /// Get localized retry button text based on status
  static String getRetryButtonText(BuildContext context, NetworkConnectionStatus status) {
    final localizations = S.of(context);

    return status.isChecking
        ? localizations.networkRetryChecking
        : localizations.networkRetry;
  }

  /// Get localized continue button text
  static String getContinueButtonText(BuildContext context) {
    return S.of(context).networkContinue;
  }

  /// Get localized reconnection success message
  static String getReconnectionMessage(BuildContext context) {
    return S.of(context).networkReconnectedSuccess;
  }

  /// Get localized unstable warning message
  static String getUnstableWarningMessage(BuildContext context) {
    return S.of(context).networkUnstableWarning;
  }

  /// Get localized unstable description message
  static String getUnstableDescriptionMessage(BuildContext context) {
    return S.of(context).networkUnstableDescription;
  }

  /// Get localized checking connection message
  static String getCheckingConnectionMessage(BuildContext context) {
    return S.of(context).networkCheckingConnection;
  }

  /// Get status icon (language-independent) - uses enum's built-in icon
  static String getStatusIcon(NetworkConnectionStatus status) {
    return status.statusIcon;
  }
}
