import 'package:flutter/material.dart';

// Generated localization
import 'package:sales_app/generated/l10n.dart';

/// Maps validation error keys to localized error messages
/// This class belongs to the presentation layer as it handles UI concerns (localization, context)
/// 
/// Usage:
/// ```dart
/// final errorKey = ValidationUtils.validateEmail(email);
/// final errorMessage = ValidationMessageMapper.getValidationMessage(context, errorKey);
/// ```
class ValidationMessageMapper {
  /// Get localized validation message from error key
  static String? getValidationMessage(BuildContext context, String? errorKey, {
    String? fieldName,
    int? minLength,
    int? maxLength,
    int? otpLength,
  }) {
    if (errorKey == null) return null;
    
    final localizations = S.of(context);
    
    switch (errorKey) {
      case 'validationEmailRequired':
        return localizations.validationEmailRequired;
      case 'validationEmailInvalid':
        return localizations.validationEmailInvalid;
      case 'validationPhoneRequired':
        return localizations.validationPhoneRequired;
      case 'validationPhoneStartWithZero':
        return localizations.validationPhoneStartWithZero;
      case 'validationPhoneLength':
        return localizations.validationPhoneLength;
      case 'validationPhoneInvalid':
        return localizations.validationPhoneInvalid;
      case 'validationIdNumberRequired':
        return localizations.validationIdNumberRequired;
      case 'validationIdNumberLength':
        return localizations.validationIdNumberLength;
      case 'validationIdNumberNumeric':
        return localizations.validationIdNumberNumeric;
      case 'validationFullNameRequired':
        return localizations.validationFullNameRequired;
      case 'validationFullNameMinLength':
        return localizations.validationFullNameMinLength;
      case 'validationFullNameMaxLength':
        return localizations.validationFullNameMaxLength;
      case 'validationFullNameCharacters':
        return localizations.validationFullNameCharacters;
      case 'validationAddressRequired':
        return localizations.validationAddressRequired;
      case 'validationAddressMinLength':
        return localizations.validationAddressMinLength;
      case 'validationAddressMaxLength':
        return localizations.validationAddressMaxLength;
      case 'validationRequired':
        return fieldName != null 
            ? localizations.validationRequired(fieldName) 
            : localizations.validationRequired('');
      case 'validationMinLength':
        return (fieldName != null && minLength != null) 
            ? localizations.validationMinLength(fieldName, minLength) 
            : errorKey;
      case 'validationMaxLength':
        return (fieldName != null && maxLength != null)
            ? localizations.validationMaxLength(fieldName, maxLength)
            : localizations.validationMaxLength(fieldName ?? '', maxLength ?? 0);
      case 'validationNumericOnly':
        return fieldName != null 
            ? localizations.validationNumericOnly(fieldName) 
            : errorKey;
      case 'validationPasswordRequired':
        return localizations.validationPasswordRequired;
      case 'validationPasswordMinLength':
        return localizations.validationPasswordMinLength;
      case 'validationPasswordMaxLength':
        return localizations.validationPasswordMaxLength;
      case 'validationPasswordStrength':
        return localizations.validationPasswordStrength;
      case 'validationConfirmPasswordRequired':
        return localizations.validationConfirmPasswordRequired;
      case 'validationConfirmPasswordMismatch':
        return localizations.validationConfirmPasswordMismatch;
      case 'validationDateOfBirthRequired':
        return localizations.validationDateOfBirthRequired;
      case 'validationDateOfBirthAge':
        return localizations.validationDateOfBirthAge;
      case 'validationDateOfBirthInvalid':
        return localizations.validationDateOfBirthInvalid;
      case 'validationIssueDateRequired':
        return localizations.validationIssueDateRequired;
      case 'validationExpiryDateRequired':
        return localizations.validationExpiryDateRequired;
      case 'validationBirthDateRequired':
        return localizations.validationBirthDateRequired;
      case 'validationReferrerCodeMinLength':
        return localizations.validationReferrerCodeMinLength;
      case 'validationReferrerCodeMaxLength':
        return localizations.validationReferrerCodeMaxLength;
      case 'validationReferrerCodeCharacters':
        return localizations.validationReferrerCodeCharacters;
      case 'validationAmountRequired':
        return localizations.validationAmountRequired;
      case 'validationAmountFormat':
        return localizations.validationAmountFormat;
      case 'validationAmountPositive':
        return localizations.validationAmountPositive;
      case 'validationAmountTooLarge':
        return localizations.validationAmountTooLarge;
      case 'validationUrlRequired':
        return localizations.validationUrlRequired;
      case 'validationUrlInvalid':
        return localizations.validationUrlInvalid;
      case 'validationOtpRequired':
        return localizations.validationOtpRequired;
      case 'validationOtpLength':
        return otpLength != null 
            ? localizations.validationOtpLength(otpLength) 
            : errorKey;
      case 'validationOtpNumeric':
        return localizations.validationOtpNumeric;
      case 'validationBankAccountRequired':
        return localizations.validationBankAccountRequired;
      case 'validationBankAccountLength':
        return localizations.validationBankAccountLength;
      case 'validationBankAccountNumeric':
        return localizations.validationBankAccountNumeric;
      case 'validationTaxCodeRequired':
        return localizations.validationTaxCodeRequired;
      case 'validationTaxCodeLength':
        return localizations.validationTaxCodeLength;
      case 'validationTaxCodeNumeric':
        return localizations.validationTaxCodeNumeric;
      case 'validationOwnCapitalRequired':
        return localizations.validationOwnCapitalRequired;
      case 'validationOwnCapitalInvalid':
        return localizations.validationOwnCapitalInvalid;
      case 'validationOwnCapitalTooLarge':
        return localizations.validationOwnCapitalTooLarge;
      case 'validationLoanAmountRequired':
        return localizations.validationLoanAmountRequired;
      case 'validationLoanAmountTooSmall':
        return localizations.validationLoanAmountTooSmall;
      case 'validationLoanAmountTooLarge':
        return localizations.validationLoanAmountTooLarge;
      case 'validationIssuePlaceRequired':
        return localizations.validationIssuePlaceRequired;
      case 'validationIssuePlaceMaxLength':
        return localizations.validationIssuePlaceMaxLength;
      case 'validationPassportNumberRequired':
        return localizations.validationPassportNumberRequired;
      case 'validationPassportNumberMaxLength':
        return localizations.validationPassportNumberMaxLength;
      case 'validationPassportNumberCharacters':
        return localizations.validationPassportNumberCharacters;
      default:
        return errorKey;
    }
  }
} 