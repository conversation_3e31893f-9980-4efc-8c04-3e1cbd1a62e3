/// Presentation Utils Master Barrel Export
/// Utility classes for presentation layer concerns
/// 
/// This file provides a single import point for all presentation utilities.
/// These utilities handle UI-specific concerns like localization, error mapping,
/// and validation message formatting.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/utils/utils.dart';
/// 
/// // Access any presentation utility:
/// 
/// // Error message mapping
/// final errorMessage = ErrorMessageMapper.getErrorMessage(context, failure);
/// 
/// // Validation message mapping
/// final validationMessage = ValidationMessageMapper.getValidationMessage(context, errorKey);
/// 
/// // Loan step localization
/// final stepTitle = LoanStepLocalizer.getStepTitle(context, loanStep);
/// 
/// // Network state localization
/// final statusMessage = NetworkStateLocalizer.getStatusMessage(context, status);
/// ```
/// 
/// ## Utility Categories:
/// - **Error Mapping**: Convert domain errors to user-friendly messages
/// - **Validation Mapping**: Convert validation errors to localized messages
/// - **Step Localization**: Localize loan process steps
/// - **Network Localization**: Localize network status messages
library presentation_utils;

// Error mapping utilities
export 'error_message_mapper.dart';

// Validation mapping utilities
export 'validation_message_mapper.dart';

// Step localization utilities
export 'loan_step_localizer.dart';

// Network state localization utilities
export 'network_state_localizer.dart';

// QR parsing utilities
export 'qr_parse_helper.dart'; 