/// Presentation Screens Master Barrel Export
/// Single import point for all screens in the presentation layer
/// 
/// This file provides centralized access to all screen categories:
/// - Auth screens (login, register, forgot password)
/// - Home screens (main dashboard, tabs)
/// - Identity screens (identity upload, QR scan)
/// - Document screens (document management)
/// - Image capture screens (camera, photo capture)
/// - Loan screens (loan application flow)
/// - Registration screens (CTV registration flow)
/// - Demo screens (UI demos, navigation demos)
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/screens/screens.dart';
/// 
/// // Access any screen from any category:
/// 
/// // Auth screens
/// LoginScreen()
/// 
/// // Home screens
/// HomeScreen()
/// 
/// // Identity screens
/// IdentityUploadPage()
/// QRScanScreen()
/// 
/// // Document screens
/// DocumentScreen()
/// 
/// // Image capture screens
/// ImageCaptureScreen()
/// 
/// // Loan screens
/// CreateLoanScreen()
/// 
/// // Registration screens
/// CTVRegistrationScreen()
/// 
/// // Demo screens
/// UIDemoScreen()
/// ```
/// 
/// ## Screen Organization:
/// - **auth/**: Authentication and user management screens
/// - **home/**: Main dashboard and navigation screens
/// - **identity/**: Identity verification and QR scanning screens
/// - **document/**: Document management and viewing screens
/// - **image_capture/**: Camera and photo capture screens
/// - **loan/**: Loan application and management screens
/// - **registration/**: CTV registration flow screens
/// - **demo/**: UI demos and development screens
library screens;

// Auth screens - authentication and user management
export 'auth/auth.dart';

// Home screens - main dashboard and navigation
export 'home/home.dart';

// Identity screens - identity verification and QR scanning
export 'identity/identity.dart';

// Document screens - document management and viewing
export 'document/document.dart';

// Image capture screens - camera and photo capture
export 'image_capture/image_capture.dart';

// Loan screens - loan application and management
export 'loan/loan.dart';

// Demo screens - UI demos and development
export 'demo/demo.dart'; 

// Splash screens - app startup and loading
export 'splash/splash.dart'; 