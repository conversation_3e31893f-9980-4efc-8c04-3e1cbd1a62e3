import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/core.dart';
import 'package:image/image.dart' as img;

import 'widgets/widgets.dart';

// Navigation imports (Clean Architecture compliant)
import '../../router/navigation_extensions.dart';
// Mixins barrel export
import '../../mixins/mixins.dart';
// Common widgets barrel export for CommonDialog
import '../../widgets/common/common.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';

class CaptureDocumentScreen extends ConsumerStatefulWidget {
  final String title;
  final DocumentSide side;
  final DocumentType documentType;
  final Function(File image) onImageCaptured;

  const CaptureDocumentScreen({
    super.key,
    required this.title,
    required this.side,
    required this.documentType,
    required this.onImageCaptured,
  });

  @override
  ConsumerState<CaptureDocumentScreen> createState() => _CaptureDocumentScreenState();
}

class _CaptureDocumentScreenState extends ConsumerState<CaptureDocumentScreen> 
    with WidgetsBindingObserver, CameraPermissionMixin {
  CameraController? _controller;
  bool _isCameraInitialized = false;
  bool _isCapturing = false;
  bool _isPermissionChecking = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _checkPermissionAndInitializeCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_controller == null) return;

    if (state == AppLifecycleState.inactive) {
      _controller?.dispose();
      _controller = null;
      _isCameraInitialized = false;
    } else if (state == AppLifecycleState.resumed) {
      _checkPermissionAndInitializeCamera();
    }
  }

  /// Kiểm tra permission và khởi tạo camera
  Future<void> _checkPermissionAndInitializeCamera() async {
    if (_isPermissionChecking) return;
    
    setState(() {
      _isPermissionChecking = true;
    });

    try {
      // Sử dụng CameraPermissionMixin để request permission
      // Mixin sẽ tự động hiển thị dialog khi cần thiết
      final permissionResult = await requestCameraPermission(
        maxRetries: 1,
        showDialogOnPermanentDenial: true,
        showDialogOnTemporaryDenial: true,
        onGranted: () {
          AppLogger.info('CaptureDocumentScreen: Camera permission granted');
        },
        onDenied: () {
          AppLogger.warning('CaptureDocumentScreen: Camera permission denied');
        },
        onPermanentlyDenied: () {
          AppLogger.warning('CaptureDocumentScreen: Camera permission permanently denied');
        },
        onError: (errorMessage) {
          AppLogger.error('CaptureDocumentScreen: Camera permission error: $errorMessage');
        },
        onBack: () {
          AppLogger.info('CaptureDocumentScreen: User chose to go back');
          if (mounted) {
            context.goBack();
          }
        },
      );

      if (permissionResult.isGranted) {
        AppLogger.info('CaptureDocumentScreen: Permission granted, proceeding with camera initialization');
        await _initializeCamera();
      }
    } catch (e) {
      AppLogger.error('CaptureDocumentScreen: Error during permission check', error: e);
      if (mounted) {
        _showGeneralError('${S.of(context).camera_permission_error}: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPermissionChecking = false;
        });
      }
    }
  }

  Future<void> _initializeCamera() async {
    try {
      // Dispose controller cũ nếu có
      if (_controller != null) {
        await _controller!.dispose();
        _controller = null;
        _isCameraInitialized = false;
      }

      AppLogger.info('CaptureDocumentScreen: Initializing camera');
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        AppLogger.error('CaptureDocumentScreen: No cameras available');
        if (mounted) {
          _showGeneralError(S.of(context).camera_not_found);
        }
        return;
      }

      _controller = CameraController(
        cameras.first,
        ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await _controller!.initialize();
      AppLogger.info('CaptureDocumentScreen: Camera initialized successfully');

      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
        });
      }
    } catch (e) {
      AppLogger.error('CaptureDocumentScreen: Error initializing camera', error: e);
      if (mounted) {
        _showGeneralError('${S.of(context).camera_init_error}: ${e.toString()}');
      }
    }
  }

  /// Hiển thị lỗi chung (cho permission hoặc camera)
  void _showGeneralError(String message) {
    if (!mounted) return;
    final s = S.of(context);
    CommonDialog.showConfirmDialogWithResult(
      context: context,
      message: message,
      confirmText: s.retry,
      cancelText: s.close,
    ).then((retry) {
      if (!mounted) return;
      if (retry) {
        _checkPermissionAndInitializeCamera();
      } else {
        context.goBack();
      }
    });
  }

  Future<File> cropDocumentImage({
    required File originalImage,
    required Rect overlayRectOnScreen,
    required Size previewWidgetSize,
  }) async {
    final bytes = await originalImage.readAsBytes();
    final image = img.decodeImage(bytes);
    if (image == null) throw Exception(S.current.cannot_decode_image);
    final imageWidth = image.width;
    final imageHeight = image.height;
    final previewW = previewWidgetSize.width;
    final previewH = previewWidgetSize.height;
    final scaleX = imageWidth / previewW;
    final scaleY = imageHeight / previewH;
    final cropX = (overlayRectOnScreen.left * scaleX).round();
    final cropY = (overlayRectOnScreen.top * scaleY).round();
    final cropW = (overlayRectOnScreen.width * scaleX).round();
    final cropH = (overlayRectOnScreen.height * scaleY).round();
    final safeCropX = cropX.clamp(0, imageWidth - 1);
    final safeCropY = cropY.clamp(0, imageHeight - 1);
    final safeCropW = cropW.clamp(0, imageWidth - safeCropX);
    final safeCropH = cropH.clamp(0, imageHeight - safeCropY);
    final cropped = img.copyCrop(
      image,
      x: safeCropX,
      y: safeCropY,
      width: safeCropW,
      height: safeCropH,
    );
    final croppedFile = File('${originalImage.parent.path}/cropped_${originalImage.uri.pathSegments.last}');
    await croppedFile.writeAsBytes(img.encodeJpg(cropped));
    return croppedFile;
  }

  Future<void> _captureImage() async {
    if (_controller == null || !_controller!.value.isInitialized || _isCapturing) {
      return;
    }

    try {
      setState(() {
        _isCapturing = true;
      });

      // Lấy layout khung dùng chung
      final layout = DocumentFrameLayout(context, widget.documentType);
      final cardWidth = layout.cardWidth;
      final cardHeight = layout.cardHeight;
      final frameTop = layout.frameTop;
      final previewSize = context.size ?? MediaQuery.of(context).size;
      final overlayRect = Rect.fromLTWH(
        (previewSize.width - cardWidth) / 2,
        frameTop,
        cardWidth,
        cardHeight,
      );

      final xFile = await _controller!.takePicture();
      final file = File(xFile.path);

      final croppedFile = await cropDocumentImage(
        originalImage: file,
        overlayRectOnScreen: overlayRect,
        previewWidgetSize: previewSize,
      );

      if (!mounted) return;
      context.pushPreviewDocumentWithParams(
        image: croppedFile,
        side: widget.side,
        documentType: widget.documentType,
        onAccept: widget.onImageCaptured,
      );
    } catch (e) {
      AppLogger.error('Error capturing image', error: e);
    } finally {
      if (mounted) {
        setState(() {
          _isCapturing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Sử dụng DocumentFrameLayout với documentType động
    final layout = DocumentFrameLayout(context, widget.documentType);
    final cardWidth = layout.cardWidth;
    final cardHeight = layout.cardHeight;
    final frameTop = layout.frameTop;

    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: DocumentAppBarWidget(side: widget.side, documentType: widget.documentType),
      body: Container(
        color: AppColors.black,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Camera preview - chỉ render một lần (theo tài liệu camera package)
            if (_isCameraInitialized && _controller != null)
              Positioned.fill(
                    child: CameraPreview(_controller!),
              ),

            // Document overlay widget (chung)
            DocumentOverlayWidget(
              cardWidth: cardWidth,
              cardHeight: cardHeight,
              frameTop: frameTop,
            ),

            // Guidance text widget (chung)
            DocumentGuidanceWidget.forCapture(
              frameTop: frameTop,
              cardHeight: cardHeight,
            ),

            // Capture button widget (chung)
            DocumentCaptureButtonWidget(
              onTap: _captureImage,
              isCapturing: _isCapturing,
            ),
          ],
        ),
      ),
    );
  }


}