/// Document Screens Barrel Export
/// Centralized access to all document-related screens
/// 
/// This file provides a single import point for all document screens.
/// Import this file instead of importing individual screen files.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/screens/document/document.dart';
/// 
/// // Now you can use any document screen:
/// Navigator.push(context, MaterialPageRoute(builder: (context) => CaptureDocumentScreen(...)));
/// ```
library document_screens;

// Document screens
export 'capture_document_screen.dart';
export 'preview_document_screen.dart';

// Document widgets
export 'widgets/document_frame_painter.dart'; 