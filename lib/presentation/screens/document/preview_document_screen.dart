import 'dart:io';
import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/presentation/screens/document/widgets/widgets.dart';

// Navigation imports (Clean Architecture compliant)
import '../../../generated/l10n.dart';
import '../../router/navigation_extensions.dart';

class PreviewDocumentScreen extends StatelessWidget {
  final File image;
  final DocumentSide side;
  final DocumentType documentType;
  final Function(File image) onAccept;

  const PreviewDocumentScreen({
    super.key,
    required this.image,
    required this.side,
    required this.documentType,
    required this.onAccept,
  });

  @override
  Widget build(BuildContext context) {
    final layout = DocumentFrameLayout(context, documentType);
    final cardWidth = layout.cardWidth;
    final cardHeight = layout.cardHeight;
    final frameTop = layout.frameTop;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: DocumentAppBarWidget(
        side: side,
        documentType: documentType,
        onBackPressed: () => Navigator.of(context).pop(),
      ),
      body: Container(
        color: AppColors.black,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Ảnh crop căn giữa, đúng tỷ lệ khung
            Align(
              alignment: Alignment.topCenter,
              child: Container(
                margin: EdgeInsets.only(top: frameTop),
                width: cardWidth,
                height: cardHeight,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: AppColors.black,
                ),
                clipBehavior: Clip.hardEdge,
                child: Image.file(
                  image,
                  width: cardWidth,
                  height: cardHeight,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            // Guidance text widget (chung)
            DocumentGuidanceWidget.forPreview(
              label: side == DocumentSide.back
                  ? S.current.checkBackSide
                  : S.current.checkFrontSide,
              frameTop: frameTop,
              cardHeight: cardHeight,
              documentType: documentType,
            ),
            // Action buttons widget (chung)
            DocumentActionButtonsWidget(
              onRetake: () => context.goBack(),
              onConfirm: () {
                onAccept(image);
                context.goBack();
                context.goBack();
              },
            ),
          ],
        ),
      ),
    );
  }
}
