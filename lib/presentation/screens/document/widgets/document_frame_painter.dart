import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';

enum CornerType { topLeft, topRight, bottomLeft, bottomRight }

class DocumentFramePainter extends CustomPainter {
  final double width;
  final double height;
  final double borderWidth;
  final double cornerLength;
  final double cornerRadius;

  DocumentFramePainter({
    required this.width,
    required this.height,
    this.borderWidth = 3.0,
    this.cornerLength = 32,
    this.cornerRadius = 16.0, // Bo tròn góc CCCD
  });

  @override
  void paint(Canvas canvas, Size size) {
    final documentRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: width,
      height: height,
    );
    _drawRoundedCornerFrame(canvas, documentRect);
  }

  void _drawRoundedCornerFrame(Canvas canvas, Rect rect) {
    final paint = Paint()
      ..color = AppColors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth
      ..strokeCap = StrokeCap.round;

    _drawCorner(canvas, rect.topLeft, cornerLength, cornerRadius, paint, CornerType.topLeft);
    _drawCorner(canvas, rect.topRight, cornerLength, cornerRadius, paint, CornerType.topRight);
    _drawCorner(canvas, rect.bottomLeft, cornerLength, cornerRadius, paint, CornerType.bottomLeft);
    _drawCorner(canvas, rect.bottomRight, cornerLength, cornerRadius, paint, CornerType.bottomRight);
  }

  void _drawCorner(
    Canvas canvas,
    Offset corner,
    double length,
    double radius,
    Paint paint,
    CornerType type,
  ) {
    Offset hStart, hEnd, vStart, vEnd;
    Rect arcRect;
    double startAngle;

    switch (type) {
      case CornerType.topLeft:
        // Đoạn ngang: từ (corner.dx + r, corner.dy) đến (corner.dx + length, corner.dy)
        hStart = corner.translate(radius, 0);
        hEnd = corner.translate(length, 0);
        // Đoạn dọc: từ (corner.dx, corner.dy + r) đến (corner.dx, corner.dy + length)
        vStart = corner.translate(0, radius);
        vEnd = corner.translate(0, length);
        // Cung tròn: topLeft = corner, size = 2*radius
        arcRect = Rect.fromLTWH(corner.dx, corner.dy, 2 * radius, 2 * radius);
        startAngle = 180;
        break;
      case CornerType.topRight:
        hStart = corner.translate(-radius, 0);
        hEnd = corner.translate(-length, 0);
        vStart = corner.translate(0, radius);
        vEnd = corner.translate(0, length);
        arcRect = Rect.fromLTWH(corner.dx - 2 * radius, corner.dy, 2 * radius, 2 * radius);
        startAngle = 270;
        break;
      case CornerType.bottomLeft:
        hStart = corner.translate(radius, 0);
        hEnd = corner.translate(length, 0);
        vStart = corner.translate(0, -radius);
        vEnd = corner.translate(0, -length);
        arcRect = Rect.fromLTWH(corner.dx, corner.dy - 2 * radius, 2 * radius, 2 * radius);
        startAngle = 90;
        break;
      case CornerType.bottomRight:
        hStart = corner.translate(-radius, 0);
        hEnd = corner.translate(-length, 0);
        vStart = corner.translate(0, -radius);
        vEnd = corner.translate(0, -length);
        arcRect = Rect.fromLTWH(corner.dx - 2 * radius, corner.dy - 2 * radius, 2 * radius, 2 * radius);
        startAngle = 0;
        break;
    }
    // Vẽ đường ngang
    canvas.drawLine(hStart, hEnd, paint);
    // Vẽ đường dọc
    canvas.drawLine(vStart, vEnd, paint);
    // Vẽ cung bo tròn
    canvas.drawArc(
      arcRect,
      startAngle * (3.141592653589793 / 180),
      90 * (3.141592653589793 / 180),
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(DocumentFramePainter oldDelegate) {
    return width != oldDelegate.width ||
        height != oldDelegate.height ||
        borderWidth != oldDelegate.borderWidth ||
        cornerLength != oldDelegate.cornerLength ||
        cornerRadius != oldDelegate.cornerRadius;
  }
}
