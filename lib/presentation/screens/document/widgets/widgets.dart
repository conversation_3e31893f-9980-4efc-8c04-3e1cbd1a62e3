/// Document Widgets Barrel Export
/// Centralized access to all document-related widgets
/// 
/// This file provides a single import point for all document widgets.
/// Import this file instead of importing individual widget files.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/screens/document/widgets/widgets.dart';
/// 
/// // Now you can use any document widget:
/// CustomPaint(painter: DocumentFramePainter(...))
/// ```
library document_widgets;

// Document widgets
export 'document_frame_painter.dart';
export 'document_overlay_widget.dart';
export 'document_app_bar_widget.dart';
export 'document_guidance_widget.dart';
export 'document_capture_button_widget.dart';
export 'document_action_buttons_widget.dart';

export 'document_frame_layout.dart'; // Thêm export layout dùng chung 