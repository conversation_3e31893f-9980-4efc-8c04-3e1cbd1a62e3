import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';

/// Widget chung cho capture button
/// Sử dụng cho capture screen
class DocumentCaptureButtonWidget extends StatelessWidget {
  final VoidCallback onTap;
  final bool isCapturing;

  const DocumentCaptureButtonWidget({
    super.key,
    required this.onTap,
    this.isCapturing = false,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonSize = screenWidth < 360 ? AppDimens.captureButtonSize * 0.85 : AppDimens.captureButtonSize;
    final innerSize = screenWidth < 360 ? AppDimens.captureButtonInnerSize * 0.85 : AppDimens.captureButtonInnerSize;
    final innerSizePressed = screenWidth < 360 ? AppDimens.captureButtonInnerSizePressed * 0.85 : AppDimens.captureButtonInnerSizePressed;

    return Positioned(
      left: 0,
      right: 0,
      bottom: AppDimens.containerMd,
      child: Center(
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: isCapturing ? null : onTap,
            borderRadius: BorderRadius.circular(buttonSize / 2),
            child: Container(
              width: buttonSize,
              height: buttonSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: AppDimens.borderWidthThick,
                ),
              ),
              child: Center(
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: AppDimens.animationFast),
                  width: isCapturing ? innerSizePressed : innerSize,
                  height: isCapturing ? innerSizePressed : innerSize,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
} 