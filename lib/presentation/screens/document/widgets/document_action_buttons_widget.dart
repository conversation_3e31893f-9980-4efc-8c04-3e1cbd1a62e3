import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/generated/l10n.dart';

/// Widget chung cho action buttons của preview screen
class DocumentActionButtonsWidget extends StatelessWidget {
  final VoidCallback onRetake;
  final VoidCallback onConfirm;

  const DocumentActionButtonsWidget({
    super.key,
    required this.onRetake,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: AppDimens.containerMd,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Retake button
          _ActionButton(
            onTap: onRetake,
            icon: Icons.refresh,
            isOutlined: true,
            label: S.of(context).retake,
          ),
          SizedBox(width: MediaQuery.of(context).size.width < 360 ? 24 : 32),
          // Accept button
          _ActionButton(
            onTap: onConfirm,
            icon: Icons.check,
            label: S.of(context).confirm,
          ),
        ],
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final VoidCallback onTap;
  final IconData icon;
  final bool isOutlined;
  final String? label;

  const _ActionButton({
    required this.onTap,
    required this.icon,
    this.isOutlined = false,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonSize = screenWidth < 360 ? AppDimens.previewActionButtonSize * 0.9 : AppDimens.previewActionButtonSize;
    final iconSize = screenWidth < 360 ? AppDimens.previewActionIconSize * 0.9 : AppDimens.previewActionIconSize;

    return Material(
      color: Colors.transparent,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(buttonSize / 2),
            child: Container(
              width: buttonSize,
              height: buttonSize,
              decoration: BoxDecoration(
                color: isOutlined ? Colors.transparent : Colors.white,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: AppDimens.borderWidthMedium,
                ),
              ),
              child: Icon(
                icon,
                color: isOutlined ? Colors.white : Colors.black,
                size: iconSize,
              ),
            ),
          ),
          if (label != null) ...[
            AppDimens.h8,
            Text(
              label!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }
} 