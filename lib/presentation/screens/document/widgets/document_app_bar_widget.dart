import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/generated/l10n.dart';

// Navigation imports (Clean Architecture compliant)
import '../../../router/navigation_extensions.dart';

/// Widget chung cho AppBar của document screens
/// Sử dụng cho cả capture và preview screen
class DocumentAppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  final DocumentSide side;
  final DocumentType documentType;
  final VoidCallback? onBackPressed;

  const DocumentAppBarWidget({
    super.key,
    required this.side,
    required this.documentType,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: _buildBackButton(context),
      title: _buildTitle(context),
      centerTitle: true,
      automaticallyImplyLeading: false,
    );
  }

  Widget _buildBackButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: const Icon(
          Icons.arrow_back_ios_new,
          color: Colors.white,
          size: 20,
        ),
        onPressed: onBackPressed ?? () => context.goBack(),
        style: IconButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    String title;
    if (documentType == DocumentType.passport) {
      title = S.of(context).capturePassportGuidanceTitle;
    } else {
      title = side == DocumentSide.front 
          ? S.of(context).captureFrontSide 
          : S.of(context).captureBackSide;
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
} 