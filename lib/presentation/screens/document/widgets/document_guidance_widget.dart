import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/generated/l10n.dart';

/// Widget chung cho guidance text của document screens
/// Sử dụng cho cả capture và preview screen
class DocumentGuidanceWidget extends StatelessWidget {
  final String title;
  final String subtitle;
  final double frameTop;
  final double cardHeight;

  const DocumentGuidanceWidget({
    super.key,
    required this.title,
    required this.subtitle,
    required this.frameTop,
    required this.cardHeight,
  });

  /// Factory constructor cho capture screen
  factory DocumentGuidanceWidget.forCapture({
    required double frameTop,
    required double cardHeight,
  }) {
    return DocumentGuidanceWidget(
      title: S.current.placeDocumentInFrame,
      subtitle: S.current.clickToCapture,
      frameTop: frameTop,
      cardHeight: cardHeight,
    );
  }

  /// Factory constructor cho preview screen
  factory DocumentGuidanceWidget.forPreview({
    String? label,
    required double frameTop,
    required double cardHeight,
    DocumentType? documentType,
  }) {
    String title;
    String subtitle;
    if (documentType == DocumentType.passport) {
      title = S.current.previewPassportGuidanceTitle;
      subtitle = S.current.passportGuidanceSubtitle;
    } else {
      title = label ?? S.current.checkFrontSide;
      subtitle = S.current.pleaseCheckPhoto;
    }
    return DocumentGuidanceWidget(
      title: title,
      subtitle: subtitle,
      frameTop: frameTop,
      cardHeight: cardHeight,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: frameTop + cardHeight + AppDimens.spacingXL,
      left: 0,
      right: 0,
      child: Container(
        margin: AppDimens.marginHorizontalLg,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
            ),
            AppDimens.h16,
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white
                        .withValues(alpha: AppDimens.alphaNearOpaque),
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
