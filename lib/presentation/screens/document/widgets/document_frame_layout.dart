import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';

class DocumentFrameLayout {
  final double cardWidth;
  final double cardHeight;
  final double frameTop;

  DocumentFrameLayout(BuildContext context, DocumentType documentType)
      : cardWidth =
            MediaQuery.of(context).size.width * AppDimens.alphaNearOpaque,
        cardHeight =
            (MediaQuery.of(context).size.width * AppDimens.alphaNearOpaque) /
                AppDimens.getDocumentRatio(documentType),
        frameTop = ((MediaQuery.of(context).size.height -
                MediaQuery.of(context).padding.top -
                kToolbarHeight -
                ((MediaQuery.of(context).size.width *
                        AppDimens.alphaNearOpaque) /
                    AppDimens.getDocumentRatio(documentType))) /
            AppDimens.scaleHuge);
}
