import 'package:flutter/material.dart';

import 'document_frame_painter.dart';

/// Widget chung cho overlay và frame document
/// Sử dụng cho cả capture và preview screen
class DocumentOverlayWidget extends StatelessWidget {
  final double cardWidth;
  final double cardHeight;
  final double frameTop;
  final Color overlayColor;
  final Color holeColor;

  const DocumentOverlayWidget({
    super.key,
    required this.cardWidth,
    required this.cardHeight,
    required this.frameTop,
    this.overlayColor = const Color.fromRGBO(0, 0, 0, 0.85),
    this.holeColor = const Color.fromRGBO(0, 0, 0, 0.15),
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    
    return Stack(
      children: [
        // Overlay with hole for document frame - vùng trong sáng hơn vùng ngoài
        CustomPaint(
          painter: _OverlayWithHolePainter(
            holeRect: Rect.fromLTWH(
              (size.width - cardWidth) / 2,
              frameTop,
              cardWidth,
              cardHeight,
            ),
            overlayColor: overlayColor,
            holeColor: holeColor,
          ),
          size: Size(size.width, size.height),
        ),

        // Document frame overlay - chỉ vẽ khung
        Positioned(
          top: frameTop,
          left: 0,
          right: 0,
          height: cardHeight,
          child: CustomPaint(
            painter: DocumentFramePainter(
              width: cardWidth,
              height: cardHeight,
            ),
            size: Size(size.width, cardHeight),
          ),
        ),
      ],
    );
  }
}

/// Custom painter to create overlay with hole for document frame
/// Vùng trong khung sáng hơn vùng ngoài - tạo spotlight effect
class _OverlayWithHolePainter extends CustomPainter {
  final Rect holeRect;
  final Color overlayColor;
  final Color holeColor;

  _OverlayWithHolePainter({
    required this.holeRect,
    required this.overlayColor,
    required this.holeColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Tạo path cho toàn bộ màn hình
    final outerPath = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height));
    
    // Tạo path cho hole (vùng sáng) với border radius đồng bộ với khung
    final innerPath = Path()
      ..addRRect(RRect.fromRectAndRadius(
        holeRect,
        const Radius.circular(16.0), // Đồng bộ với cornerRadius của DocumentFramePainter
      ));
    
    // Combine paths - loại bỏ vùng inner khỏi outer
    final overlayPath = Path.combine(
      PathOperation.difference,
      outerPath,
      innerPath,
    );
    
    // Vẽ overlay (vùng tối)
    final overlayPaint = Paint()
      ..color = overlayColor
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(overlayPath, overlayPaint);
    
    // Vẽ vùng sáng nhẹ bên trong khung để tạo hiệu ứng spotlight
    final holePaint = Paint()
      ..color = holeColor
      ..style = PaintingStyle.fill;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        holeRect,
        const Radius.circular(16.0), // Đồng bộ với cornerRadius của DocumentFramePainter
      ),
      holePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _OverlayWithHolePainter ||
        oldDelegate.holeRect != holeRect ||
        oldDelegate.overlayColor != overlayColor ||
        oldDelegate.holeColor != holeColor;
  }
} 