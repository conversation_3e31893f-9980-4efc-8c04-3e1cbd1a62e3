import 'dart:async';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/presentation/router/navigation_extensions.dart';
import 'package:sales_app/presentation/controllers/controllers.dart';
import 'package:sales_app/presentation/widgets/widgets.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _fadeController;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _fadeAnimation;
  bool _navigated = false;
  bool _animationCompleted = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashAnimation();
  }

  void _initializeAnimations() {
    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Fade animation controller
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 700),
      vsync: this,
    );

    // Logo scale animation
    _logoScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Fade animation
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  void _startSplashAnimation() async {
    // Start logo animation
    await _logoController.forward();
    
    // Start fade animation
    await _fadeController.forward();
    
    // Wait a bit then check authentication
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Đánh dấu animation đã hoàn thành
    if (mounted) {
      setState(() {
        _animationCompleted = true;
      });
      
      // Kiểm tra trạng thái auth hiện tại sau khi animation xong
      _checkCurrentAuthState();
    }
  }

  void _checkCurrentAuthState() {
    if (_navigated) return;
    
    final authState = ref.read(authControllerProvider);
    authState.when(
      (isLoading, isLoggedIn, user, failure, isEmailValid, isPasswordValid) {
        // Default state - check if user is logged in
        if (isLoggedIn && user != null) {
          _navigated = true;
          AppLogger.info('User already logged in, navigating to home');
          context.replaceWithHome();
        } else {
          _navigated = true;
          AppLogger.info('No valid authentication, navigating to login');
          context.goToLogin();
        }
      },
      initial: () {
        // Initial state - có thể trigger kiểm tra auth nếu cần
        AppLogger.info('Auth state is initial, waiting for auth check...');
      },
      loading: () {
        // Still loading - wait a bit more
        AppLogger.info('Authentication still loading, waiting...');
        Future.delayed(const Duration(milliseconds: 700), () {
          if (mounted && !_navigated) {
            _checkCurrentAuthState();
          }
        });
      },
      authenticated: (user) {
        _navigated = true;
        AppLogger.info('User authenticated, navigating to home');
        context.replaceWithHome();
      },
      unauthenticated: (failure) {
        _navigated = true;
        AppLogger.info('User not authenticated, navigating to login');
        context.goToLogin();
      },
      error: (failure) {
        _navigated = true;
        AppLogger.warning('Authentication error, navigating to login');
        context.goToLogin();
      },
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Lắng nghe thay đổi trạng thái authentication
    ref.listen<AuthState>(authControllerProvider, (previous, next) {
      if (_navigated || !_animationCompleted) return;
      next.when(
        (isLoading, isLoggedIn, user, failure, isEmailValid, isPasswordValid) {
          // Handle default state
        },
        initial: () {},
        loading: () {},
        authenticated: (user) {
          _navigated = true;
          AppLogger.info('User authenticated, navigating to home');
          if (mounted) context.replaceWithHome();
        },
        unauthenticated: (failure) {
          _navigated = true;
          AppLogger.info('User not authenticated, navigating to login');
          if (mounted) context.goToLogin();
        },
        error: (failure) {
          _navigated = true;
          AppLogger.warning('Authentication error, navigating to login');
          if (mounted) context.goToLogin();
        },
      );
    });

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primaryColor,
              AppColors.secondaryColor,
            ],
          ),
        ),
        child: Stack(
          children: [
            // Background SVG
            Positioned.fill(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: const AppImage(
                  AppImages.background,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            
            // Content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo with animation
                  ScaleTransition(
                    scale: _logoScaleAnimation,
                    child: AppImage(
                        AppImages.klbLogoFull,
                        width: 200.w,
                        height: 53.h,
                        fit: BoxFit.contain,
                      ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 