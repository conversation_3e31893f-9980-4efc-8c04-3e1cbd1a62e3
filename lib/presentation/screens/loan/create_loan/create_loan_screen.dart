// Flutter/Dart imports
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

// Third-party imports
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Internal imports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';

import 'package:sales_app/presentation/models/models.dart';
import 'package:sales_app/presentation/utils/utils.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:sales_app/presentation/screens/loan/create_loan/steps/steps.dart';
import 'package:sales_app/presentation/router/navigation_extensions.dart';
import 'package:sales_app/domain/domain.dart';

class CreateLoanScreen extends HookConsumerWidget {
  const CreateLoanScreen({super.key});

  static final GlobalKey<FormState> step2FormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> step4FormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> step5FormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> step6FormKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasNavigatedToQRScan = useState(false); // Flag để tránh duplicate navigation

    // Sử dụng ref.listen để theo dõi thay đổi của showQRScanner
    ref.listen<CreateLoanFlowData?>(
      createLoanControllerProvider.select((state) => state.data),
      (previous, next) {
        if (next == null) return;
      
        // Reset flag khi showQRScanner = false (khi quay lại màn hình)
        if (!next.showQRScanner) {
          if (hasNavigatedToQRScan.value) {
            AppLogger.info('Resetting hasNavigatedToQRScan flag');
            hasNavigatedToQRScan.value = false;
          }
          return;
        }
        
        // Chỉ navigation khi:
        // 1. showQRScanner = true
        // 2. Chưa navigation trước đó
        // 3. Có sự thay đổi từ false sang true
        if (next.showQRScanner && 
            !hasNavigatedToQRScan.value && 
            previous?.showQRScanner != true) {
          hasNavigatedToQRScan.value = true;
          
          AppLogger.info('showQRScanner = true, chuyển sang QR scan', data: {
            'currentStep': next.currentStep.name,
            'previousShowQRScanner': previous?.showQRScanner,
          });
          
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (context.mounted) {
              final controller = ref.read(createLoanControllerProvider.notifier);
              _goToQRScan(context, ref, controller);
            }
          });
        }
      },
    );

    // Determine leading widget based on current step
    Widget? buildLeading(CreateLoanFlowData data, WidgetRef ref) {
      if (data.currentStep == LoanStep.success) return null;
      return IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        onPressed: () {
          if (data.currentStep == LoanStep.identityDocument) {
            context.goBack();
          } else {
            ref.read(createLoanControllerProvider.notifier).goBack();
          }
        },
      );
    }

    return BaseScreen(
      title: S.of(context).createNewLoan,
      automaticallyImplyLeading: false,
      leading: buildLeading(ref.watch(createLoanControllerProvider).data!, ref),
      body: BaseStateConsumerBuilder<CreateLoanFlowData>(
        stateProvider: createLoanControllerProvider,
        successBuilder: (data, ref) => _buildLoanFlow(context, ref, data),
        loadingBuilder: (ref) => const BaseLoadingWidget(),
        errorBuilder: (failure, onRetry, ref) => BaseErrorWidget(
          failure: failure,
          onRetry: onRetry,
        ),
        showContentWhileSubmitting: true,
        showErrorWidgetOnSubmittingError: false,
      ).withErrorHandler(
        createLoanControllerProvider,
        onError: () {
          // Custom error handling logic
          AppLogger.error('Error occurred in CreateLoanScreen');
        },
      ),
    );
  }

  /// Build loan flow content when state is success
  Widget _buildLoanFlow(
      BuildContext context, WidgetRef ref, CreateLoanFlowData data) {
    final controller = ref.read(createLoanControllerProvider.notifier);

    // Biến kiểm tra enable cho button ở bước 1 và các bước khác (trừ bước 2)
    final shouldDisableContinueButton =
        ((data.currentStep == LoanStep.identityDocument ||
          data.currentStep == LoanStep.coBorrowerDocument) &&
                controller.isContinueEnabled() == false)
        ? false
        : controller.isContinueEnabled();

    // Widget hiển thị nội dung của từng bước
    Widget buildStepContent() {
      switch (data.currentStep) {
        case LoanStep.identityDocument:
          return const Step1IdentityDocumentScreen();
        case LoanStep.borrowerInfo:
          return Step2BorrowerInfoScreen(
            formKey: step2FormKey,
          );
        case LoanStep.coBorrowerDocument:
          return Step3CoBorrowerDocumentScreen(
            onStepCompleted: (data) {
              controller.updateStep3Data(data);
            },
          );
        case LoanStep.coBorrowerInfo:
          return Step4CoBorrowerInfoScreen(
            formKey: step4FormKey,
          );
        case LoanStep.loanRequest:
          return Step5LoanRequestScreen(
            formKey: step5FormKey,
          );
        case LoanStep.financialInfo:
          return Step6FinancialInfoScreen(
            formKey: step6FormKey,
          );
        case LoanStep.collateralInfo:
          return Step7CollateralInfoScreen(
            onStepCompleted: (data) {
              controller.updateStep7Data(data);
            },
          );
        case LoanStep.collateralDetail:
          return Step8CollateralDetailScreen(
            onStepCompleted: (data) {
              controller.updateStep8Data(data);
            },
          );
        case LoanStep.documentList:
          return Step9DocumentListScreen(
            onStepCompleted: (data) {
              controller.updateStep9Data(data);
            },
          );
        case LoanStep.loanConfirmation:
          return const Step10LoanConfirmationScreen();
        case LoanStep.success:
          return Step11SuccessScreen(
            onGoHome: () {
              // Navigate to home screen
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
            onCreateNewLoan: () {
              // Reset controller and start new loan
              controller.reset();
            },
            onCreateAdditionalLoan: () {
              // Keep customer info and start new loan
              // TODO: Implement resetForAdditionalLoan
              // controller.resetForAdditionalLoan();
            },
          );
      }
    }

    return Column(
        children: [
          LoanProgressIndicator(
          currentStep: data.currentStep.stepNumber,
            totalSteps: CreateLoanController.totalSteps,
            stepTitle:
              LoanStepLocalizer.getStepTitle(context, data.currentStep),
          ),
          Expanded(
            child: buildStepContent(),
          ),
        if (!data.currentStep.isLast)
            BottomButton(
              enabled: shouldDisableContinueButton,
              title: S.of(context).continueText,
            onPressed: () async {
             if (!_isCurrentStepValid(data.currentStep)) return;
              await controller.handleContinue();
            },
          ),
      ],
    );
  }

bool _isCurrentStepValid(LoanStep step) {
  switch (step) {
    case LoanStep.borrowerInfo:
      return step2FormKey.currentState?.validate() ?? false;
    case LoanStep.coBorrowerInfo:
      return step4FormKey.currentState?.validate() ?? false;
    case LoanStep.loanRequest:
      return step5FormKey.currentState?.validate() ?? false;
    case LoanStep.financialInfo:
      return step6FormKey.currentState?.validate() ?? false;
    default:
      return true;
  }
}
  /// Navigate to QR scan screen using CommonQRScanner qua GoRouter
  void _goToQRScan(BuildContext context, WidgetRef ref, CreateLoanController controller) {
    context.goToQRScan(
      onQRCodeDetected: (qrData) {
        _handleQRCodeDetected(context, ref, qrData, controller);
      },
      onSkip: () => _handleQRScanSkip(context, ref, controller),
      onBack: () => _handleQRScanBack(context, ref, controller),
    );
  }

  /// Handle QR code detected event
  void _handleQRCodeDetected(BuildContext context, WidgetRef ref, String qrData, CreateLoanController controller) {
    // Parse QR data to CCCD info with error dialog
    final cccdInfo = QRParseHelper.parseWithErrorDialog<CCCDInfoEntity>(
      context: context,
      qrData: qrData,
      parseFunction: QRParser.parseCccdInfoFromQr,
    );
    
    if (cccdInfo != null) {
      _handleSuccessfulQRParse(context, ref, cccdInfo, controller);
    }
  }

  /// Handle successful QR parse
  void _handleSuccessfulQRParse(BuildContext context, WidgetRef ref, CCCDInfoEntity cccdInfo, CreateLoanController controller) {
    AppLogger.info('QR Parse successful in loan flow', data: {
      'fullName': cccdInfo.fullName,
      'idNumber': cccdInfo.idNumber,
    });

    // Cập nhật CCCD info vào controller (sử dụng method nhận CCCDInfoEntity trực tiếp)
    controller.handleSuccessfulQRParse(cccdInfo);
    
    // Navigate back to previous screen using navigation extensions
    if (context.mounted) {
      context.goBack();
    }
  }

  /// Handle QR scan skip event
  void _handleQRScanSkip(BuildContext context, WidgetRef ref, CreateLoanController controller) {
    // Gọi controller method để xử lý skip
    controller.handleQRSkip();
    
    // Navigate back to previous screen using navigation extensions
    if (context.mounted) {
      context.goBack();
    }
  }

  /// Handle QR scan back event
  void _handleQRScanBack(BuildContext context, WidgetRef ref, CreateLoanController controller) {
    // Gọi controller method để xử lý back
    controller.handleQRBack();
    
    // Navigate back to previous screen using navigation extensions
    if (context.mounted) {
      context.goBack();
    }
  }
}
