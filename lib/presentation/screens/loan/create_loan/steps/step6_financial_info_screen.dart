import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/enums/enums.dart';
import 'package:sales_app/core/utils/currency_formatter.dart';
import 'package:sales_app/presentation/models/loan/steps/steps.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:sales_app/core/utils/income_source_mapper.dart';
import 'package:sales_app/domain/entities/common/system_config_entity.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/presentation/utils/validation_message_mapper.dart';
import 'package:sales_app/core/utils/validation_utils.dart';

import '../../../../../domain/entities/location/province_entity.dart';
import '../../../../../domain/entities/location/ward_entity.dart';

class Step6FinancialInfoScreen extends HookConsumerWidget {
  final GlobalKey<FormState> formKey;
  const Step6FinancialInfoScreen({
    super.key,
    required this.formKey,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final controller = ref.read(createLoanControllerProvider.notifier);

    // Controllers cho các text field
    final dailyRevenueController = useTextEditingController(text: '0');
    final dailyIncomeController = useTextEditingController(text: '0');
    final specificAddressController = useTextEditingController();

    // State variables
    final selectedIncomeSource = useState<IncomeSource?>(null);
    final selectedProvinceStep6 = useState<ProvinceEntity?>(null);
    final selectedWardStep6 = useState<WardEntity?>(null);
    final hasSetDefaultIncomeSource = useState<bool>(false);
    final hasUserInteracted = useState<bool>(false);

    // Khôi phục dữ liệu từ controller nếu có và load data cần thiết
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) async{
        if (controller.incomeSources.isEmpty && !controller.isLoadingIncomeSources) {
         await  controller.loadIncomeSources();
        }
        // Load provinces nếu chưa có (cho phần địa chỉ hiện tại)
        if (controller.provinces.isEmpty && !controller.isLoadingProvinces) {
         await controller.loadProvinces();
        }

        if (controller.step6Data != null) {
          hasUserInteracted.value = false;
          final d = controller.step6Data!;
          selectedIncomeSource.value = d.incomeSource;
          dailyIncomeController.text = d.dailyIncome > 0 ? CurrencyFormatter.formatCurrency(d.dailyIncome) : '';
          dailyRevenueController.text = d.dailyRevenue != null && d.dailyRevenue! > 0 ? CurrencyFormatter.formatCurrency(d.dailyRevenue!) : '';
          specificAddressController.text = d.specificAddress ?? '';
        }
      });
      return null;
    }, [controller.incomeSources, controller.provinces]);

    // Set default income source nếu chưa có selection và đã load xong data
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!hasSetDefaultIncomeSource.value && selectedIncomeSource.value == null && controller.incomeSources.isNotEmpty) {
          final defaultConfig = IncomeSourceMapper.getDefaultSystemConfig(controller.incomeSources);
          if (defaultConfig != null) {
            controller.selectIncomeSource(defaultConfig);
            selectedIncomeSource.value = IncomeSourceMapper.fromSystemConfig(defaultConfig);
            hasSetDefaultIncomeSource.value = true;
          }
        }
      });
      return null;
    }, [controller.incomeSources]);

    void updateController() {
      // if (selectedIncomeSource.value == null) return;
      final data = Step6FinancialInfoData(
        incomeSource: selectedIncomeSource.value!,
        dailyRevenue: selectedIncomeSource.value == IncomeSource.business
            ? CurrencyFormatter.parseCurrency(dailyRevenueController.text)
            : null,
        dailyIncome: CurrencyFormatter.parseCurrency(dailyIncomeController.text),
        province: selectedProvinceStep6.value?.id,
        ward: selectedWardStep6.value?.id,
        specificAddress: specificAddressController.text.trim().isEmpty
            ? null
            : specificAddressController.text.trim(),
      );
      controller.updateFinancialInfo(data);
    }

    // Theo dõi thay đổi của text controllers để trigger updateController
    useEffect(() {
      void listener() {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          updateController();
        });
      }
      dailyRevenueController.addListener(listener);
      dailyIncomeController.addListener(listener);
      specificAddressController.addListener(listener);
      return () {
        dailyRevenueController.removeListener(listener);
        dailyIncomeController.removeListener(listener);
        specificAddressController.removeListener(listener);
      };
    }, []);
    // Sync selectedProvince, selectedWard và selectedIncomeSource từ controller data
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        selectedProvinceStep6.value = controller.selectedProvinceStep6;
        selectedWardStep6.value = controller.selectedWardStep6;
        // Sync marital status từ controller nếu có
        if (controller.selectedIncomeSource != null) {
          final incomeSource = IncomeSourceMapper.fromSystemConfig(
              controller.selectedIncomeSource!);
          if (incomeSource != null) {
            selectedIncomeSource.value = incomeSource;
          } else {
            // Fallback về single nếu không tìm thấy
            selectedIncomeSource.value = IncomeSource.business;
          }
        }
      });
      return null;
    }, [
      controller.selectedProvinceStep6,
      controller.selectedWardStep6,
      controller.selectedIncomeSource
    ]);

    Widget buildIncomeSourceDropdown() {
      DropdownState state = controller.isLoadingIncomeSources
          ? DropdownState.loading
          : (controller.incomeSources.isEmpty
              ? DropdownState.empty
              : DropdownState.normal);

      return CommonDropdown<SystemConfigEntity>(
        label: S.of(context).income_source,
        items: controller.incomeSources,
        value: controller.selectedIncomeSource,
        state: state,
        emptyMessage: 'Không có dữ liệu nguồn thu',
        onRetry: () => controller.loadIncomeSources(),
        onChanged: (systemConfig) {
          hasUserInteracted.value = true;
          final incomeSource =
              IncomeSourceMapper.fromSystemConfig(systemConfig);
          if (incomeSource != null) {
            selectedIncomeSource.value = incomeSource;
          } else {
            selectedIncomeSource.value = IncomeSource.business;
          }
          WidgetsBinding.instance.addPostFrameCallback((_) {
            controller.selectIncomeSource(systemConfig);
            updateController();
          });
        },
        validator: (value) {
           final selected = value ?? controller.selectedIncomeSource;
          if (selected == null) {
            return 'Vui lòng chọn nguồn thu';
          }
          return null;
        },
        labelBuilder: (systemConfig) => systemConfig.label,
        isDense: true,
      );
    }

    return SafeArea(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: SingleChildScrollView(
          padding: AppDimens.paddingAllLg,
          child: Form(
            key: formKey,
            // autovalidateMode: hasUserInteracted.value
            //     ? AutovalidateMode.onUserInteraction
            //     : AutovalidateMode.disabled,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).financial_info_title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                AppDimens.h16,
                buildIncomeSourceDropdown(),
                AppDimens.h8,
                if (selectedIncomeSource.value == IncomeSource.business) ...[
                  CommonTextField.currency(
                    label: S.of(context).daily_revenue,
                    controller: dailyRevenueController,
                    isDense: true,
                    inputFormatters: [
                      CurrencyInputFormatter(),
                    ],
                    onChanged: (value) {
                      hasUserInteracted.value = true;
                      updateController();
                    },
                    validator: (value) {
                      final actualValue = value?.isNotEmpty == true ? value : dailyRevenueController.text;
                      final errorKey = ValidationUtils.validateMoney(actualValue,  S.of(context).daily_revenue);
                      return ValidationMessageMapper.getValidationMessage(
                        context,
                        errorKey,
                        fieldName: S.of(context).daily_revenue,
                      );
                    },
                  ),
                  AppDimens.h8,
                ],
                CommonTextField.currency(
                  label: S.of(context).daily_income,
                  controller: dailyIncomeController,
                  isDense: true,
                  inputFormatters: [
                    CurrencyInputFormatter(),
                  ],
                  onChanged: (value) {
                    hasUserInteracted.value = true;
                    updateController();
                  },
                  validator: (value) {
                    final actualValue = value?.isNotEmpty == true ? value : dailyIncomeController.text;
                    final errorKey = ValidationUtils.validateMoney(actualValue, S.of(context).daily_income);
                    return ValidationMessageMapper.getValidationMessage(
                      context,
                      errorKey,
                      fieldName: S.of(context).daily_income,
                    );
                  },
                ),
                AppDimens.h16,
                if (selectedIncomeSource.value == IncomeSource.business) ...[
                  Text(
                    S.of(context).business_location_title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  AppDimens.h16,
                  _buildProvinceSelectionStep6(context, ref, selectedProvinceStep6,
                      updateController, hasUserInteracted),

                  AppDimens.h8,

                  // Phường/Xã
                  if (selectedProvinceStep6.value != null) ...[
                    _buildWardSelectionStep6(context, ref, selectedWardStep6,
                        updateController, hasUserInteracted),
                  ],
                  AppDimens.h8,
                  CommonTextField(
                    label: S.of(context).specific_address,
                    controller: specificAddressController,
                    isDense: true,
                    textInputAction: TextInputAction.done,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(250),
                    ],
                    maxLines: 3,
                    onChanged: (value) {
                      hasUserInteracted.value = true;
                      updateController();
                    },
                    validator: (value) {
                      final actualValue = value?.isNotEmpty == true ? value : specificAddressController.text;
                      final errorKey = ValidationUtils.validateAddress(actualValue);
                      return ValidationMessageMapper.getValidationMessage(
                        context,
                        errorKey,
                        fieldName: S.of(context).specific_address,
                      );
                    },
                  ),
                  ]
              ],
            ),
          ),
        ),
      ),
    );
  }
}

Widget _buildProvinceSelectionStep6(
  BuildContext context,
  WidgetRef ref,
  ValueNotifier<ProvinceEntity?> selectedProvinceStep6,
  VoidCallback updateController,
  ValueNotifier<bool> hasUserInteracted,
) {
  final controller = ref.read(createLoanControllerProvider.notifier);

  DropdownState state = controller.isLoadingProvinces
      ? DropdownState.loading
      : (controller.provinces.isEmpty
          ? DropdownState.empty
          : DropdownState.normal);

  return CommonDropdown<ProvinceEntity>(
    label: S.of(context).province,
    items: controller.provinces,
    value: controller.selectedProvinceStep6,
    state: state,
    emptyMessage: 'Không có dữ liệu Tỉnh/thành phố',
    onRetry: () => controller.loadProvinces(),
    onChanged: (province) {
      hasUserInteracted.value = true;
      selectedProvinceStep6.value = province;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.selectProvinceStep6(province);
        updateController();
      });
    },
    validator: (value) {
      final selected = value ?? controller.selectedProvinceStep6;
      if (selected == null) {
        return 'Vui lòng chọn Tỉnh/thành phố';
      }
      return null;
    },
    labelBuilder: (province) => province.name,
    isDense: true,
  );
}

Widget _buildWardSelectionStep6(
  BuildContext context,
  WidgetRef ref,
  ValueNotifier<WardEntity?> selectedWardStep6,
  VoidCallback updateController,
  ValueNotifier<bool> hasUserInteracted,
) {
  final controller = ref.read(createLoanControllerProvider.notifier);

  DropdownState state = controller.isLoadingWards
      ? DropdownState.loading
      : (controller.wards.isEmpty ? DropdownState.empty : DropdownState.normal);

  return CommonDropdown<WardEntity>(
    label: S.of(context).ward,
    value: controller.selectedWardStep6,
    items: controller.wards,
    state: state,
    emptyMessage: S.of(context).no_ward_data,
    onRetry: () {
      if (controller.selectedProvince != null) {
        controller.loadWards(controller.selectedProvince!.id);
      }
    },
    enabled: controller.wards.isNotEmpty && !controller.isLoadingWards,
    labelBuilder: (ward) => ward.name,
    onChanged: (ward) {
      hasUserInteracted.value = true;
      selectedWardStep6.value = ward;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.selectWardStep6(ward);
        updateController();
      });
    },
    validator: (value) {
      final selected = value ?? controller.selectedWardStep6;
      if (selected == null) {
        return 'Vui lòng chọn phường/xã ';
      }
      return null;
    },
  );
}
