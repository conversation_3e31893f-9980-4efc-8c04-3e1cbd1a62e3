import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/enums/enums.dart';
import 'package:sales_app/presentation/models/loan/steps/steps.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';
import 'package:sales_app/generated/l10n.dart';

class Step3CoBorrowerDocumentScreen extends HookConsumerWidget {
  final void Function(Step3CoBorrowerDocumentData data) onStepCompleted;

  const Step3CoBorrowerDocumentScreen({
    super.key,
    required this.onStepCompleted,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final controller = ref.read(createLoanControllerProvider.notifier);

    final selectedDocType = useState<DocumentType>(
      controller.step3Data?.documentType ?? DocumentType.cccd,
    );
    final frontImagePath = useState<String?>(
      controller.step3Data?.frontImagePath,
    );
    final backImagePath = useState<String?>(
      controller.step3Data?.backImagePath,
    );

    final hasCompleted = useRef(false);

    void updateController() {
      final step3Data = Step3CoBorrowerDocumentData(
        documentType: selectedDocType.value,
        frontImagePath: frontImagePath.value ?? '',
        backImagePath: backImagePath.value,
        // Thêm các field khác nếu cần
      );

      // Validate đủ ảnh để hoàn thành bước này
      final isComplete = step3Data.isReadyForUpload;
      if (isComplete && !hasCompleted.value) {
        hasCompleted.value = true;
        onStepCompleted(step3Data);
      } else if (!isComplete) {
        hasCompleted.value = false;
      }
    }

    return SafeArea(
      child: SingleChildScrollView(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              S.of(context).step3_identity_document_description,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            AppDimens.h24,
            CommonDropdown<DocumentType>(
              label: S.of(context).document_type_label,
              items: DocumentType.values,
              value: selectedDocType.value,
              isRequired: false,
              labelBuilder: (type) => type.label,
              onChanged: (type) {
                selectedDocType.value = type;
                frontImagePath.value = null;
                backImagePath.value = null;
                controller.updateStep3Data(Step3CoBorrowerDocumentData(documentType: type));
                updateController();
              },
            ),
            AppDimens.h24,
            DocumentImagePicker(
              label: (selectedDocType.value != DocumentType.passport)
                  ? S.of(context).document_front_side_label
                  : S.of(context).document_info_label,
              imagePath: frontImagePath.value,
              onImageSelected: (path) {
                frontImagePath.value = path;
                updateController();
              },
              documentType: selectedDocType.value,
            ),
            AppDimens.h16,
            if (selectedDocType.value != DocumentType.passport)
              DocumentImagePicker(
                label: S.of(context).document_back_side_label,
                imagePath: backImagePath.value,
                onImageSelected: (path) {
                  backImagePath.value = path;
                  updateController();
                },
                documentType: selectedDocType.value,
              ),
          ],
        ),
      ),
    );
  }
}