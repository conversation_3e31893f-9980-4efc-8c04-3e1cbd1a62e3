import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/utils/currency_formatter.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';
import 'package:sales_app/presentation/models/loan/steps/steps.dart';
import 'package:sales_app/presentation/models/models.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:intl/intl.dart';

class Step10LoanConfirmationScreen extends HookConsumerWidget {
  const Step10LoanConfirmationScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(createLoanControllerProvider.notifier);

    // State for confirmation checkbox - sync với controller state
    final isConfirmed = useState<bool>(controller.step10Data?.isConfirmed ?? false);

    // Handle checkbox change
    void handleCheckboxChange(bool? value) {
      final newValue = value ?? false;
      isConfirmed.value = newValue;

      // Update controller state ngay lập tức
      Future.microtask(() {
        controller.updateStep10Data(Step10LoanConfirmationData(isConfirmed: newValue));
      });
    }

    return SafeArea(
      child: SingleChildScrollView(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thông tin người vay chính
            if (controller.step2Data != null) ...[
              _buildExpandableInfoCard(
                context,
                'Thông tin người vay chính',
                Icons.person_outline,
                [
                  _buildInfoRow('Họ và tên', controller.step2Data!.fullName),
                  _buildInfoRow('Số giấy tờ', controller.step2Data!.documentNumber),
                  _buildInfoRow('Ngày cấp', controller.step2Data!.issueDate != null
                    ? DateFormat('dd/MM/yyyy').format(controller.step2Data!.issueDate!)
                    : 'Chưa có thông tin'),
                  _buildInfoRow('Ngày hết hạn', controller.step2Data!.expiryDate != null
                    ? DateFormat('dd/MM/yyyy').format(controller.step2Data!.expiryDate!)
                    : 'Chưa có thông tin'),
                  _buildInfoRow('Nơi cấp', controller.step2Data!.issuePlace),
                  _buildInfoRow('Ngày sinh', controller.step2Data!.birthDate != null
                    ? DateFormat('dd/MM/yyyy').format(controller.step2Data!.birthDate!)
                    : 'Chưa có thông tin'),
                  _buildInfoRow('Giới tính', controller.step2Data!.gender.label),
                  _buildInfoRow('Hộ khẩu thường trú', controller.step2Data!.permanentAddress),
                  _buildInfoRow('Địa chỉ hiện tại', controller.step2Data!.sameAsPermAddress
                    ? controller.step2Data!.permanentAddress
                    : '${controller.step2Data!.specificAddress ?? ''}, ${controller.step2Data!.ward ?? ''}, ${controller.step2Data!.province ?? ''}'),
                  _buildInfoRow('Tình trạng hôn nhân', controller.step2Data!.maritalStatus.label),
                  _buildInfoRow('Số điện thoại', controller.step2Data!.phoneNumber),
                ],
              ),
              AppDimens.h16,
            ],

            // Thông tin người đồng vay (nếu có)
            if (controller.step2Data?.hasCoBorrower == true && controller.step4Data != null) ...[
              _buildExpandableInfoCard(
                context,
                'Thông tin người đồng vay',
                Icons.people_outline,
                [
                  _buildInfoRow('Họ và tên', controller.step4Data!.fullName),
                  _buildInfoRow('Số giấy tờ', controller.step4Data!.documentNumber),
                  _buildInfoRow('Ngày cấp', controller.step4Data!.issueDate != null
                    ? DateFormat('dd/MM/yyyy').format(controller.step4Data!.issueDate!)
                    : 'Chưa có thông tin'),
                  _buildInfoRow('Ngày hết hạn', controller.step4Data!.expiryDate != null
                    ? DateFormat('dd/MM/yyyy').format(controller.step4Data!.expiryDate!)
                    : 'Chưa có thông tin'),
                  _buildInfoRow('Nơi cấp', controller.step4Data!.issuePlace),
                  _buildInfoRow('Ngày sinh', controller.step4Data!.birthDate != null
                    ? DateFormat('dd/MM/yyyy').format(controller.step4Data!.birthDate!)
                    : 'Chưa có thông tin'),
                  _buildInfoRow('Giới tính', controller.step4Data!.gender.label),
                  _buildInfoRow('Hộ khẩu thường trú', controller.step4Data!.permanentAddress),
                  _buildInfoRow('Địa chỉ hiện tại', controller.step4Data!.sameAsPermAddress
                    ? controller.step4Data!.permanentAddress
                    : '${controller.step4Data!.specificAddress ?? ''}, ${controller.step4Data!.ward ?? ''}, ${controller.step4Data!.province ?? ''}'),
                  _buildInfoRow('Tình trạng hôn nhân', controller.step4Data!.maritalStatus.label),
                  _buildInfoRow('Số điện thoại', controller.step4Data!.phoneNumber),
                ],
              ),
              AppDimens.h16,
            ],

            // Đề nghị vay vốn
            if (controller.step5Data != null) ...[
              _buildExpandableInfoCard(
                context,
                'Đề nghị vay vốn',
                Icons.account_balance_wallet_outlined,
                [
                  _buildInfoRow('Hình thức vay vốn', controller.step5Data!.hasCollateral ? 'Có TSĐB' : 'Không TSĐB'),
                  _buildInfoRow('Vốn tự có', CurrencyFormatter.formatCurrency(controller.step5Data!.ownCapital)),
                  _buildInfoRow('Số tiền đề nghị vay', CurrencyFormatter.formatCurrency(controller.step5Data!.loanAmount)),
                  _buildInfoRow('Thời hạn vay', controller.step5Data!.loanTerm.label),
                  _buildInfoRow('Tổng nhu cầu', CurrencyFormatter.formatCurrency(controller.step5Data!.totalNeed)),
                  _buildInfoRow('CN/PGD', controller.step5Data!.branchCode),
                  _buildInfoRow('Phương thức vay', 'Vay trả góp'),
                  _buildInfoRow('Mục đích sử dụng vốn', controller.step5Data!.loanPurpose.label),
                  // Comment: "Tên mục đích" không có trong SRS Bước 10
                  // if (controller.step5Data!.loanPurpose.name == 'other' && controller.step5Data!.customPurposeName != null)
                  //   _buildInfoRow('Tên mục đích', controller.step5Data!.customPurposeName!),
                  _buildInfoRow('Hình thức trả nợ', 'Trả góp nợ gốc và lãi tiền vay hàng ngày'),
                  _buildInfoRow('Phương thức giải ngân', controller.step5Data!.disbursementMethod.label),
                  // Comment: "Số tài khoản nhận tiền" không có trong SRS Bước 10
                  // if (controller.step5Data!.accountNumber != null)
                  //   _buildInfoRow('Số tài khoản nhận tiền', controller.step5Data!.accountNumber!),
                ],
              ),
              AppDimens.h16,
            ],

            // Tình hình tài chính
            if (controller.step6Data != null) ...[
              _buildExpandableInfoCard(
                context,
                'Tình hình tài chính',
                Icons.trending_up_outlined,
                [
                  _buildInfoRow('Nguồn thu', controller.step6Data!.incomeSource.label),
                  if (controller.step6Data!.dailyRevenue != null)
                    _buildInfoRow('Doanh số bình quân/Ngày', CurrencyFormatter.formatCurrency(controller.step6Data!.dailyRevenue!)),
                  _buildInfoRow('Thu nhập bình quân/Ngày', CurrencyFormatter.formatCurrency(controller.step6Data!.dailyIncome)),
                  if (controller.step6Data!.province != null)
                    _buildInfoRow('Tỉnh/Thành phố', controller.step6Data!.province!),
                  // Comment: "Phường/Xã" không có trong SRS Bước 10
                  // if (controller.step6Data!.ward != null)
                  //   _buildInfoRow('Phường/Xã', controller.step6Data!.ward!),
                  if (controller.step6Data!.specificAddress != null)
                    _buildInfoRow('Địa chỉ cụ thể', controller.step6Data!.specificAddress!),
                ],
              ),
              AppDimens.h16,
            ],

            // Tài sản bảo đảm (nếu có)
            if (controller.step5Data?.hasCollateral == true) ...[
              if (controller.step7Data != null || controller.step8Data != null) ...[
                _buildExpandableInfoCard(
                  context,
                  'Tài sản bảo đảm',
                  Icons.security_outlined,
                  [
                    if (controller.step7Data != null) ...[
                      _buildInfoRow('Loại tài sản', controller.step7Data!.assetType),
                      _buildInfoRow('Giá trị tài sản', CurrencyFormatter.formatCurrency(controller.step7Data!.assetValue)),
                      _buildInfoRow('Giá trị tài sản (bằng chữ)', controller.step7Data!.assetValueInWords),
                      _buildInfoRow('Hiện trạng tài sản', controller.step7Data!.assetCondition is String ? controller.step7Data!.assetCondition as String : (controller.step7Data!.assetCondition as dynamic).label as String),
                      _buildInfoRow('Chủ sở hữu tài sản', controller.step7Data!.ownerName),
                      _buildInfoRow('Năm sinh', DateFormat('dd/MM/yyyy').format(controller.step7Data!.ownerBirthDate)),
                      _buildInfoRow('Loại đăng ký xe', controller.step7Data!.registrationType),
                    ],
                    if (controller.step8Data != null) ...[
                      _buildInfoRow('Biển kiểm soát', controller.step8Data!.licensePlate),
                      _buildInfoRow('Tên tài sản', controller.step8Data!.assetName),
                      _buildInfoRow('Số khung', controller.step8Data!.chassisNumber),
                      _buildInfoRow('Số máy', controller.step8Data!.engineNumber),
                      _buildInfoRow('Số chứng nhận đăng ký xe', controller.step8Data!.registrationCertificateNumber),
                      _buildInfoRow('Nơi cấp', controller.step8Data!.issuePlace),
                      _buildInfoRow('Ngày cấp', DateFormat('dd/MM/yyyy').format(controller.step8Data!.issueDate)),
                      // _buildInfoRow('Tình trạng tài sản khi giao', controller.step8Data!.assetCondition),
                      _buildInfoRow('Tổng giá trị tài sản bảo đảm', CurrencyFormatter.formatCurrency(controller.step8Data!.totalAssetValue)),
                    ],
                  ],
                ),
                AppDimens.h16,
              ],
            ],

            // Checkbox xác nhận
            CommonCheckbox(
              label: 'Tôi xác nhận các nội dung khách hàng đã nêu trên trùng khớp và phù hợp; khách hàng có đủ điều kiện vay vốn và trả nợ vay',
              value: isConfirmed.value,
              onChanged: handleCheckboxChange,
            ),

            AppDimens.h24,
          ],
        ),
      ),
    );
  }

  // Helper method để build info card với tiêu đề và nội dung gộp chung
  Widget _buildExpandableInfoCard(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: AppDimens.borderRadius8,
        border: Border.all(
          color: AppColors.primaryColor.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header với icon và tiêu đề
          Container(
            padding: AppDimens.paddingAllMd,
            child: Row(
              children: [
                Container(
                  width: AppDimens.containerMd,
                  height: AppDimens.containerMd,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor.withValues(alpha: 0.1),
                    borderRadius: AppDimens.borderRadius8,
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.primaryColor,
                    size: AppDimens.iconMD,
                  ),
                ),
                AppDimens.w12,
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: AppDimens.fontLG,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Nội dung thông tin
          Padding(
            padding: EdgeInsets.only(
              left: AppDimens.spacingMD,
              right: AppDimens.spacingMD,
              bottom: AppDimens.spacingMD,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method để build info row
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimens.spacingSM),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: AppDimens.fontSM,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          AppDimens.w8,
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: AppDimens.fontSM,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}