import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/enums/enums.dart';
import 'package:sales_app/core/utils/currency_formatter.dart';
import 'package:sales_app/core/utils/validation_utils.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';
import 'package:sales_app/presentation/utils/validation_message_mapper.dart';
import 'package:sales_app/presentation/models/loan/steps/steps.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/core/utils/loan_purpose_mapper.dart';
import 'package:sales_app/core/utils/loan_term_mapper.dart';
import 'package:sales_app/domain/domain.dart';

class Step5LoanRequestScreen extends HookConsumerWidget {
  final GlobalKey<FormState> formKey;

  const Step5LoanRequestScreen({
    super.key,
    required this.formKey,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final controller = ref.read(createLoanControllerProvider.notifier);

    // Form controllers
    final ownCapitalController = useTextEditingController(text: '0');
    final loanAmountController = useTextEditingController(text: '0');
    final totalNeedController = useTextEditingController(text: '0');
    final customPurposeController = useTextEditingController();

    // Form state với giá trị mặc định
    final hasCollateral = useState<bool>(true); // Mặc định chọn "Có TSĐB"
    final selectedLoanTerm = useState<LoanTerm?>(null); // Gán null khi vào màn
    final selectedLoanPurpose =
        useState<LoanPurpose?>(null); // Gán null khi vào màn
    final selectedDisbursementMethod =
        useState<DisbursementMethod?>(DisbursementMethod.cash);
    final selectedAccountNumber = useState<String?>(null);
    final hasUserInteracted = useState<bool>(false);

    // Mock branch code - in real app this would come from user session
    const branchCode = 'CN1';

    // Tính tổng nhu cầu
    void updateTotalNeed() {
      final ownCapital =
          CurrencyFormatter.parseCurrency(ownCapitalController.text);
      final loanAmount =
          CurrencyFormatter.parseCurrency(loanAmountController.text);
      final total = ownCapital + loanAmount;
      totalNeedController.text = CurrencyFormatter.formatCurrency(total);
    }

    // Cập nhật controller
    void updateController() {
      final ownCapital =
          CurrencyFormatter.parseCurrency(ownCapitalController.text);
      final loanAmount =
          CurrencyFormatter.parseCurrency(loanAmountController.text);
      // Bắt buộc phải có loanTerm và loanPurpose, nếu thiếu thì không update
      if (selectedLoanTerm.value == null || selectedLoanPurpose.value == null) {
        return;
      }
      final data = Step5LoanRequestData(
        hasCollateral: hasCollateral.value,
        ownCapital: ownCapital,
        loanAmount: loanAmount,
        loanTerm: selectedLoanTerm.value!,
        branchCode: branchCode,
        loanPurpose: selectedLoanPurpose.value!,
        customPurposeName: selectedLoanPurpose.value == LoanPurpose.other
            ? customPurposeController.text.trim()
            : null,
        disbursementMethod:
            selectedDisbursementMethod.value ?? DisbursementMethod.cash,
        accountNumber:
            selectedDisbursementMethod.value == DisbursementMethod.transfer
                ? selectedAccountNumber.value
                : null,
      );
      controller.updateLoanRequest(data);
    }

    // Khôi phục dữ liệu từ controller nếu có
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (controller.step5Data != null) {
          hasUserInteracted.value = false;
          final data = controller.step5Data!;
          hasCollateral.value = data.hasCollateral;
          ownCapitalController.text = CurrencyFormatter.formatCurrency(
              data.ownCapital > 0 ? data.ownCapital : 0);
          loanAmountController.text = CurrencyFormatter.formatCurrency(
              data.loanAmount > 0 ? data.loanAmount : 0);
          selectedLoanTerm.value = data.loanTerm;
          selectedLoanPurpose.value = data.loanPurpose;
          customPurposeController.text = data.customPurposeName ?? '';
          selectedDisbursementMethod.value = data.disbursementMethod;
          selectedAccountNumber.value = data.accountNumber;
          updateTotalNeed();
        }
      });
      return null;
    }, []);

    // Load loan terms nếu chưa có
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        /// init data lần đầu vào step
        if (controller.loanTerms.isEmpty && !controller.isLoadingLoanTerms) {
          await controller.loadLoanTerms();
        }
      });
      return null;
    }, [controller.loanTerms]);

    // Load loan purposes nếu chưa có
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (controller.loanPurposes.isEmpty &&
            !controller.isLoadingLoanPurposes) {
          await controller.loadLoanPurposes();
        }
      });
      return null;
    }, [controller.loanPurposes]);

    // Khi controller.selectedLoanTerm thay đổi, map sang enum
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (controller.selectedLoanTerm != null) {
          final mapped =
              LoanTermMapper.fromSystemConfig(controller.selectedLoanTerm!);
          if (mapped != null) {
            selectedLoanTerm.value = mapped;
          }
        }
      });
      return null;
    }, [controller.selectedLoanTerm]);

    // Khi controller.selectedLoanPurpose thay đổi, map sang enum
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (controller.selectedLoanPurpose != null) {
          final mapped = LoanPurposeMapper.fromSystemConfig(
              controller.selectedLoanPurpose!);
          if (mapped != null) {
            selectedLoanPurpose.value = mapped;
          }
        }
      });
      return null;
    }, [controller.selectedLoanPurpose]);
    // Theo dõi thay đổi của text controllers để trigger validate/callback
    useEffect(() {
      void listener() {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          updateTotalNeed();
          updateController();
        });
      }

      ownCapitalController.addListener(listener);
      loanAmountController.addListener(listener);
      customPurposeController.addListener(listener);
      return () {
        ownCapitalController.removeListener(listener);
        loanAmountController.removeListener(listener);
        customPurposeController.removeListener(listener);
      };
    }, []);

    // Theo dõi thay đổi của các state variables
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        updateController();
      });
      return null;
    }, [
      hasCollateral.value,
      selectedLoanTerm.value,
      selectedLoanPurpose.value,
      selectedDisbursementMethod.value,
      selectedAccountNumber.value,
    ]);

    // Theo dõi thay đổi để cập nhật tổng nhu cầu
    useEffect(() {
      updateTotalNeed();
      return null;
    }, [ownCapitalController.text, loanAmountController.text]);

    // Theo dõi thay đổi phương thức giải ngân để load danh sách tài khoản nếu cần
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (selectedDisbursementMethod.value == DisbursementMethod.transfer) {
          // Lấy số giấy tờ từ step2Data nếu có (ưu tiên CCCD/CMND)
          final idNumber = controller.step2Data?.documentNumber;
          if (idNumber != null &&
              controller.paymentAccounts.isEmpty &&
              !controller.isLoadingPaymentAccounts) {
            controller.loadPaymentAccounts(idNumber);
          }
        }
      });
      return null;
    }, [selectedDisbursementMethod.value]);

    Widget buildEditableField({
      required BuildContext context,
      required String label,
      required TextEditingController controller,
      required String fieldName,
      required String? Function(String?) validator,
      TextInputType? keyboardType,
      int maxLines = 1,
      List<TextInputFormatter>? inputFormatters,
      TextInputAction? textInputAction,
      required Function(String) onChanged,
    }) {
      return CommonTextField(
        label: label,
        controller: controller,
        enabled: true,
        keyboardType: keyboardType,
        maxLines: maxLines,
        inputFormatters: inputFormatters,
        textInputAction: textInputAction,
        isDense: true,
        onChanged: onChanged,
        validator: (value) {
          final actualValue =
              value?.isNotEmpty == true ? value : controller.text;
          final errorKey = validator(actualValue);
          return ValidationMessageMapper.getValidationMessage(
            context,
            errorKey,
            fieldName: label,
          );
        },
      );
    }

    Widget buildLoanPurposeDropdown() {
      final state = controller.isLoadingLoanPurposes
          ? DropdownState.loading
          : (controller.loanPurposes.isEmpty
              ? DropdownState.empty
              : DropdownState.normal);

      return CommonDropdown<SystemConfigEntity>(
        label: S.of(context).loan_purpose,
        items: controller.loanPurposes,
        value: controller.selectedLoanPurpose,
        state: state,
        emptyMessage: S.of(context).no_loan_purpose_data,
        onRetry: () => controller.loadLoanPurposes(),
        onChanged: (purposeConfig) {
          hasUserInteracted.value = true;
          final mapped = LoanPurposeMapper.fromSystemConfig(purposeConfig);
          if (mapped != null) {
            selectedLoanPurpose.value = mapped;
          }
          WidgetsBinding.instance.addPostFrameCallback((_) {
            controller.selectLoanPurpose(purposeConfig);
            updateController();
          });
        },
        labelBuilder: (purpose) =>
            (purpose as SystemConfigEntity).label.toString(),
        isDense: true,
        validator: (value) {
          final selected = value ?? controller.selectedLoanPurpose;
          if (selected == null) {
            return 'Vui lòng chọn mục đích sử dụng vốn';
          }
          return null;
        },
      );
    }

    Widget buildLoanTermDropdown() {
      final state = controller.isLoadingLoanTerms
          ? DropdownState.loading
          : (controller.loanTerms.isEmpty
              ? DropdownState.empty
              : DropdownState.normal);

      return CommonDropdown<SystemConfigEntity>(
        label: S.of(context).loan_term,
        items: controller.loanTerms,
        value: controller.selectedLoanTerm,
        state: state,
        emptyMessage: S.of(context).no_loan_term_data,
        onRetry: () => controller.loadLoanTerms(),
        onChanged: (item) {
          hasUserInteracted.value = true;
          final mapped = LoanTermMapper.fromSystemConfig(item);
          if (mapped != null) {
            selectedLoanTerm.value = mapped;
          }
          WidgetsBinding.instance.addPostFrameCallback((_) {
            controller.selectLoanTerm(item);
            updateController();
          });
        },
        validator: (value) {
          final selected = value ?? controller.selectedLoanTerm;
          if (selected == null) {
            return 'Vui lòng chọn thời hạn vay';
          }
          return null;
        },
        labelBuilder: (item) => item.label,
        isDense: true,
      );
    }

    Widget buildPaymentAccountDropdown() {
      if (controller.isLoadingPaymentAccounts) {
        return const Center(
          child: SizedBox(
            width: 32,
            height: 32,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        );
      }
      // if (controller.paymentAccounts.isEmpty) {
      //   return _buildEmptyStateWithRetry(
      //     context,
      //     S.of(context).no_payment_account_data,
      //     () {
      //       final idNumber = controller.step2Data?.documentNumber;
      //       if (idNumber != null) {
      //         controller.loadPaymentAccounts(idNumber);
      //       }
      //     },
      //   );
      // }
      return CommonDropdown<String>(
        label: S.of(context).recipient_account_number,
        items: controller.paymentAccounts.map((e) => e.accountNumber).toList(),
        value: selectedAccountNumber.value,
        onChanged: (account) {
          hasUserInteracted.value = true;
          selectedAccountNumber.value = account;
          controller.selectPaymentAccount(account);
          WidgetsBinding.instance.addPostFrameCallback((_) {
            updateController();
          });
        },
        labelBuilder: (account) {
          final acc = controller.paymentAccounts.firstWhere(
            (e) => e.accountNumber == account,
            orElse: () => PaymentAccountEntity(
                accountNumber: account ?? '', accountName: ''),
          );
          return '${acc.accountNumber} - ${acc.accountName}';
        },
        validator: (value) {
          final selected = value ?? selectedAccountNumber.value;
          if (selected == null) {
            return 'Vui lòng chọn tài khoản';
          }
          return null;
        },
        isDense: true,
      );
    }

    return SafeArea(
      child: GestureDetector(
        onTap: () {
          // Ẩn bàn phím khi tap ra ngoài
          FocusScope.of(context).unfocus();
        },
        child: SingleChildScrollView(
          padding: AppDimens.paddingAllLg,
          child: Form(
            key: formKey,
            // autovalidateMode: hasUserInteracted.value
            //     ? AutovalidateMode.onUserInteraction
            //     : AutovalidateMode.disabled,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Section: Hình thức vay vốn
                Text(
                  S.of(context).loan_type,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                AppDimens.h16,

                // Hình thức vay vốn
                CommonRadioGroup<bool>(
                  items: const [true, false],
                  value: hasCollateral.value,
                  onChanged: (value) {
                    hasUserInteracted.value = true;
                    hasCollateral.value = value ?? true;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                  labelBuilder: (value) => value
                      ? S.of(context).collateral_yes
                      : S.of(context).collateral_no,
                  isVertical: false,
                ),

                AppDimens.h24,

                // Section: Phương án vay vốn
                Text(
                  S.of(context).loan_plan,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),

                AppDimens.h16,

                // Vốn tự có (không bắt buộc)
                CommonTextField.currency(
                  label: S.of(context).own_capital,
                  controller: ownCapitalController,
                  isDense: true,
                  inputFormatters: [
                    CurrencyInputFormatter(),
                  ],
                  onChanged: (value) {
                    hasUserInteracted.value = true;
                    ownCapitalController.text = value;
                    updateTotalNeed();
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                  validator: (value) {
                    final actualValue = value?.isNotEmpty == true
                        ? value
                        : ownCapitalController.text;
                    return ValidationMessageMapper.getValidationMessage(
                      context,
                      ValidationUtils.validateOwnCapital(actualValue),
                      fieldName: S.of(context).own_capital,
                    );
                  },
                ),

                AppDimens.h8,

                // Số tiền đề nghị vay
                CommonTextField.currency(
                  label: S.of(context).loan_amount,
                  controller: loanAmountController,
                  isDense: true,
                  inputFormatters: [
                    CurrencyInputFormatter(),
                  ],
                  onChanged: (value) {
                    hasUserInteracted.value = true;
                    loanAmountController.text = value;
                    updateTotalNeed();
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                  validator: (value) {
                    final actualValue = value?.isNotEmpty == true
                        ? value
                        : loanAmountController.text;
                    return ValidationMessageMapper.getValidationMessage(
                      context,
                      ValidationUtils.validateLoanAmount(actualValue),
                      fieldName: S.of(context).loan_amount,
                    );
                  },
                ),

                AppDimens.h8,

                // Thời hạn vay
                buildLoanTermDropdown(),

                AppDimens.h8,

                // Tổng nhu cầu (read-only)
                CommonTextField.currency(
                  label: S.of(context).total_need,
                  controller: totalNeedController,
                  enabled: false,
                  isDense: true,
                  backgroundColor: AppColors.lightGray,
                ),

                AppDimens.h8,

                // CN/PGD (read-only)
                Builder(
                  builder: (context) {
                    final branchAsync = ref.watch(currentBranchProvider);
                    return branchAsync.when(
                      data: (branch) => CommonTextField(
                        label: S.of(context).branch_code,
                        controller:
                            TextEditingController(text: branch?.name ?? '---'),
                        enabled: false,
                        isDense: true,
                        backgroundColor: AppColors.lightGray,
                      ),
                      loading: () => CommonTextField(
                        label: S.of(context).branch_code,
                        controller: TextEditingController(text: ''),
                        enabled: false,
                        isDense: true,
                        backgroundColor: AppColors.lightGray,
                      ),
                      error: (err, stack) => CommonTextField(
                        label: S.of(context).branch_code,
                        controller: TextEditingController(text: '---'),
                        enabled: false,
                        isDense: true,
                        backgroundColor: AppColors.lightGray,
                      ),
                    );
                  },
                ),

                AppDimens.h8,

                // Phương thức vay (read-only)
                CommonTextField(
                  label: S.of(context).loan_method,
                  controller: TextEditingController(
                      text: S.of(context).installment_loan),
                  enabled: false,
                  isDense: true,
                  backgroundColor: AppColors.lightGray,
                ),

                AppDimens.h8,

                // Mục đích sử dụng vốn
                buildLoanPurposeDropdown(),
                if (selectedLoanPurpose.value == LoanPurpose.other) ...[
                  AppDimens.h8,
                  buildEditableField(
                    context: context,
                    label: S.of(context).custom_purpose_name,
                    controller: customPurposeController,
                    fieldName: 'customPurposeName',
                    // validator: (value) => ValidationUtils.validateRequired(value, S.of(context).custom_purpose_name),
                    textInputAction: TextInputAction.next,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(100),
                    ],

                    validator: (value) {
                      final actualValue = value?.isNotEmpty == true
                          ? value
                          : customPurposeController.text;
                      return ValidationMessageMapper.getValidationMessage(
                        maxLength: 100,
                        context,
                        ValidationUtils.validateRequired(actualValue, '',
                            maxLength: 100),
                        fieldName: 'tên mục đích',
                      );
                    },
                    onChanged: (value) {
                      hasUserInteracted.value = true;
                      customPurposeController.text = value;
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        updateController();
                      });
                    },
                  ),
                ],

                AppDimens.h8,

                // Hình thức trả nợ (read-only)
                CommonTextField(
                  label: S.of(context).repayment_method,
                  controller: TextEditingController(
                      text: S.of(context).daily_repayment),
                  enabled: false,
                  isDense: true,
                  backgroundColor: AppColors.lightGray,
                ),

                AppDimens.h8,

                // Phương thức giải ngân
                CommonRadioGroup<DisbursementMethod>(
                  label: S.of(context).disbursement_method,
                  items: DisbursementMethod.values,
                  value: selectedDisbursementMethod.value,
                  onChanged: (method) {
                    hasUserInteracted.value = true;
                    selectedDisbursementMethod.value = method;
                    if (method == DisbursementMethod.cash) {
                      selectedAccountNumber.value = null;
                    }
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                  labelBuilder: (method) => method == DisbursementMethod.cash
                      ? S.of(context).cash
                      : S.of(context).transfer,
                  isVertical: false,
                ),

                // Số tài khoản nhận tiền (hiển thị khi chọn chuyển khoản)
                if (selectedDisbursementMethod.value ==
                    DisbursementMethod.transfer) ...[
                  AppDimens.h8,
                  buildPaymentAccountDropdown(),
                ],

                AppDimens.h16,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
