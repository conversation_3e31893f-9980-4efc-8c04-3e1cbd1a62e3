import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/di/injection.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/services/file_picker_service.dart';

import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';

import 'package:sales_app/presentation/models/loan/steps/steps.dart';
import 'package:sales_app/presentation/models/models.dart';

import 'package:sales_app/presentation/screens/image_capture/image_review_screen.dart';
import 'package:sales_app/presentation/screens/image_capture/document_camera_screen.dart';

import 'dart:io';

// Custom painter để vẽ viền nét đứt
class DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;
  final BorderRadius borderRadius;

  DashedBorderPainter({
    required this.color,
    this.strokeWidth = 1.0,
    this.dashWidth = 5.0,
    this.dashSpace = 3.0,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndCorners(
        Rect.fromLTWH(0, 0, size.width, size.height),
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      ));

    final dashPath = _createDashedPath(path, dashWidth, dashSpace);
    canvas.drawPath(dashPath, paint);
  }

  Path _createDashedPath(Path source, double dashWidth, double dashSpace) {
    final Path dashedPath = Path();
    final pathMetrics = source.computeMetrics();

    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      bool draw = true;

      while (distance < pathMetric.length) {
        final double length = draw ? dashWidth : dashSpace;
        if (distance + length > pathMetric.length) {
          if (draw) {
            dashedPath.addPath(
              pathMetric.extractPath(distance, pathMetric.length),
              Offset.zero,
            );
          }
          break;
        } else {
          if (draw) {
            dashedPath.addPath(
              pathMetric.extractPath(distance, distance + length),
              Offset.zero,
            );
          }
          distance += length;
          draw = !draw;
        }
      }
    }
    return dashedPath;
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class Step9DocumentListScreen extends HookConsumerWidget {
  final void Function(Step9DocumentListData data) onStepCompleted;

  const Step9DocumentListScreen({
    super.key,
    required this.onStepCompleted,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(createLoanControllerProvider.notifier);

    // State variables for document lists
    final marriageDocuments = useState<List<String>>([]);
    final residenceDocuments = useState<List<String>>([]);
    final vehicleAppraisalDocuments = useState<List<String>>([]);
    final vehicleRegistrationDocuments = useState<List<String>>([]);
    final businessLicenseDocuments = useState<List<String>>([]);

    // Theo dõi validation attempt từ controller
    final lastValidationAttempt = useRef<int>(0);

    // Khôi phục dữ liệu từ controller nếu có
    useEffect(() {
      Future.microtask(() async {
        if (controller.step9Data != null) {
          final data = controller.step9Data!;
          marriageDocuments.value = List.from(data.marriageDocuments);
          residenceDocuments.value = List.from(data.residenceDocuments);
          vehicleAppraisalDocuments.value =
              List.from(data.vehicleAppraisalDocuments);
          vehicleRegistrationDocuments.value =
              List.from(data.vehicleRegistrationDocuments);
          businessLicenseDocuments.value =
              List.from(data.businessLicenseDocuments);
        }
      });
      return null;
    }, []);

    // Cập nhật lastValidationAttempt khi có validation attempt mới
    useEffect(() {
      if (controller.validationAttempt > lastValidationAttempt.value) {
        lastValidationAttempt.value = controller.validationAttempt;
      }
      return null;
    }, [controller.validationAttempt]);

    // Validation và gọi callback
    void handleComplete() {
      // Kiểm tra các trường bắt buộc theo SRS
      if (marriageDocuments.value.isEmpty ||
          residenceDocuments.value.isEmpty ||
          vehicleAppraisalDocuments.value.isEmpty ||
          vehicleRegistrationDocuments.value.isEmpty) {
        return;
      }

      final data = Step9DocumentListData(
        marriageDocuments: marriageDocuments.value,
        residenceDocuments: residenceDocuments.value,
        vehicleAppraisalDocuments: vehicleAppraisalDocuments.value,
        vehicleRegistrationDocuments: vehicleRegistrationDocuments.value,
        businessLicenseDocuments: businessLicenseDocuments.value,
      );

      onStepCompleted(data);
    }

    // Helper method để thêm documents
    void addDocuments(
        ValueNotifier<List<String>> documentList, List<String> paths) {
      documentList.value = [...documentList.value, ...paths];
      handleComplete();
    }

    // Helper method để xóa document
    void removeDocument(ValueNotifier<List<String>> documentList, int index) {
      final newList = List<String>.from(documentList.value);
      newList.removeAt(index);
      documentList.value = newList;
      handleComplete();
    }

    // Helper method để xem và quản lý documents
    void viewDocuments(BuildContext context,
        ValueNotifier<List<String>> documentList, String title) {
      if (documentList.value.isEmpty) return;

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ImageReviewScreen(
            title: title,
            imagePaths: documentList.value,
            onImagesUpdated: (updatedPaths) {
              documentList.value = updatedPaths;
              handleComplete();
            },
          ),
        ),
      );
    }

    return SafeArea(
      child: SingleChildScrollView(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // GTTT người vay chính (read-only)
            _buildReadOnlySection(
              context,
              'GTTT người vay chính',
              // state.step1Data != null
              //     ? [
              //         state.step1Data!.,
              //         if (state.step1Data!.backImagePath != null)
              //           state.step1Data!.backImagePath!
              //       ]
              //     : 
                  [],
            ),

            AppDimens.h16,

            // GTTT người đồng vay (read-only, nếu có)
            if (controller.step2Data?.hasCoBorrower == true) ...[
              _buildReadOnlySection(
                context,
                'GTTT người đồng vay',
                controller.step3Data != null
                    ? [
                        controller.step3Data!.frontImagePath??'',
                        if (controller.step3Data!.backImagePath != null)
                          controller.step3Data!.backImagePath!
                      ]
                    : [],
              ),
              AppDimens.h16,
            ],

            // Giấy tờ tình trạng hôn nhân, mối quan hệ
            _buildDocumentSection(
              context,
              'Giấy tờ tình trạng hôn nhân, mối quan hệ',
              marriageDocuments,
                controller,
              isRequired: true,
              onAdd: (paths) => addDocuments(marriageDocuments, paths),
              onRemove: (index) => removeDocument(marriageDocuments, index),
              onView: () => viewDocuments(
                  context, marriageDocuments, 'Giấy tờ tình trạng hôn nhân'),
            ),

            AppDimens.h16,

            // Giấy tờ chứng minh nơi cư trú
            _buildDocumentSection(
              context,
              'Giấy tờ chứng minh nơi cư trú',
              residenceDocuments,
                controller,
              isRequired: true,
              onAdd: (paths) => addDocuments(residenceDocuments, paths),
              onRemove: (index) => removeDocument(residenceDocuments, index),
              onView: () => viewDocuments(
                  context, residenceDocuments, 'Giấy tờ chứng minh nơi cư trú'),
            ),

            AppDimens.h16,

            // Tờ trình thẩm định xe mô tô, gắn máy
            _buildDocumentSection(
              context,
              'Tờ trình thẩm định xe mô tô, gắn máy',
              vehicleAppraisalDocuments,
                controller,
              isRequired: true,
              onAdd: (paths) => addDocuments(vehicleAppraisalDocuments, paths),
              onRemove: (index) =>
                  removeDocument(vehicleAppraisalDocuments, index),
              onView: () => viewDocuments(
                  context, vehicleAppraisalDocuments, 'Tờ trình thẩm định xe'),
            ),

            AppDimens.h16,

            // Giấy đăng ký xe
            _buildDocumentSection(
              context,
              'Giấy đăng ký xe',
              vehicleRegistrationDocuments,
                controller,
              isRequired: true,
              onAdd: (paths) =>
                  addDocuments(vehicleRegistrationDocuments, paths),
              onRemove: (index) =>
                  removeDocument(vehicleRegistrationDocuments, index),
              onView: () => viewDocuments(
                  context, vehicleRegistrationDocuments, 'Giấy đăng ký xe'),
            ),

            AppDimens.h16,

            // Chứng nhận hộ kinh doanh, khác (không bắt buộc)
            _buildDocumentSection(
              context,
              'Chứng nhận hộ kinh doanh, khác (nếu có)',
              businessLicenseDocuments,
                controller,
              isRequired: false,
              onAdd: (paths) => addDocuments(businessLicenseDocuments, paths),
              onRemove: (index) =>
                  removeDocument(businessLicenseDocuments, index),
              onView: () => viewDocuments(context, businessLicenseDocuments,
                  'Chứng nhận hộ kinh doanh'),
            ),
          ],
        ),
      ),
    );
  }

  // Widget để hiển thị section read-only (GTTT)
  Widget _buildReadOnlySection(
      BuildContext context, String title, List<String> imagePaths) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        AppDimens.h8,
        if (imagePaths.isNotEmpty)
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: imagePaths.length > 3 ? 3 : imagePaths.length, // Giới hạn tối đa 3 ảnh
              itemBuilder: (context, index) {
                return Container(
                  margin: EdgeInsets.only(right: AppDimens.spacingSM),
                  width: 120,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: AppDimens.borderRadius8,
                    border: Border.all(
                        color: AppColors.primaryColor.withValues(alpha: 0.3)),
                  ),
                  child: ClipRRect(
                    borderRadius: AppDimens.borderRadius8,
                    child: Image.file(
                      File(imagePaths[index]),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color: AppColors.lightGray,
                        child: const Icon(
                          Icons.image_not_supported,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          )
        else
          Container(
            height: 80,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.lightGray,
              borderRadius: AppDimens.borderRadius8,
            ),
            child: Center(
              child: Text(
                'Chưa có ảnh',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ),
      ],
    );
  }

  // Widget để hiển thị section upload documents
  Widget _buildDocumentSection(
    BuildContext context,
    String title,
    ValueNotifier<List<String>> documents,
    CreateLoanController controller, {
    required bool isRequired,
    required Function(List<String>) onAdd,
    required Function(int) onRemove,
    required VoidCallback onView,
  }) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (isRequired) ...[
              const SizedBox(width: 4),
              Text(
                '*',
                style: TextStyle(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
        AppDimens.h8,

        // Upload button - chỉ hiển thị khi chưa có file
        if (documents.value.isEmpty) ...[
          GestureDetector(
            onTap: () => _showUploadOptions(context, onAdd),
            child: CustomPaint(
              painter: DashedBorderPainter(
                color: isRequired && documents.value.isEmpty && controller.validationAttempt > 0
                    ? theme.colorScheme.error
                    : AppColors.primaryColor.withValues(alpha: 0.5),
                strokeWidth: 1.5,
                dashWidth: 8.0,
                dashSpace: 6.0,
                borderRadius: AppDimens.borderRadius8,
              ),
              child: Container(
                height: 60,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.05),
                  borderRadius: AppDimens.borderRadius8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.cloud_upload_outlined,
                      color: AppColors.primaryColor,
                      size: AppDimens.iconMD,
                    ),
                    AppDimens.w8,
                    Text(
                      'Chạm để tải lên tệp',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],

        // Error message for required fields
        if (isRequired &&
            documents.value.isEmpty &&
            controller.validationAttempt > 0) ...[
          AppDimens.h4,
          Text(
            'Bắt buộc nhập',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],

        // Display uploaded documents - hiển thị trực tiếp không bọc container
        if (documents.value.isNotEmpty) ...[
          AppDimens.h8,
          // Preview images grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: documents.value.length > 3 ? 3 : documents.value.length, // Giới hạn tối đa 3 ảnh
            itemBuilder: (context, index) {
              final path = documents.value[index];
              final isImage = path.toLowerCase().endsWith('.png') ||
                  path.toLowerCase().endsWith('.jpg') ||
                  path.toLowerCase().endsWith('.jpeg');

              return GestureDetector(
                onTap: onView,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: AppDimens.borderRadius8,
                    border: Border.all(
                      color: AppColors.primaryColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: AppDimens.borderRadius8,
                    child: isImage
                        ? Image.file(
                            File(path),
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Container(
                              color: AppColors.lightGray,
                              child: const Icon(
                                Icons.image_not_supported,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          )
                        : Container(
                            color: AppColors.primaryColor.withValues(alpha: 0.1),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.description,
                                  color: AppColors.primaryColor,
                                  size: AppDimens.iconMD,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'PDF',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: AppColors.primaryColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ),
                ),
              );
            },
          ),

          // Action row
          AppDimens.h8,
          GestureDetector(
            onTap: onView,
            child: Row(
              children: [
                Icon(
                  Icons.visibility_outlined,
                  color: AppColors.primaryColor,
                  size: AppDimens.iconSM,
                ),
                AppDimens.w8,
                Text(
                  'Xem tất cả ${documents.value.length} tệp',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.primaryColor,
                  size: AppDimens.iconSM,
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  // Show upload options bottom sheet
  void _showUploadOptions(BuildContext context, Function(List<String>) onAdd) {
    final filePickerService = getIt<FilePickerService>();

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
            top: Radius.circular(AppDimens.borderRadius16.topLeft.x)),
      ),
      builder: (context) => Container(
        padding: AppDimens.paddingAllLg,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Chọn hình thức tải lên',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            AppDimens.h16,

            // Chụp ảnh - Mở camera để chụp ảnh
            ListTile(
              leading: const Icon(Icons.camera_alt, color: AppColors.primaryColor),
              title: const Text('Chụp ảnh'),
              subtitle: const Text('Mở camera để chụp nhiều ảnh'),
              onTap: () {
                Navigator.pop(context);

                // Mở màn hình camera để chụp nhiều ảnh
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => DocumentCameraScreen(
                      title: 'Chụp chứng từ',
                      maxImages: 10,
                      onImagesSelected: (imagePaths) {
                        onAdd(imagePaths);
                      },
                    ),
                  ),
                );
              },
            ),

            // Chọn ảnh từ thư viện
            ListTile(
              leading: const Icon(Icons.photo_library, color: AppColors.primaryColor),
              title: const Text('Chọn ảnh trong thư viện'),
              subtitle: const Text('Chọn nhiều ảnh từ thư viện'),
              onTap: () async {
                Navigator.pop(context);

                // Chọn nhiều ảnh từ thư viện
                final imagePaths = await filePickerService.pickImages(
                  maxSizeInMB: 5,
                  allowMultiple: true,
                );

                if (imagePaths != null && imagePaths.isNotEmpty) {
                  onAdd(imagePaths);
                }
              },
            ),

            // Chọn tệp tin
            ListTile(
              leading: const Icon(Icons.attach_file, color: AppColors.primaryColor),
              title: const Text('Chọn tệp tin'),
              subtitle: const Text('PNG, JPG, PDF (tối đa 5MB)'),
              onTap: () async {
                Navigator.pop(context);

                // Chọn file documents (ảnh + PDF)
                final filePaths = await filePickerService.pickDocuments(
                  maxSizeInMB: 5,
                  allowMultiple: true,
                );

                if (filePaths != null && filePaths.isNotEmpty) {
                  onAdd(filePaths);
                }
              },
            ),

            AppDimens.h8,

            // Hủy bỏ
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Hủy bỏ'),
            ),
          ],
        ),
      ),
    );
  }
}
