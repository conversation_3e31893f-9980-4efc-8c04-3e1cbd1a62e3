
// Flutter/Dart imports
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

// Third-party imports
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Internal imports
import 'package:sales_app/core/core.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';
import 'package:sales_app/presentation/models/models.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';

class Step1IdentityDocumentScreen extends HookConsumerWidget {
  const Step1IdentityDocumentScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final controller = ref.read(createLoanControllerProvider.notifier);

    final selectedDocType = useState<DocumentType>(
      controller.step1Data?.documentType ?? DocumentType.cccd
    );
    final frontImagePath = useState<String?>(
      controller.step1Data?.frontImagePath
    );
    final backImagePath = useState<String?>(
      controller.step1Data?.backImagePath
    );

    final hasCompleted = useRef(false);

    void updateController() {
      final step1Data = Step1DocumentData(
        documentType: selectedDocType.value,
        frontImagePath: frontImagePath.value,
        backImagePath: backImagePath.value,
        // URL sẽ được cập nhật sau khi upload thành công
        frontImageUrl: null,
        backImageUrl: null,
      );

      final isComplete = step1Data.isReadyForUpload;
      
      if (isComplete && !hasCompleted.value) {
        hasCompleted.value = true;
        controller.updateStep1Data(step1Data);
      } else if (!isComplete) {
        hasCompleted.value = false;
      }
    }

    return SafeArea(
      child: SingleChildScrollView(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              S.of(context).step1_identity_document_description,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            AppDimens.h24,
            CommonDropdown<DocumentType>(
              label: S.of(context).document_type_label,
              items: DocumentType.values,
              value: selectedDocType.value,
              isRequired: false,
              labelBuilder: (type) => type.label,
              onChanged: (type) {
                selectedDocType.value = type;
                frontImagePath.value = null;
                backImagePath.value = null;
                // Clear luôn data step1 trong controller
                controller.updateStep1Data(Step1DocumentData(documentType: type));
                updateController();
              },
            ),
            AppDimens.h24,
            DocumentImagePicker(
              label: (selectedDocType.value != DocumentType.passport)
                  ? S.of(context).document_front_side_label
                  : S.of(context).document_info_label,
              imagePath: frontImagePath.value,
              onImageSelected: (path) {
                frontImagePath.value = path;
                updateController();
              },
              documentType: selectedDocType.value,
            ),
            AppDimens.h16,
            if (selectedDocType.value != DocumentType.passport)
              DocumentImagePicker(
                label: S.of(context).document_back_side_label,
                imagePath: backImagePath.value,
                onImageSelected: (path) {
                  backImagePath.value = path;
                  updateController();
                },
                documentType: selectedDocType.value,
              ),
          ],
        ),
      ),
    );
  }
} 