import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/enums/enums.dart';
import 'package:sales_app/core/utils/validation_utils.dart';
import 'package:sales_app/core/utils/app_logger.dart';
import 'package:sales_app/core/utils/date_parser.dart';
import 'package:sales_app/core/utils/marital_status_mapper.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';

import 'package:sales_app/presentation/utils/validation_message_mapper.dart';
import 'package:sales_app/core/utils/input_formatters.dart';

import '../../../../models/models.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/domain/domain.dart';

class Step2BorrowerInfoScreen extends HookConsumerWidget {
  final GlobalKey<FormState> formKey;
  const Step2BorrowerInfoScreen({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final controller = ref.read(createLoanControllerProvider.notifier);

    // Controllers cho các text field
    final fullNameController = useTextEditingController();
    final documentNumberController = useTextEditingController();
    final issuePlaceController = useTextEditingController();
    final phoneController = useTextEditingController();
    final specificAddressController = useTextEditingController();
    final permanentAddressController = useTextEditingController();

    // State variables
    final hasCoBorrower = useState<bool>(false);
    final selectedGender = useState<Gender>(Gender.male);
    final selectedMaritalStatus = useState<MaritalStatus>(MaritalStatus.single);
    final issueDate = useState<DateTime?>(null);
    final expiryDate = useState<DateTime?>(null);
    final birthDate = useState<DateTime?>(null);
    final sameAsPermAddress = useState<bool>(true);
    final selectedProvince = useState<ProvinceEntity?>(null);
    final selectedWard = useState<WardEntity?>(null);

    // Track xem user đã tương tác với form chưa
    final hasUserInteracted = useState<bool>(false);

    // Track xem đã set mặc định chưa để tránh set lại
    final hasSetDefaultMaritalStatus = useState<bool>(false);

    // Khôi phục dữ liệu từ controller nếu có và load data cần thiết
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        // Load marital statuses nếu chưa có
        if (controller.maritalStatuses.isEmpty &&
            !controller.isLoadingMaritalStatuses) {
          controller.loadMaritalStatuses();
        }

        // Load provinces nếu chưa có (cho phần địa chỉ hiện tại)
        if (controller.provinces.isEmpty && !controller.isLoadingProvinces) {
          controller.loadProvinces();
        }

        // Khôi phục dữ liệu từ controller nếu có
        if (controller.step2Data != null) {
          // Reset user interaction flag khi khôi phục data
          hasUserInteracted.value = false;
          final d = controller.step2Data!;
          hasCoBorrower.value = d.hasCoBorrower;
          fullNameController.text = d.fullName;
          documentNumberController.text = d.documentNumber;
          issueDate.value = d.issueDate;
          expiryDate.value = d.expiryDate;
          issuePlaceController.text = d.issuePlace;
          birthDate.value = d.birthDate;
          selectedGender.value = d.gender;
          permanentAddressController.text = d.permanentAddress;
          selectedMaritalStatus.value = d.maritalStatus;

          // Sync selectedMaritalStatus với controller nếu có
          if (d.maritalStatus != MaritalStatus.single &&
              controller.maritalStatuses.isNotEmpty) {
            final maritalStatusEntity = MaritalStatusMapper.toSystemConfig(
              d.maritalStatus,
              controller.maritalStatuses,
            );
            if (maritalStatusEntity != null) {
              controller.selectMaritalStatus(maritalStatusEntity);
            } else {
              AppLogger.warning('Marital status not found in list', data: {
                'maritalStatus': d.maritalStatus.name,
                'availableStatuses':
                    controller.maritalStatuses.map((e) => e.code).toList(),
              });
            }
          }
          phoneController.text = d.phoneNumber;
          sameAsPermAddress.value = d.sameAsPermAddress;
          specificAddressController.text = d.specificAddress ?? '';
        }
      });
      return null;
    }, []);

    void updateController() {
      final step2Data = Step2BorrowerData(
        hasCoBorrower: hasCoBorrower.value,
        fullName: fullNameController.text.trim(),
        documentNumber: documentNumberController.text.trim(),
        issueDate: issueDate.value,
        expiryDate: expiryDate.value,
        issuePlace: issuePlaceController.text.trim(),
        birthDate: birthDate.value,
        gender: selectedGender.value,
        permanentAddress: permanentAddressController.text.trim(),
        maritalStatus: selectedMaritalStatus.value,
        phoneNumber: phoneController.text.trim(),
        sameAsPermAddress: sameAsPermAddress.value,
        province: selectedProvince.value?.id,
        ward: selectedWard.value?.id,
        specificAddress: specificAddressController.text.trim().isEmpty
            ? null
            : specificAddressController.text.trim(),
      );

      controller.updateBorrowerInfo(step2Data);
    }

    // Map dữ liệu từ QR scan (CCCDInfoEntity) vào các field nếu có
    useEffect(() {
      final cccdInfo = controller.step1Data?.cccdInfo;
      AppLogger.info('Checking for CCCDInfoEntity', data: {
        'hasStep1Data': controller.step1Data != null,
        'hasCCCDInfo': cccdInfo != null,
        'cccdInfoFullName': cccdInfo?.fullName,
        'cccdInfoDateOfBirth': cccdInfo?.dateOfBirth,
        'cccdInfoIssueDate': cccdInfo?.issueDate,
      });

      if (cccdInfo != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Reset user interaction flag khi map data từ QR
          hasUserInteracted.value = false;

          AppLogger.info('Mapping QR scan data to Step2 fields', data: {
            'fullName': cccdInfo.fullName,
            'idNumber': cccdInfo.idNumber,
            'address': cccdInfo.address,
            'dateOfBirth': cccdInfo.dateOfBirth,
            'issueDate': cccdInfo.issueDate,
            'issuePlace': cccdInfo.issuePlace,
            'gender': cccdInfo.gender,
          });

          // Map các field từ CCCDInfoEntity
          if (fullNameController.text.isEmpty && cccdInfo.fullName.isNotEmpty) {
            fullNameController.text = cccdInfo.fullName;
          }

          if (documentNumberController.text.isEmpty &&
              cccdInfo.idNumber.isNotEmpty) {
            documentNumberController.text = cccdInfo.idNumber;
          }

          if (permanentAddressController.text.isEmpty &&
              cccdInfo.address.isNotEmpty) {
            permanentAddressController.text = cccdInfo.address;
          }

          if (issuePlaceController.text.isEmpty &&
              cccdInfo.issuePlace.isNotEmpty) {
            issuePlaceController.text = cccdInfo.issuePlace;
          }

          // Map ngày sinh từ CCCDInfoEntity (giống logic cũ)
          if (birthDate.value == null && cccdInfo.dateOfBirth.isNotEmpty) {
            final parsedBirthDate = DateParser.parseDate(cccdInfo.dateOfBirth);
            if (parsedBirthDate != null) {
              birthDate.value = parsedBirthDate;
            }
          }

          // Map ngày cấp từ CCCDInfoEntity (giống logic cũ)
          if (issueDate.value == null && cccdInfo.issueDate.isNotEmpty) {
            final parsedIssueDate = DateParser.parseDate(cccdInfo.issueDate);
            if (parsedIssueDate != null) {
              issueDate.value = parsedIssueDate;
            }
          }

          // Map giới tính từ CCCDInfoEntity
          selectedGender.value = DateParser.parseGenderFromCCCD(cccdInfo);

          // Trigger update controller sau khi map data
          updateController();
        });
      }
      return null;
    }, [controller.step1Data?.cccdInfo]);

    // Sync selectedProvince, selectedWard và selectedMaritalStatus từ controller data
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        selectedProvince.value = controller.selectedProvince;
        selectedWard.value = controller.selectedWard;

        // Sync marital status từ controller nếu có
        if (controller.selectedMaritalStatus != null) {
          final maritalStatus = MaritalStatusMapper.fromSystemConfig(
              controller.selectedMaritalStatus!);
          if (maritalStatus != null) {
            selectedMaritalStatus.value = maritalStatus;
          } else {
            // Fallback về single nếu không tìm thấy
            selectedMaritalStatus.value = MaritalStatus.single;
          }
        }
      });
      return null;
    }, [
      controller.selectedProvince,
      controller.selectedWard,
      controller.selectedMaritalStatus
    ]);

    // Set mặc định marital status khi load lần đầu
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Chỉ set mặc định khi chưa có selection, đã load xong data và chưa set mặc định
        if (controller.selectedMaritalStatus == null &&
            controller.maritalStatuses.isNotEmpty &&
            !hasSetDefaultMaritalStatus.value) {
          final defaultConfig = MaritalStatusMapper.getDefaultSystemConfig(
              controller.maritalStatuses);
          if (defaultConfig != null) {
            controller.selectMaritalStatus(defaultConfig);
            selectedMaritalStatus.value = MaritalStatus.single;
            hasSetDefaultMaritalStatus.value = true;
          }
        }
      });
      return null;
    }, [controller.maritalStatuses]);

    // Theo dõi thay đổi của text controllers để trigger validate/callback
    useEffect(() {
      void listener() {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          updateController();
        });
      }

      fullNameController.addListener(listener);
      documentNumberController.addListener(listener);
      issuePlaceController.addListener(listener);
      phoneController.addListener(listener);
      permanentAddressController.addListener(listener);
      specificAddressController.addListener(listener);

      return () {
        fullNameController.removeListener(listener);
        documentNumberController.removeListener(listener);
        issuePlaceController.removeListener(listener);
        phoneController.removeListener(listener);
        permanentAddressController.removeListener(listener);
        specificAddressController.removeListener(listener);
      };
    }, []);

    Widget buildEditableField({
      required BuildContext context,
      required String label,
      required TextEditingController controller,
      required String fieldName,
      required String? Function(String?) validator,
      TextInputType? keyboardType,
      int maxLines = 1,
      List<TextInputFormatter>? inputFormatters,
      TextInputAction? textInputAction,
      required Function(String) onChanged,
    }) {
      return CommonTextField(
        label: label,
        controller: controller,
        enabled: true,
        keyboardType: keyboardType,
        maxLines: maxLines,
        inputFormatters: inputFormatters,
        textInputAction: textInputAction,
        isDense: true,
        onChanged: onChanged,
        validator: (value) {
          final actualValue =
              value?.isNotEmpty == true ? value : controller.text;
          final errorKey = validator(actualValue);
          return ValidationMessageMapper.getValidationMessage(
            context,
            errorKey,
            fieldName: label,
          );
        },
      );
    }

    return SafeArea(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: SingleChildScrollView(
          padding: AppDimens.paddingAllLg,
          child: Form(
            key: formKey,
            // autovalidateMode: hasUserInteracted.value
            //     ? AutovalidateMode.onUserInteraction
            //     : AutovalidateMode.disabled,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Toggle có người đồng vay
                CommonToggle(
                  textStyle: theme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                  label: S.of(context).has_co_borrower,
                  value: hasCoBorrower.value,
                  onChanged: (value) {
                    hasCoBorrower.value = value;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                  isRequired: true,
                ),

                AppDimens.h24,

                // Section: Thông tin nhận dạng giấy tờ
                Text(
                  S.of(context).identity_document_info,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),

                AppDimens.h16,

                // Họ và tên
                buildEditableField(
                  context: context,
                  label: S.of(context).full_name,
                  controller: fullNameController,
                  fieldName: 'fullName',
                  validator: ValidationUtils.validateFullName,
                  textInputAction: TextInputAction.next,
                  inputFormatters: nameInputFormatters,
                  onChanged: (value) {
                    hasUserInteracted.value = true;
                    fullNameController.text = value;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                ),

                AppDimens.h8,

                // Số giấy tờ
                buildEditableField(
                  context: context,
                  label: S.of(context).document_number,
                  controller: documentNumberController,
                  fieldName: 'documentNumber',
                  validator: (controller.step1Data?.documentType ==
                          DocumentType.passport)
                      ? ValidationUtils.validatePassportNumber
                      : ValidationUtils.validateIdNumber,
                  keyboardType: (controller.step1Data?.documentType ==
                          DocumentType.passport)
                      ? TextInputType.text
                      : TextInputType.number,
                  textInputAction: TextInputAction.done,
                  onChanged: (value) {
                    hasUserInteracted.value = true;
                    documentNumberController.text = value;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                ),

                AppDimens.h8,

                // Ngày cấp
                CommonDatePicker(
                  label: S.of(context).issue_date,
                  onDateSelected: (date) {
                    hasUserInteracted.value = true;
                    issueDate.value = date;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                  selectedDate: issueDate.value,
                  isRequired: true,
                  lastDate: DateTime.now(),
                  isDense: true,
                  validator: (_) {
                    // Nếu đã có giá trị (từ QR hoặc user chọn), không báo lỗi
                    if (issueDate.value != null) return null;
                    return ValidationMessageMapper.getValidationMessage(
                      context,
                      ValidationUtils.validateIssueDate(issueDate.value),
                      fieldName: S.of(context).issue_date,
                    );
                  },
                ),

                AppDimens.h8,

                // Ngày hết hạn
                CommonDatePicker(
                  label: S.of(context).expiry_date,
                  onDateSelected: (date) {
                    hasUserInteracted.value = true;
                    expiryDate.value = date;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                  selectedDate: expiryDate.value,
                  isRequired: true,
                  firstDate: DateTime.now(),
                  isDense: true,
                  validator: (_) {
                    if (expiryDate.value != null) return null;
                    return ValidationMessageMapper.getValidationMessage(
                      context,
                      ValidationUtils.validateExpiryDate(expiryDate.value),
                      fieldName: S.of(context).expiry_date,
                    );
                  },
                ),

                AppDimens.h8,
                // Nơi cấp
                buildEditableField(
                  context: context,
                  label: S.of(context).issue_place,
                  controller: issuePlaceController,
                  fieldName: 'issuePlace',
                  validator: ValidationUtils.validateIssuePlace,
                  textInputAction: TextInputAction.next,
                  inputFormatters: issuePlaceInputFormatters,
                  onChanged: (value) {
                    hasUserInteracted.value = true;
                    issuePlaceController.text = value;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                ),

                AppDimens.h8,

                // Ngày sinh
                CommonDatePicker(
                  label: S.of(context).birth_date,
                  selectedDate: birthDate.value,
                  isRequired: true,
                  onDateSelected: (date) {
                    hasUserInteracted.value = true;
                    birthDate.value = date;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                  lastDate: DateTime.now(),
                  isDense: true,
                  validator: (_) {
                    if (birthDate.value != null) return null;
                    return ValidationMessageMapper.getValidationMessage(
                      context,
                      ValidationUtils.validateBirthDate(birthDate.value),
                      fieldName: S.of(context).birth_date,
                    );
                  },
                ),

                AppDimens.h8,

                // Giới tính
                CommonDropdown<Gender>(
                  label: S.of(context).gender,
                  onChanged: (gender) {
                    hasUserInteracted.value = true;
                    selectedGender.value = gender;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                  items: Gender.values,
                  value: selectedGender.value,
                  labelBuilder: (gender) => gender.label,
                  isRequired: false,
                  isDense: true,
                ),

                AppDimens.h8,

                // Địa chỉ thường trú
                buildEditableField(
                  context: context,
                  label: S.of(context).permanent_address,
                  controller: permanentAddressController,
                  fieldName: 'permanentAddress',
                  validator: ValidationUtils.validateAddress,
                  textInputAction: TextInputAction.next,
                  inputFormatters: permanentAddressInputFormatters,
                  maxLines: 3,
                  onChanged: (value) {
                    hasUserInteracted.value = true;
                    permanentAddressController.text = value;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                ),

                AppDimens.h24,

                // Section: Thông tin cá nhân
                Text(
                  S.of(context).personal_info,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),

                AppDimens.h16,

                // Tình trạng hôn nhân
                _buildMaritalStatusSelection(context, ref,
                    selectedMaritalStatus, updateController, hasUserInteracted),

                AppDimens.h8,
                // Số điện thoại
                buildEditableField(
                  context: context,
                  label: S.of(context).phone_number,
                  controller: phoneController,
                  fieldName: 'phoneNumber',
                  validator: ValidationUtils.validatePhone,
                  keyboardType: TextInputType.phone,
                  textInputAction: TextInputAction.next,
                  inputFormatters: phoneInputFormatters,
                  onChanged: (value) {
                    hasUserInteracted.value = true;
                    phoneController.text = value;
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      updateController();
                    });
                  },
                ),

                AppDimens.h8,

                // Checkbox địa chỉ hiện tại trùng với thường trú
                CommonCheckbox(
                  textStyle: theme.textTheme.titleSmall?.copyWith(
                    color: AppColors.textPrimary,
                  ),
                  label: S.of(context).same_as_permanent_address,
                  value: sameAsPermAddress.value,
                  onChanged: (value) {
                    if ((value ?? false) != sameAsPermAddress.value) {
                      hasUserInteracted.value = true;
                      sameAsPermAddress.value = value ?? false;

                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        // Nếu user bỏ chọn (địa chỉ hiện tại khác thường trú), load provinces nếu chưa có
                        if (!sameAsPermAddress.value &&
                            controller.provinces.isEmpty &&
                            !controller.isLoadingProvinces) {
                          controller.loadProvinces();
                        }

                        updateController();
                      });
                    }
                  },
                ),

                // Hiển thị form địa chỉ nếu không trùng
                if (!sameAsPermAddress.value) ...[
                  AppDimens.h8,

                  // Tỉnh/Thành phố
                  _buildProvinceSelection(context, ref, selectedProvince,
                      updateController, hasUserInteracted),

                  AppDimens.h8,

                  // Phường/Xã
                  if (selectedProvince.value != null) ...[
                    _buildWardSelection(context, ref, selectedWard,
                        updateController, hasUserInteracted),
                  ],

                  AppDimens.h8,

                  // Địa chỉ cụ thể
                  // đồng bộ lại textFiled
                  CommonTextField(
                    label: S.of(context).specific_address,
                    controller: specificAddressController,
                    isDense: true,
                    textInputAction: TextInputAction.done,
                    inputFormatters: specificAddressInputFormatters,
                    maxLines: 3,
                    onChanged: (value) {
                      hasUserInteracted.value = true;
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        updateController();
                      });
                    },
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        // Kiểm tra ký tự đặc biệt cho phép
                        final allowedPattern =
                            RegExp(r'^[a-zA-ZÀ-ỹ0-9\s,\-/]+$');
                        if (!allowedPattern.hasMatch(value)) {
                          return 'Địa chỉ cho phép nhập chữ, số, và các kí tự đặc biệt sau: dấu phẩy (,), dấu gạch ngang (-), dấu gạch chéo (/)';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

Widget _buildMaritalStatusSelection(
  BuildContext context,
  WidgetRef ref,
  ValueNotifier<MaritalStatus> selectedMaritalStatus,
  VoidCallback updateController,
  ValueNotifier<bool> hasUserInteracted,
) {
  final controller = ref.read(createLoanControllerProvider.notifier);

  DropdownState state = controller.isLoadingMaritalStatuses
      ? DropdownState.loading
      : (controller.maritalStatuses.isEmpty
          ? DropdownState.empty
          : DropdownState.normal);

  return CommonDropdown<SystemConfigEntity>(
    label: S.of(context).marital_status,
    items: controller.maritalStatuses,
    value: controller.selectedMaritalStatus,
    state: state,
    emptyMessage: 'Không có dữ liệu tình trạng hôn nhân',
    onRetry: () => controller.loadMaritalStatuses(),
    onChanged: (status) {
      hasUserInteracted.value = true;
      final maritalStatus = MaritalStatusMapper.fromSystemConfig(status);
      if (maritalStatus != null) {
        selectedMaritalStatus.value = maritalStatus;
      } else {
        selectedMaritalStatus.value = MaritalStatus.single;
      }
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.selectMaritalStatus(status);
        updateController();
      });
    },
    labelBuilder: (status) => status.label,
    isRequired: false,
    isDense: true,
  );
}

Widget _buildProvinceSelection(
  BuildContext context,
  WidgetRef ref,
  ValueNotifier<ProvinceEntity?> selectedProvince,
  VoidCallback updateController,
  ValueNotifier<bool> hasUserInteracted,
) {
  final controller = ref.read(createLoanControllerProvider.notifier);

  DropdownState state = controller.isLoadingProvinces
      ? DropdownState.loading
      : (controller.provinces.isEmpty
          ? DropdownState.empty
          : DropdownState.normal);

  return CommonDropdown<ProvinceEntity>(
    label: S.of(context).province,
    items: controller.provinces,
    value: controller.selectedProvince,
    state: state,
    emptyMessage: S.of(context).no_province_data,
    onRetry: () => controller.loadProvinces(),
    onChanged: (province) {
      hasUserInteracted.value = true;
      selectedProvince.value = province;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.selectProvince(province);
        updateController();
      });
    },
    labelBuilder: (province) => province.name,
    isRequired: false,
    isDense: true,
  );
}

Widget _buildWardSelection(
  BuildContext context,
  WidgetRef ref,
  ValueNotifier<WardEntity?> selectedWard,
  VoidCallback updateController,
  ValueNotifier<bool> hasUserInteracted,
) {
  final controller = ref.read(createLoanControllerProvider.notifier);

  DropdownState state = controller.isLoadingWards
      ? DropdownState.loading
      : (controller.wards.isEmpty
          ? DropdownState.empty
          : DropdownState.normal);

  return CommonDropdown<WardEntity>(
    label: S.of(context).ward,
    value: controller.selectedWard,
    items: controller.wards,
    state: state,
    emptyMessage: S.of(context).no_ward_data,
    onRetry: () {
      if (controller.selectedProvince != null) {
        controller.loadWards(controller.selectedProvince!.id);
      }
    },
    enabled: controller.wards.isNotEmpty && !controller.isLoadingWards,
    labelBuilder: (ward) => ward.name,
    isRequired: false,
    onChanged: (ward) {
      hasUserInteracted.value = true;
      selectedWard.value = ward;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.selectWard(ward);
        updateController();
      });
    },
  );
}


