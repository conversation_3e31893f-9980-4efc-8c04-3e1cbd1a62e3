import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/utils/currency_formatter.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:intl/intl.dart';

class Step11SuccessScreen extends HookConsumerWidget {
  final VoidCallback onGoHome;
  final VoidCallback onCreateNewLoan;
  final VoidCallback onCreateAdditionalLoan;

  const Step11SuccessScreen({
    super.key,
    required this.onGoHome,
    required this.onCreateNewLoan,
    required this.onCreateAdditionalLoan,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final controller = ref.read(createLoanControllerProvider.notifier);
    final now = DateTime.now();

    return SafeArea(
      child: SingleChildScrollView(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AppDimens.h32,

            // Success Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.green.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check_circle,
                size: 80,
                color: AppColors.green,
              ),
            ),

            AppDimens.h24,

            // Success Title
            Text(
              'Khởi tạo khoản vay thành công',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),

            AppDimens.h16,

            // Date Time
            Text(
              'Ngày giờ: ${DateFormat('dd/MM/yyyy HH:mm:ss').format(now)}',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            AppDimens.h32,

            // Loan Information Card
            Container(
              width: double.infinity,
              padding: AppDimens.paddingAllLg,
              decoration: BoxDecoration(
                color: AppColors.backgroundColor,
                borderRadius: AppDimens.borderRadius12,
                border: Border.all(
                  color: AppColors.green.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Thông tin hồ sơ',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  AppDimens.h16,

                  // Customer Name
                  _buildInfoRow(
                    'Họ tên',
                    controller.step2Data?.fullName ?? 'Chưa có thông tin',
                  ),

                  AppDimens.h12,

                  // Loan Amount
                  _buildInfoRow(
                    'Khoản vay',
                    controller.step5Data != null
                        ? CurrencyFormatter.formatCurrency(
                            controller.step5Data!.loanAmount)
                        : 'Chưa có thông tin',
                  ),

                  AppDimens.h12,

                  // Loan Term
                  _buildInfoRow(
                    'Thời hạn vay',
                    controller.step5Data?.loanTerm.label ?? 'Chưa có thông tin',
                  ),
                ],
              ),
            ),

            AppDimens.h32,

            // Action Buttons
            Column(
              children: [
                // Go Home Button
                BottomButton(
                  title: 'Về trang chủ',
                  onPressed: onGoHome,
                  backgroundColor: AppColors.primaryColor,
                ),

                AppDimens.h16,

                // Create New Loan Button
                CommonButton(
                  title: 'Tạo khoản vay KH mới',
                  onPressed: onCreateNewLoan,
                  backgroundColor: AppColors.surfaceColor,
                  textColor: AppColors.primaryColor,
                  width: double.infinity,
                  height: 48,
                ),

                AppDimens.h16,

                // Create Additional Loan Button
                CommonButton(
                  title: 'Tạo thêm khoản vay KH hiện hữu',
                  onPressed: onCreateAdditionalLoan,
                  backgroundColor: AppColors.surfaceColor,
                  textColor: AppColors.primaryColor,
                  width: double.infinity,
                  height: 48,
                ),
              ],
            ),

            AppDimens.h24,
          ],
        ),
      ),
    );
  }

  // Helper method to build info row
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: TextStyle(
              fontSize: AppDimens.fontSM,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        AppDimens.w8,
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: TextStyle(
              fontSize: AppDimens.fontSM,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
