import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/enums/enums.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/utils/currency_formatter.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';
import 'package:sales_app/presentation/models/loan/steps/steps.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';

class Step8CollateralDetailScreen extends HookConsumerWidget {
  final void Function(Step8CollateralDetailData data) onStepCompleted;

  const Step8CollateralDetailScreen({
    super.key,
    required this.onStepCompleted,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final controller = ref.read(createLoanControllerProvider.notifier);

    // Form controllers
    final licensePlateController = useTextEditingController();
    final assetNameController = useTextEditingController();
    final chassisNumberController = useTextEditingController();
    final engineNumberController = useTextEditingController();
    final registrationCertificateController = useTextEditingController();
    final issuePlaceController = useTextEditingController();
    final totalAssetValueController = useTextEditingController();

    // State variables
    final selectedAssetCondition = useState<AssetCondition>(AssetCondition.inUse);
    final issueDate = useState<DateTime?>(null);

    // Form key for validation
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // Theo dõi validation attempt từ controller
    final lastValidationAttempt = useRef<int>(0);

    // Khôi phục dữ liệu từ controller nếu có
    useEffect(() {
      Future.microtask(() async {
        if (controller.step8Data != null) {
          final data = controller.step8Data!;
          licensePlateController.text = data.licensePlate;
          assetNameController.text = data.assetName;
          chassisNumberController.text = data.chassisNumber;
          engineNumberController.text = data.engineNumber;
          registrationCertificateController.text = data.registrationCertificateNumber;
          issuePlaceController.text = data.issuePlace;
          issueDate.value = data.issueDate;
          selectedAssetCondition.value = AssetCondition.values.firstWhere(
            (condition) => condition.label == data.assetCondition,
            orElse: () => AssetCondition.inUse,
          );
          totalAssetValueController.text = CurrencyFormatter.formatCurrency(data.totalAssetValue);
        }
      });
      return null;
    }, []);

    // Validation và gọi callback - chỉ gọi khi tất cả field hợp lệ
    void handleComplete() {
      // Kiểm tra validation mà không hiển thị lỗi
      if (licensePlateController.text.trim().isEmpty ||
          assetNameController.text.trim().isEmpty ||
          chassisNumberController.text.trim().isEmpty ||
          engineNumberController.text.trim().isEmpty ||
          registrationCertificateController.text.trim().isEmpty ||
          issuePlaceController.text.trim().isEmpty ||
          issueDate.value == null ||
          totalAssetValueController.text.trim().isEmpty) {
        return; // Không gọi callback nếu chưa đủ thông tin
      }

      final totalValue = CurrencyFormatter.parseCurrency(totalAssetValueController.text);

      final data = Step8CollateralDetailData(
        licensePlate: licensePlateController.text.trim(),
        assetName: assetNameController.text.trim(),
        chassisNumber: chassisNumberController.text.trim(),
        engineNumber: engineNumberController.text.trim(),
        registrationCertificateNumber: registrationCertificateController.text.trim(),
        issuePlace: issuePlaceController.text.trim(),
        issueDate: issueDate.value!,
        assetCondition: selectedAssetCondition.value.label,
        totalAssetValue: totalValue,
      );

      onStepCompleted(data);
    }

    // Helper để kiểm tra có nên hiển thị validation errors không
    bool shouldShowValidationErrors() {
      return controller.validationAttempt > lastValidationAttempt.value;
    }

    // Cập nhật lastValidationAttempt khi có validation attempt mới
    useEffect(() {
      if (controller.validationAttempt > lastValidationAttempt.value) {
        lastValidationAttempt.value = controller.validationAttempt;
        // Trigger form validation
        Future.microtask(() {
          formKey.currentState?.validate();
        });
      }
      return null;
    }, [controller.validationAttempt]);

    // Theo dõi thay đổi để tự động gọi handleComplete (không hiển thị lỗi)
    useEffect(() {
      Future.microtask(() {
        handleComplete();
      });
      return null;
    }, [
      selectedAssetCondition.value,
      issueDate.value,
    ]);

    // Theo dõi thay đổi text controllers
    useEffect(() {
      void listener() {
        Future.microtask(() {
          handleComplete();
        });
      }

      licensePlateController.addListener(listener);
      assetNameController.addListener(listener);
      chassisNumberController.addListener(listener);
      engineNumberController.addListener(listener);
      registrationCertificateController.addListener(listener);
      issuePlaceController.addListener(listener);
      totalAssetValueController.addListener(listener);

      return () {
        licensePlateController.removeListener(listener);
        assetNameController.removeListener(listener);
        chassisNumberController.removeListener(listener);
        engineNumberController.removeListener(listener);
        registrationCertificateController.removeListener(listener);
        issuePlaceController.removeListener(listener);
        totalAssetValueController.removeListener(listener);
      };
    }, []);

    return SafeArea(
      child: GestureDetector(
        onTap: () {
          // Ẩn bàn phím khi tap ra ngoài
          FocusScope.of(context).unfocus();
        },
        child: Form(
          key: formKey,
          child: SingleChildScrollView(
            padding: AppDimens.paddingAllLg,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Section: Thông tin chi tiết
                Text(
                  'Thông tin chi tiết',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),

                AppDimens.h16,

                // Biển kiểm soát
                CommonTextField(
                  label: 'Biển kiểm soát',
                  controller: licensePlateController,
                  textInputAction: TextInputAction.next,
                  isDense: true,
                  onChanged: (value) {
                    // Không gọi handleComplete() để tránh validation ngay lập tức
                  },
                  validator: (value) {
                    if (!shouldShowValidationErrors()) return null;
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),

                AppDimens.h8,

                // Tên tài sản
                CommonTextField(
                  label: 'Tên tài sản',
                  controller: assetNameController,
                  textInputAction: TextInputAction.next,
                  isDense: true,
                  onChanged: (value) {
                    // Không gọi handleComplete() để tránh validation ngay lập tức
                  },
                  validator: (value) {
                    if (!shouldShowValidationErrors()) return null;
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),

                AppDimens.h8,

                // Số khung
                CommonTextField(
                  label: 'Số khung',
                  controller: chassisNumberController,
                  textInputAction: TextInputAction.next,
                  isDense: true,
                  onChanged: (value) {
                    // Không gọi handleComplete() để tránh validation ngay lập tức
                  },
                  validator: (value) {
                    if (!shouldShowValidationErrors()) return null;
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),

                AppDimens.h8,

                // Số máy
                CommonTextField(
                  label: 'Số máy',
                  controller: engineNumberController,
                  textInputAction: TextInputAction.next,
                  isDense: true,
                  onChanged: (value) {
                    // Không gọi handleComplete() để tránh validation ngay lập tức
                  },
                  validator: (value) {
                    if (!shouldShowValidationErrors()) return null;
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),

                AppDimens.h8,

                // Số giấy chứng nhận đăng ký xe
                CommonTextField(
                  label: 'Số giấy chứng nhận đăng ký xe',
                  controller: registrationCertificateController,
                  textInputAction: TextInputAction.next,
                  isDense: true,
                  onChanged: (value) {
                    // Không gọi handleComplete() để tránh validation ngay lập tức
                  },
                  validator: (value) {
                    if (!shouldShowValidationErrors()) return null;
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),

                AppDimens.h8,

                // Nơi cấp
                CommonTextField(
                  label: 'Nơi cấp',
                  controller: issuePlaceController,
                  textInputAction: TextInputAction.next,
                  isDense: true,
                  onChanged: (value) {
                    // Không gọi handleComplete() để tránh validation ngay lập tức
                  },
                  validator: (value) {
                    if (!shouldShowValidationErrors()) return null;
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),

                AppDimens.h8,

                // Ngày cấp
                CommonDatePicker(
                  label: 'Ngày cấp',
                  selectedDate: issueDate.value,
                  onDateSelected: (date) {
                    issueDate.value = date;
                  },
                  isRequired: true,
                  isDense: true,
                ),

                // Hiển thị lỗi cho ngày cấp nếu cần
                if (shouldShowValidationErrors() && issueDate.value == null) ...[
                  AppDimens.h4,
                  Text(
                    'Bắt buộc nhập',
                    style: TextStyle(
                      color: theme.colorScheme.error,
                      fontSize: AppDimens.fontSM,
                    ),
                  ),
                ],

                AppDimens.h8,

                // Tình trạng tài sản khi giao
                CommonDropdown<AssetCondition>(
                  label: 'Tình trạng tài sản khi giao',
                  items: AssetCondition.values,
                  value: selectedAssetCondition.value,
                  labelBuilder: (condition) => condition.label,
                  isRequired: true,
                  isDense: true,
                  onChanged: (condition) {
                    selectedAssetCondition.value = condition;
                  },
                ),

                AppDimens.h8,

                // Tổng trị giá tài sản bảo đảm
                CommonTextField.currency(
                  label: 'Tổng trị giá tài sản bảo đảm',
                  controller: totalAssetValueController,
                  textInputAction: TextInputAction.done,
                  isDense: true,
                  inputFormatters: [
                    CurrencyInputFormatter(maxValue: 1000000000),
                  ],
                  onChanged: (value) {
                    // Không gọi handleComplete() để tránh validation ngay lập tức
                  },
                  validator: (value) {
                    if (!shouldShowValidationErrors()) return null;
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}