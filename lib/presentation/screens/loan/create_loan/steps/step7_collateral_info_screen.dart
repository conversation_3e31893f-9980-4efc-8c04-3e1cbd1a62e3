import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/core/theme/app_color.dart';
import 'package:sales_app/core/constants/app_dimens.dart';
import 'package:sales_app/core/enums/enums.dart';
import 'package:sales_app/core/utils/currency_formatter.dart';
import 'package:sales_app/presentation/models/loan/steps/steps.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'package:sales_app/presentation/controllers/loan/create_loan_controller.dart';
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/domain/domain.dart';

class Step7CollateralInfoScreen extends HookConsumerWidget {
  final GlobalKey<FormState>? formKey;
  final void Function(Step7CollateralInfoData data)? onStepCompleted;

  const Step7CollateralInfoScreen({
    super.key,
    this.formKey,
    this.onStepCompleted,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(createLoanControllerProvider.notifier);

    // Controllers cho các text field
    final assetValueController = useTextEditingController();
    final assetValueInWordsController = useTextEditingController();
    final ownerNameController = useTextEditingController();
    final ownerBirthDateController = useTextEditingController();
    // Sửa: Nếu model không có assetLocation, dùng ''
    final assetLocationController = useTextEditingController();

    // State variables
    final selectedAssetType = useState<SystemConfigEntity?>(null);
    final selectedAssetCondition = useState<AssetCondition>(AssetCondition.inUse);
    final selectedRegistrationType = useState<String>('Có mã QR');
    final selectedProvince = useState<ProvinceEntity?>(null);
    final selectedWard = useState<WardEntity?>(null);
    final hasUserInteracted = useState<bool>(false);

    // Khôi phục dữ liệu từ controller nếu có
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final d = controller.step7Data;
        if (d != null) {
          assetValueController.text = d.assetValue > 0 ? CurrencyFormatter.formatCurrency(d.assetValue) : '';
          assetValueInWordsController.text = d.assetValueInWords;
          ownerNameController.text = d.ownerName;
          ownerBirthDateController.text = d.ownerBirthDate.year.toString();
          // Sửa: Nếu không có assetLocation thì để chuỗi rỗng
          assetLocationController.text = (d as dynamic).assetLocation ?? '';
          selectedAssetCondition.value = d.assetCondition;
          // Sửa: ép kiểu String
          selectedRegistrationType.value = d.registrationType ?? '';
        }
        // Đồng bộ selectedAssetType theo id/code, không overwrite nếu user đã chọn
        if (controller.selectedAssetType != null &&
            (selectedAssetType.value == null || selectedAssetType.value?.code != controller.selectedAssetType?.code)) {
          final match = controller.assetTypes.firstWhere(
            (e) => e.code == controller.selectedAssetType?.code,
            orElse: () => controller.selectedAssetType!,
          );
          selectedAssetType.value = match;
        }
        if (controller.selectedProvinceStep7 != null &&
            (selectedProvince.value == null || selectedProvince.value?.id != controller.selectedProvinceStep7?.id)) {
          selectedProvince.value = controller.selectedProvinceStep7;
        }
        if (controller.selectedWardStep7 != null &&
            (selectedWard.value == null || selectedWard.value?.id != controller.selectedWardStep7?.id)) {
          selectedWard.value = controller.selectedWardStep7;
        }
      });
      return null;
    }, [controller.assetTypes, controller.selectedAssetType, controller.selectedProvinceStep7, controller.selectedWardStep7]);

    void updateController() {
      final year = int.tryParse(ownerBirthDateController.text);
      final ownerBirthDate = year != null ? DateTime(year) : DateTime.now();
      final data = Step7CollateralInfoData(
        assetType: selectedAssetType.value?.label ?? '',
        assetValue: CurrencyFormatter.parseCurrency(assetValueController.text),
        assetValueInWords: assetValueInWordsController.text.trim(),
        assetCondition: selectedAssetCondition.value,
        ownerName: ownerNameController.text.trim(),
        ownerBirthDate: ownerBirthDate,
        registrationType: selectedRegistrationType.value,
        assetLocation: assetLocationController.text.trim(),
      );
      controller.updateStep7Data(data);
    }

    // Tự động cập nhật assetValueInWords khi assetValue thay đổi
    useEffect(() {
      void listener() {
        final assetValue = CurrencyFormatter.parseCurrency(assetValueController.text);
        if (assetValue > 0) {
          assetValueInWordsController.text = CurrencyFormatter.numberToWords(assetValue);
        } else {
          assetValueInWordsController.text = '';
        }
        updateController();
      }
      assetValueController.addListener(listener);
      return () => assetValueController.removeListener(listener);
    }, []);

    // Theo dõi thay đổi của các trường để update controller
    useEffect(() {
      void listener() => updateController();
      assetLocationController.addListener(listener);
      ownerNameController.addListener(listener);
      ownerBirthDateController.addListener(listener);
      assetValueInWordsController.addListener(listener);
      return () {
        assetLocationController.removeListener(listener);
        ownerNameController.removeListener(listener);
        ownerBirthDateController.removeListener(listener);
        assetValueInWordsController.removeListener(listener);
      };
    }, []);

    // Đồng bộ dropdown từ controller
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        selectedAssetType.value = controller.selectedAssetType;
        selectedProvince.value = controller.selectedProvinceStep7;
        selectedWard.value = controller.selectedWardStep7;
      });
      return null;
    }, [controller.selectedAssetType, controller.selectedProvinceStep7, controller.selectedWardStep7]);

    // Dropdown loại tài sản
    Widget buildAssetTypeDropdown() {
      final dropdownState = controller.isLoadingAssetTypes
          ? DropdownState.loading
          : (controller.assetTypes.isEmpty ? DropdownState.empty : DropdownState.normal);
      return CommonDropdown<SystemConfigEntity>(
        label: 'Loại tài sản',
        items: controller.assetTypes,
        value: selectedAssetType.value,
        state: dropdownState,
        emptyMessage: 'Không có dữ liệu loại tài sản',
        onRetry: () => controller.loadAssetTypes(),
        onChanged: (type) {
          hasUserInteracted.value = true;
          selectedAssetType.value = type;
          controller.selectAssetType(type);
          updateController();
        },
        labelBuilder: (type) => type.label,
        isRequired: true,
        isDense: true,
      );
    }

    // Dropdown tỉnh/thành phố
    Widget buildProvinceDropdown() {
      final dropdownState = controller.isLoadingProvinces
          ? DropdownState.loading
          : (controller.provinces.isEmpty ? DropdownState.empty : DropdownState.normal);
      return CommonDropdown<ProvinceEntity>(
        label: S.of(context).province,
        items: controller.provinces,
        value: selectedProvince.value,
        state: dropdownState,
        emptyMessage: S.of(context).no_province_data,
        onRetry: () => controller.loadProvinces(),
        onChanged: (province) {
          hasUserInteracted.value = true;
          selectedProvince.value = province;
          // Reset ward khi đổi tỉnh
          selectedWard.value = null;
          controller.selectProvinceStep7(province);
          controller.selectWardStep7(null);
          updateController();
        },
        labelBuilder: (province) => province.name,
        isRequired: true,
        isDense: true,
      );
    }

    // Dropdown phường/xã
    Widget buildWardDropdown() {
      final dropdownState = controller.isLoadingWards
          ? DropdownState.loading
          : (controller.wards.isEmpty ? DropdownState.empty : DropdownState.normal);
      return CommonDropdown<WardEntity>(
        label: S.of(context).ward,
        items: controller.wards,
        value: selectedWard.value,
        state: dropdownState,
        emptyMessage: S.of(context).no_ward_data,
        onRetry: () {
          if (selectedProvince.value != null) {
            controller.loadWards(selectedProvince.value!.id);
          }
        },
        enabled: controller.wards.isNotEmpty && !controller.isLoadingWards,
        labelBuilder: (ward) => ward.name,
        isRequired: true,
        isDense: true,
        onChanged: (ward) {
          hasUserInteracted.value = true;
          selectedWard.value = ward;
          controller.selectWardStep7(ward);
          updateController();
        },
      );
    }

    // Radio loại đăng ký xe
    Widget buildRegistrationTypeRadio() {
      return CommonRadioGroup<String>(
        label: 'Loại đăng ký xe',
        items: const ['Có mã QR', 'Không có mã QR'],
        value: selectedRegistrationType.value,
        onChanged: (type) {
          selectedRegistrationType.value = type ?? '';
          updateController();
        },
        labelBuilder: (type) => type,
        isVertical: false,
        isRequired: true,
      );
    }

    // Dropdown hiện trạng tài sản
    Widget buildAssetConditionDropdown() {
      return CommonDropdown<AssetCondition>(
        label: 'Hiện trạng tài sản',
        items: AssetCondition.values,
        value: selectedAssetCondition.value,
        state: DropdownState.normal,
        onChanged: (condition) {
          selectedAssetCondition.value = condition;
          updateController();
        },
        labelBuilder: (c) => c.label,
        isRequired: true,
        isDense: true,
      );
    }

    return SafeArea(
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          padding: AppDimens.paddingAllLg,
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildAssetTypeDropdown(),
                AppDimens.h8,
                CommonTextField.currency(
                  label: 'Giá trị tài sản',
                  controller: assetValueController,
                  isDense: true,
                  inputFormatters: [CurrencyInputFormatter(maxValue: 1000000000000)],
                  onChanged: (value) => updateController(),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),
                AppDimens.h8,
                CommonTextField(
                  label: 'Giá trị tài sản (bằng chữ)',
                  controller: assetValueInWordsController,
                  enabled: false,
                  isDense: true,
                  backgroundColor: AppColors.lightGray,
                ),
                AppDimens.h8,
                buildAssetConditionDropdown(),
                AppDimens.h8,
                CommonTextField(
                  label: 'Chủ sở hữu tài sản',
                  controller: ownerNameController,
                  isDense: true,
                  onChanged: (value) => updateController(),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),
                AppDimens.h8,
                CommonTextField(
                  label: 'Năm sinh',
                  controller: ownerBirthDateController,
                  isDense: true,
                  keyboardType: TextInputType.number,
                  onChanged: (value) => updateController(),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),
                AppDimens.h8,
                buildRegistrationTypeRadio(),
                AppDimens.h8,
                buildProvinceDropdown(),
                AppDimens.h8,
                buildWardDropdown(),
                AppDimens.h8,
                CommonTextField(
                  label: 'Địa chỉ tài sản',
                  controller: assetLocationController,
                  isDense: true,
                  onChanged: (value) => updateController(),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Bắt buộc nhập';
                    }
                    return null;
                  },
                ),
                AppDimens.h16,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
