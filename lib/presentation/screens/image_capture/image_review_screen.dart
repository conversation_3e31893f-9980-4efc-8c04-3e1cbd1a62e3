import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';
import 'dart:io';

/// <PERSON><PERSON>n hình xem lại ảnh và xóa
/// Cho phép chọn và xóa nhiều ảnh cùng lúc
class ImageReviewScreen extends HookWidget {
  final String title;
  final List<String> imagePaths;
  final Function(List<String>) onImagesUpdated;

  const ImageReviewScreen({
    super.key,
    required this.title,
    required this.imagePaths,
    required this.onImagesUpdated,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedIndices = useState<Set<int>>({});
    final isSelectAll = useState<bool>(false);

    // State local để quản lý danh sách ảnh
    final currentImagePaths = useState<List<String>>(imagePaths);

    // Toggle select all
    void toggleSelectAll() {
      if (isSelectAll.value) {
        selectedIndices.value = {};
        isSelectAll.value = false;
      } else {
        selectedIndices.value = Set.from(List.generate(currentImagePaths.value.length, (index) => index));
        isSelectAll.value = true;
      }
    }

    // Toggle individual selection
    void toggleSelection(int index) {
      final newSelection = Set<int>.from(selectedIndices.value);
      if (newSelection.contains(index)) {
        newSelection.remove(index);
      } else {
        newSelection.add(index);
      }
      selectedIndices.value = newSelection;
      isSelectAll.value = newSelection.length == currentImagePaths.value.length;
    }

    // Delete selected images
    void deleteSelected() {
      if (selectedIndices.value.isEmpty) return;

      CommonDialog.showConfirmDialog(
        context: context,
        title: 'Xác nhận xóa',
        message: 'Bạn có chắc chắn muốn xóa ${selectedIndices.value.length} ảnh đã chọn?',
        onConfirm: () {
          // Create new list without selected images
          final newImagePaths = <String>[];
          for (int i = 0; i < currentImagePaths.value.length; i++) {
            if (!selectedIndices.value.contains(i)) {
              newImagePaths.add(currentImagePaths.value[i]);
            }
          }

          // Cập nhật state local và callback
          currentImagePaths.value = newImagePaths;
          selectedIndices.value = {};
          isSelectAll.value = false;
          onImagesUpdated(newImagePaths);
        },
      );
    }

    // Show upload options
    void showUploadOptions() {
      CommonBottomSheet.showUploadOptions(
        context: context,
        onImagesSelected: (newPaths) {
          // Thêm ảnh mới vào danh sách hiện tại
          final updatedPaths = [...currentImagePaths.value, ...newPaths];
          currentImagePaths.value = updatedPaths;
          onImagesUpdated(updatedPaths);
        },
      );
    }

    return BaseScreen(
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppColors.white,
        ),
      ),
      actions: [
        // Button thêm ảnh trong AppBar
        IconButton(
          onPressed: showUploadOptions,
          icon: const Icon(
            Icons.add_a_photo,
            color: AppColors.white,
          ),
          tooltip: 'Thêm ảnh',
        ),
        if (currentImagePaths.value.isNotEmpty)
          TextButton(
            onPressed: toggleSelectAll,
            child: Text(
              isSelectAll.value ? 'Bỏ chọn tất cả' : 'Chọn tất cả',
              style: const TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
      body: Scaffold(
        backgroundColor: Colors.transparent, // Để BaseScreen's white background hiển thị
        // FloatingActionButton chỉ hiển thị khi chưa có ảnh hoặc đang không select
        floatingActionButton: currentImagePaths.value.isEmpty || selectedIndices.value.isEmpty
            ? FloatingActionButton(
                onPressed: showUploadOptions,
                backgroundColor: AppColors.primaryColor,
                foregroundColor: AppColors.white,
                tooltip: 'Thêm ảnh',
                child: const Icon(Icons.add_a_photo),
              )
            : null,
        body: currentImagePaths.value.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.add_a_photo,
                      size: 60,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  AppDimens.h24,
                  Text(
                    'Chưa có ảnh nào',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  AppDimens.h8,
                  Text(
                    'Thêm ảnh hoặc tài liệu để bắt đầu',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  AppDimens.h32,
                  CommonButton(
                    title: 'Thêm ảnh đầu tiên',
                    onPressed: showUploadOptions,
                    backgroundColor: AppColors.primaryColor,
                    textColor: AppColors.white,
                    width: 200,
                    height: 48,
                  ),
                ],
              ),
            )
          : Column(
              children: [
                // Image grid
                Expanded(
                  child: GridView.builder(
                    padding: AppDimens.paddingAllMd,
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                      childAspectRatio: 1,
                    ),
                    itemCount: currentImagePaths.value.length,
                    itemBuilder: (context, index) {
                      final isSelected = selectedIndices.value.contains(index);
                      final path = currentImagePaths.value[index];
                      final isImage = path.toLowerCase().endsWith('.png') ||
                          path.toLowerCase().endsWith('.jpg') ||
                          path.toLowerCase().endsWith('.jpeg');

                      return GestureDetector(
                        onTap: () => toggleSelection(index),
                        child: Stack(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: AppDimens.borderRadius8,
                                border: Border.all(
                                  color: isSelected
                                    ? AppColors.primaryColor
                                    : AppColors.lightGray,
                                  width: isSelected ? 3 : 1,
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: AppDimens.borderRadius8,
                                child: isImage
                                    ? Image.file(
                                        File(path),
                                        fit: BoxFit.cover,
                                        width: double.infinity,
                                        height: double.infinity,
                                      )
                                    : Container(
                                        color: AppColors.primaryColor.withValues(alpha: 0.1),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            const Icon(
                                              Icons.description,
                                              color: AppColors.primaryColor,
                                              size: 32,
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'PDF',
                                              style: theme.textTheme.bodySmall?.copyWith(
                                                color: AppColors.primaryColor,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                              ),
                            ),
                            
                            // Selection overlay
                            if (isSelected)
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: AppDimens.borderRadius8,
                                  color: AppColors.primaryColor.withValues(alpha: 0.3),
                                ),
                              ),
                            
                            // Checkbox
                            Positioned(
                              top: 8,
                              right: 8,
                              child: Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: isSelected 
                                    ? AppColors.primaryColor 
                                    : AppColors.white,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isSelected
                                      ? AppColors.primaryColor
                                      : AppColors.lightGray,
                                    width: 2,
                                  ),
                                ),
                                child: isSelected
                                    ? const Icon(
                                        Icons.check,
                                        color: AppColors.white,
                                        size: 16,
                                      )
                                    : null,
                              ),
                            ),
                            
                            // Image number
                            Positioned(
                              bottom: 8,
                              left: 8,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.black.withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  '${index + 1}',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: AppColors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
                
                // Bottom action bar
                if (selectedIndices.value.isNotEmpty)
                  Container(
                    padding: AppDimens.paddingAllMd,
                    decoration: const BoxDecoration(
                      color: AppColors.surfaceColor,
                      border: Border(
                        top: BorderSide(
                          color: AppColors.lightGray,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Đã chọn ${selectedIndices.value.length} ảnh',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        CommonButton(
                          title: 'Xóa',
                          onPressed: deleteSelected,
                          backgroundColor: theme.colorScheme.error,
                          textColor: AppColors.white,
                          width: 80,
                          height: 40,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
      ),
    );
  }
}
