import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/presentation/widgets/common/common.dart';

/// <PERSON>àn hình camera để chụp nhiều ảnh chứng từ
/// Tương tự như camera app thông thường
class DocumentCameraScreen extends HookWidget {
  final String title;
  final Function(List<String>) onImagesSelected;
  final int maxImages;

  const DocumentCameraScreen({
    super.key,
    required this.title,
    required this.onImagesSelected,
    this.maxImages = 10,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cameraController = useRef<CameraController?>(null);
    final isCameraInitialized = useState<bool>(false);
    final isCapturing = useState<bool>(false);
    final capturedImages = useState<List<String>>([]);

    // Initialize camera
    useEffect(() {
      _initializeCamera(cameraController, isCameraInitialized);
      return () {
        cameraController.value?.dispose();
      };
    }, []);

    // Chụp ảnh
    Future<void> captureImage() async {
      if (cameraController.value == null || 
          !cameraController.value!.value.isInitialized || 
          isCapturing.value ||
          capturedImages.value.length >= maxImages) {
        return;
      }

      try {
        isCapturing.value = true;
        final xFile = await cameraController.value!.takePicture();
        capturedImages.value = [...capturedImages.value, xFile.path];
      } catch (e) {
        AppLogger.error('Error capturing image', error: e);
      } finally {
        isCapturing.value = false;
      }
    }

    // Xóa ảnh
    void removeImage(int index) {
      final newList = List<String>.from(capturedImages.value);
      newList.removeAt(index);
      capturedImages.value = newList;
    }

    // Hoàn thành
    void completeCapture() {
      if (capturedImages.value.isNotEmpty) {
        onImagesSelected(capturedImages.value);
      }
      Navigator.pop(context);
    }

    return BaseScreen(
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          color: AppColors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        if (capturedImages.value.isNotEmpty)
          TextButton(
            onPressed: completeCapture,
            child: Text(
              'Xong (${capturedImages.value.length})',
              style: const TextStyle(
                color: AppColors.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
      body: Container(
        color: AppColors.black, // Override BaseScreen's white background
        child: Column(
          children: [
          // Preview captured images
          if (capturedImages.value.isNotEmpty) ...[
            Container(
              height: 100,
              padding: AppDimens.paddingAllMd,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: capturedImages.value.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: EdgeInsets.only(right: AppDimens.spacingSM),
                    width: 80,
                    height: 80,
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: AppDimens.borderRadius8,
                          child: Image.file(
                            File(capturedImages.value[index]),
                            fit: BoxFit.cover,
                            width: 80,
                            height: 80,
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => removeImage(index),
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: AppColors.black.withValues(alpha: 0.7),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                color: AppColors.white,
                                size: 14,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            Container(
              height: 1,
              color: AppColors.white.withValues(alpha: 0.3),
            ),
          ],

          // Camera preview
          Expanded(
            child: isCameraInitialized.value && cameraController.value != null
                ? CameraPreview(cameraController.value!)
                : Container(
                    color: AppColors.black,
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.white,
                      ),
                    ),
                  ),
          ),

          // Bottom controls
          Container(
            padding: AppDimens.paddingAllLg,
            color: AppColors.black,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Gallery placeholder
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppColors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.photo_library,
                    color: AppColors.white,
                    size: 30,
                  ),
                ),

                // Capture button
                GestureDetector(
                  onTap: capturedImages.value.length < maxImages ? captureImage : null,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: capturedImages.value.length < maxImages
                        ? AppColors.white
                        : AppColors.lightGray,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.white,
                        width: 4,
                      ),
                    ),
                    child: isCapturing.value
                        ? const CircularProgressIndicator(
                            color: AppColors.black,
                            strokeWidth: 2,
                          )
                        : const Icon(
                            Icons.camera_alt,
                            color: AppColors.black,
                            size: 40,
                          ),
                  ),
                ),

                // Done button
                GestureDetector(
                  onTap: capturedImages.value.isNotEmpty ? completeCapture : null,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: capturedImages.value.isNotEmpty 
                        ? AppColors.green 
                        : AppColors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: AppColors.white,
                      size: 30,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Status bar
          Container(
            padding: AppDimens.paddingAllMd,
            color: AppColors.black,
            child: Text(
              'Đã chụp: ${capturedImages.value.length}/$maxImages ảnh',
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppColors.white.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
        ),
      ),
    );
  }
}

// Helper function to initialize camera
Future<void> _initializeCamera(
  ObjectRef<CameraController?> controllerRef,
  ValueNotifier<bool> isInitialized,
) async {
  try {
    final cameras = await availableCameras();
    if (cameras.isEmpty) return;

    controllerRef.value = CameraController(
      cameras.first,
      ResolutionPreset.high,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    await controllerRef.value?.initialize();
    isInitialized.value = true;
  } catch (e) {
    AppLogger.error('Error initializing camera', error: e);
  }
}
