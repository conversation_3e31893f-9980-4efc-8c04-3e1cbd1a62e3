import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../../core/theme/app_color.dart';
import '../../../core/constants/app_dimens.dart';
import '../../../core/enums/user_role.dart';
import '../../../domain/entities/entities.dart';
import '../../router/navigation_extensions.dart';
import 'tabs/home_tab.dart';
import 'tabs/report_tab.dart';
import 'tabs/profile_tab.dart';

/// Màn hình trang chủ mới với BottomNavigationBar và phân quyền theo role
class HomeScreenV2 extends StatefulWidget {
  final User? initialUser;

  const HomeScreenV2({
    super.key,
    this.initialUser,
  });

  @override
  State<HomeScreenV2> createState() => _HomeScreenV2State();
}

class _HomeScreenV2State extends State<HomeScreenV2>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  // Mock user data - trong thực tế sẽ lấy từ state management
  late User currentUser;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Khởi tạo user data
    currentUser = widget.initialUser ?? _getMockUser();

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Lấy mock user data để demo
  User _getMockUser() {
    return const User(
      id: '1',
      name: 'Nguyễn Văn An',
      role: UserRole.ctv,
      phoneNumber: '**********',
      email: '<EMAIL>',
      unreadNotifications: 5,
      isActive: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: TabBarView(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(), // Disable swipe
        children: [
          // Tab 1: Trang chủ
          HomeTab(user: currentUser),

          // Tab 2: Báo cáo
          ReportTab(user: currentUser),

          // Tab 3: Cá nhân
          ProfileTab(user: currentUser),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
      // Debug: Demo FAB (chỉ hiển thị trong debug mode)
      floatingActionButton: kDebugMode ? _buildDebugFab() : null,
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: AppDimens.blurRadiusMedium,
            offset: Offset(0, -AppDimens.spacingXXS),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: AppDimens.bottomBarHeight,
          padding: AppDimens.paddingHorizontalSm, // Giảm horizontal padding từ Md xuống Sm
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildBottomNavItem(
                index: 0,
                icon: Icons.home_outlined,
                activeIcon: Icons.home,
                label: 'Trang chủ',
              ),
              _buildBottomNavItem(
                index: 1,
                icon: Icons.bar_chart_outlined,
                activeIcon: Icons.bar_chart,
                label: 'Báo cáo',
              ),
              _buildBottomNavItem(
                index: 2,
                icon: Icons.person_outline,
                activeIcon: Icons.person,
                label: 'Cá nhân',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavItem({
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
  }) {
    final isActive = _currentIndex == index;

    return GestureDetector(
      onTap: () {
        setState(() {
          _currentIndex = index;
          _tabController.animateTo(index);
        });
      },
      behavior: HitTestBehavior.translucent,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: AppDimens.animationNormal),
        padding: EdgeInsets.symmetric(
          vertical: AppDimens.spacingXS, // Giảm vertical padding từ SM xuống XS
          horizontal: AppDimens.spacingXS,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: AppDimens.animationFast),
              child: Icon(
                isActive ? activeIcon : icon,
                key: ValueKey('$index-$isActive'),
                size: AppDimens.iconMD, // Giảm size icon từ LG xuống MD
                color: isActive ? AppColors.primaryColor : AppColors.textSecondary,
              ),
            ),
            AppDimens.h4, // Giảm spacing từ h8 xuống h4
            Flexible( // Wrap Text với Flexible để tránh overflow
              child: AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: AppDimens.animationNormal),
                style: TextStyle(
                  fontSize: AppDimens.fontSM, // Giảm font size từ MD xuống SM
                  color: isActive ? AppColors.primaryColor : AppColors.textSecondary,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                ),
                child: Text(
                  label,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build debug FAB với menu để chọn demo screen
  Widget _buildDebugFab() {
    return FloatingActionButton(
      onPressed: _showDebugMenu,
      backgroundColor: AppColors.primaryColor,
      child: const Icon(Icons.bug_report, color: Colors.white),
    );
  }

  /// Hiển thị menu debug với các options
  void _showDebugMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppColors.backgroundColor,
          borderRadius: BorderRadius.only(
            topLeft: AppDimens.borderRadius16.topLeft,
            topRight: AppDimens.borderRadius16.topRight,
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: AppDimens.paddingAllLg,
                child: Row(
                  children: [
                    Icon(
                      Icons.bug_report,
                      color: AppColors.primaryColor,
                      size: AppDimens.iconMD,
                    ),
                    AppDimens.w12,
                    Text(
                      'Debug Menu',
                      style: TextStyle(
                        fontSize: AppDimens.fontLG,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.close,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              // Divider
              const Divider(color: AppColors.grey300, height: 1),

              // Menu Items
              _buildDebugMenuItem(
                icon: Icons.navigation,
                title: 'Navigation Demo',
                subtitle: 'Test navigation patterns',
                onTap: () {
                  Navigator.pop(context);
                  context.goToNavigationDemo();
                },
              ),

              _buildDebugMenuItem(
                icon: Icons.palette,
                title: 'UI Components Demo',
                subtitle: 'Showcase common & shared widgets',
                onTap: () {
                  Navigator.pop(context);
                  context.goToUiDemo();
                },
              ),

              AppDimens.h16,
            ],
          ),
        ),
      ),
    );
  }

  /// Build debug menu item
  Widget _buildDebugMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: AppDimens.containerMd,
        height: AppDimens.containerMd,
        decoration: BoxDecoration(
          color: AppColors.primaryColor.withValues(alpha: 0.1),
          borderRadius: AppDimens.borderRadius8,
        ),
        child: Icon(
          icon,
          color: AppColors.primaryColor,
          size: AppDimens.iconMD,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: AppDimens.fontMD,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: AppDimens.fontSM,
          color: AppColors.textSecondary,
        ),
      ),
      onTap: onTap,
      contentPadding: AppDimens.paddingHorizontalLg,
    );
  }
}