import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';

class ModernCtvOverviewV3 extends StatefulWidget {
  const ModernCtvOverviewV3({super.key});

  @override
  State<ModernCtvOverviewV3> createState() => _ModernCtvOverviewV3State();
}

class _ModernCtvOverviewV3State extends State<ModernCtvOverviewV3>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  late Animation<double> _fadeAnimation;

  // Mock data theo SRS
  final Map<String, dynamic> mockData = {
    'limitData': {
      'totalLimit': **********0.0, // Hạn mức được cấp
      'creditLimitRemaining': **********.0, // <PERSON>ạn mức tín chấp còn lại
      'collateralValue': **********.0, // Gi<PERSON> trị TSBĐ
    },
    'debtData': {
      'debtWithCollateral': **********.0, // Dư nợ có TSBĐ
      'debtWithoutCollateral': **********.0, // Dư nợ không TSBĐ
      'totalCurrentDebt': **********.0, // Tổng dư nợ hiện tại
      'averageMonthlyDebt': **********.0, // Dư nợ bình quân tháng
    },
    'paymentData': {
      'principalPerDay': 2000000.0, // Số tiền gốc/ngày
      'interestPerDay': 500000.0, // Số tiền lãi/ngày
      'totalPaymentPerDay': 2500000.0, // Số tiền nộp/ngày
    },
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    // Delay animation
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          FadeTransition(
            opacity: _fadeAnimation,
            child: Text(
              'Tổng quan',
              style: TextStyle(
                fontSize: AppDimens.fontLG,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),

          AppDimens.h16,

          // Compact TabView Container
          FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              height: AppDimens.containerHeight320, // Increased from 280 to 320 for larger chart
              decoration: BoxDecoration(
                color: AppColors.backgroundColor,
                borderRadius: AppDimens.borderRadius16,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.06),
                    blurRadius: AppDimens.blurRadiusXL,
                    offset: AppDimens.offsetMedium,
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Compact Tab Bar
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.surfaceColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(AppDimens.borderRadius16.topLeft.x),
                        topRight: Radius.circular(AppDimens.borderRadius16.topRight.x),
                      ),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      labelColor: AppColors.primaryColor,
                      unselectedLabelColor: AppColors.textSecondary,
                      indicatorColor: AppColors.primaryColor,
                      indicatorWeight: 2,
                      indicatorPadding: AppDimens.paddingHorizontalMd,
                      labelStyle: TextStyle(
                        fontSize: AppDimens.fontSM,
                        fontWeight: FontWeight.w600,
                      ),
                      unselectedLabelStyle: TextStyle(
                        fontSize: AppDimens.fontSM,
                        fontWeight: FontWeight.w500,
                      ),
                      onTap: (index) {
                        _animationController.reset();
                        _animationController.forward();
                      },
                      tabs: const [
                        Tab(text: 'Hạn mức'),
                        Tab(text: 'Dư nợ'),
                        Tab(text: 'Thanh toán'),
                      ],
                    ),
                  ),

                  // Compact Tab Content
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildLimitTab(),
                        _buildDebtTab(),
                        _buildPaymentTab(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLimitTab() {
    final data = mockData['limitData'];
    final double totalLimit = (data['totalLimit'] as num).toDouble();
    final double creditLimitRemaining = (data['creditLimitRemaining'] as num).toDouble();
    final double usedLimit = totalLimit - creditLimitRemaining;
    final double usagePercentage = totalLimit > 0 ? (usedLimit / totalLimit) : 0.0;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Padding(
          padding: AppDimens.paddingAllMd,
          child: Column(
            children: [
              // Enhanced circular chart with larger size
              Expanded(
                flex: 4, // Increased from 3 to 4 for more chart space
                child: Center(
                  child: Container(
                    width: 160, // Increased from 120 to 160
                    height: 160, // Increased from 120 to 160
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryColor.withValues(alpha: 0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Background circle
                        CircularProgressIndicator(
                          value: 1.0,
                          strokeWidth: 12, // Increased from 8 to 12
                          backgroundColor: AppColors.textSecondary.withValues(alpha: 0.1),
                          valueColor: AlwaysStoppedAnimation(AppColors.textSecondary.withValues(alpha: 0.1)),
                        ),
                        // Progress circle
                        CircularProgressIndicator(
                          value: usagePercentage * _progressAnimation.value,
                          strokeWidth: 12, // Increased from 8 to 12
                          backgroundColor: Colors.transparent,
                          valueColor: const AlwaysStoppedAnimation(AppColors.primaryColor),
                          strokeCap: StrokeCap.round,
                        ),
                        // Center content
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                '${(usagePercentage * 100).toInt()}%',
                                style: TextStyle(
                                  fontSize: AppDimens.fontXXL, // Increased font size
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryColor,
                                ),
                              ),
                              AppDimens.h4,
                              Text(
                                'đã sử dụng',
                                style: TextStyle(
                                  fontSize: AppDimens.fontSM, // Increased from XS to SM
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              AppDimens.h16,

              // Compact info grid
              Expanded(
                flex: 2,
                child: Row(
                  children: [
                    Expanded(
                      child: _buildCompactInfo(
                        'Tổng hạn mức',
                        _formatCurrency(totalLimit),
                        AppColors.textPrimary,
                      ),
                    ),
                    AppDimens.w8,
                    Expanded(
                      child: _buildCompactInfo(
                        'Còn lại',
                        _formatCurrency(creditLimitRemaining),
                        AppColors.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDebtTab() {
    final data = mockData['debtData'];
    final double totalCurrentDebt = (data['totalCurrentDebt'] as num).toDouble();
    final double debtWithCollateral = (data['debtWithCollateral'] as num).toDouble();
    final double debtWithoutCollateral = (data['debtWithoutCollateral'] as num).toDouble();
    final double collateralPercentage = totalCurrentDebt > 0 ? (debtWithCollateral / totalCurrentDebt) : 0.0;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Padding(
          padding: AppDimens.paddingAllMd,
          child: Column(
            children: [
              // Enhanced circular chart with larger size
              Expanded(
                flex: 4, // Increased from 3 to 4 for more chart space
                child: Center(
                  child: Container(
                    width: 160, // Increased from 120 to 160
                    height: 160, // Increased from 120 to 160
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.error.withValues(alpha: 0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Background circle
                        CircularProgressIndicator(
                          value: 1.0,
                          strokeWidth: 12, // Increased from 8 to 12
                          backgroundColor: AppColors.textSecondary.withValues(alpha: 0.1),
                          valueColor: AlwaysStoppedAnimation(AppColors.textSecondary.withValues(alpha: 0.1)),
                        ),
                        // Debt with collateral
                        CircularProgressIndicator(
                          value: collateralPercentage * _progressAnimation.value,
                          strokeWidth: 12, // Increased from 8 to 12
                          backgroundColor: Colors.transparent,
                          valueColor: const AlwaysStoppedAnimation(AppColors.error),
                          strokeCap: StrokeCap.round,
                        ),
                        // Center content
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                _formatCurrency(totalCurrentDebt),
                                style: TextStyle(
                                  fontSize: AppDimens.fontLG, // Increased from MD to LG
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.error,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              AppDimens.h4,
                              Text(
                                'tổng dư nợ',
                                style: TextStyle(
                                  fontSize: AppDimens.fontSM, // Increased from XS to SM
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              AppDimens.h16,

              // Compact info grid
              Expanded(
                flex: 2,
                child: Row(
                  children: [
                    Expanded(
                      child: _buildCompactInfo(
                        'Có TSBĐ',
                        _formatCurrency(debtWithCollateral),
                        AppColors.error,
                      ),
                    ),
                    AppDimens.w8,
                    Expanded(
                      child: _buildCompactInfo(
                        'Không TSBĐ',
                        _formatCurrency(debtWithoutCollateral),
                        AppColors.warning,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPaymentTab() {
    final data = mockData['paymentData'];
    final double totalPaymentPerDay = (data['totalPaymentPerDay'] as num).toDouble();
    final double principalPerDay = (data['principalPerDay'] as num).toDouble();
    final double interestPerDay = (data['interestPerDay'] as num).toDouble();
    final double principalPercentage = totalPaymentPerDay > 0 ? (principalPerDay / totalPaymentPerDay) : 0.0;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Padding(
          padding: AppDimens.paddingAllMd,
          child: Column(
            children: [
              // Enhanced circular chart with larger size
              Expanded(
                flex: 4, // Increased from 3 to 4 for more chart space
                child: Center(
                  child: Container(
                    width: 160, // Increased from 120 to 160
                    height: 160, // Increased from 120 to 160
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryColor.withValues(alpha: 0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Background circle
                        CircularProgressIndicator(
                          value: 1.0,
                          strokeWidth: 12, // Increased from 8 to 12
                          backgroundColor: AppColors.textSecondary.withValues(alpha: 0.1),
                          valueColor: AlwaysStoppedAnimation(AppColors.textSecondary.withValues(alpha: 0.1)),
                        ),
                        // Principal portion
                        CircularProgressIndicator(
                          value: principalPercentage * _progressAnimation.value,
                          strokeWidth: 12, // Increased from 8 to 12
                          backgroundColor: Colors.transparent,
                          valueColor: const AlwaysStoppedAnimation(AppColors.primaryColor),
                          strokeCap: StrokeCap.round,
                        ),
                        // Center content
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                _formatCurrency(totalPaymentPerDay),
                                style: TextStyle(
                                  fontSize: AppDimens.fontLG, // Increased from MD to LG
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryColor,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              AppDimens.h4,
                              Text(
                                'mỗi ngày',
                                style: TextStyle(
                                  fontSize: AppDimens.fontSM, // Increased from XS to SM
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              AppDimens.h16,

              // Compact info grid
              Expanded(
                flex: 2,
                child: Row(
                  children: [
                    Expanded(
                      child: _buildCompactInfo(
                        'Tiền gốc',
                        _formatCurrency(principalPerDay),
                        AppColors.primaryColor,
                      ),
                    ),
                    AppDimens.w8,
                    Expanded(
                      child: _buildCompactInfo(
                        'Tiền lãi',
                        _formatCurrency(interestPerDay),
                        AppColors.secondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCompactInfo(String label, String value, Color color) {
    return Container(
      padding: EdgeInsets.all(AppDimens.spacingSM),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: AppDimens.borderRadius8,
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: AppDimens.borderWidthThin,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: AppDimens.fontXS,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          AppDimens.h4,
          Text(
            value,
            style: TextStyle(
              fontSize: AppDimens.fontSM,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _formatCurrency(num amount) {
    if (amount >= **********) {
      return '${(amount / **********).toStringAsFixed(1)} tỷ';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)} triệu';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} nghìn';
    }
    return '${amount.toStringAsFixed(0)} VNĐ';
  }
}