import 'package:flutter/material.dart';
import '../../../../../core/theme/app_color.dart';
import '../../../../../core/constants/app_dimens.dart';
import '../../../../../domain/entities/entities.dart';

/// Widget hiển thị danh sách hồ sơ chưa hoàn tất theo từng sản phẩm
class IncompleteRecordsSection extends StatelessWidget {
  final User user;

  const IncompleteRecordsSection({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final records = _getIncompleteRecords();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Hồ sơ chưa hoàn tất',
              style: TextStyle(
                fontSize: AppDimens.fontLG,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            GestureDetector(
              onTap: () => _viewAllIncompleteRecords(context),
              child: Text(
                'Xem thêm',
                style: TextStyle(
                  fontSize: AppDimens.fontSM,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primaryColor,
                ),
              ),
            ),
          ],
        ),
        AppDimens.h12,
        SizedBox(
          height: 120, // Giảm height vì không cần space cho button nữa
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: records.length,
            separatorBuilder: (context, index) => AppDimens.w12,
            itemBuilder: (context, index) {
              return _buildHorizontalIncompleteRecordItem(context, records[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHorizontalIncompleteRecordItem(
    BuildContext context,
    IncompleteRecord record,
  ) {
    return GestureDetector(
      onTap: () {
        if (record.incompleteCount > 0) {
          _completeRecord(context, record);
        }
      },
      child: Container(
        width: 160,
        padding: AppDimens.paddingAllMd,
        decoration: BoxDecoration(
          color: AppColors.backgroundColor,
          borderRadius: AppDimens.borderRadius12,
          border: Border.all(
            color: AppColors.surfaceColor,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon and count
            Row(
              children: [
                Container(
                  width: AppDimens.containerSm,
                  height: AppDimens.containerSm,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor.withValues(alpha: 0.1),
                    borderRadius: AppDimens.borderRadius4,
                  ),
                  child: Icon(
                    record.icon,
                    color: AppColors.primaryColor,
                    size: AppDimens.iconSM,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimens.spacingXS,
                    vertical: AppDimens.spacingXXS,
                  ),
                  decoration: BoxDecoration(
                    color: record.incompleteCount > 0 ? AppColors.error : AppColors.success,
                    borderRadius: AppDimens.borderRadius4,
                  ),
                  child: Text(
                    '${record.incompleteCount}',
                    style: TextStyle(
                      fontSize: AppDimens.fontXS,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textWhite,
                    ),
                  ),
                ),
              ],
            ),
            AppDimens.h8,
            // Product name
            Text(
              record.productName,
              style: TextStyle(
                fontSize: AppDimens.fontSM,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            AppDimens.h4,
            // Status text
            Text(
              record.incompleteCount > 0 
                ? '${record.incompleteCount} cần hoàn thiện'
                : 'Đã hoàn thành',
              style: TextStyle(
                fontSize: AppDimens.fontXS,
                color: AppColors.textSecondary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const Spacer(), // Đẩy CTA xuống dưới
            // CTA với text + arrow
            if (record.incompleteCount > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Hoàn thiện ngay',
                    style: TextStyle(
                      fontSize: AppDimens.fontSM,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: AppDimens.iconXS,
                    color: AppColors.primaryColor,
                  ),
                ],
              )
            else
              Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: AppDimens.iconXS,
                    color: AppColors.success,
                  ),
                  AppDimens.w4,
                  Text(
                    'Hoàn thành',
                    style: TextStyle(
                      fontSize: AppDimens.fontXS,
                      fontWeight: FontWeight.w600,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  void _viewAllIncompleteRecords(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Xem tất cả hồ sơ chưa hoàn tất')),
    );
  }

  void _completeRecord(BuildContext context, IncompleteRecord record) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Hoàn thiện hồ sơ: ${record.productName}')),
    );
  }

  List<IncompleteRecord> _getIncompleteRecords() {
    return [
      const IncompleteRecord(
        productId: 'loan',
        productName: 'Vay trả góp ngày',
        icon: Icons.credit_card,
        color: AppColors.primaryColor,
        incompleteCount: 3,
      ),
      const IncompleteRecord(
        productId: 'contract',
        productName: 'Hợp đồng điện tử',
        icon: Icons.description,
        color: AppColors.secondaryColor,
        incompleteCount: 1,
      ),
      const IncompleteRecord(
        productId: 'customer',
        productName: 'Khách hàng mới',
        icon: Icons.person_add,
        color: AppColors.success,
        incompleteCount: 5,
      ),
      const IncompleteRecord(
        productId: 'payment',
        productName: 'Thu tiền KH',
        icon: Icons.payments,
        color: AppColors.warning,
        incompleteCount: 0,
      ),
    ];
  }
}

/// Class đại diện cho một hồ sơ chưa hoàn tất
class IncompleteRecord {
  final String productId;
  final String productName;
  final IconData icon;
  final Color color;
  final int incompleteCount;

  const IncompleteRecord({
    required this.productId,
    required this.productName,
    required this.icon,
    required this.color,
    required this.incompleteCount,
  });
} 