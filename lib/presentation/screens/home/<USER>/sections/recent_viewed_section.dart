import 'package:flutter/material.dart';
import '../../../../../core/theme/app_color.dart';
import '../../../../../core/constants/app_dimens.dart';
import '../../../../../domain/entities/entities.dart';

/// Widget hiển thị mục "Đã xem gần đây" với 2 tabs: <PERSON><PERSON><PERSON><PERSON> hàng và <PERSON>ản vay
class RecentViewedSection extends StatefulWidget {
  final User user;

  const RecentViewedSection({
    super.key,
    required this.user,
  });

  @override
  State<RecentViewedSection> createState() => _RecentViewedSectionState();
}

class _RecentViewedSectionState extends State<RecentViewedSection>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Text(
          'Đã xem gần đây',
          style: TextStyle(
            fontSize: AppDimens.fontLG,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        
        AppDimens.h16,
        
        // Tab Selector - thiết kế mới theo hình ảnh
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppDimens.spacingXXS),
          decoration: BoxDecoration(
            color: AppColors.textSecondary.withValues(alpha: 0.05),
            borderRadius: AppDimens.borderRadius12,
          ),
          child: Row(
            children: [
              Expanded(child: _buildTabButton('Khách hàng', 0)),
              AppDimens.w8,
              Expanded(child: _buildTabButton('Khoản vay', 1)),
            ],
          ),
        ),
        
        AppDimens.h16,
        
        // Tab Content
        SizedBox(
          height: 180,
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildCustomersTab(),
              _buildLoansTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabButton(String title, int index) {
    final isSelected = _tabController.index == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _tabController.animateTo(index);
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppDimens.spacingMD,
          vertical: AppDimens.spacingSM,
        ),
        decoration: BoxDecoration(
          color: isSelected 
            ? AppColors.backgroundColor 
            : Colors.transparent,
          borderRadius: AppDimens.borderRadius8,
          border: isSelected 
            ? Border.all(
                color: AppColors.textSecondary.withValues(alpha: 0.1),
                width: 1,
              )
            : null,
        ),
        child: AnimatedDefaultTextStyle(
          duration: const Duration(milliseconds: 200),
          style: TextStyle(
            fontSize: AppDimens.fontMD,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color: isSelected 
              ? AppColors.primaryColor 
              : AppColors.textSecondary,
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildCustomersTab() {
    final customers = _getRecentCustomers();
    
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.zero,
      itemCount: customers.length,
      separatorBuilder: (context, index) => AppDimens.w12,
      itemBuilder: (context, index) {
        final customer = customers[index];
        return _buildCustomerCard(customer);
      },
    );
  }

  Widget _buildLoansTab() {
    final loans = _getRecentLoans();
    
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.zero,
      itemCount: loans.length,
      separatorBuilder: (context, index) => AppDimens.w12,
      itemBuilder: (context, index) {
        final loan = loans[index];
        return _buildLoanCard(loan);
      },
    );
  }

  Widget _buildCustomerCard(RecentCustomer customer) {
    return Container(
      width: 160,
      padding: AppDimens.paddingAllMd,
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: AppColors.textSecondary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Avatar
          CircleAvatar(
            radius: 24,
            backgroundColor: AppColors.primaryColor.withValues(alpha: 0.1),
            child: Text(
              customer.name.isNotEmpty ? customer.name[0].toUpperCase() : 'K',
              style: TextStyle(
                fontSize: AppDimens.fontLG,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
          ),
          AppDimens.h12,
          
          // Customer Name
          Text(
            customer.name,
            style: TextStyle(
              fontSize: AppDimens.fontMD,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          AppDimens.h4,
          
          // Phone Number
          Text(
            customer.phoneNumber,
            style: TextStyle(
              fontSize: AppDimens.fontSM,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          AppDimens.h8,
          
          // View Time
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppDimens.spacingXS,
              vertical: 2,
            ),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: AppDimens.borderRadius4,
            ),
            child: Text(
              customer.viewedTime,
              style: TextStyle(
                fontSize: AppDimens.fontXS,
                color: AppColors.info,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoanCard(RecentLoan loan) {
    return Container(
      width: 160,
      padding: AppDimens.paddingAllMd,
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: AppColors.textSecondary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Loan Icon
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: AppDimens.borderRadius12,
            ),
            child: const Icon(
              Icons.monetization_on_rounded,
              color: AppColors.success,
              size: 24,
            ),
          ),
          AppDimens.h12,
          
          // Customer Name
          Text(
            loan.customerName,
            style: TextStyle(
              fontSize: AppDimens.fontMD,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          AppDimens.h4,
          
          // Loan Amount
          Text(
            loan.formattedAmount,
            style: TextStyle(
              fontSize: AppDimens.fontSM,
              color: AppColors.success,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          AppDimens.h8,
          
          // View Time
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppDimens.spacingXS,
              vertical: 2,
            ),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: AppDimens.borderRadius4,
            ),
            child: Text(
              loan.viewedTime,
              style: TextStyle(
                fontSize: AppDimens.fontXS,
                color: AppColors.info,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<RecentCustomer> _getRecentCustomers() {
    return [
      const RecentCustomer(
        id: '1',
        name: 'Nguyễn Văn A',
        phoneNumber: '0901234567',
        viewedTime: '2h trước',
      ),
      const RecentCustomer(
        id: '2',
        name: 'Trần Thị B',
        phoneNumber: '0987654321',
        viewedTime: '3h trước',
      ),
      const RecentCustomer(
        id: '3',
        name: 'Lê Văn C',
        phoneNumber: '0912345678',
        viewedTime: '1 ngày trước',
      ),
      const RecentCustomer(
        id: '4',
        name: 'Phạm Thị D',
        phoneNumber: '0945678901',
        viewedTime: '2 ngày trước',
      ),
    ];
  }

  List<RecentLoan> _getRecentLoans() {
    return [
      const RecentLoan(
        id: '1',
        customerName: 'Nguyễn Văn A',
        amount: 50000000,
        viewedTime: '1h trước',
      ),
      const RecentLoan(
        id: '2',
        customerName: 'Trần Thị B',
        amount: 75000000,
        viewedTime: '2h trước',
      ),
      const RecentLoan(
        id: '3',
        customerName: 'Lê Văn C',
        amount: 30000000,
        viewedTime: '5h trước',
      ),
      const RecentLoan(
        id: '4',
        customerName: 'Hoàng Văn E',
        amount: 120000000,
        viewedTime: '1 ngày trước',
      ),
    ];
  }
}

/// Class đại diện cho khách hàng đã xem gần đây
class RecentCustomer {
  final String id;
  final String name;
  final String phoneNumber;
  final String viewedTime;

  const RecentCustomer({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.viewedTime,
  });
}

/// Class đại diện cho khoản vay đã xem gần đây
class RecentLoan {
  final String id;
  final String customerName;
  final double amount;
  final String viewedTime;

  const RecentLoan({
    required this.id,
    required this.customerName,
    required this.amount,
    required this.viewedTime,
  });

  String get formattedAmount {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(0)} triệu VNĐ';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)} nghìn VNĐ';
    } else {
      return '${amount.toStringAsFixed(0)} VNĐ';
    }
  }
} 