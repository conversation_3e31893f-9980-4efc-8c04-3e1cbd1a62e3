import 'package:flutter/material.dart';
import '../../../../../core/theme/app_color.dart';
import '../../../../../core/constants/app_dimens.dart';
import '../../../../../domain/entities/entities.dart';

/// Widget hiển thị danh sách thông tin sản phẩm
class ProductInfoSection extends StatelessWidget {
  final User user;

  const ProductInfoSection({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final products = _getProductInfoList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Thông tin sản phẩm',
              style: TextStyle(
                fontSize: AppDimens.fontLG,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            GestureDetector(
              onTap: () => _viewAllProducts(context),
              child: Text(
                'Xem thêm',
                style: TextStyle(
                  fontSize: AppDimens.fontSM,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primaryColor,
                ),
              ),
            ),
          ],
        ),
        AppDimens.h12,
        // Horizontal scroll list
        SizedBox(
          height: 150, // Tăng height để chứa mô tả chi tiết
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.only(right: AppDimens.spacingMD),
            itemCount: products.length,
            separatorBuilder: (context, index) => AppDimens.w12,
            itemBuilder: (context, index) {
              return _buildDetailedProductCard(context, products[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDetailedProductCard(BuildContext context, ProductInfo product) {
    return Container(
      width: 250, // Tăng width để chứa mô tả chi tiết
      padding: AppDimens.paddingAllMd,
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: AppColors.surfaceColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon và Product Name cùng hàng
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Icon với background nhẹ
              Container(
                width: AppDimens.containerMd,
                height: AppDimens.containerMd,
                decoration: BoxDecoration(
                  color: product.color.withValues(alpha: 0.1),
                  borderRadius: AppDimens.borderRadius8,
                ),
                child: Icon(
                  product.icon,
                  color: product.color,
                  size: AppDimens.iconMD,
                ),
              ),
              AppDimens.w12,

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: TextStyle(
                        fontSize: AppDimens.fontMD,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    AppDimens.h4,

                    // Product Code
                    Text(
                      product.code,
                      style: TextStyle(
                        fontSize: AppDimens.fontSM,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          AppDimens.h12,

          // Detailed Information List
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: product.details
                  .map((detail) => _buildDetailItem(detail))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String detail) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimens.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 4,
            height: 4,
            margin: EdgeInsets.only(
              top: AppDimens.spacingXS,
              right: AppDimens.spacingXS,
            ),
            decoration: const BoxDecoration(
              color: AppColors.primaryColor,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              detail,
              style: TextStyle(
                fontSize: AppDimens.fontXS,
                color: AppColors.textSecondary,
                height: 1.4,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  void _viewAllProducts(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Xem tất cả sản phẩm')),
    );
  }

  List<ProductInfo> _getProductInfoList() {
    return [
      const ProductInfo(
        id: 'TG471',
        name: 'Vay trả góp ngày',
        code: 'TG471',
        description:
            'Sản phẩm vay không cần tài sản thế chấp, thủ tục đơn giản, giải ngân nhanh chóng',
        icon: Icons.credit_card,
        color: AppColors.primaryColor,
        details: [
          'Hạn mức lên đến 100,000,000 VND với thời hạn vay tối đa 270 ngày',
          'Lãi suất: Theo quy định KienlongBank từng thời kỳ',
          'Thủ tục đơn giản, giải ngân nhanh trong ngày',
        ],
      ),
      const ProductInfo(
        id: 'TD001',
        name: 'Vay theo lương',
        code: 'TD001',
        description:
            'Vay theo mức lương, lãi suất ưu đãi cho khách hàng có thu nhập ổn định',
        icon: Icons.account_balance_wallet,
        color: AppColors.secondaryColor,
        details: [
          'Hạn mức vay lên đến 30 lần lương cơ bản',
          'Thời hạn vay từ 6 - 60 tháng',
          'Lãi suất ưu đãi cho khách hàng lương cao',
          'Không cần tài sản đảm bảo',
        ],
      ),
      const ProductInfo(
        id: 'KH123',
        name: 'Vay mua nhà',
        code: 'KH123',
        description:
            'Hỗ trợ vay mua nhà với lãi suất cạnh tranh và thời gian vay linh hoạt',
        icon: Icons.home,
        color: AppColors.success,
        details: [
          'Hạn mức vay lên đến 85% giá trị tài sản',
          'Thời hạn vay tối đa 25 năm',
          'Lãi suất cố định 12 tháng đầu',
          'Hỗ trợ thủ tục pháp lý miễn phí',
        ],
      ),
      const ProductInfo(
        id: 'SP789',
        name: 'Vay kinh doanh',
        code: 'SP789',
        description: 'Gói vay dành cho doanh nghiệp và hộ kinh doanh cá thể',
        icon: Icons.business,
        color: AppColors.warning,
        details: [
          'Hạn mức vay lên đến 5 tỷ VND',
          'Thời hạn vay linh hoạt từ 12 - 84 tháng',
          'Lãi suất ưu đãi cho doanh nghiệp VIP',
          'Hỗ trợ vốn lưu động và đầu tư TSCĐ',
        ],
      ),
    ];
  }
}

/// Class đại diện cho thông tin sản phẩm
class ProductInfo {
  final String id;
  final String name;
  final String code;
  final String description;
  final IconData icon;
  final Color color;
  final List<String> details; // Added detailed information list

  const ProductInfo({
    required this.id,
    required this.name,
    required this.code,
    required this.description,
    required this.icon,
    required this.color,
    required this.details, // Required detailed information
  });
}
