import 'package:flutter/material.dart';
import '../../../../../core/theme/app_color.dart';
import '../../../../../core/constants/app_dimens.dart';
import '../../../../../domain/entities/entities.dart';

/// Widget hiển thị banner các sản phẩm ch<PERSON>h của KienlongBank
class ProductBannersSection extends StatelessWidget {
  final User user;

  const ProductBannersSection({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final banners = _getProductBanners();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sản phẩm nổi bật KLB',
          style: TextStyle(
            fontSize: AppDimens.fontXL,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        AppDimens.h16,
        <PERSON><PERSON><PERSON>ox(
          height: 180,
          child: PageView.builder(
            itemCount: banners.length,
            controller: PageController(viewportFraction: 0.9),
            itemBuilder: (context, index) {
              final banner = banners[index];
              return _buildBannerItem(context, banner, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBannerItem(BuildContext context, ProductBanner banner, int index) {
    return Container(
      margin: EdgeInsets.only(right: AppDimens.spacingMD),
      decoration: BoxDecoration(
        borderRadius: AppDimens.borderRadius16,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: AppDimens.borderRadius16,
        child: Stack(
          children: [
            // Background Gradient
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    banner.primaryColor,
                    banner.secondaryColor,
                  ],
                ),
              ),
            ),
            
            // Content
            Padding(
              padding: AppDimens.paddingAllLg,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Icon and Title Row
                  Row(
                    children: [
                      Container(
                        width: AppDimens.containerMd,
                        height: AppDimens.containerMd,
                        decoration: BoxDecoration(
                          color: AppColors.textWhite.withValues(alpha: 0.2),
                          borderRadius: AppDimens.borderRadius8,
                        ),
                        child: Icon(
                          banner.icon,
                          color: AppColors.textWhite,
                          size: AppDimens.iconMD,
                        ),
                      ),
                      AppDimens.w12,
                      Expanded(
                        child: Text(
                          banner.title,
                          style: TextStyle(
                            fontSize: AppDimens.fontLG,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textWhite,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  AppDimens.h12,
                  
                  // Description
                  Text(
                    banner.description,
                    style: TextStyle(
                      fontSize: AppDimens.fontSM,
                      color: AppColors.textWhite.withValues(alpha: 0.9),
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const Spacer(),
                  
                  // Action Button
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _onBannerTap(context, banner),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.textWhite,
                            foregroundColor: banner.primaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: AppDimens.borderRadius8,
                            ),
                            padding: AppDimens.paddingVerticalSm,
                          ),
                          child: Text(
                            'Tìm hiểu thêm',
                            style: TextStyle(
                              fontSize: AppDimens.fontSM,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Corner decoration
            Positioned(
              top: -20,
              right: -20,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.textWhite.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onBannerTap(BuildContext context, ProductBanner banner) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Xem chi tiết: ${banner.title}')),
    );
  }

  List<ProductBanner> _getProductBanners() {
    return [
      const ProductBanner(
        id: 'klb_loan',
        title: 'Vay Tín Chấp KLB',
        description: 'Lãi suất ưu đãi chỉ từ 1.2%/tháng. Thủ tục đơn giản, giải ngân nhanh trong ngày.',
        icon: Icons.account_balance_wallet,
        primaryColor: AppColors.primaryColor,
        secondaryColor: AppColors.secondaryColor,
        imageUrl: '',
      ),
      const ProductBanner(
        id: 'klb_home',
        title: 'Vay Mua Nhà KLB',
        description: 'Lãi suất cố định 24 tháng đầu. Hỗ trợ vay lên đến 85% giá trị tài sản.',
        icon: Icons.home,
        primaryColor: AppColors.success,
        secondaryColor: AppColors.lightGreen,
        imageUrl: '',
      ),
      const ProductBanner(
        id: 'klb_business',
        title: 'Vay Kinh Doanh KLB',
        description: 'Gói tín dụng linh hoạt cho doanh nghiệp SME. Thời hạn vay lên đến 10 năm.',
        icon: Icons.business,
        primaryColor: AppColors.warning,
        secondaryColor: AppColors.orange,
        imageUrl: '',
      ),
      const ProductBanner(
        id: 'klb_card',
        title: 'Thẻ Tín Dụng KLB',
        description: 'Miễn phí thường niên. Hoàn tiền lên đến 8% cho các giao dịch mua sắm.',
        icon: Icons.credit_card,
        primaryColor: AppColors.info,
        secondaryColor: AppColors.lightBlue,
        imageUrl: '',
      ),
    ];
  }
}

/// Class đại diện cho banner sản phẩm
class ProductBanner {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color primaryColor;
  final Color secondaryColor;
  final String imageUrl;

  const ProductBanner({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.primaryColor,
    required this.secondaryColor,
    required this.imageUrl,
  });
} 