import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';

class ModernCtvOverview extends StatefulWidget {
  const ModernCtvOverview({super.key});

  @override
  State<ModernCtvOverview> createState() => _ModernCtvOverviewState();
}

class _ModernCtvOverviewState extends State<ModernCtvOverview>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  late Animation<double> _scaleAnimation;

  // Mock data theo SRS
  final Map<String, dynamic> mockData = {
    'limitData': {
      'totalLimit': **********0.0, // Hạn mức đ<PERSON>c cấp
      'creditLimitRemaining': **********.0, // <PERSON>ạn mức tín chấp còn lại
      'collateralValue': **********.0, // <PERSON><PERSON><PERSON> trị <PERSON>SBĐ
    },
    'debtData': {
      'debtWithCollateral': **********.0, // <PERSON><PERSON> nợ có TSBĐ
      'debtWithoutCollateral': **********.0, // Dư nợ không TSBĐ
      'totalCurrentDebt': **********.0, // Tổng dư nợ hiện tại
      'averageMonthlyDebt': **********.0, // Dư nợ bình quân tháng
    },
    'paymentData': {
      'principalPerDay': 2000000.0, // Số tiền gốc/ngày
      'interestPerDay': 500000.0, // Số tiền lãi/ngày
      'totalPaymentPerDay': 2500000.0, // Số tiền nộp/ngày
    },
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
      ),
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.elasticOut),
      ),
    );

    // Delay animation để smooth hơn
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title đồng bộ với các widget khác
          Text(
            'Tổng quan',
            style: TextStyle(
              fontSize: AppDimens.fontLG,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),

          AppDimens.h16,

          // Container chính
          Container(
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: AppDimens.borderRadius16,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 16,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Tab Bar
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.surfaceColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppDimens.borderRadius16.topLeft.x),
                      topRight: Radius.circular(AppDimens.borderRadius16.topRight.x),
                    ),
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: AppColors.primaryColor,
                    unselectedLabelColor: AppColors.textSecondary,
                    indicatorColor: AppColors.primaryColor,
                    indicatorWeight: 3,
                    indicatorPadding: AppDimens.paddingHorizontalLg,
                    labelStyle: TextStyle(
                      fontSize: AppDimens.fontMD,
                      fontWeight: FontWeight.w600,
                    ),
                    unselectedLabelStyle: TextStyle(
                      fontSize: AppDimens.fontMD,
                      fontWeight: FontWeight.w500,
                    ),
                    onTap: (index) {
                      // Reset animation khi chuyển tab
                      _animationController.reset();
                      _animationController.forward();
                    },
                    tabs: const [
                      Tab(text: 'Hạn mức'),
                      Tab(text: 'Dư nợ'),
                      Tab(text: 'Thanh toán'),
                    ],
                  ),
                ),

                // Tab Content
                SizedBox(
                  height: 320,
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildLimitTab(),
                      _buildDebtTab(),
                      _buildPaymentTab(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLimitTab() {
    final data = mockData['limitData'];
    final double totalLimit = (data['totalLimit'] as num).toDouble();
    final double creditLimitRemaining = (data['creditLimitRemaining'] as num).toDouble();
    final double usedLimit = totalLimit - creditLimitRemaining;

    return Padding(
      padding: AppDimens.paddingAllLg,
      child: Column(
        children: [
          // Biểu đồ vòng tròn siêu nổi bật
          Expanded(
            flex: 4,
            child: Center(
              child: _buildProminentCircularChart(
                usedValue: usedLimit,
                totalValue: totalLimit,
                primaryColor: AppColors.primaryColor,
                secondaryColor: AppColors.primaryColor.withValues(alpha: 0.3),
                centerMainText: '${((usedLimit / totalLimit) * 100).toInt()}%',
                centerSubText: _formatCurrency(usedLimit),
                centerDescText: 'đã sử dụng',
              ),
            ),
          ),

          AppDimens.h16,

          // Thông tin đơn giản
          Expanded(
            flex: 1,
            child: _buildSimpleInfoRow([
              InfoItem(
                label: 'Tổng hạn mức',
                value: _formatCurrency(totalLimit),
                color: AppColors.textPrimary,
              ),
              InfoItem(
                label: 'Còn lại',
                value: _formatCurrency(creditLimitRemaining),
                color: AppColors.primaryColor,
              ),
            ]),
          ),
        ],
      ),
    );
  }

  Widget _buildDebtTab() {
    final data = mockData['debtData'];
    final double totalCurrentDebt = (data['totalCurrentDebt'] as num).toDouble();
    final double debtWithCollateral = (data['debtWithCollateral'] as num).toDouble();

    return Padding(
      padding: AppDimens.paddingAllLg,
      child: Column(
        children: [
          // Biểu đồ vòng tròn siêu nổi bật
          Expanded(
            flex: 4,
            child: Center(
              child: _buildProminentCircularChart(
                usedValue: debtWithCollateral,
                totalValue: totalCurrentDebt,
                primaryColor: AppColors.primaryColor,
                secondaryColor: AppColors.primaryColor.withValues(alpha: 0.3),
                centerMainText: '${((debtWithCollateral / totalCurrentDebt) * 100).toInt()}%',
                centerSubText: _formatCurrency(totalCurrentDebt),
                centerDescText: 'tổng dư nợ',
              ),
            ),
          ),

          AppDimens.h16,

          // Thông tin đơn giản
          Expanded(
            flex: 1,
            child: _buildSimpleInfoRow([
              InfoItem(
                label: 'Có TSBĐ',
                value: _formatCurrency(debtWithCollateral),
                color: AppColors.primaryColor,
              ),
              InfoItem(
                label: 'Không TSBĐ',
                value: _formatCurrency(totalCurrentDebt - debtWithCollateral),
                color: AppColors.textSecondary,
              ),
            ]),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentTab() {
    final data = mockData['paymentData'];
    final double totalPaymentPerDay = (data['totalPaymentPerDay'] as num).toDouble();
    final double principalPerDay = (data['principalPerDay'] as num).toDouble();

    return Padding(
      padding: AppDimens.paddingAllLg,
      child: Column(
        children: [
          // Biểu đồ vòng tròn siêu nổi bật
          Expanded(
            flex: 4,
            child: Center(
              child: _buildProminentCircularChart(
                usedValue: principalPerDay,
                totalValue: totalPaymentPerDay,
                primaryColor: AppColors.primaryColor,
                secondaryColor: AppColors.primaryColor.withValues(alpha: 0.3),
                centerMainText: '${((principalPerDay / totalPaymentPerDay) * 100).toInt()}%',
                centerSubText: _formatCurrency(totalPaymentPerDay),
                centerDescText: 'mỗi ngày',
              ),
            ),
          ),

          AppDimens.h16,

          // Thông tin đơn giản
          Expanded(
            flex: 1,
            child: _buildSimpleInfoRow([
              InfoItem(
                label: 'Tiền gốc',
                value: _formatCurrency(principalPerDay),
                color: AppColors.primaryColor,
              ),
              InfoItem(
                label: 'Tiền lãi',
                value: _formatCurrency(totalPaymentPerDay - principalPerDay),
                color: AppColors.textSecondary,
              ),
            ]),
          ),
        ],
      ),
    );
  }

  Widget _buildProminentCircularChart({
    required double usedValue,
    required double totalValue,
    required Color primaryColor,
    required Color secondaryColor,
    required String centerMainText,
    required String centerSubText,
    required String centerDescText,
  }) {
    final double safeUsedValue = usedValue.toDouble();
    final double safeTotalValue = totalValue.toDouble();
    double percentage = safeTotalValue > 0 ? (safeUsedValue / safeTotalValue) : 0.0;
    percentage = percentage.clamp(0.0, 1.0);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: 180,
            height: 180,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.backgroundColor,
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withValues(alpha: 0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Stack(
              children: [
                // Simple progress circle with clean design
                Center(
                  child: SizedBox(
                    width: 150,
                    height: 150,
                    child: Stack(
                      children: [
                        // Background track - simple and clean
                        CircularProgressIndicator(
                          value: 1.0,
                          strokeWidth: 12,
                          backgroundColor: AppColors.textSecondary.withValues(alpha: 0.1),
                          valueColor: AlwaysStoppedAnimation(AppColors.textSecondary.withValues(alpha: 0.1)),
                        ),
                        // Main progress - simple color
                        CircularProgressIndicator(
                          value: percentage * _progressAnimation.value,
                          strokeWidth: 12,
                          backgroundColor: Colors.transparent,
                          valueColor: AlwaysStoppedAnimation(primaryColor),
                          strokeCap: StrokeCap.round,
                        ),
                      ],
                    ),
                  ),
                ),

                // Clean center content
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        centerMainText,
                        style: TextStyle(
                          fontSize: AppDimens.fontXXL,
                          fontWeight: FontWeight.bold,
                          color: primaryColor,
                        ),
                      ),
                      AppDimens.h4,
                      Text(
                        centerSubText,
                        style: TextStyle(
                          fontSize: AppDimens.fontMD,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      AppDimens.h2,
                      Text(
                        centerDescText,
                        style: TextStyle(
                          fontSize: AppDimens.fontSM,
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSimpleInfoRow(List<InfoItem> items) {
    return Row(
      children: items.map((item) => Expanded(
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: AppDimens.spacingSM / 2),
          padding: AppDimens.paddingAllMd,
          decoration: BoxDecoration(
            color: item.color.withValues(alpha: 0.08),
            borderRadius: AppDimens.borderRadius8,
            border: Border.all(
              color: item.color.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                item.label,
                style: TextStyle(
                  fontSize: AppDimens.fontXS,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              AppDimens.h4,
              Text(
                item.value,
                style: TextStyle(
                  fontSize: AppDimens.fontSM,
                  fontWeight: FontWeight.bold,
                  color: item.color,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      )).toList(),
    );
  }

  String _formatCurrency(num amount) {
    if (amount >= **********) {
      return '${(amount / **********).toStringAsFixed(1)} tỷ';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)} triệu';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} nghìn';
    }
    return '${amount.toStringAsFixed(0)} VNĐ';
  }
}

// Data model cho thông tin đơn giản
class InfoItem {
  final String label;
  final String value;
  final Color color;

  const InfoItem({
    required this.label,
    required this.value,
    required this.color,
  });
}