import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';

class ModernCtvOverviewV2 extends StatefulWidget {
  const ModernCtvOverviewV2({super.key});

  @override
  State<ModernCtvOverviewV2> createState() => _ModernCtvOverviewV2State();
}

class _ModernCtvOverviewV2State extends State<ModernCtvOverviewV2>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _progressAnimations;
  late Animation<double> _slideAnimation;

  // Mock data theo SRS
  final Map<String, dynamic> mockData = {
    'limitData': {
      'totalLimit': **********0.0, // Hạn mức được cấp
      'creditLimitRemaining': **********.0, // <PERSON>ạn mức tín chấp còn lại
      'collateralValue': **********.0, // <PERSON><PERSON><PERSON> trị <PERSON>SBĐ
    },
    'debtData': {
      'debtWithCollateral': **********.0, // <PERSON><PERSON> nợ có TSBĐ
      'debtWithoutCollateral': **********.0, // Dư nợ không TSBĐ
      'totalCurrentDebt': **********.0, // Tổng dư nợ hiện tại
      'averageMonthlyDebt': **********.0, // Dư nợ bình quân tháng
    },
    'paymentData': {
      'principalPerDay': 2000000.0, // Số tiền gốc/ngày
      'interestPerDay': 500000.0, // Số tiền lãi/ngày
      'totalPaymentPerDay': 2500000.0, // Số tiền nộp/ngày
    },
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    // Tạo animations cho từng progress bar
    _progressAnimations = List.generate(3, (index) =>
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            index * 0.2,
            0.6 + index * 0.2,
            curve: Curves.easeOutCubic,
          ),
        ),
      ),
    );
    
    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOutQuart),
      ),
    );
    
    // Delay animation
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          AnimatedBuilder(
            animation: _slideAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, _slideAnimation.value),
                child: Opacity(
                  opacity: 1 - (_slideAnimation.value / 50),
                  child: Text(
                    'Tổng quan',
                    style: TextStyle(
                      fontSize: AppDimens.fontLG,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              );
            },
          ),
          
          AppDimens.h16,

          // Cards container
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Column(
                children: [
                  // Hạn mức Card
                  _buildLimitCard(),
                  AppDimens.h12,
                  
                  // Dư nợ Card
                  _buildDebtCard(),
                  AppDimens.h12,
                  
                  // Thanh toán Card
                  _buildPaymentCard(),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLimitCard() {
    final data = mockData['limitData'];
    final double totalLimit = (data['totalLimit'] as num).toDouble();
    final double creditLimitRemaining = (data['creditLimitRemaining'] as num).toDouble();
    final double usedLimit = totalLimit - creditLimitRemaining;
    final double usagePercentage = totalLimit > 0 ? (usedLimit / totalLimit) : 0.0;
    
    return Transform.translate(
      offset: Offset(0, _slideAnimation.value),
      child: Opacity(
        opacity: 1 - (_slideAnimation.value / 50),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.success.withValues(alpha: 0.08),
                AppColors.success.withValues(alpha: 0.02),
              ],
            ),
            borderRadius: AppDimens.borderRadius16,
            border: Border.all(
              color: AppColors.success.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Padding(
            padding: AppDimens.paddingAllLg,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: AppColors.success,
                        borderRadius: AppDimens.borderRadius12,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.success.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.account_balance_outlined,
                        color: Colors.white,
                        size: 22,
                      ),
                    ),
                    AppDimens.w12,
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Hạn mức',
                            style: TextStyle(
                              fontSize: AppDimens.fontMD,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          Text(
                            'Sử dụng ${(usagePercentage * 100).toInt()}%',
                            style: TextStyle(
                              fontSize: AppDimens.fontSM,
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Mini circular progress
                    SizedBox(
                      width: 50,
                      height: 50,
                      child: Stack(
                        children: [
                          CircularProgressIndicator(
                            value: 1.0,
                            strokeWidth: 4,
                            backgroundColor: AppColors.success.withValues(alpha: 0.2),
                            valueColor: AlwaysStoppedAnimation(AppColors.success.withValues(alpha: 0.2)),
                          ),
                          CircularProgressIndicator(
                            value: usagePercentage * _progressAnimations[0].value,
                            strokeWidth: 4,
                            backgroundColor: Colors.transparent,
                            valueColor: const AlwaysStoppedAnimation(AppColors.success),
                          ),
                          Center(
                            child: Text(
                              '${(usagePercentage * 100).toInt()}%',
                              style: TextStyle(
                                fontSize: AppDimens.fontXS,
                                fontWeight: FontWeight.bold,
                                color: AppColors.success,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                AppDimens.h16,
                
                // Progress bar cho tổng hạn mức
                Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Tổng hạn mức',
                          style: TextStyle(
                            fontSize: AppDimens.fontSM,
                            color: AppColors.textSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          _formatCurrency(totalLimit),
                          style: TextStyle(
                            fontSize: AppDimens.fontSM,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                    AppDimens.h8,
                    Container(
                      height: 6,
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.2),
                        borderRadius: AppDimens.borderRadius4,
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: usagePercentage * _progressAnimations[0].value,
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.success,
                            borderRadius: AppDimens.borderRadius4,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                
                AppDimens.h12,
                
                // Thông tin chi tiết
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Còn lại',
                        _formatCurrency(creditLimitRemaining),
                        AppColors.success,
                      ),
                    ),
                    AppDimens.w16,
                    Expanded(
                      child: _buildInfoItem(
                        'Đã dùng',
                        _formatCurrency(usedLimit),
                        AppColors.warning,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDebtCard() {
    final data = mockData['debtData'];
    final double totalCurrentDebt = (data['totalCurrentDebt'] as num).toDouble();
    final double debtWithCollateral = (data['debtWithCollateral'] as num).toDouble();
    final double debtWithoutCollateral = (data['debtWithoutCollateral'] as num).toDouble();
    final double collateralPercentage = totalCurrentDebt > 0 ? (debtWithCollateral / totalCurrentDebt) : 0.0;
    
    return Transform.translate(
      offset: Offset(0, _slideAnimation.value * 0.7),
      child: Opacity(
        opacity: 1 - (_slideAnimation.value / 70),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.error.withValues(alpha: 0.08),
                AppColors.error.withValues(alpha: 0.02),
              ],
            ),
            borderRadius: AppDimens.borderRadius16,
            border: Border.all(
              color: AppColors.error.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Padding(
            padding: AppDimens.paddingAllLg,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: AppColors.error,
                        borderRadius: AppDimens.borderRadius12,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.error.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.trending_up_outlined,
                        color: Colors.white,
                        size: 22,
                      ),
                    ),
                    AppDimens.w12,
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Dư nợ',
                            style: TextStyle(
                              fontSize: AppDimens.fontMD,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          Text(
                            _formatCurrency(totalCurrentDebt),
                            style: TextStyle(
                              fontSize: AppDimens.fontSM,
                              color: AppColors.error,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Mini donut chart
                    SizedBox(
                      width: 50,
                      height: 50,
                      child: Stack(
                        children: [
                          CircularProgressIndicator(
                            value: 1.0,
                            strokeWidth: 4,
                            backgroundColor: AppColors.warning.withValues(alpha: 0.2),
                            valueColor: AlwaysStoppedAnimation(AppColors.warning.withValues(alpha: 0.2)),
                          ),
                          CircularProgressIndicator(
                            value: collateralPercentage * _progressAnimations[1].value,
                            strokeWidth: 4,
                            backgroundColor: Colors.transparent,
                            valueColor: const AlwaysStoppedAnimation(AppColors.error),
                          ),
                          Center(
                            child: Text(
                              '${(collateralPercentage * 100).toInt()}%',
                              style: TextStyle(
                                fontSize: AppDimens.fontXS,
                                fontWeight: FontWeight.bold,
                                color: AppColors.error,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                AppDimens.h16,
                
                // Breakdown
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Có TSBĐ',
                        _formatCurrency(debtWithCollateral),
                        AppColors.error,
                      ),
                    ),
                    AppDimens.w16,
                    Expanded(
                      child: _buildInfoItem(
                        'Không TSBĐ',
                        _formatCurrency(debtWithoutCollateral),
                        AppColors.warning,
                      ),
                    ),
                  ],
                ),
                
                AppDimens.h12,
                
                // BQ tháng
                Container(
                  padding: AppDimens.paddingAllMd,
                  decoration: BoxDecoration(
                    color: AppColors.error.withValues(alpha: 0.05),
                    borderRadius: AppDimens.borderRadius8,
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.calendar_month_outlined,
                        color: AppColors.error,
                        size: 18,
                      ),
                      AppDimens.w8,
                      Text(
                        'BQ dư nợ/tháng: ',
                        style: TextStyle(
                          fontSize: AppDimens.fontSM,
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        _formatCurrency(data['averageMonthlyDebt']),
                        style: TextStyle(
                          fontSize: AppDimens.fontSM,
                          fontWeight: FontWeight.w600,
                          color: AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentCard() {
    final data = mockData['paymentData'];
    final double totalPaymentPerDay = (data['totalPaymentPerDay'] as num).toDouble();
    final double principalPerDay = (data['principalPerDay'] as num).toDouble();
    final double interestPerDay = (data['interestPerDay'] as num).toDouble();
    final double principalPercentage = totalPaymentPerDay > 0 ? (principalPerDay / totalPaymentPerDay) : 0.0;
    
    return Transform.translate(
      offset: Offset(0, _slideAnimation.value * 0.4),
      child: Opacity(
        opacity: 1 - (_slideAnimation.value / 100),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primaryColor.withValues(alpha: 0.08),
                AppColors.primaryColor.withValues(alpha: 0.02),
              ],
            ),
            borderRadius: AppDimens.borderRadius16,
            border: Border.all(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Padding(
            padding: AppDimens.paddingAllLg,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        borderRadius: AppDimens.borderRadius12,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryColor.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.payment_outlined,
                        color: Colors.white,
                        size: 22,
                      ),
                    ),
                    AppDimens.w12,
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Thanh toán hàng ngày',
                            style: TextStyle(
                              fontSize: AppDimens.fontMD,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          Text(
                            _formatCurrency(totalPaymentPerDay),
                            style: TextStyle(
                              fontSize: AppDimens.fontSM,
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Mini pie chart
                    SizedBox(
                      width: 50,
                      height: 50,
                      child: Stack(
                        children: [
                          CircularProgressIndicator(
                            value: 1.0,
                            strokeWidth: 4,
                            backgroundColor: AppColors.secondaryColor.withValues(alpha: 0.2),
                            valueColor: AlwaysStoppedAnimation(AppColors.secondaryColor.withValues(alpha: 0.2)),
                          ),
                          CircularProgressIndicator(
                            value: principalPercentage * _progressAnimations[2].value,
                            strokeWidth: 4,
                            backgroundColor: Colors.transparent,
                            valueColor: const AlwaysStoppedAnimation(AppColors.primaryColor),
                          ),
                          Center(
                            child: Text(
                              '${(principalPercentage * 100).toInt()}%',
                              style: TextStyle(
                                fontSize: AppDimens.fontXS,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                AppDimens.h16,
                
                // Breakdown
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        'Tiền gốc',
                        _formatCurrency(principalPerDay),
                        AppColors.primaryColor,
                      ),
                    ),
                    AppDimens.w16,
                    Expanded(
                      child: _buildInfoItem(
                        'Tiền lãi',
                        _formatCurrency(interestPerDay),
                        AppColors.secondaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, Color color) {
    return Container(
      padding: AppDimens.paddingAllMd,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.08),
        borderRadius: AppDimens.borderRadius8,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: AppDimens.fontXS,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          AppDimens.h4,
          Text(
            value,
            style: TextStyle(
              fontSize: AppDimens.fontSM,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _formatCurrency(num amount) {
    if (amount >= **********) {
      return '${(amount / **********).toStringAsFixed(1)} tỷ';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)} triệu';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} nghìn';
    }
    return '${amount.toStringAsFixed(0)} VNĐ';
  }
} 