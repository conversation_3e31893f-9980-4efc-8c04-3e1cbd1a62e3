import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';

class ModernUserGuide extends StatelessWidget {
  const ModernUserGuide({super.key});

  // Mock data hướng dẫn cơ bản theo SRS
  static const List<Map<String, dynamic>> mockGuideData = [
    {
      'icon': Icons.person_add_outlined,
      'title': 'Quản lý khách hàng',
      'description': 'Tì<PERSON> kiếm, thêm mới và quản lý thông tin khách hàng một cách dễ dàng',
      'steps': [
        'Nhấn vào "Tìm kiếm" để tìm khách hàng theo CCCD',
        '<PERSON><PERSON> dụng "Tạo khoản vay" để tạo hồ sơ vay mới',
        '<PERSON> dõi trạng thái hồ sơ trong phần "Đã xem gần đây"',
      ],
    },
    {
      'icon': Icons.analytics_outlined,
      'title': '<PERSON> dõi báo cáo',
      'description': '<PERSON>em báo cáo tổng quan về hạn mức, dư nợ và hiệu suất công việc',
      'steps': [
        'Kiểm tra "Tổng quan" để xem hạn mức và dư nợ',
        'Xem "Báo cáo nhanh" để theo dõi KPI',
        'Sử dụng "Báo cáo" để xem chi tiết hơn',
      ],
    },
    {
      'icon': Icons.settings_outlined,
      'title': 'Thiết lập tài khoản',
      'description': 'Cập nhật thông tin cá nhân và cài đặt ứng dụng',
      'steps': [
        'Vào "Cài đặt" để cập nhật thông tin cá nhân',
        'Bật thông báo để nhận cập nhật kịp thời',
        'Đổi mật khẩu định kỳ để bảo mật tài khoản',
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'Hướng dẫn sử dụng',
            style: TextStyle(
              fontSize: AppDimens.fontLG,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),

          AppDimens.h16,

          // Guide cards
          ...List.generate(
            mockGuideData.length,
            (index) => _buildGuideCard(mockGuideData[index]),
          ),

          // Support section
          AppDimens.h16,
          _buildSupportSection(),

          // Bottom spacing
          AppDimens.h16,
        ],
      ),
    );
  }

  Widget _buildGuideCard(Map<String, dynamic> guide) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimens.spacingMD),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: AppDimens.borderRadius16,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primaryColor.withValues(alpha: 0.1),
            borderRadius: AppDimens.borderRadius8,
          ),
          child: Icon(
            guide['icon'] as IconData,
            color: AppColors.primaryColor,
            size: 20,
          ),
        ),
        title: Text(
          guide['title'],
          style: TextStyle(
            fontSize: AppDimens.fontMD,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        subtitle: Padding(
          padding: EdgeInsets.only(top: AppDimens.spacingXS),
          child: Text(
            guide['description'],
            style: TextStyle(
              fontSize: AppDimens.fontSM,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        iconColor: AppColors.primaryColor,
        collapsedIconColor: AppColors.textSecondary,
        tilePadding: AppDimens.paddingAllMd,
        childrenPadding: EdgeInsets.fromLTRB(
          AppDimens.spacingMD,
          0,
          AppDimens.spacingMD,
          AppDimens.spacingMD,
        ),
        children: [
          Container(
            width: double.infinity,
            padding: AppDimens.paddingAllMd,
            decoration: BoxDecoration(
              color: AppColors.surfaceColor,
              borderRadius: AppDimens.borderRadius12,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Các bước thực hiện:',
                  style: TextStyle(
                    fontSize: AppDimens.fontSM,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                AppDimens.h8,
                ...List.generate(
                  (guide['steps'] as List).length,
                  (index) => _buildStepItem(
                    index + 1,
                    guide['steps'][index],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepItem(int stepNumber, String stepText) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimens.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: AppDimens.borderRadius4,
            ),
            child: Center(
              child: Text(
                stepNumber.toString(),
                style: TextStyle(
                  fontSize: AppDimens.fontXS,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          AppDimens.w8,
          Expanded(
            child: Text(
              stepText,
              style: TextStyle(
                fontSize: AppDimens.fontSM,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w400,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSupportSection() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryColor.withValues(alpha: 0.08),
            AppColors.secondaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: AppDimens.borderRadius16,
        border: Border.all(
          color: AppColors.primaryColor.withValues(alpha: 0.15),
          width: 1,
        ),
      ),
      child: Padding(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: AppDimens.borderRadius8,
                  ),
                  child: const Icon(
                    Icons.headset_mic_outlined,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                AppDimens.w12,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Cần hỗ trợ?',
                        style: TextStyle(
                          fontSize: AppDimens.fontMD,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      AppDimens.h4,
                      Text(
                        'Liên hệ đội ngũ hỗ trợ kỹ thuật 24/7',
                        style: TextStyle(
                          fontSize: AppDimens.fontSM,
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            AppDimens.h16,

            Row(
              children: [
                Expanded(
                  child: _buildContactButton(
                    icon: Icons.phone_outlined,
                    label: 'Gọi hotline',
                    value: '1900 xxxx',
                    color: AppColors.success,
                  ),
                ),
                AppDimens.w12,
                Expanded(
                  child: _buildContactButton(
                    icon: Icons.chat_outlined,
                    label: 'Live chat',
                    value: 'Trò chuyện',
                    color: AppColors.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactButton({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: AppDimens.paddingAllMd,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          // Handle contact action
        },
        borderRadius: AppDimens.borderRadius12,
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            AppDimens.h8,
            Text(
              label,
              style: TextStyle(
                fontSize: AppDimens.fontXS,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            AppDimens.h4,
            Text(
              value,
              style: TextStyle(
                fontSize: AppDimens.fontSM,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}