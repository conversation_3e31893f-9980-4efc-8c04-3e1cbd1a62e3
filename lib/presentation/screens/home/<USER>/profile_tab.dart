import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sales_app/presentation/controllers/auth/auth_controller.dart';
import 'package:sales_app/presentation/controllers/auth/auth_state.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';
import '../../../../domain/entities/entities.dart';
import '../../../router/navigation_extensions.dart';
import '../../../widgets/common/common.dart';
import 'package:sales_app/generated/l10n.dart';

/// Tab cá nhân - hiển thị thông tin người dùng và cài đặt
class ProfileTab extends ConsumerStatefulWidget {
  final User user;

  const ProfileTab({
    super.key,
    required this.user,
  });

  @override
  ConsumerState<ProfileTab> createState() => _ProfileTabState();
}

class _ProfileTabState extends ConsumerState<ProfileTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    // Listen to auth state changes for logout
    ref.listen<AuthState>(authControllerProvider, (previous, next) {
      next.when(
        (isLoading, isLoggedIn, user, failure, isEmailValid, isPasswordValid) {
          // Handle default state
        },
        initial: () {},
        loading: () {},
        authenticated: (user) {},
        unauthenticated: (failure) {
          // User has been logged out, navigate to login
          if (mounted) {
            context.goToLogin();
          }
        },
        error: (failure) {
          // Handle logout error but still navigate to login
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(S.of(context).logout_error),
                backgroundColor: AppColors.error,
              ),
            );
            context.goToLogin();
          }
        },
      );
    });
    
    return Scaffold(
      backgroundColor: AppColors.surfaceColor,
      appBar: AppBar(
        title: Text(
          S.of(context).profile,
          style: const TextStyle(
            color: AppColors.textWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: AppDimens.paddingAllLg,
          child: Column(
            children: [
              // Logout Button using BottomButton
              _buildLogoutButton(),
              AppDimens.h24, // Extra padding at bottom
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return CommonButton(
      title: S.of(context).logout,
      onPressed: _logout,
      backgroundColor: AppColors.error,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.logout,
            color: AppColors.textWhite,
          ),
          AppDimens.w8,
          Text(
            S.of(context).logout,
            style: TextStyle(
              fontSize: AppDimens.fontMD,
              fontWeight: FontWeight.w600,
              color: AppColors.textWhite,
            ),
          ),
        ],
      ),
    );
  }

  void _logout() {
    CommonDialog.showConfirmDialog(
      context: context,
      title: S.of(context).logout_title,
      message: S.of(context).logout_message,
      confirmText: S.of(context).logout_confirm,
      cancelText: S.of(context).logout_cancel,
      isDangerous: true,
      onConfirm: () {
        ref.read(authControllerProvider.notifier).logout();
      },
    );
  }
} 