import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';

class ModernQuickActions extends StatelessWidget {
  final int notificationCount;

  const ModernQuickActions({
    super.key,
    required this.notificationCount,
  });

  // Simplified actions - ít hơn và đơn giản hơn
  final List<SimpleActionItem> quickActions = const [
    SimpleActionItem(
      icon: Icons.add_circle_outline,
      title: 'Tạo mới',
      color: AppColors.success,
      badge: null,
    ),
    SimpleActionItem(
      icon: Icons.attach_money,
      title: 'Thu tiền',
      color: AppColors.warning,
      badge: 8,
    ),
    SimpleActionItem(
      icon: Icons.people_outline,
      title: 'Giới thiệu',
      color: AppColors.info,
      badge: null,
    ),
    SimpleActionItem(
      icon: Icons.help_outline,
      title: 'Hỗ trợ',
      color: AppColors.textSecondary,
      badge: null,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: AppDimens.marginHorizontalLg.copyWith(top: AppDimens.spacingLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Simple header
          Text(
            'Thao tác nhanh',
            style: TextStyle(
              fontSize: AppDimens.fontMD, // Giảm font size
              fontWeight: FontWeight.w500, // Giảm font weight
              color: AppColors.textSecondary, // Màu nhẹ hơn
            ),
          ),

          AppDimens.h12,

          // Simple horizontal list
          SizedBox(
            height: 70, // Giảm height đáng kể
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              itemCount: quickActions.length,
              separatorBuilder: (context, index) => AppDimens.w12,
              itemBuilder: (context, index) {
                final action = quickActions[index];
                return _buildSimpleActionItem(action, context);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleActionItem(SimpleActionItem action, BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _onActionTap(action, context),
        borderRadius: AppDimens.borderRadius12,
        child: Container(
          width: 80, // Width nhỏ hơn
          padding: AppDimens.paddingAllSm, // Padding nhỏ hơn
          decoration: BoxDecoration(
            color: AppColors.backgroundColor, // Background đơn giản
            borderRadius: AppDimens.borderRadius12,
            border: Border.all(
              color: AppColors.surfaceColor, // Border nhẹ
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Simple icon với badge
              Stack(
                clipBehavior: Clip.none,
                children: [
                  Container(
                    width: 32, // Icon container nhỏ hơn
                    height: 32,
                    decoration: BoxDecoration(
                      color: action.color.withValues(alpha: 0.1), // Background nhẹ
                      borderRadius: AppDimens.borderRadius8,
                    ),
                    child: Icon(
                      action.icon,
                      color: action.color,
                      size: AppDimens.iconXS + 2, // Icon nhỏ hơn
                    ),
                  ),
                  if (action.badge != null)
                    Positioned(
                      top: -4,
                      right: -4,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: AppColors.error,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            action.badge! > 9 ? '9+' : '${action.badge}',
                            style: TextStyle(
                              fontSize: AppDimens.fontXS - 2, // Font rất nhỏ
                              fontWeight: FontWeight.bold,
                              color: AppColors.textWhite,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),

              AppDimens.h4,

              // Simple title
              Text(
                action.title,
                style: TextStyle(
                  fontSize: AppDimens.fontXS, // Font nhỏ
                  fontWeight: FontWeight.w500, // Weight vừa phải
                  color: AppColors.textSecondary, // Màu nhẹ
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onActionTap(SimpleActionItem action, BuildContext context) {
    String message;
    switch (action.title) {
      case 'Tạo mới':
        message = 'Tạo khoản vay mới';
        break;
      case 'Thu tiền':
        message = 'Thu tiền từ ${action.badge} khách hàng';
        break;
      case 'Giới thiệu':
        message = 'Giới thiệu cộng tác viên mới';
        break;
      case 'Hỗ trợ':
        message = 'Liên hệ hỗ trợ';
        break;
      default:
        message = 'Thực hiện ${action.title}';
    }

    // Simple feedback - không quá nổi bật
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.textSecondary.withValues(alpha: 0.9),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
        shape: RoundedRectangleBorder(
          borderRadius: AppDimens.borderRadius8,
        ),
      ),
    );
  }
}

// Simplified data model
class SimpleActionItem {
  final IconData icon;
  final String title;
  final Color color;
  final int? badge;

  const SimpleActionItem({
    required this.icon,
    required this.title,
    required this.color,
    required this.badge,
  });
}
