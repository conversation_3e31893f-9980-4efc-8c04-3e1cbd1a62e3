import 'package:flutter/material.dart';
import '../../../../core/constants/app_dimens.dart';
import '../../../../domain/entities/entities.dart';
import 'sections/product_modules_section.dart';
import 'sections/incomplete_records_section.dart';
import 'sections/product_info_section.dart';
import 'sections/recent_viewed_section.dart';
import 'sections/product_banners_section.dart';
import 'sections/user_guide_section.dart';

/// Widget Body cho trang chủ - hiển thị nội dung theo role người dùng
class HomeBody extends StatelessWidget {
  final User user;

  const HomeBody({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: AppDimens.paddingHorizontalLg,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 1. Danh sách module sản phẩm (hiển thị cho tất cả role)
          AppDimens.h32,

          ProductModulesSection(user: user),

          // 2. <PERSON><PERSON> sơ chưa ho<PERSON> tấ<PERSON> (chỉ hiển thị cho CTV, <PERSON><PERSON> trưởng, <PERSON><PERSON>h<PERSON>, <PERSON><PERSON><PERSON><PERSON> trưởng)
          if (user.shouldShowIncompleteRecords) ...[
            IncompleteRecordsSection(user: user),
            AppDimens.h24,
          ],

          // 3. Thông tin sản phẩm (hiển thị cho tất cả role)
          ProductInfoSection(user: user),

          AppDimens.h24,

          // 4. Đã xem gần đây (chỉ hiển thị cho CTV, Tổ trưởng, Tổ phó, Nhóm trưởng)
          if (user.shouldShowRecentViewed) ...[
            RecentViewedSection(user: user),
            AppDimens.h24,
          ],

          // 5. Banner sản phẩm (chỉ hiển thị cho role khác)
          if (user.shouldShowProductBanners) ...[
            ProductBannersSection(user: user),
            AppDimens.h24,
          ],

          // 6. Hướng dẫn sử dụng (hiển thị cho tất cả role)
          UserGuideSection(user: user),
        ],
      ),
    );
  }
} 