import 'package:flutter/material.dart';
import '../../../../core/theme/app_color.dart';
import '../../../../core/constants/app_dimens.dart';
import '../../../../domain/entities/entities.dart';

/// Tab báo cáo - hiển thị các thống kê và báo cáo theo role
class ReportTab extends StatefulWidget {
  final User user;

  const ReportTab({
    super.key,
    required this.user,
  });

  @override
  State<ReportTab> createState() => _ReportTabState();
}

class _ReportTabState extends State<ReportTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppColors.surfaceColor,
      appBar: AppBar(
        title: const Text(
          'Báo cáo',
          style: TextStyle(
            color: AppColors.textWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: Padding(
          padding: AppDimens.paddingAllLg,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: AppDimens.paddingAllXl,
                decoration: BoxDecoration(
                  color: AppColors.backgroundColor,
                  borderRadius: AppDimens.borderRadius16,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.bar_chart_rounded,
                      size: AppDimens.iconXXL * 2,
                      color: AppColors.primaryColor,
                    ),
                    AppDimens.h16,
                    Text(
                      'Báo cáo & Thống kê',
                      style: TextStyle(
                        fontSize: AppDimens.fontXXL,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    AppDimens.h8,
                    Text(
                      'Tính năng đang được phát triển',
                      style: TextStyle(
                        fontSize: AppDimens.fontMD,
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    AppDimens.h16,
                    Text(
                      'Sẽ hiển thị các báo cáo chi tiết\ntheo vai trò: ${widget.user.displayName}',
                      style: TextStyle(
                        fontSize: AppDimens.fontSM,
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 