import 'package:flutter_hooks/flutter_hooks.dart';
import 'dart:io';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';
// Widgets barrel export
import 'package:sales_app/presentation/widgets/widgets.dart';
// Controllers barrel export
import 'package:sales_app/presentation/controllers/controllers.dart';
// Presentation models
import 'package:sales_app/presentation/models/ctv_registration_flow_data.dart';
// Navigation extensions
import '../../router/navigation_extensions.dart';
// Presentation utils
import '../../utils/utils.dart';
// Domain entities
import 'package:sales_app/domain/entities/entities.dart';


class IdentityUploadPage extends HookConsumerWidget {
  final String? customTitle;
  final String? customDescription;
  final bool isCTVFlow;

  const IdentityUploadPage({
    super.key,
    this.customTitle,
    this.customDescription,
    this.isCTVFlow = false,
  });

  /// Navigate to QR scan screen using CommonQRScanner qua GoRouter
  void _goToQRScan(BuildContext context, WidgetRef ref, bool isCTVFlow) {
    context.goToQRScan(
      onQRCodeDetected: (qrData) {
        _handleQRCodeDetected(context, ref, qrData, isCTVFlow);
      },
      onSkip: () => _handleQRScanSkip(context, ref, isCTVFlow),
      onBack: () => _handleQRScanBack(context, ref, isCTVFlow),
      customTitle: _getQRScanTitle(isCTVFlow),
      customGuidanceText: 'Đưa QR vào khung hình hoặc chọn ảnh từ thư viện',
      customErrorMessage: 'Không thể đọc thông tin từ mã QR. Vui lòng thử lại hoặc chọn ảnh từ thư viện.',
    );
  }

  /// Handle QR code detected event
  void _handleQRCodeDetected(BuildContext context, WidgetRef ref, String qrData, bool isCTVFlow) {
    AppLogger.info('QR Code detected', data: {
      'isCTVFlow': isCTVFlow,
      'qrDataLength': qrData.length,
    });

    // Parse QR data to CCCD info with error dialog
    final cccdInfo = QRParseHelper.parseWithErrorDialog<CCCDInfoEntity>(
      context: context,
      qrData: qrData,
      parseFunction: QRParser.parseCccdInfoFromQr,
      errorMessage: 'Không thể đọc thông tin từ mã QR. Vui lòng thử lại hoặc chọn ảnh từ thư viện.',
      dialogTitle: 'Lỗi quét QR',
      dialogContent: 'Mã QR không đúng định dạng hoặc bị hỏng. Vui lòng thử lại hoặc chọn ảnh từ thư viện.',
    );
    
    if (cccdInfo != null) {
      _handleSuccessfulQRParse(context, ref, cccdInfo, isCTVFlow);
    }
  }

  /// Handle successful QR parse
  void _handleSuccessfulQRParse(BuildContext context, WidgetRef ref, CCCDInfoEntity cccdInfo, bool isCTVFlow) {
    AppLogger.info('QR Parse successful', data: {
      'isCTVFlow': isCTVFlow,
      'cccdInfo': cccdInfo.toJson(),
    });

    // Cập nhật CCCD info vào controller
    final ctvController = ref.read(cTVRegistrationControllerProvider.notifier);
    ctvController.updateCCCDInfo(cccdInfo);
    
    // Chuyển step sang xác nhận thông tin cá nhân
    ctvController.goToStep(CTVRegistrationStep.personalInfoConfirmation);
    
    // Navigate sang màn xác nhận thông tin cá nhân
    // Camera sẽ tự động stop khi navigate
    context.replaceWithPersonalInfoConfirmation(
      isCTVFlow: isCTVFlow,
      cccdInfo: cccdInfo,
    );
  }

  /// Handle QR scan skip event
  void _handleQRScanSkip(BuildContext context, WidgetRef ref, bool isCTVFlow) {
    AppLogger.info('QR Scan skipped', data: {
      'isCTVFlow': isCTVFlow,
    });

    // Nếu user bỏ qua, vẫn chuyển step như bình thường
    final ctvController = ref.read(cTVRegistrationControllerProvider.notifier);
    ctvController.goToStep(CTVRegistrationStep.personalInfoConfirmation);
    
    // Navigate sang màn xác nhận thông tin cá nhân với CCCD info rỗng
    context.replaceWithPersonalInfoConfirmation(
      isCTVFlow: isCTVFlow,
      cccdInfo: null,
    );
  }

  /// Handle QR scan back event
  void _handleQRScanBack(BuildContext context, WidgetRef ref, bool isCTVFlow) {
    AppLogger.info('QR Scan back pressed', data: {
      'isCTVFlow': isCTVFlow,
    });

    // Nếu user back, chuyển step về upload giấy tờ
    final ctvController = ref.read(cTVRegistrationControllerProvider.notifier);
    ctvController.goToStep(CTVRegistrationStep.documentUpload);
    
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else {
      // Nếu không thể pop, quay về identity upload
      context.goToIdentityUpload(isCTVFlow: isCTVFlow);
    }
  }

  /// Get QR scan title based on flow type
  String _getQRScanTitle(bool isCTVFlow) {
    return isCTVFlow
        ? 'Quét QR giấy tờ người được giới thiệu'
        : 'Quét QR giấy tờ tùy thân';
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final selectedDocType = useState<DocumentType>(DocumentType.cccd);
    final frontImage = useState<String?>(null);
    final backImage = useState<String?>(null);
    final frontFile = useState<File?>(null);
    final backFile = useState<File?>(null);
    final hasNavigatedToQRScan = useState(false); // Flag để tránh duplicate navigation

    // Sử dụng controller mới cho flow đăng ký CTV
    final ctvController = ref.read(cTVRegistrationControllerProvider.notifier);
    final ctvState = ref.watch(cTVRegistrationControllerProvider);

    void handleImagePick(bool isFront, String path) {
      if (isFront) {
        frontImage.value = path;
        frontFile.value = File(path);
      } else {
        backImage.value = path;
        backFile.value = File(path);
      }
    }

    final isFormValid = frontFile.value != null && backFile.value != null;

    void handleContinue() async {
      if (!isFormValid || ctvState.isProcessing) return;
      await ctvController.uploadIdentityDocuments(
        documentType: selectedDocType.value,
        frontFile: frontFile.value!,
        backFile: backFile.value!,
      );
    }

    void handleRetry() async {
      if (!isFormValid || ctvState.isProcessing) return;
      await ctvController.retryUpload(
        documentType: selectedDocType.value,
        frontFile: frontFile.value!,
        backFile: backFile.value!,
      );
    }

    // Theo dõi state để chuyển bước khi upload thành công
    useEffect(() {
      final currentData = ctvState.data;
      
      // Reset flag khi step thay đổi (khi quay lại màn hình)
      if (currentData?.currentStep == CTVRegistrationStep.documentUpload) {
        hasNavigatedToQRScan.value = false;
      }
      
      // Chỉ navigation khi:
      // 1. Upload thành công
      // 2. Step chuyển sang qrScan  
      // 3. Chưa navigation trước đó
      if (ctvState.isSuccess && 
          currentData?.currentStep == CTVRegistrationStep.qrScan &&
          !hasNavigatedToQRScan.value) {
        
        hasNavigatedToQRScan.value = true;
        
        AppLogger.info('Upload thành công, chuyển sang QR scan', data: {
          'isCTVFlow': isCTVFlow,
          'frontUrl': currentData?.identityUploadResult?.frontImage,
          'backUrl': currentData?.identityUploadResult?.backImage,
        });
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (context.mounted) {
            _goToQRScan(context, ref, isCTVFlow);
          }
        });
      }
      return null;
    }, [ctvState]);

    return BaseScreen(
      title: customTitle ?? (isCTVFlow ? 'Giới thiệu CTV' : 'Đăng ký CTV'),
      body: BaseStateConsumerBuilder<CTVRegistrationFlowData>(
        stateProvider: cTVRegistrationControllerProvider,
        successBuilder: (data, ref) => _buildContent(
          context,
          theme,
          selectedDocType,
          frontImage,
          backImage,
          frontFile,
          backFile,
          handleImagePick,
          isFormValid,
          handleContinue,
          handleRetry,
          ref,
          ctvState,
        ),
        errorBuilder: (failure, onRetry, ref) => _buildContent(
          context,
          theme,
          selectedDocType,
          frontImage,
          backImage,
          frontFile,
          backFile,
          handleImagePick,
          isFormValid,
          handleContinue,
          handleRetry,
          ref,
          ctvState,
        ),
        loadingBuilder: (ref) => _buildContent(
          context,
          theme,
          selectedDocType,
          frontImage,
          backImage,
          frontFile,
          backFile,
          handleImagePick,
          isFormValid,
          handleContinue,
          handleRetry,
          ref,
          ctvState,
        ),
        initialBuilder: (ref) => _buildContent(
          context,
          theme,
          selectedDocType,
          frontImage,
          backImage,
          frontFile,
          backFile,
          handleImagePick,
          isFormValid,
          handleContinue,
          handleRetry,
          ref,
          ctvState,
        ),
        submittingOverlay: const DefaultSubmittingWidget(),
        showContentWhileSubmitting: true,
        showErrorWidgetOnSubmittingError: false,
      ).withErrorHandler<CTVRegistrationFlowData>(
        cTVRegistrationControllerProvider,
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    ThemeData theme,
    ValueNotifier<DocumentType> selectedDocType,
    ValueNotifier<String?> frontImage,
    ValueNotifier<String?> backImage,
    ValueNotifier<File?> frontFile,
    ValueNotifier<File?> backFile,
    Function(bool, String) handleImagePick,
    bool isFormValid,
    VoidCallback handleContinue,
    VoidCallback handleRetry,
    WidgetRef ref,
    BaseState<CTVRegistrationFlowData> ctvState,
  ) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: AppDimens.paddingAllMd,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header section
                _buildHeaderSection(theme),

                AppDimens.h24,

                // Document type selection
                _buildDocumentTypeSection(theme, selectedDocType, frontImage, backImage, frontFile, backFile),

                AppDimens.h24,

                // Image upload section
                _buildImageUploadSection(
                    theme, frontImage, backImage, handleImagePick, documentType: selectedDocType.value),
              ],
            ),
          ),
        ),

        // Bottom button
        BottomButton(
          title: S.of(context).continueText,
          onPressed: isFormValid ? handleContinue : null,
          enabled: isFormValid,
        ),
      ],
    );
  }

  Widget _buildHeaderSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
          Text(
            'Cung cấp giấy tờ tuỳ thân',
            style: theme.textTheme.titleMedium?.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          AppDimens.h8,

        // Description
        Text(
          customDescription ??
              'Vui lòng chọn loại giấy tờ tùy thân để xác thực thông tin',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontSize: 14,
            color: AppColors.textPrimary,
          ),
        ),

        AppDimens.h16,

        // Info card
        Container(
          padding: AppDimens.paddingAllMd,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: AppDimens.borderRadius12,
            border: Border.all(
              color: Colors.red.withValues(alpha: 0.5),
              width: AppDimens.borderWidthThin,
            ),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppColors.primaryColor,
                size: 20,
              ),
              AppDimens.w12,
              Expanded(
                child: Text(
                  'Đảm bảo ảnh rõ nét, không bị mờ hoặc che khuất thông tin quan trọng',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.primaryColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentTypeSection(
      ThemeData theme, 
      ValueNotifier<DocumentType> selectedDocType,
      ValueNotifier<String?> frontImage,
      ValueNotifier<String?> backImage,
      ValueNotifier<File?> frontFile,
      ValueNotifier<File?> backFile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loại giấy tờ',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        AppDimens.h8,
        CommonDropdown<DocumentType>(
          items: DocumentType.values.where((element) => element != DocumentType.passport).toList(),
          value: selectedDocType.value,
          label: 'Loại giấy tờ',
          hint: 'Chọn loại giấy tờ',
          isRequired: true,
          onChanged: (doc) {
            // Chỉ reset khi thực sự thay đổi loại giấy tờ
            if (selectedDocType.value != doc) {
              selectedDocType.value = doc;
              
              // Reset các trường ảnh khi thay đổi loại giấy tờ
              _resetImageFields(frontImage, backImage, frontFile, backFile);
              
            }
          },
          labelBuilder: (doc) => doc.label,
          backgroundColor: AppColors.surfaceColor,
          borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
      ],
    );
  }

  Widget _buildImageUploadSection(
    ThemeData theme,
    ValueNotifier<String?> frontImage,
    ValueNotifier<String?> backImage,
    Function(bool, String) handleImagePick,
    {required DocumentType documentType}
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppDimens.h8,

        // Front image
        DocumentImagePicker(
          label: 'Ảnh mặt trước',
          imagePath: frontImage.value,
          onImageSelected: (path) => handleImagePick(true, path),
          documentType: documentType,
        ),

        AppDimens.h16,

        // Back image
        DocumentImagePicker(
          label: 'Ảnh mặt sau',
          imagePath: backImage.value,
          onImageSelected: (path) => handleImagePick(false, path),
          documentType: documentType,
        ),

        AppDimens.h16,
      ],
    );
  }

  /// Reset tất cả các trường ảnh về null
  void _resetImageFields(
    ValueNotifier<String?> frontImage,
    ValueNotifier<String?> backImage,
    ValueNotifier<File?> frontFile,
    ValueNotifier<File?> backFile,
  ) {
    frontImage.value = null;
    backImage.value = null;
    frontFile.value = null;
    backFile.value = null;
  }

}
