# Identity Upload Page

Mà<PERSON> hình upload giấy tờ tùy thân với 2 mặt, sử dụng BaseState và AutoDisposeBaseStateMixin.

## 🎯 Tính năng

- **Chọn loại GTTT**: CCCD, Thẻ <PERSON><PERSON><PERSON>, <PERSON><PERSON> chiếu
- **Upload 2 mặt**: Mặt trước và mặt sau
- **State Management**: Sử dụng BaseState với AutoDisposeBaseStateMixin
- **Error Handling**: Xử lý lỗi tự động với retry
- **Navigation**: Tự động chuyển màn hình khi upload thành công

## 🏗️ Kiến trúc

### Controller
```dart
@riverpod
class IdentityUploadController extends _$IdentityUploadController 
    with AutoDisposeBaseStateMixin<IdentityUploadResult>
```

### Flow
1. User chọn loại GTTT và chụp/upload 2 mặt
2. <PERSON><PERSON> khi c<PERSON> đủ 2 mặt, user nhấn "Tiếp tục"
3. Upload cả 2 mặt lên server qua Media API
4. Navigate đến màn hình tiếp theo (QR Scan)

### States
- **Initial**: Trạng thái ban đầu
- **Loading**: Đang upload
- **Success**: Upload thành công
- **Error**: Có lỗi xảy ra
- **Submitting**: Đang xử lý (với overlay)

## 📱 Sử dụng

### Basic Usage
```dart
// Navigate to identity upload page
context.goToIdentityUpload();

// With custom parameters
context.goToIdentityUpload(
  customTitle: 'Upload CCCD',
  customDescription: 'Vui lòng upload CCCD của bạn',
  isCTVFlow: true,
);
```

### Controller Usage
```dart
// Watch state
final uploadState = ref.watch(identityUploadControllerProvider);

// Access controller
final controller = ref.read(identityUploadControllerProvider.notifier);

// Upload documents
await controller.uploadIdentityDocuments(
  documentType: DocumentType.cccd,
  frontImagePath: '/path/to/front.jpg',
  backImagePath: '/path/to/back.jpg',
);

// Retry on error
await controller.retryUpload(
  documentType: DocumentType.cccd,
  frontImagePath: '/path/to/front.jpg',
  backImagePath: '/path/to/back.jpg',
);
```

### State Handling
```dart
// Check states
if (uploadState.isSuccess) {
  // Upload successful
  final result = uploadState.data;
  print('Front URL: ${result?.frontImage.previewUrl}');
  print('Back URL: ${result?.backImage.previewUrl}');
}

if (uploadState.isError) {
  // Handle error
  final errorMessage = uploadState.errorMessage;
  print('Error: $errorMessage');
}

if (uploadState.isProcessing) {
  // Show loading indicator
}
```

## 🔧 Configuration

### Document Types
```dart
enum DocumentType {
  cccd('CCCD'),
  theCanCuoc('Thẻ Căn Cước'),
  passport('Hộ chiếu');
}
```

### File Types
```dart
enum FileType {
  idCard, // For identity documents
  // ... other types
}
```

## 🎨 UI Components

### BaseStateBuilder
Sử dụng `BaseStateBuilder` để handle tất cả states một cách gọn gàng:

```dart
BaseStateBuilder<IdentityUploadResult>(
  state: uploadState,
  successBuilder: (data) => _buildContent(...),
  errorBuilder: (failure, onRetry) => _buildContent(...),
  loadingBuilder: _buildContent(...),
  initialBuilder: _buildContent(...),
  showContentWhileSubmitting: true,
  showErrorWidgetOnSubmittingError: false,
)
```

### DocumentImagePicker
Widget chuyên dụng cho việc chọn ảnh GTTT:

```dart
DocumentImagePicker(
  label: 'Ảnh mặt trước',
  imagePath: frontImage.value,
  onImageSelected: (path) => handleImagePick(true, path),
)
```

## 🚀 API Integration

### Media API
Upload ảnh qua Media API với endpoint:
- `POST /media/v1/upload`
- File type: `IDCARD`
- Bucket: `salt`

### Response
```dart
class IdentityUploadResult {
  final DocumentType documentType;
  final MediaUploadResponse frontImage;
  final MediaUploadResponse backImage;
  final DateTime uploadedAt;
}
```

## 🔄 Error Handling

### Automatic Retry
- Network errors được retry tự động
- Validation errors hiển thị message rõ ràng
- Server errors được handle với user-friendly messages

### Manual Retry
```dart
// User can retry manually
await controller.retryUpload(...);
```

## 📋 Requirements

### Dependencies
- `hooks_riverpod`: State management
- `riverpod_annotation`: Code generation
- `dartz`: Functional programming (Either)
- `get_it`: Dependency injection

### Generated Files
- `identity_upload_controller.g.dart`: Riverpod generated code
- `base_state.freezed.dart`: Freezed generated code

## 🧪 Testing

### Unit Tests
```dart
// Test controller methods
test('should upload documents successfully', () async {
  // Test implementation
});

// Test state transitions
test('should transition from loading to success', () async {
  // Test implementation
});
```

### Widget Tests
```dart
// Test UI rendering
testWidgets('should render upload form', (WidgetTester tester) async {
  // Test implementation
});
```

## 📝 Notes

- Sử dụng `AutoDisposeBaseStateMixin` cho auto-dispose behavior
- Controller tự động dispose khi không có listener
- State được preserve trong quá trình submit
- Error handling được centralize trong BaseState
- Navigation được trigger tự động khi success 