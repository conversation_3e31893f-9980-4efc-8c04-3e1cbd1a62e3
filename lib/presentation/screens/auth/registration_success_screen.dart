// Core barrel export
import 'package:sales_app/core/core.dart';

// Generated localization
import 'package:sales_app/generated/l10n.dart';
import 'package:sales_app/presentation/router/navigation_extensions.dart';

// Widgets barrel export
import 'package:sales_app/presentation/widgets/widgets.dart';



class RegistrationSuccessScreen extends StatelessWidget {
  final String? fullName;
  final String? branchName;
  final String? position;
  final String? customTitle;
  final String? customMessage;
  final String? customButtonText;
  final String? referrerCode;
  final String? referrerName;
  final bool isCTVFlow;

  const RegistrationSuccessScreen({
    super.key,
    this.fullName,
    this.branchName,
    this.position,
    this.customTitle,
    this.customMessage,
    this.customButtonText,
    this.referrerCode,
    this.referrerName,
    this.isCTVFlow = false,
  });

  @override
  Widget build(BuildContext context) {
    return BaseScreen(
      title: customTitle ?? S.of(context).success_ctv_message,
      leading: const SizedBox.shrink(), // Ẩn nút back
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  const SizedBox(height: 32),
                  _buildSuccessMessage(context),
                  const SizedBox(height: 32),
                  _buildInfoCard(context),
                ],
              ),
            ),
          ),
          _buildActionButton(context),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage(BuildContext context) {
    return Column(
      children: [
        AppImages.icCheckSuccess.toSquareImage(
          size: 130,
          fit: BoxFit.contain,
        ),
        const SizedBox(height: 24),
        Text(
          customTitle ?? S.of(context).success_ctv_message,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Text(
          customMessage ?? S.of(context).bankWillContact,
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textSecondary,
            height: 1.6,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInfoCard(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primaryColor.withValues(alpha: 0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildInfoRow(
            S.of(context).fullName,
            fullName ?? '',
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            S.of(context).business_unit_registration,
            branchName ?? '',
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            S.of(context).position,
            position ?? (isCTVFlow ? S.of(context).referrer : S.of(context).newCTV),
            isLast: (referrerCode?.isNotEmpty == true) ? false : true,
          ),
          if (referrerCode?.isNotEmpty == true) ...[
            const SizedBox(height: 16),
            _buildInfoRow(
              S.of(context).code_seller,
              referrerCode!,
            ),
          ],
          if (referrerName?.isNotEmpty == true) ...[
            const SizedBox(height: 16),
            _buildInfoRow(
              S.of(context).name_seller,
              referrerName!,
              isLast: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isLast = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 0),
      decoration: BoxDecoration(
        border: isLast
            ? null
            : const Border(
                bottom: BorderSide(
                  color: Color(0xFFE0E0E0),
                  width: 1,
                ),
              ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF757575),
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              textAlign: TextAlign.right,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    final buttonText =
        isCTVFlow ? (customButtonText ?? S.of(context).close) : S.of(context).login;
    return SizedBox(
      width: double.infinity,
      child: BottomButton(
        title: buttonText,
        onPressed: () {
          context.cancelRegistrationAndGoToLogin();
        },
      ),
    );
  }
}
