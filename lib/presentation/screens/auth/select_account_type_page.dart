import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Auth widgets barrel export
import 'widgets/widgets.dart';

class SelectAccountTypePage extends StatelessWidget {
  const SelectAccountTypePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        title: SizedBox(
          height: 30,
          child: Image.asset(
            'assets/images/kienlongbank_logo.png',
            fit: BoxFit.contain,
          ),
        ),
        centerTitle: true,
        backgroundColor: AppColors.backgroundColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.primaryColor),
      ),
      body: const Center(
        child: AccountTypeSelector(),
      ),
    );
  }
}
