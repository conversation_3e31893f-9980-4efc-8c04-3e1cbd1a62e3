/// Auth Screens Barrel Export
/// 
/// This file provides a single import point for all authentication-related screens.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/screens/auth/auth.dart';
/// 
/// // Now you can use any auth screen:
/// LoginScreen()
/// PersonalInfoConfirmationScreen()
/// KYCIdGuideScreen()
/// ```
library auth_screens;

// Auth screens
export 'login_screen.dart';
export 'personal_info_confirmation_screen.dart';
export 'kyc_id_guide_screen.dart';
export 'cvt_policy_screen.dart';
export 'registration_success_screen.dart';
export 'select_account_type_page.dart';
// Auth widgets barrel export
export 'widgets/widgets.dart'; 