import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Auth screens
import '../registration_success_screen.dart';

class PersonalInfoBottomButtons extends StatelessWidget {
  final bool isLoading;
  final bool isFormValid;
  final VoidCallback onCancel;
  final VoidCallback onConfirm;

  const PersonalInfoBottomButtons({
    super.key,
    required this.isLoading,
    required this.isFormValid,
    required this.onCancel,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(AppDimens.spacingLG, AppDimens.spacingMD, AppDimens.spacingLG, AppDimens.spacingXL),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: AppDimens.alphaLow / 2),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: onCancel,
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: AppDimens.spacingLG),
                      side: BorderSide.none,
                      backgroundColor: AppColors.primaryColor.withValues(alpha: AppDimens.alphaLow / 2),
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                    ),
                    child: const Text(
                      'Hủy',
                      style: TextStyle(
                        color: AppColors.primaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                const RegistrationSuccessScreen()),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      'Xác nhận',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    // isLoading
                    //     ? const SizedBox(
                    //         height: 20,
                    //         width: 20,
                    //         child: CircularProgressIndicator(
                    //           strokeWidth: 2,
                    //           valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    //         ),
                    //       )
                    //     : const Text(
                    //         'Xác nhận',
                    //         style: TextStyle(
                    //           fontSize: 16,
                    //           fontWeight: FontWeight.w500,
                    //         ),
                    //       ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
