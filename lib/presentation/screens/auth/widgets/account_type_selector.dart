import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Navigation extensions
import 'package:sales_app/presentation/router/navigation_extensions.dart';

class AccountTypeSelector extends StatelessWidget {
  const AccountTypeSelector({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
      padding: AppDimens.paddingHorizontalLg,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppDimens.h32, // Giảm khoảng cách trên
          _buildHeader(context),
          AppDimens.h24, // Giảm khoảng cách
          Expanded(
            child: _buildAccountTypeOptions(context: context, theme: theme),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context); 
    return Text(
      'Vai trò của bạn là?',
      textAlign: TextAlign.center,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: AppColors.textPrimary,
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }



  Widget _buildAccountTypeOptions(
      {required BuildContext context, required ThemeData theme}) {
    return ListView(
      physics: const BouncingScrollPhysics(),
      padding: AppDimens.paddingVerticalLg,
      children: [
        _buildAccountTypeOption(
          icon: Icons.person_outline,
          title: 'Cộng tác viên bán hàng',
          subtitle: 'Tham gia mạng lưới bán hàng linh hoạt\n',
          onTap: () {
            // Use Go Router navigation
            context.goToCtvPolicy();
          },
          theme: theme,
        ),
        AppDimens.h20, // Giảm khoảng cách giữa các option
        _buildAccountTypeOption(
          icon: Icons.apartment_outlined,
          title: 'Cán bộ KienlongBank',
          subtitle: 'Đăng ký tài khoản dành cho nhân sự nội bộ',
          onTap: () {
            // Navigator.push(context, MaterialPageRoute(builder: (_) => const CBNVScreen()));
          },
          theme: theme,
        ),
      ],
    );
  }

  Widget _buildAccountTypeOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required ThemeData theme,
  }) {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: AppDimens.maxWidthSmall),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppDimens.spacingXL,
              vertical: AppDimens.spacingLG,
            ),
            decoration: BoxDecoration(
              color: AppColors.lightGray, // Màu xám rõ ràng hơn giống hình
              borderRadius: AppDimens.borderRadius8, // Giảm độ bo tròn
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.primaryColor,
                  size: 32, // Tăng kích thước icon
                ),
                AppDimens.w20, // Tăng khoảng cách
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      AppDimens.h4,
                      Text(
                        subtitle,
                        maxLines: 2,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
