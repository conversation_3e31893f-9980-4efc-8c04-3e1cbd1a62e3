
// Core barrel export
import 'package:sales_app/core/core.dart';
// Widgets barrel export
import 'package:sales_app/presentation/widgets/widgets.dart';

class LocationDropdown extends StatelessWidget {
  final List<String> items;
  final String? value;
  final String label;
  final String hint;
  final ValueChanged<String?>? onChanged;
  final String? Function(String?)? validator;
  final bool isDense;

  const LocationDropdown({
    super.key,
    required this.items,
    required this.label,
    required this.hint,
    this.value,
    this.onChanged,
    this.validator,
    this.isDense = false,
  });

  @override
  Widget build(BuildContext context) {
    return CommonDropdown<String>(
      items: items,
      value: value,
      label: label,
      hint: hint,
      isRequired: true,
      onChanged: (item) => onChanged?.call(item),
      labelBuilder: (item) => item,
      backgroundColor: AppColors.surfaceColor,
      borderColor: AppColors.primaryColor.withValues(alpha: 0.2),
      isDense: isDense,
    );
  }
}
