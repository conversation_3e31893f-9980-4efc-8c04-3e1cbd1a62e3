import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';

class PersonalInfoField extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  final bool isAddress;

  const PersonalInfoField({
    super.key,
    required this.label,
    required this.value,
    required this.icon,
    this.isAddress = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: AppDimens.spacingMD, vertical: AppDimens.spacingSM),
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: AppDimens.borderRadius8,
            border: Border.all(
              color: AppColors.primaryColor.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 11,
                ),
              ),
              const SizedBox(height: 2),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    icon,
                    size: 16,
                    color: AppColors.primaryColor.withValues(alpha: 0.5),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      value,
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: isAddress ? 13 : 12,
                        height: isAddress ? 1.4 : 1.2,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
