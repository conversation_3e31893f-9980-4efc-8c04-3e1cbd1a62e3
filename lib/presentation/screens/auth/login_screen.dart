import 'package:flutter/foundation.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';
// Widgets barrel export
import 'package:sales_app/presentation/widgets/widgets.dart';
// Common widgets
// Controllers barrel export
import 'package:sales_app/presentation/controllers/controllers.dart';
// Presentation utils barrel export
import 'package:sales_app/presentation/utils/utils.dart';
// Auth widgets barrel export
import 'widgets/widgets.dart';
// Navigation extensions
import '../../router/navigation_extensions.dart';

class LoginScreen extends BaseLoadingHookWidget {
  const LoginScreen({super.key});

  @override
  bool isLoading(WidgetRef ref) {
    final authState = ref.watch(authControllerProvider);
    return authState.maybeWhen(
      null, // $default parameter
      loading: () => true,
      orElse: () => false,
    );
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.login;

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final authState = ref.watch(authControllerProvider);
    final authController = ref.read(authControllerProvider.notifier);

    final emailFocusNode = useFocusNode();
    final passwordFocusNode = useFocusNode();

    // Listen to auth state changes
    ref.listen<AuthState>(authControllerProvider, (previous, next) {
      next.when(
        (isLoading, isLoggedIn, user, failure, isEmailValid, isPasswordValid) {
          // Handle default state
        },
        initial: () {},
        loading: () {},
        authenticated: (user) {
          AppLogger.info('User authenticated, navigating to home');
          context.replaceWithHome();
        },
        unauthenticated: (failure) {
          if (failure != null) {
            final errorMessage =
                ErrorMessageMapper.getErrorMessage(context, failure);
            CommonDialog.showErrorDialog(
              context: context,
              message: errorMessage,
            );
          }
        },
        error: (failure) {
          final errorMessage =
              ErrorMessageMapper.getErrorMessage(context, failure);
          CommonDialog.showErrorDialog(
            context: context,
            message: errorMessage,
          );
        },
      );
    });
    final emailProvider = StateProvider<String>((ref) => '');
    final passwordProvider = StateProvider<String>((ref) => '');

    final email = ref.watch(emailProvider);
    final password = ref.watch(passwordProvider);

    // Đồng bộ giá trị từ state local-> controller
    useEffect(() {
      emailController.text = email;
      emailController.selection = TextSelection.collapsed(offset: email.length);
      passwordController.text = password;
      passwordController.selection = TextSelection.collapsed(offset: password.length);
      return null;
    }, [email, password]);

    // Handle text field changes for validation
    useEffect(() {
      void onEmailChanged() {
        authController.updateEmailValidation(emailController.text);
      }

      void onPasswordChanged() {
        authController.updatePasswordValidation(passwordController.text);
      }

      emailController.addListener(onEmailChanged);
      passwordController.addListener(onPasswordChanged);

      return () {
        emailController.removeListener(onEmailChanged);
        passwordController.removeListener(onPasswordChanged);
      };
    }, [emailController, passwordController]);

    useEffect(() {
      AppLogger.info('LoginScreen initialized');
      return () => AppLogger.debug('LoginScreen disposed');
    }, []);

    void handleLogin() {
      authController.clearError();
      authController.login(
        email: emailController.text.trim(),
        password: passwordController.text,
      );
    }

    void handleBiometricLogin() {
      authController.biometricLogin();
    }

    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
        body: Stack(
      children: [
        Positioned.fill(
          child: AppImages.background.toImage(
            fit: BoxFit.cover,
          ),
        ),
        SafeArea(
          child: SingleChildScrollView(
            padding: AppDimens.paddingHorizontalMd,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: screenHeight - MediaQuery.of(context).padding.top,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  AppDimens.h48,
                  AppDimens.h16,

                  // Logo
                  SizedBox(
                    width: AppDimens.logoWidthSmall,
                    height: AppDimens.logoHeightSmall,
                    child: AppImages.kienlongbankLogo.toImage(
                      fit: BoxFit.contain,
                    ),
                  ),

                  AppDimens.h24,

                  // Environment Dropdown
                  const EnvironmentDropdown(
                    isDense: true,
                  ),

                  AppDimens.h32,

                  // Email Field
                  CommonTextField(
                    controller: emailController,
                    label: S.of(context).account,
                    onChanged: (value) {
                      ref.read(emailProvider.notifier).state = value;
                    },
                    prefixIcon: const Icon(Icons.email),
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    allowClear: true,
                    backgroundColor: AppColors.white.withValues(alpha: 0.1),
                    textColor: AppColors.white,
                    labelColor: AppColors.white,
                    iconColor: AppColors.white.withValues(alpha: 0.7),
                    borderColor: AppColors.white,
                    focusNode: emailFocusNode,
                    nextFocusNode: passwordFocusNode,
                  ),

                  AppDimens.h16,

                  // Password Field
                  CommonTextField(
                    controller: passwordController,
                    label: S.of(context).password,
                    prefixIcon: const Icon(Icons.lock),
                    isSensitive: true,
                    allowClear: true,
                    textInputAction: TextInputAction.done,
                    onChanged: (value) {
                      ref.read(passwordProvider.notifier).state = value;
                    },
                    // ignore: deprecated_member_use
                    backgroundColor: AppColors.white.withValues(alpha: 0.1),
                    textColor: AppColors.white,
                    labelColor: AppColors.white,
                    iconColor: AppColors.white.withValues(alpha: 0.7),
                    borderColor: AppColors.white,
                    focusNode: passwordFocusNode,
                  ),

                  AppDimens.h16,

                  // Register Link
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          AppLogger.event('User navigated to register');
                          // Use Go Router navigation
                          context.goToSelectAccountType();
                        },
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          padding: AppDimens.paddingHorizontalSm,
                        ),
                        child: Text(
                          S.of(context).register_account,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppColors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ),
                    ],
                  ),

                  AppDimens.h32,

                  // Login Button
                  CommonButton(
                    title: S.of(context).login,
                    onPressed: handleLogin,
                    enabled: authState.canLogin,
                    backgroundColor: AppColors.buttonColor,
                  ),

                  AppDimens.h24,

                  // Biometric Login
                  Center(
                    child: IconButton(
                      onPressed: handleBiometricLogin,
                      icon: Icon(
                        Icons.fingerprint,
                        color: AppColors.white,
                        size: AppDimens.iconXL,
                      ),
                      tooltip: 'Đăng nhập bằng vân tay',
                    ),
                  ),

                  // 🧪 DEBUG: Session Expired Test Buttons (only in debug mode)
                  if (kDebugMode) ...[
                    AppDimens.h16,
                    Center(
                      child: Column(
                        children: [
                          TextButton.icon(
                            onPressed: () {
                              ref.read(authControllerProvider.notifier).debugForceSessionExpired();
                            },
                            icon: Icon(
                              Icons.bug_report,
                              color: AppColors.white.withValues(alpha: 0.7),
                              size: AppDimens.iconSM,
                            ),
                            label: Text(
                              '🧪 Test Session Expired (Direct)',
                              style: TextStyle(
                                color: AppColors.white.withValues(alpha: 0.7),
                                fontSize: AppDimens.fontSM,
                              ),
                            ),
                          ),
                          AppDimens.h8,
                          TextButton.icon(
                            onPressed: () async {
                              await ref.read(authControllerProvider.notifier).debugSetExpiredRefreshToken();
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Expired refresh token set. Make any API call to trigger refresh failure.'),
                                    duration: Duration(seconds: 3),
                                  ),
                                );
                              }
                            },
                            icon: Icon(
                              Icons.refresh,
                              color: AppColors.white.withValues(alpha: 0.7),
                              size: AppDimens.iconSM,
                            ),
                            label: Text(
                              '🧪 Set Expired Refresh Token',
                              style: TextStyle(
                                color: AppColors.white.withValues(alpha: 0.7),
                                fontSize: AppDimens.fontSM,
                              ),
                            ),
                          ),
                          AppDimens.h8,
                          TextButton.icon(
                            onPressed: () {
                              ref.read(authControllerProvider.notifier).debugForceLogout();
                            },
                            icon: Icon(
                              Icons.exit_to_app,
                              color: AppColors.white.withValues(alpha: 0.7),
                              size: AppDimens.iconSM,
                            ),
                            label: Text(
                              '🧪 Test Force Logout',
                              style: TextStyle(
                                color: AppColors.white.withValues(alpha: 0.7),
                                fontSize: AppDimens.fontSM,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ],
    ));
  }
}
