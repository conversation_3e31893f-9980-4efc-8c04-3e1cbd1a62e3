import 'dart:async';
import 'package:flutter_hooks/flutter_hooks.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';
// Widgets barrel export
import 'package:sales_app/presentation/widgets/widgets.dart';
// Controllers barrel export
import 'package:sales_app/presentation/controllers/controllers.dart';
// Presentation models
import 'package:sales_app/presentation/models/models.dart';
// Presentation utils barrel export
import 'package:sales_app/presentation/utils/utils.dart';
// Navigation extensions
import 'package:sales_app/presentation/router/navigation_extensions.dart';

class PersonalInfoConfirmationScreen extends HookConsumerWidget {
  final bool isCTVFlow;

  const PersonalInfoConfirmationScreen({
    super.key,
    this.isCTVFlow = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Flag để tránh duplicate navigation
    final hasNavigatedToSuccess = useState(false);

    // Load data on init - provinces cho registration flow, branch và referrer từ profile cho CTV flow
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        final controller = ref.read(cTVRegistrationControllerProvider.notifier);
        if (isCTVFlow) {
          // CTV flow: load branch và referrer từ profile của user đã login
          // Sử dụng hàm gộp để load cả 2 data cùng lúc
          await controller.loadDataFromLocalStorage();
        } else {
          // Registration flow: load provinces để user chọn
          controller.loadProvinces();
        }
      });
      return null;
    }, [isCTVFlow]);

    // Listen for registration success and reset flags when data changes
    useEffect(() {
      final ctvState = ref.watch(cTVRegistrationControllerProvider);
      final currentData = ctvState.data;

      // Reset navigation flag khi quay lại step này
      if (currentData?.currentStep == CTVRegistrationStep.personalInfoConfirmation) {
        hasNavigatedToSuccess.value = false;
      }

      if (ctvState.isSuccess &&
          currentData?.currentStep == CTVRegistrationStep.success &&
          !hasNavigatedToSuccess.value) {
        hasNavigatedToSuccess.value = true;

        AppLogger.info(
            'CTV registration successful, navigating to success screen',
            data: {
              'fullName': currentData?.personalInfoConfirmation?.fullName,
              'branchName': currentData?.selectedBranch?.name,
              'referrerName': currentData?.selectedReferrer?.referrerName,
            });

        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (context.mounted) {
            context.goToRegistrationSuccess(
              isCTVFlow: isCTVFlow,
              fullName: currentData?.personalInfoConfirmation?.fullName,
              branchName: currentData?.selectedBranch?.name,
              position: 'CTV',
              referrerCode: currentData?.selectedReferrer?.referrerCode,
              referrerName: currentData?.selectedReferrer?.referrerName,
            );
          }
        });
      }
      return null;
    }, [ref.watch(cTVRegistrationControllerProvider)]);

    return BaseScreen(
      title:
          isCTVFlow ? S.of(context).ctv_referral_title : S.of(context).personalInfoConfirmation,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios_new_rounded),
        onPressed: () {
          // Về màn hình identity upload và clean data
          final ctvController = ref.read(cTVRegistrationControllerProvider.notifier);
          
          // Reset step về documentUpload
          ctvController.goToStep(CTVRegistrationStep.documentUpload);
          
          // Clean CCCD info (dữ liệu từ QR scan)
          ctvController.clearCCCDInfo();
          
          // Clean referrer data
          ctvController.clearReferrer();
          
          // Clean province/branch data cho registration flow
          if (!isCTVFlow) {
            // Sử dụng clearLocationSelection để clean cả province và branch
            ctvController.clearLocationSelection();
          }
          
          AppLogger.info('Cleaned all data when going back to upload screen', data: {
            'isCTVFlow': isCTVFlow,
            'step': CTVRegistrationStep.documentUpload,
            'clearedCCCDInfo': true,
            'clearedPersonalInfo': true,
            'clearedReferrer': true,
            'clearedLocation': !isCTVFlow,
          });
          
          context.goBack();
        },
      ),
      body: BaseStateHookBuilder<CTVRegistrationFlowData>(
        stateProvider: cTVRegistrationControllerProvider,
        successBuilder: (data, ref) => _buildContent(context, data, ref),
        loadingBuilder: (ref) => const BaseLoadingWidget(),
        errorBuilder: (failure, onRetry, ref) => BaseErrorWidget(
          failure: failure,
          onRetry: onRetry,
        ),
        emptyBuilder: (ref) => const BaseEmptyWidget(),
        submittingOverlay: const DefaultSubmittingWidget(),
        showContentWhileSubmitting: true,
        showErrorWidgetOnSubmittingError: false,
        onRetry: () {
          final controller =
              ref.read(cTVRegistrationControllerProvider.notifier);
          controller.loadProvinces();
        },
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    CTVRegistrationFlowData data,
    WidgetRef ref,
  ) {
    final formKey = useMemoized(() => GlobalKey<FormState>(), []);

    return Form(
      key: formKey,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimens.spacingLG,
                vertical: AppDimens.spacingMD,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildExtractedInfoSection(context, data, ref),
                  AppDimens.h24,
                  _buildUserInputSection(context, data, ref),
                ],
              ),
            ),
          ),
          _buildBottomButtons(context, data, ref, formKey),
        ],
      ),
    );
  }

  Widget _buildExtractedInfoSection(
    BuildContext context,
    CTVRegistrationFlowData data,
    WidgetRef ref,
  ) {
    final controller = ref.read(cTVRegistrationControllerProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.badge_outlined,
              size: 16,
              color: AppColors.primaryColor,
            ),
            AppDimens.w8,
            Text(
              S.of(context).info_from_id_documents,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        AppDimens.h16,

        // Full Name Field (editable)
        _buildEditableField(
          context: context,
          label: S.of(context).fullName,
          initialValue: data.cccdInfo?.fullName ?? '',
          ref: ref,
          fieldName: 'fullName',
          validator: ValidationUtils.validateFullName,
          onChanged: (value) {
            AppLogger.info('Full name changed', data: {
              'oldValue': data.cccdInfo?.fullName,
              'newValue': value,
              'hasCCCDInfo': data.cccdInfo != null,
            });
            if (data.cccdInfo != null) {
              final updatedCCCD = data.cccdInfo!.copyWith(fullName: value);
              controller.updateCCCDInfo(updatedCCCD);
            }
            // Đồng bộ luôn với personalInfoConfirmation.fullName
            final currentInfo = data.personalInfoConfirmation ??
                const PersonalInfoConfirmationData();
            final updatedInfo = currentInfo.copyWith(fullName: value);
            controller.updatePersonalInfo(updatedInfo);
          },
        ),
       AppDimens.h8,

        // ID Number Field (editable)
        _buildEditableField(
          context: context,
          label: "Số giấy tờ tuỳ thân",
          initialValue: data.cccdInfo?.idNumber ?? '',
          ref: ref,
          fieldName: 'idNumber',
          validator: ValidationUtils.validateIdNumber,
          keyboardType: TextInputType.number,
          onChanged: (value) {
            if (data.cccdInfo != null) {
              final updatedCCCD = data.cccdInfo!.copyWith(idNumber: value);
              controller.updateCCCDInfo(updatedCCCD);
            }
            // Đồng bộ luôn với personalInfoConfirmation.documentNumber
            final currentInfo = data.personalInfoConfirmation ??
                const PersonalInfoConfirmationData();
            final updatedInfo = currentInfo.copyWith(documentNumber: value);
            controller.updatePersonalInfo(updatedInfo);
          },
        ),
       AppDimens.h8,

        // Address Field (editable)
        _buildEditableField(
          context: context,
          label: S.of(context).residentialAddress,
          initialValue: data.cccdInfo?.address ?? '',
          ref: ref,
          fieldName: 'address',
          validator: ValidationUtils.validateAddress,
          maxLines: 3,
          onChanged: (value) {
            if (data.cccdInfo != null) {
              final updatedCCCD = data.cccdInfo!.copyWith(address: value);
              controller.updateCCCDInfo(updatedCCCD);
            }
            // Đồng bộ luôn với personalInfoConfirmation.address
            final currentInfo = data.personalInfoConfirmation ??
                const PersonalInfoConfirmationData();
            final updatedInfo = currentInfo.copyWith(address: value);
            controller.updatePersonalInfo(updatedInfo);
          },
        ),
      ],
    );
  }

  Widget _buildUserInputSection(
    BuildContext context,
    CTVRegistrationFlowData data,
    WidgetRef ref,
  ) {
    final controller = ref.read(cTVRegistrationControllerProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.edit_outlined,
              size: 16,
              color: AppColors.primaryColor,
            ),
            AppDimens.h8,
            const Text(
              'Thông tin bổ sung',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        AppDimens.h16,

        // Phone Number Field
        _buildEditableField(
          context: context,
          label: S.of(context).phoneNumber,
          initialValue: data.personalInfoConfirmation?.phoneNumber ?? '',
          ref: ref,
          fieldName: 'phoneNumber',
          validator: ValidationUtils.validatePhone,
          keyboardType: TextInputType.phone,
          onChanged: (value) {
            final currentInfo = data.personalInfoConfirmation ??
                const PersonalInfoConfirmationData();
            final updatedInfo = currentInfo.copyWith(phoneNumber: value);
            controller.updatePersonalInfo(updatedInfo);
          },
        ),
        AppDimens.h8,

        // Position Field (read-only)
        CommonTextField(
          label: S.of(context).position,
          controller: TextEditingController(text: 'Cộng tác viên'),
          enabled: false,
        ),
        AppDimens.h8,

        // CTV Flow: Hiển thị branch từ profile của user đã login
        if (isCTVFlow) ...[
          // Branch từ profile (read-only)
          CommonTextField(
            label: 'Chi nhánh đăng ký',
            controller: TextEditingController(
              text: data.selectedBranch?.name ?? '',
            ),
            enabled: false,
          ),
          AppDimens.h8,

          // Branch Address (read-only, chỉ hiển thị khi đã có branch)
          if (data.selectedBranch != null) ...[
            CommonTextField(
              label: S.of(context).branchAddress,
              controller: TextEditingController(
                text: data.selectedBranch?.address ?? '',
              ),
              enabled: false,
              maxLines: 2,
            ),
            AppDimens.h8,
          ],

          // Email của người được giới thiệu (optional cho CTV referral flow)
          _buildEditableField(
            context: context,
            label: 'Email người được giới thiệu (nếu có)',
            initialValue: data.personalInfoConfirmation?.email ?? '',
            ref: ref,
            fieldName: 'email',
            validator: ValidationUtils.validateEmailOptional,
            keyboardType: TextInputType.emailAddress,
            onChanged: (value) {
              final currentInfo = data.personalInfoConfirmation ??
                  const PersonalInfoConfirmationData();
              final updatedInfo = currentInfo.copyWith(email: value);
              controller.updatePersonalInfo(updatedInfo);
            },
          ),
          AppDimens.h8,

          // Referrer Input (cho CTV referral flow - data từ local)
          _buildReferrerInput(context, data, ref),

        ] else ...[
          // Province Selection (editable for registration flow)
          _buildProvinceSelection(context, data, ref),
          AppDimens.h8,

          // Branch Selection (only show if province is selected)
          if (data.selectedProvince != null) ...[
            _buildBranchSelection(context, data, ref),
            AppDimens.h8,
          ],

          // Branch Address (read-only, chỉ hiển thị khi đã chọn chi nhánh)
          if (data.selectedBranch != null) ...[
            CommonTextField(
              label: S.of(context).dvkd_address,
              controller: TextEditingController(
                text: data.selectedBranch?.address ?? '',
              ),
              enabled: false,
              maxLines: 2,
            ),
           AppDimens.h8,
          ],
          // Email Field (optional)
          _buildEditableField(
            context: context,
            label: S.of(context).email_optional,
            initialValue: data.personalInfoConfirmation?.email ?? '',
            ref: ref,
            fieldName: 'email',
            validator: ValidationUtils.validateEmailOptional,
            keyboardType: TextInputType.emailAddress,
            onChanged: (value) {
              final currentInfo = data.personalInfoConfirmation ??
                  const PersonalInfoConfirmationData();
              final updatedInfo = currentInfo.copyWith(email: value);
              controller.updatePersonalInfo(updatedInfo);
            },
          ),
          AppDimens.h8,
          // Referrer Input (editable for registration flow)
          _buildReferrerInput(context, data, ref),
        ],
      ],
    );
  }

  Widget _buildEditableField({
    required BuildContext context,
    required String label,
    required String initialValue,
    required WidgetRef ref,
    required String fieldName,
    required String? Function(String?) validator,
    TextInputType? keyboardType,
    int maxLines = 1,
    required Function(String) onChanged,
  }) {
    final textController = useTextEditingController();

    // Sync controller với data khi data thay đổi
    useEffect(() {
      AppLogger.info('Updating text controller for field: $fieldName', data: {
        'fieldName': fieldName,
        'oldValue': textController.text,
        'newValue': initialValue,
        'isDifferent': textController.text != initialValue,
      });
      textController.text = initialValue;
      return null;
    }, [initialValue]);

    return CommonTextField(
      label: label,
      controller: textController,
      enabled: true,
      keyboardType: keyboardType,
      maxLines: maxLines,
      onChanged: (value) {
        onChanged(value);
      },
      validator: (value) {
        // Sử dụng controller.text nếu value là null hoặc empty
        final actualValue =
            value?.isNotEmpty == true ? value : textController.text;

        AppLogger.info('Validating field: $fieldName', data: {
          'fieldName': fieldName,
          'formValue': value,
          'formValueLength': value?.length,
          'formValueIsEmpty': value?.isEmpty,
          'formValueIsNull': value == null,
          'controllerText': textController.text,
          'controllerTextLength': textController.text.length,
          'actualValue': actualValue,
          'actualValueLength': actualValue?.length,
          'initialValue': initialValue,
          'initialValueLength': initialValue.length,
        });

        final errorKey = validator(actualValue);

        AppLogger.info('Validation result for $fieldName', data: {
          'fieldName': fieldName,
          'errorKey': errorKey,
          'hasError': errorKey != null,
        });

        return ValidationMessageMapper.getValidationMessage(
          context,
          errorKey,
          fieldName: fieldName,
        );
      },
    );
  }

  Widget _buildProvinceSelection(
    BuildContext context,
    CTVRegistrationFlowData data,
    WidgetRef ref,
  ) {
    final controller = ref.read(cTVRegistrationControllerProvider.notifier);

    if (data.provinces.isEmpty) {
      return _buildEmptyStateWithRetry(context, 'Không có dữ liệu tỉnh/thành',
          () {
        controller.loadProvinces();
      });
    }

    return _buildProvinceDropdown(context, data, controller);
  }

  Widget _buildProvinceDropdown(
    BuildContext context,
    CTVRegistrationFlowData data,
    CTVRegistrationController controller,
  ) {
    return CommonDropdown<ProvinceEntity>(
      label: 'Tỉnh/Thành phố',
      hint: 'Chọn tỉnh/thành phố',
      value: data.selectedProvince,
      items: data.provinces,
      enabled: data.provinces.isNotEmpty,
      labelBuilder: (province) => province.name,
      onChanged: (province) {
        controller.selectProvince(province);
      },
    );
  }

  Widget _buildBranchSelection(
    BuildContext context,
    CTVRegistrationFlowData data,
    WidgetRef ref,
  ) {
    final controller = ref.read(cTVRegistrationControllerProvider.notifier);

    if (data.isLoadingBranches) {
      return _buildLoadingState(context, 'Đang tải chi nhánh...');
    }

    if (data.branches.isEmpty) {
      return _buildEmptyStateWithRetry(
          context, 'Không có chi nhánh cho tỉnh đã chọn', () {
        if (data.selectedProvince != null) {
          controller.loadBranches(data.selectedProvince!.id);
        }
      });
    }

    return _buildBranchDropdown(context, data, controller);
  }

  Widget _buildBranchDropdown(
    BuildContext context,
    CTVRegistrationFlowData data,
    CTVRegistrationController controller,
  ) {
    return CommonDropdown<BranchEntity>(
      label: 'ĐVKD đăng ký',
      hint: 'ĐVKD đăng ký',
      value: data.selectedBranch,
      items: data.branches,
      enabled: data.branches.isNotEmpty && !data.isLoadingBranches,
      labelBuilder: (branch) => branch.name,
      onChanged: (branch) {
        controller.selectBranch(branch);
      },
    );
  }

  Widget _buildReferrerInput(
    BuildContext context,
    CTVRegistrationFlowData data,
    WidgetRef ref,
  ) {
    // Xử lý khác nhau cho CTV flow và registration flow
    if (isCTVFlow) {
      return _buildCTVReferrerInput(context, data, ref);
    } else {
      return _buildRegistrationReferrerInput(context, data, ref);
    }
  }

  /// Referrer input cho CTV referral flow - data từ local (read-only)
  Widget _buildCTVReferrerInput(
    BuildContext context,
    CTVRegistrationFlowData data,
    WidgetRef ref,
  ) {
    final referrer = data.selectedReferrer;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Mã người giới thiệu (read-only)
        CommonTextField(
          label: 'Mã người giới thiệu',
          controller: TextEditingController(text: referrer?.referrerCode ?? ''),
          enabled: false, // Read-only
          hintText: 'Mã giới thiệu từ profile',
        ),
        AppDimens.h8,

        // Tên người giới thiệu (read-only)
        if (referrer != null)
          CommonTextField(
            label: 'Người giới thiệu',
            controller: TextEditingController(text: referrer.referrerName),
            enabled: false, // Read-only
          ),
      ],
    );
  }

  /// Referrer input cho registration flow - search từ API
  Widget _buildRegistrationReferrerInput(
    BuildContext context,
    CTVRegistrationFlowData data,
    WidgetRef ref,
  ) {
    final controller = ref.read(cTVRegistrationControllerProvider.notifier);
    final referrerCodeController = useTextEditingController();
    final isLoading = data.isLoadingReferrer;
    final referrer = data.selectedReferrer;

    // Simplified state management - chỉ track editing state
    final isUserEditing = useRef(false);
    final editingTimer = useRef<Timer?>(null);
    final lastSyncedCode = useRef<String?>(null);

    // Sync textfield với referrer data - chỉ khi cần thiết
    useEffect(() {
      final currentReferrerCode = referrer?.referrerCode;

      AppLogger.info('Referrer sync check', data: {
        'currentReferrerCode': currentReferrerCode,
        'lastSyncedCode': lastSyncedCode.value,
        'isUserEditing': isUserEditing.value,
        'textFieldValue': referrerCodeController.text,
      });

      // Chỉ sync khi:
      // 1. User không đang edit
      // 2. Referrer code thực sự thay đổi
      // 3. Có referrer data để sync
      if (!isUserEditing.value &&
          currentReferrerCode != lastSyncedCode.value &&
          currentReferrerCode != null) {
        AppLogger.info('Syncing textfield with referrer data', data: {
          'newCode': currentReferrerCode,
          'oldCode': lastSyncedCode.value,
        });

        referrerCodeController.text = currentReferrerCode;
        lastSyncedCode.value = currentReferrerCode;
      }

      // Khi referrer bị clear và user không edit, chỉ update tracking
      if (currentReferrerCode == null &&
          lastSyncedCode.value != null &&
          !isUserEditing.value) {
        AppLogger.info('Referrer cleared, updating tracking');
        lastSyncedCode.value = null;
      }

      return null;
    }, [referrer?.referrerCode, isUserEditing.value]);

    // Cleanup timer on dispose
    useEffect(() {
      return () {
        editingTimer.value?.cancel();
      };
    }, []);

    // Hàm để search referrer
    final searchReferrer = useCallback(() {
      final code = referrerCodeController.text.trim();

      AppLogger.info('Search referrer triggered', data: {
        'code': code,
        'codeLength': code.length,
        'isLoading': isLoading,
        'currentReferrerCode': referrer?.referrerCode,
      });

      if (code.length >= 3 && !isLoading) {
        // Chỉ search nếu code khác với referrer hiện tại
        if (referrer?.referrerCode != code) {
          AppLogger.info('Searching referrer with code: $code');
          controller.loadReferrerByCode(code);
        } else {
          AppLogger.info('Code matches current referrer, skip search');
        }
      } else if (code.length < 3 && code.isNotEmpty) {
        // Clear referrer nếu mã quá ngắn
        if (referrer != null) {
          AppLogger.info('Code too short, clearing referrer');
          controller.clearReferrer();
        }
      } else if (code.isEmpty) {
        // Clear referrer nếu field trống
        if (referrer != null) {
          AppLogger.info('Code empty, clearing referrer');
          controller.clearReferrer();
        }
      }
    }, [referrerCodeController.text, isLoading, referrer?.referrerCode]);

    // Hàm xử lý khi user editing
    final handleUserEditing = useCallback(() {
      isUserEditing.value = true;

      // Cancel timer cũ và tạo timer mới
      editingTimer.value?.cancel();
      editingTimer.value = Timer(const Duration(milliseconds: 1500), () {
        AppLogger.info('Auto reset editing flag after 1.5 seconds');
        isUserEditing.value = false;
      });
    }, []);

    // Hàm xử lý khi field mất focus
    final handleFieldComplete = useCallback(() {
      // Cancel timer và reset flag
      editingTimer.value?.cancel();
      isUserEditing.value = false;

      // Gọi search referrer
      searchReferrer();
    }, [searchReferrer]);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Focus(
          onFocusChange: (hasFocus) {
            if (!hasFocus) {
              // Cancel timer và reset flag khi user unfocus
              editingTimer.value?.cancel();
              isUserEditing.value = false;
              AppLogger.info('Referrer input lost focus, reset editing flag');
            }
          },
          child: CommonTextField(
            label: 'Mã người giới thiệu (nếu có)',
            controller: referrerCodeController,
            enabled: !isLoading,
            hintText: 'Nhập mã giới thiệu',
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.done,
            validator: (value) {
              final errorKey = ValidationUtils.validateReferrerCode(value);
              return ValidationMessageMapper.getValidationMessage(
                context,
                errorKey,
                fieldName: 'Mã giới thiệu',
              );
            },
            allowClear:
                referrer != null || referrerCodeController.text.isNotEmpty,
            onChanged: (value) {
              AppLogger.info('Referrer input changed', data: {
                'newValue': value,
                'hasReferrer': referrer != null,
                'currentReferrerCode': referrer?.referrerCode,
              });

              // Xử lý khi user clear field (value rỗng)
              if (value.isEmpty) {
                AppLogger.info('Referrer input cleared by user');
                // Clear referrer data
                if (referrer != null) {
                  controller.clearReferrer();
                }
                // Reset tracking
                lastSyncedCode.value = null;
                isUserEditing.value = false;
                editingTimer.value?.cancel();
                return;
              }

              // Set editing flag
              handleUserEditing();

              // Chỉ clear referrer nếu user thay đổi từ code có sẵn
              if (referrer != null && referrer.referrerCode != value.trim()) {
                AppLogger.info('Clearing referrer due to input change');
                controller.clearReferrer();
              }
            },
            onBlur: (value) {
              AppLogger.info('Referrer input lost focus', data: {
                'value': value,
                'timestamp': DateTime.now().toIso8601String(),
              });
              handleFieldComplete();
            },
          ),
        ),
        AppDimens.h8,

        // Hiển thị loading state khi đang tìm kiếm
        if (isLoading)
          _buildLoadingState(context, 'Đang tìm kiếm người giới thiệu...'),

        // Hiển thị thông tin người giới thiệu nếu tìm thấy
        if (referrer != null && !isLoading)
          CommonTextField(
            label: 'Người giới thiệu',
            controller: TextEditingController(text: referrer.referrerName),
            enabled: false,
          ),

        // Hiển thị thông báo lỗi nếu có (sẽ được xử lý bởi BaseStateErrorHandler)
        // Lỗi sẽ được hiển thị qua SnackBar thông qua BaseStateHookBuilder.withErrorHandler
      ],
    );
  }

  Widget _buildLoadingState(BuildContext context, String message) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.grey300),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
         AppDimens.h12,
          Expanded(
            child: Text(
              message,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyStateWithRetry(
      BuildContext context, String message, VoidCallback onRetry) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.grey300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.info_outline, color: AppColors.textSecondary),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  message,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          AppDimens.h12,
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: onRetry,
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primaryColor,
                side: const BorderSide(color: AppColors.primaryColor),
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
              child: const Text('Thử lại'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons(
    BuildContext context,
    CTVRegistrationFlowData data,
    WidgetRef ref,
    GlobalKey<FormState> formKey,
  ) {
    final controller = ref.read(cTVRegistrationControllerProvider.notifier);

    return BottomButton(
      primaryTitle: isCTVFlow ? 'Xác nhận' : 'Xác nhận',
      primaryOnPressed: () {
        // Debug: Log tất cả trạng thái hiện tại
        AppLogger.info('Submit button pressed, checking validation...', data: {
          'isCTVFlow': isCTVFlow,
          'hasCCCDInfo': data.cccdInfo != null,
          'cccdFullName': data.cccdInfo?.fullName,
          'cccdFullNameLength': data.cccdInfo?.fullName.length ?? 0,
          'cccdFullNameIsEmpty': data.cccdInfo?.fullName.isEmpty ?? true,
          'cccdIdNumber': data.cccdInfo?.idNumber,
          'cccdIdNumberLength': data.cccdInfo?.idNumber.length ?? 0,
          'cccdIdNumberIsEmpty': data.cccdInfo?.idNumber.isEmpty ?? true,
          'cccdAddress': data.cccdInfo?.address,
          'cccdAddressLength': data.cccdInfo?.address.length ?? 0,
          'cccdAddressIsEmpty': data.cccdInfo?.address.isEmpty ?? true,
          'hasProvince': data.selectedProvince != null,
          'hasBranch': data.selectedBranch != null,
          'hasReferrer': data.selectedReferrer != null,
          'hasPhone':
              data.personalInfoConfirmation?.phoneNumber?.isNotEmpty ?? false,
          'phoneNumber': data.personalInfoConfirmation?.phoneNumber,
          'phoneNumberLength':
              data.personalInfoConfirmation?.phoneNumber?.length ?? 0,
          'formKeyExists': formKey.currentState != null,
        });

        // Debug: Kiểm tra từng field validation
        final formState = formKey.currentState;
        if (formState != null) {
          AppLogger.info('Form state exists, validating...');

          // Validate và log kết quả
          final isValid = formState.validate();
          AppLogger.info('Form validation result: $isValid');

          if (isValid) {
            // Form is valid, proceed with submission
            AppLogger.info('Form validation passed, submitting...', data: {
              'isCTVFlow': isCTVFlow,
              'hasCCCDInfo': data.cccdInfo != null,
              'hasProvince': data.selectedProvince != null,
              'hasBranch': data.selectedBranch != null,
              'hasReferrer': data.selectedReferrer != null,
              'hasPhone':
                  data.personalInfoConfirmation?.phoneNumber?.isNotEmpty ??
                      false,
            });

            if (isCTVFlow) {
              controller.submitCTVReferral();
            } else {
              controller.submitCTVRegistration();
            }
          } else {
            AppLogger.warning(
                'Form validation failed - showing validation errors');
          }
        } else {
          AppLogger.error('Form state is null - cannot validate');
        }
      },
      primaryColor: AppColors.primaryColor,
      secondaryTitle: 'Hủy',
      secondaryOnPressed: () {
        // Cancel registration flow and return to appropriate screen
        AppLogger.info('User cancelled CTV registration', data: {
          'isCTVFlow': isCTVFlow,
        });
        
        if (isCTVFlow) {
          // CTV referral flow: return to Home
          context.goToHome();
        } else {
          // Registration flow: return to Login
          context.cancelRegistrationAndGoToLogin();
        }
      },
      secondaryColor: Colors.white,
    );
  }
}
