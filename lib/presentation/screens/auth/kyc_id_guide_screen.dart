// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';
// Widgets barrel export
import 'package:sales_app/presentation/widgets/widgets.dart';
// Common widgets
import 'package:sales_app/presentation/widgets/common/app_image.dart';
// Navigation extensions
import '../../router/navigation_extensions.dart';

class KycIdGuideScreen extends StatelessWidget {
  final String? customTitle;
  final String? customNotesText;
  final String? customAvoidText;
  final String? customButtonText;
  final List<String>? customWarningLabels;
  final bool isCTVFlow;

  const KycIdGuideScreen({
    super.key,
    this.customTitle,
    this.customNotesText,
    this.customAvoidText,
    this.customButtonText,
    this.customWarningLabels,
    this.isCTVFlow = false,
  });

  @override
  Widget build(BuildContext context) {

    final theme = Theme.of(context);
    return BaseScreen(
      title: customTitle ?? S.of(context).documentVerificationGuide,
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: AppDimens.paddingAllLg,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Center(
                    child: AppImages.cccd.toSquareImage(
                      size: AppDimens.logoWidthMedium,
                      fit: BoxFit.contain,
                    ),
                  ),
                  AppDimens.h24,
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      S.of(context).notesWhenTakingDocuments,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  AppDimens.h16,
                  _BulletList(
                    items: customNotesText != null
                        ? customNotesText!.split('/')
                        : [
                            'Giấy tờ còn hạn sử dụng. Là hình gốc, không scan và photocopy.',
                            'Chụp trong môi trường đủ ánh sáng.',
                            'Đảm bảo ảnh rõ nét, không bị mờ loá.',
                          ],
                  ),
                  AppDimens.h12,

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _WarningIcon(
                        imageStr: AppImages.cccdBlur,
                        label: 'Không chụp quá mờ',
                        color: AppColors.error,
                        bgColor: AppColors.error.withValues(alpha: 0.1),
                      ),
                      _WarningIcon(
                        imageStr: AppImages.cccdCrop,
                        label: 'Không chụp mất góc',
                        color: AppColors.error,
                        bgColor: AppColors.error.withValues(alpha: 0.1),
                      ),
                      _WarningIcon(
                        imageStr: AppImages.cccdFlare,
                        label: 'Không chụp loá sáng',
                        color: AppColors.error,
                        bgColor: AppColors.error.withValues(alpha: 0.1),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          BottomButton(
            title: customButtonText ?? S.of(context).continueText,
            onPressed: () {
              context.goToIdentityUpload(isCTVFlow: isCTVFlow);
            },
          ),
        ],
      ),
    );
  }
}

class _BulletList extends StatelessWidget {
  final List<String> items;

  const _BulletList({required this.items});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: items
          .map((e) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ',
                        style: TextStyle(
                            fontSize: 18, color: AppColors.primaryColor)),
                    Expanded(
                      child: Text(
                        e,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
    );
  }
}

class _WarningIcon extends StatelessWidget {
  final String imageStr;
  final String label;
  final Color color;
  final Color bgColor;

  const _WarningIcon({
    required this.imageStr,
    required this.label,
    required this.color,
    required this.bgColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      children: [
        Container(
          width: 90,
          height: 58,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: AppDimens.borderRadius12,
            border: Border.all(
                color: AppColors.textSecondary.withValues(alpha: 0.5)),
          ),
          child: imageStr.toImage(
            fit: BoxFit.contain,
          ),
        ),
        AppDimens.h12,
        SizedBox(
          width: 90,
          child: Text(
            label,
            maxLines: 2,
            textAlign: TextAlign.center,
            style: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.error,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
