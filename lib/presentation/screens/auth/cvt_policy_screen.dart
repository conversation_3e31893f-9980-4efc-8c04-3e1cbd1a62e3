// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';
// Widgets barrel export
import 'package:sales_app/presentation/widgets/widgets.dart';
// Controllers barrel export
import 'package:sales_app/presentation/controllers/controllers.dart';
// Navigation extensions
import 'package:sales_app/presentation/router/navigation_extensions.dart';

class CTVPolicyScreen extends ConsumerStatefulWidget {
  const CTVPolicyScreen({super.key});

  @override
  ConsumerState<CTVPolicyScreen> createState() => _CTVPolicyScreenState();
}

class _CTVPolicyScreenState extends ConsumerState<CTVPolicyScreen> {
  @override
  void initState() {
    super.initState();
    // Load article data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(articleControllerProvider.notifier).loadArticleByCode(ArticleCodes.ctvPolicy);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BaseScreen(
      title: S.of(context).cvt_policy_title,
      body: Column(
        children: [
          Expanded(
            child: BaseStateConsumerBuilder<ArticleEntity>(
              stateProvider: articleControllerProvider,
              successBuilder: (article, ref) => _buildContent(article),      
            ).withErrorHandler<ArticleEntity>(
              articleControllerProvider,
            ),
          ),
          BottomButton(
            title: S.of(context).register,
            onPressed: () {
              // Use Go Router navigation
              context.goToKycIdGuide();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ArticleEntity article) {

    return SingleChildScrollView(
      padding: AppDimens.paddingAllLg,
      child: CommonMarkdown(
        data: article.content.replaceAll("\\n", '\n'),
      ),
    );
  }
}
