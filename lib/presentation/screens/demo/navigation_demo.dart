import 'package:flutter/material.dart';
import '../../../core/constants/app_dimens.dart';
import '../../../core/theme/app_color.dart';
import '../../router/navigation_extensions.dart';

/// Demo screen to test navigation functionality
/// Located in presentation layer for Clean Architecture compliance
class NavigationDemo extends StatelessWidget {
  const NavigationDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Navigation Demo'),
        backgroundColor: AppColors.blue,
        foregroundColor: AppColors.white,
      ),
      body: Padding(
        padding: AppDimens.paddingAllLg,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Go Router Navigation Demo',
                style: TextStyle(fontSize: AppDimens.fontDisplay, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Push Navigation (with back button)
              _buildSection(
                'Push Navigation (with back button)',
                AppColors.green,
                [
                  _buildButton('Push to Select Account Type', () => context.goToSelectAccountType()),
                  _buildButton('Push to CTV Policy', () => context.goToCtvPolicy()),
                  _buildButton('Push to KYC ID Guide', () => context.goToKycIdGuide()),
                  _buildButton('Push to Identity Upload', () => context.goToIdentityUpload()),
                  _buildButton('Push to Personal Info', () => context.goToPersonalInfoConfirmation()),
                  _buildButton('Push to Registration Success', () => context.goToRegistrationSuccess()),
                ],
              ),

              const SizedBox(height: 24),

              // Replace Navigation (no back button)
              _buildSection(
                'Replace Navigation (no back button)',
                AppColors.orange,
                [
                  _buildButton('Replace with Login', () => context.replaceWithLogin()),
                  _buildButton('Replace with Home', () => context.replaceWithHome()),
                ],
              ),

              const SizedBox(height: 24),

              // Utility Navigation
              _buildSection(
                'Utility Navigation',
                AppColors.blue,
                [
                  _buildButton('Go Back', () => context.goBack()),
                  _buildButton('Push to Home', () => context.pushToHome()),
                ],
              ),

              AppDimens.h32,

              // Navigation Info
              Container(
                padding: AppDimens.paddingAllLg,
                decoration: BoxDecoration(
                  color: AppColors.grey100,
                  borderRadius: AppDimens.borderRadius8,
                  border: Border.all(color: AppColors.grey300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Navigation Patterns:',
                      style: TextStyle(fontSize: AppDimens.fontLG, fontWeight: FontWeight.bold),
                    ),
                    AppDimens.h8,
                    const Text('🟢 Push: Adds to stack, shows back button'),
                    const Text('🟠 Replace: Clears stack, no back button'),
                    const Text('🔵 Utility: Smart navigation helpers'),
                    AppDimens.h8,
                    const Text(
                      'Test the back button behavior after each navigation!',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, Color color, List<Widget> buttons) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: AppDimens.fontXL,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        AppDimens.h12,
        ...buttons,
      ],
    );
  }

  Widget _buildButton(String text, VoidCallback onPressed) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimens.spacingSM),
      child: ElevatedButton(
        onPressed: onPressed,
        child: Text(text),
      ),
    );
  }
}
