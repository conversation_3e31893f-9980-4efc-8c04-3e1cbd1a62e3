import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/enums/loan_step.dart';
import 'package:sales_app/presentation/widgets/common/fixed_step_navigator.dart';
import 'package:sales_app/presentation/utils/loan_step_localizer.dart';

class FloatingNavigatorDemoScreen extends HookWidget {
  const FloatingNavigatorDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final currentStep = useState(LoanStep.identityDocument);
    final hasCoBorrower = useState(false);
    final hasCollateral = useState(false);

    // Tạo danh sách các bước dựa trên điều kiện
    List<LoanStep> createEnabledSteps() {
      final steps = <LoanStep>[
        LoanStep.identityDocument,
        LoanStep.borrowerInfo,
      ];

      if (hasCoBorrower.value) {
        steps.addAll([
          LoanStep.coBorrowerDocument,
          LoanStep.coBorrowerInfo,
        ]);
      }

      steps.addAll([
        LoanStep.loanRequest,
        LoanStep.financialInfo,
      ]);

      if (hasCollateral.value) {
        steps.addAll([
          LoanStep.collateralInfo,
          LoanStep.collateralDetail,
        ]);
      }

      steps.addAll([
        LoanStep.documentList,
        LoanStep.loanConfirmation,
        LoanStep.success,
      ]);

      return steps;
    }

    final enabledSteps = createEnabledSteps();

    void handleStepNavigation(LoanStep targetStep) {
      // Trong thực tế, chỉ cho phép navigate đến các bước đã completed
      // Ở demo này cho phép navigate tự do
      currentStep.value = targetStep;
    }

    void nextStep() {
      final currentIndex = enabledSteps.indexOf(currentStep.value);
      if (currentIndex >= 0 && currentIndex < enabledSteps.length - 1) {
        currentStep.value = enabledSteps[currentIndex + 1];
      }
    }

    void previousStep() {
      final currentIndex = enabledSteps.indexOf(currentStep.value);
      if (currentIndex > 0) {
        currentStep.value = enabledSteps[currentIndex - 1];
      }
    }

    void resetToFirst() {
      currentStep.value = enabledSteps.first;
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Demo Step Navigator'),
        elevation: 2,
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: Stack(
        children: [
          // Main content - full screen
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Business options
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Tùy chọn nghiệp vụ:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        SwitchListTile(
                          title: const Text('Có người đồng vay'),
                          value: hasCoBorrower.value,
                          onChanged: (value) {
                            hasCoBorrower.value = value;
                            resetToFirst();
                          },
                        ),
                        SwitchListTile(
                          title: const Text('Có tài sản bảo đảm'),
                          value: hasCollateral.value,
                          onChanged: (value) {
                            hasCollateral.value = value;
                            resetToFirst();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Current step content
                Expanded(
                  child: Card(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _getStepIcon(currentStep.value),
                            size: 80,
                            color: Colors.blue,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            LoanStepLocalizer.getStepTitle(context, currentStep.value),
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Bước ${enabledSteps.indexOf(currentStep.value) + 1} / ${enabledSteps.length}',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            _getStepDescription(currentStep.value),
                            style: const TextStyle(fontSize: 16),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Navigation buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: enabledSteps.indexOf(currentStep.value) > 0 ? previousStep : null,
                        child: const Text('⬅️ Quay lại'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: enabledSteps.indexOf(currentStep.value) < enabledSteps.length - 1 ? nextStep : null,
                        child: const Text('Tiếp tục ➡️'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // FixedStepNavigator - overlay đầu màn hình
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: FixedStepNavigator(
              currentStep: currentStep.value,
              enabledSteps: enabledSteps,
              onStepTap: handleStepNavigation,
              showHeader: true,
              showProgressBar: true,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getStepIcon(LoanStep step) {
    switch (step) {
      case LoanStep.identityDocument:
        return Icons.badge;
      case LoanStep.borrowerInfo:
        return Icons.person;
      case LoanStep.coBorrowerDocument:
        return Icons.badge_outlined;
      case LoanStep.coBorrowerInfo:
        return Icons.group;
      case LoanStep.loanRequest:
        return Icons.request_quote;
      case LoanStep.financialInfo:
        return Icons.account_balance_wallet;
      case LoanStep.collateralInfo:
        return Icons.home;
      case LoanStep.collateralDetail:
        return Icons.description;
      case LoanStep.documentList:
        return Icons.folder;
      case LoanStep.loanConfirmation:
        return Icons.check_circle_outline;
      case LoanStep.success:
        return Icons.celebration;
    }
  }

  String _getStepDescription(LoanStep step) {
    switch (step) {
      case LoanStep.identityDocument:
        return 'Cung cấp ảnh chụp CMND/CCCD hoặc Passport để xác thực danh tính người vay chính.';
      case LoanStep.borrowerInfo:
        return 'Xác nhận và bổ sung thông tin cá nhân của người vay chính.';
      case LoanStep.coBorrowerDocument:
        return 'Cung cấp giấy tờ tùy thân của người đồng vay (nếu có).';
      case LoanStep.coBorrowerInfo:
        return 'Xác nhận thông tin cá nhân của người đồng vay.';
      case LoanStep.loanRequest:
        return 'Cung cấp thông tin về khoản vay mong muốn: số tiền, thời hạn, mục đích sử dụng.';
      case LoanStep.financialInfo:
        return 'Cung cấp thông tin về tình hình tài chính: thu nhập, chi phí, tài sản hiện có.';
      case LoanStep.collateralInfo:
        return 'Cung cấp thông tin về tài sản bảo đảm (nếu có).';
      case LoanStep.collateralDetail:
        return 'Cung cấp chi tiết về tài sản bảo đảm: vị trí, giá trị, hồ sơ pháp lý.';
      case LoanStep.documentList:
        return 'Upload các chứng từ hỗ trợ: sao kê, hợp đồng lao động, v.v.';
      case LoanStep.loanConfirmation:
        return 'Xem lại và xác nhận tất cả thông tin đã cung cấp.';
      case LoanStep.success:
        return 'Hoàn thành! Hồ sơ vay vốn đã được tạo thành công.';
    }
  }
} 