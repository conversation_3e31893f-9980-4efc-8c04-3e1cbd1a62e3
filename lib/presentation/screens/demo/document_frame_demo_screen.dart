import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/presentation/screens/document/widgets/widgets.dart';

/// Demo screen để test khung chụp document mới
/// Hiển thị khung bo tròn góc thay vì hình vuông liền mạch
class DocumentFrameDemoScreen extends StatelessWidget {
  const DocumentFrameDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    const cardRatio = AppDimens.cccdRatio; // Tỷ lệ CCCD
    final cardWidth = size.width * AppDimens.alphaNearOpaque;
    final cardHeight = cardWidth / cardRatio;
    final screenHeight = size.height - MediaQuery.of(context).padding.top - kToolbarHeight;
    final frameTop = (screenHeight - cardHeight) / AppDimens.scaleHuge;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Demo Khung Chụp CCCD'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
      ),
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Background gradient để dễ nhìn
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF1a1a1a),
                  Color(0xFF2d2d2d),
                ],
              ),
            ),
          ),

          // Document overlay widget với khung mới
          DocumentOverlayWidget(
            cardWidth: cardWidth,
            cardHeight: cardHeight,
            frameTop: frameTop,
          ),

          // Guidance text
          Positioned(
            top: frameTop + cardHeight + AppDimens.spacingXL,
            left: 0,
            right: 0,
            child: Container(
              margin: AppDimens.marginHorizontalLg,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Khung chụp CCCD mới',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  AppDimens.h16,
                  Text(
                    'Bo tròn 4 góc giống CCCD thực tế\nChỉ hiển thị các góc thay vì hình vuông liền mạch',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: AppDimens.alphaNearOpaque),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Back button
          Positioned(
            top: AppDimens.spacingLG,
            left: AppDimens.spacingLG,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.arrow_back_ios_new,
                  color: Colors.white,
                  size: 20,
                ),
                onPressed: () => Navigator.of(context).pop(),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 