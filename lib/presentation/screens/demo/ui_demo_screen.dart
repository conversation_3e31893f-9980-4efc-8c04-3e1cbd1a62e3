import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/constants/app_dimens.dart';
import '../../../core/theme/app_color.dart';
import '../../widgets/widgets.dart';

/// UI Demo Screen - Showcase tất cả các common và shared widgets
/// 
/// Màn hình này hiển thị các ví dụ về cách sử dụng các UI components:
/// - Common widgets: BaseScreen, CommonButton, CommonTextField, CommonDropdown, etc.
/// - Shared widgets: UserAvatar, LoadingOverlay
/// - Loading system: BaseLoadingHookWidget
class UiDemoScreen extends HookWidget {
  const UiDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Controllers for form fields
    final textController = useTextEditingController();
    final passwordController = useTextEditingController();
    final dropdownValue = useState<String?>(null);
    final isLoading = useState(false);

    return BaseScreen(
      title: 'UI Components Demo',
      body: LoadingOverlay(
        isLoading: isLoading.value,
        child: SingleChildScrollView(
          padding: AppDimens.paddingAllLg,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildSectionHeader('🎨 UI Components Showcase'),
              AppDimens.h16,
              
              Text(
                'Đây là màn hình demo để showcase tất cả các UI components common và shared trong ứng dụng.',
                style: TextStyle(
                  fontSize: AppDimens.fontMD,
                  color: AppColors.textSecondary,
                ),
              ),
              
              AppDimens.h32,

              // Common Buttons Section
              _buildSectionHeader('🔘 Common Buttons'),
              AppDimens.h16,
              
              CommonButton(
                title: 'Primary Button',
                onPressed: () => _showSnackBar(context, 'Primary Button Pressed'),
              ),
              
              AppDimens.h12,
              
              CommonButton(
                title: 'Disabled Button',
                enabled: false,
                onPressed: () {},
              ),
              
              AppDimens.h12,
              
              CommonButton(
                title: 'Custom Color Button',
                backgroundColor: AppColors.success,
                onPressed: () => _showSnackBar(context, 'Custom Color Button Pressed'),
              ),
              
              AppDimens.h12,
              
              CommonButton(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.star, color: AppColors.textWhite, size: AppDimens.iconSM),
                    AppDimens.w8,
                    const Text('Button with Icon'),
                  ],
                ),
                onPressed: () => _showSnackBar(context, 'Icon Button Pressed'),
              ),

              AppDimens.h32,

              // Common Text Fields Section
              _buildSectionHeader('📝 Common Text Fields'),
              AppDimens.h16,
              
              CommonTextField(
                label: 'Email',
                hintText: 'Nhập email của bạn',
                controller: textController,
                keyboardType: TextInputType.emailAddress,
                prefixIcon: const Icon(Icons.email, color: AppColors.primaryColor),
              ),
              
              AppDimens.h16,
              
              CommonTextField(
                label: 'Password',
                hintText: 'Nhập mật khẩu',
                controller: passwordController,
                isSensitive: true,
                prefixIcon: const Icon(Icons.lock, color: AppColors.primaryColor),
              ),
              
              AppDimens.h16,
              
              CommonTextField(
                label: 'Disabled Field',
                hintText: 'This field is disabled',
                controller: useTextEditingController(),
                enabled: false,
              ),

              AppDimens.h32,

              // Common Dropdown Section
              _buildSectionHeader('📋 Common Dropdown'),
              AppDimens.h16,
              
              CommonDropdown<String>(
                label: 'Chọn tỉnh/thành phố',
                hint: 'Vui lòng chọn',
                items: const ['Hà Nội', 'TP. Hồ Chí Minh', 'Đà Nẵng', 'Cần Thơ', 'Hải Phòng'],
                value: dropdownValue.value,
                onChanged: (value) => dropdownValue.value = value,
                prefixIcon: const Icon(Icons.location_on, color: AppColors.primaryColor),
              ),

              AppDimens.h32,

              // User Avatar Section
              _buildSectionHeader('👤 User Avatar'),
              AppDimens.h16,
              
              Row(
                children: [
                  UserAvatar(
                    name: 'John Doe',
                    size: AppDimens.avatarSm,
                    onTap: () => _showSnackBar(context, 'Small Avatar Tapped'),
                  ),
                  AppDimens.w16,
                  UserAvatar(
                    name: 'Jane Smith',
                    size: AppDimens.avatarMd,
                    onTap: () => _showSnackBar(context, 'Medium Avatar Tapped'),
                  ),
                  AppDimens.w16,
                  UserAvatar(
                    name: 'Bob Wilson',
                    size: AppDimens.avatarLg,
                    onTap: () => _showSnackBar(context, 'Large Avatar Tapped'),
                  ),
                ],
              ),

              AppDimens.h32,

              // Loading Demo Section
              _buildSectionHeader('⏳ Loading Demo'),
              AppDimens.h16,
              
              CommonButton(
                title: isLoading.value ? 'Loading...' : 'Show Loading',
                onPressed: () => _simulateLoading(isLoading),
              ),

              AppDimens.h32,

              // Photo Source Picker Demo
              _buildSectionHeader('📷 Photo Source Picker'),
              AppDimens.h16,
              
              CommonButton(
                title: 'Show Photo Picker',
                backgroundColor: AppColors.orange,
                onPressed: () => _showPhotoSourcePicker(context),
              ),

              AppDimens.h32,

              // Custom App Bar Demo (Info only)
              _buildSectionHeader('📱 Custom App Bar'),
              AppDimens.h16,
              
              Container(
                padding: AppDimens.paddingAllMd,
                decoration: BoxDecoration(
                  color: AppColors.grey100,
                  borderRadius: AppDimens.borderRadius8,
                  border: Border.all(color: AppColors.grey300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Custom App Bar Features:',
                      style: TextStyle(
                        fontSize: AppDimens.fontLG,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    AppDimens.h8,
                    const Text('✅ Consistent styling across app'),
                    const Text('✅ Automatic back button handling'),
                    const Text('✅ Customizable actions'),
                    const Text('✅ Center title option'),
                    const Text('✅ Bottom widget support'),
                  ],
                ),
              ),

              AppDimens.h32,

              // Base Screen Demo (Info only)
              _buildSectionHeader('🏠 Base Screen'),
              AppDimens.h16,
              
              Container(
                padding: AppDimens.paddingAllMd,
                decoration: BoxDecoration(
                  color: AppColors.grey100,
                  borderRadius: AppDimens.borderRadius8,
                  border: Border.all(color: AppColors.grey300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Base Screen Features:',
                      style: TextStyle(
                        fontSize: AppDimens.fontLG,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    AppDimens.h8,
                    const Text('✅ Consistent screen structure'),
                    const Text('✅ Built-in CustomAppBar'),
                    const Text('✅ White background'),
                    const Text('✅ Easy to use and extend'),
                    AppDimens.h8,
                    const Text(
                      'Note: This screen is using BaseScreen!',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),

              AppDimens.h48,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: AppDimens.fontXL,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: AppDimens.borderRadius8,
        ),
      ),
    );
  }

  Future<void> _simulateLoading(ValueNotifier<bool> isLoading) async {
    isLoading.value = true;
    await Future.delayed(const Duration(seconds: 3));
    isLoading.value = false;
  }

  void _showPhotoSourcePicker(BuildContext context) {
    showCupertinoPhotoSourcePicker(
      context: context,
      onTakePhoto: () => _showSnackBar(context, 'Take Photo Selected'),
      onPickFromGallery: () => _showSnackBar(context, 'Pick from Gallery Selected'),
      title: 'Demo Photo Picker',
      message: 'Chọn nguồn ảnh để demo',
    );
  }
}
