import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/enums/loan_step.dart';
import 'package:sales_app/presentation/widgets/common/dynamic_loan_progress_indicator.dart';
import 'package:sales_app/presentation/widgets/common/base_screen.dart';
import 'package:sales_app/presentation/utils/loan_step_localizer.dart';

class LoanProgressDemoScreen extends HookWidget {
  const LoanProgressDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final currentStep = useState(LoanStep.identityDocument);
    final hasCoBorrower = useState(false);
    final hasCollateral = useState(false);

    // Tạo danh sách các bước dựa trên điều kiện
    List<LoanStep> createEnabledSteps() {
      final steps = <LoanStep>[
        LoanStep.identityDocument,
        LoanStep.borrowerInfo,
      ];

      if (hasCoBorrower.value) {
        steps.addAll([
          LoanStep.coBorrowerDocument,
          LoanStep.coBorrowerInfo,
        ]);
      }

      steps.addAll([
        LoanStep.loanRequest,
        LoanStep.financialInfo,
      ]);

      if (hasCollateral.value) {
        steps.addAll([
          LoanStep.collateralInfo,
          LoanStep.collateralDetail,
        ]);
      }

      steps.addAll([
        LoanStep.documentList,
        LoanStep.loanConfirmation,
        LoanStep.success,
      ]);

      return steps;
    }

    final enabledSteps = createEnabledSteps();

    void nextStep() {
      final currentIndex = enabledSteps.indexOf(currentStep.value);
      if (currentIndex >= 0 && currentIndex < enabledSteps.length - 1) {
        currentStep.value = enabledSteps[currentIndex + 1];
      }
    }

    void previousStep() {
      final currentIndex = enabledSteps.indexOf(currentStep.value);
      if (currentIndex > 0) {
        currentStep.value = enabledSteps[currentIndex - 1];
      }
    }

    void resetToFirst() {
      currentStep.value = enabledSteps.first;
    }

    return BaseScreen(
      title: 'Demo Dynamic Loan Progress',
      automaticallyImplyLeading: true,
      body: Column(
        children: [
          // Progress indicator
          DynamicLoanProgressIndicator(
            currentStep: currentStep.value,
            enabledSteps: enabledSteps,
            stepTitle: LoanStepLocalizer.getStepTitle(context, currentStep.value),
          ),
          
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Các toggle để thay đổi điều kiện
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Tùy chọn nghiệp vụ:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          SwitchListTile(
                            title: const Text('Có người đồng vay'),
                            value: hasCoBorrower.value,
                            onChanged: (value) {
                              hasCoBorrower.value = value;
                              resetToFirst();
                            },
                          ),
                          SwitchListTile(
                            title: const Text('Có tài sản bảo đảm'),
                            value: hasCollateral.value,
                            onChanged: (value) {
                              hasCollateral.value = value;
                              resetToFirst();
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Thông tin bước hiện tại
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Thông tin bước hiện tại:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text('Bước: ${currentStep.value.name}'),
                          Text('Số thứ tự: ${currentStep.value.stepNumber}'),
                          Text('Tổng số bước: ${enabledSteps.length}'),
                          Text('Vị trí trong flow: ${enabledSteps.indexOf(currentStep.value) + 1}/${enabledSteps.length}'),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Các nút điều khiển
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: enabledSteps.indexOf(currentStep.value) > 0 ? previousStep : null,
                          child: const Text('Quay lại'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: enabledSteps.indexOf(currentStep.value) < enabledSteps.length - 1 ? nextStep : null,
                          child: const Text('Tiếp tục'),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Danh sách các bước
                  Expanded(
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Danh sách các bước trong flow:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Expanded(
                              child: ListView.builder(
                                itemCount: enabledSteps.length,
                                itemBuilder: (context, index) {
                                  final step = enabledSteps[index];
                                  final isActive = step == currentStep.value;
                                  
                                  return ListTile(
                                    leading: CircleAvatar(
                                      backgroundColor: isActive ? Colors.blue : Colors.grey,
                                      child: Text(
                                        '${index + 1}',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                                        ),
                                      ),
                                    ),
                                    title: Text(
                                      LoanStepLocalizer.getStepTitle(context, step),
                                      style: TextStyle(
                                        fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                                        color: isActive ? Colors.blue : null,
                                      ),
                                    ),
                                    subtitle: Text('${step.name} (${step.stepNumber})'),
                                    onTap: () => currentStep.value = step,
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 