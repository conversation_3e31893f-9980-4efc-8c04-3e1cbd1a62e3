import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Core barrel export
import '../../core/core.dart';

// Presentation layer barrel exports
import '../screens/screens.dart';
import '../widgets/widgets.dart';

/// Router configuration provider
/// Located in presentation layer to avoid Clean Architecture violations
///
/// Clean Architecture compliance:
/// - Presentation layer can depend on Core layer (app_routes.dart)
/// - Presentation layer manages UI routing logic
/// - Core layer remains independent of UI concerns
final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    navigatorKey: GlobalNavigator.navigatorKey,
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    routes: [
      // Splash Route
      GoRoute(
        path: AppRoutes.splash,
        name: AppRoutes.splashName,
        builder: (context, state) {
          AppLogger.info('Navigating to SplashScreen');
          return const SplashScreen();
        },
      ),
      
      // Auth Routes
      GoRoute(
        path: AppRoutes.login,
        name: AppRoutes.loginName,
        builder: (context, state) {
          AppLogger.info('Navigating to LoginScreen');
          return const LoginScreen();
        },
      ),

      GoRoute(
        path: AppRoutes.selectAccountType,
        name: AppRoutes.selectAccountTypeName,
        builder: (context, state) {
          AppLogger.info('Navigating to SelectAccountTypePage');
          return const SelectAccountTypePage();
        },
      ),

      GoRoute(
        path: AppRoutes.personalInfoConfirmation,
        name: AppRoutes.personalInfoConfirmationName,
        builder: (context, state) {
          AppLogger.info('Navigating to PersonalInfoConfirmationScreen');
          final extra = state.extra as Map<String, dynamic>?;
          final isCTVFlow = extra?['isCTVFlow'] == true;
          return PersonalInfoConfirmationScreen(isCTVFlow: isCTVFlow);
        },
      ),

      GoRoute(
        path: AppRoutes.ctvPolicy,
        name: AppRoutes.ctvPolicyName,
        builder: (context, state) {
          AppLogger.info('Navigating to CtvPolicyScreen');
          return const CTVPolicyScreen();
        },
      ),

      GoRoute(
        path: AppRoutes.kycIdGuide,
        name: AppRoutes.kycIdGuideName,
        builder: (context, state) {
          AppLogger.info('Navigating to KycIdGuideScreen');
          final extra = state.extra as Map<String, dynamic>?;
          final isCTVFlow = extra?['isCTVFlow'] == true;
          return KycIdGuideScreen(
            customTitle: extra?['customTitle'],
            customNotesText: extra?['customNotesText'],
            customAvoidText: extra?['customAvoidText'],
            customButtonText: extra?['customButtonText'],
            customWarningLabels: extra?['customWarningLabels']?.cast<String>(),
            isCTVFlow: isCTVFlow,
          );
        },
      ),

      GoRoute(
        path: AppRoutes.registrationSuccess,
        name: AppRoutes.registrationSuccessName,
        builder: (context, state) {
          AppLogger.info('Navigating to RegistrationSuccessScreen');
          final extra = state.extra as Map<String, dynamic>?;
          return RegistrationSuccessScreen(
            isCTVFlow: extra?['isCTVFlow'] == true,
            customTitle: extra?['customTitle'],
            customMessage: extra?['customMessage'],
            customButtonText: extra?['customButtonText'],
            fullName: extra?['fullName'],
            branchName: extra?['branchName'],
            position: extra?['position'],
            referrerCode: extra?['referrerCode'],
            referrerName: extra?['referrerName'],
          );
        },
      ),

      // Main App Routes
      GoRoute(
        path: AppRoutes.home,
        name: AppRoutes.homeName,
        builder: (context, state) {
          AppLogger.info('Navigating to HomeScreen');
          return const HomeScreenV2();
        },
      ),

      // Document Routes with parameters
      GoRoute(
        path: AppRoutes.captureDocument,
        name: AppRoutes.captureDocumentName,
        builder: (context, state) {
          AppLogger.info('Navigating to CaptureDocumentScreen');
          final extra = state.extra as Map<String, dynamic>?;
          if (extra != null) {
            return CaptureDocumentScreen(
              title: extra['title'] ?? 'Chụp tài liệu',
              side: extra['side'],
              documentType: extra['documentType'],
              onImageCaptured: extra['onImageCaptured'],
            );
          }
          return const Scaffold(
            body: Center(child: Text('Invalid parameters')),
          );
        },
      ),

      GoRoute(
        path: AppRoutes.previewDocument,
        name: AppRoutes.previewDocumentName,
        builder: (context, state) {
          AppLogger.info('Navigating to PreviewDocumentScreen');
          final extra = state.extra as Map<String, dynamic>?;
          if (extra != null) {
            return PreviewDocumentScreen(
              image: extra['image'],
              side: extra['side'],
              documentType: extra['documentType'],
              onAccept: extra['onAccept'],
            );
          }
          return const Scaffold(
            body: Center(child: Text('Invalid parameters')),
          );
        },
      ),

      // Identity Routes
      GoRoute(
        path: AppRoutes.identityUpload,
        name: AppRoutes.identityUploadName,
        builder: (context, state) {
          AppLogger.info('Navigating to IdentityUploadPage');
          final extra = state.extra as Map<String, dynamic>?;
          return IdentityUploadPage(
            customTitle: extra?['customTitle'],
            customDescription: extra?['customDescription'],
            isCTVFlow: extra?['isCTVFlow'] ?? false,
          );
        },
      ),

      // Demo Routes
      GoRoute(
        path: AppRoutes.navigationDemo,
        name: AppRoutes.navigationDemoName,
        builder: (context, state) {
          AppLogger.info('Navigating to NavigationDemo');
          return const NavigationDemo();
        },
      ),

      GoRoute(
        path: AppRoutes.uiDemo,
        name: AppRoutes.uiDemoName,
        builder: (context, state) {
          AppLogger.info('Navigating to UiDemoScreen');
          return const UiDemoScreen();
        },
      ),
      // QR Scan Route - Generic QR Scanner
      GoRoute(
        path: AppRoutes.qrScan,
        name: AppRoutes.qrScanName,
        builder: (context, state) {
          AppLogger.info('Navigating to CommonQRScanner');
          final extra = state.extra as Map<String, dynamic>?;
          
          try {
            // Get all parameters from extra with type safety
            final onQRCodeDetected = extra?['onQRCodeDetected'] as void Function(String)?;
            final onSkip = extra?['onSkip'] as VoidCallback?;
            final onBack = extra?['onBack'] as VoidCallback?;
            final customTitle = extra?['customTitle'] as String?;
            final customGuidanceText = extra?['customGuidanceText'] as String?;
            final showSkipButton = extra?['showSkipButton'] as bool? ?? true;
            final showGalleryButton = extra?['showGalleryButton'] as bool? ?? true;
            final showFlashButton = extra?['showFlashButton'] as bool? ?? true;
            final customErrorMessage = extra?['customErrorMessage'] as String?;
            
            return CommonQRScanner(
              onQRCodeDetected: onQRCodeDetected ?? (qrData) {
                AppLogger.info('CommonQRScanner: Default onQRCodeDetected behavior', data: {
                  'qrData': qrData,
                });
                // Default behavior: just pop back
                if (context.canPop()) {
                  context.pop();
                }
              },
              onSkip: onSkip ?? () {
                AppLogger.info('CommonQRScanner: Default onSkip behavior');
                if (context.canPop()) {
                  context.pop();
                }
              },
              onBack: onBack ?? () {
                AppLogger.info('CommonQRScanner: Default onBack behavior');
                if (context.canPop()) {
                  context.pop();
                }
              },
              customTitle: customTitle,
              customGuidanceText: customGuidanceText,
              showSkipButton: showSkipButton,
              showGalleryButton: showGalleryButton,
              showFlashButton: showFlashButton,
              customErrorMessage: customErrorMessage,
            );
          } catch (e) {
            AppLogger.error('Error creating CommonQRScanner', error: e);
            return Scaffold(
              appBar: AppBar(title: const Text('Lỗi')),
              body: const Center(
                child: Text('Không thể khởi tạo QR Scanner'),
              ),
            );
          }
        },
      ),
    ],
    errorBuilder: (context, state) {
      AppLogger.error('Navigation error: ${state.error}', data: {
        'uri': state.uri.toString(),
        'error': state.error?.toString(),
      });
      return Scaffold(
        appBar: AppBar(
          title: const Text('Lỗi'),
          backgroundColor: AppColors.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Padding(
            padding: AppDimens.paddingAllLg,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppColors.error,
                ),
                AppDimens.h24,
                Text(
                  'Không tìm thấy trang',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                AppDimens.h8,
                Text(
                  'Đường dẫn: ${state.uri}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                AppDimens.h32,
                ElevatedButton(
                  onPressed: () => context.go(AppRoutes.login),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: Colors.white,
                    padding: AppDimens.paddingHorizontalLg + AppDimens.paddingVerticalMd,
                  ),
                  child: const Text('Về trang đăng nhập'),
                ),
              ],
            ),
          ),
        ),
      );
    },
    redirect: (context, state) {
      // TODO: Add authentication logic here
      AppLogger.debug('Navigation redirect check for: ${state.uri}');
      return null;
    },
  );
});
