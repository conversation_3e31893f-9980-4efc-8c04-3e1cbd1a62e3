import 'package:riverpod_annotation/riverpod_annotation.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';
// DI barrel export
import 'package:sales_app/di/injection.dart';

part 'article_controller.g.dart';

@riverpod
class ArticleController extends _$ArticleController with AutoDisposeBaseStateMixin<ArticleEntity> {
  late final ArticleRepository _articleRepository;

  @override
  BaseState<ArticleEntity> build() {
    _articleRepository = getIt<ArticleRepository>();
    return const BaseState.initial();
  }

  @override
  NetworkInfo get networkInfo => getIt<NetworkInfo>();

  /// Load article by code
  Future<void> loadArticleByCode(String code) async {
    if (state.isProcessing) return;

    await loadData(
      () async {
      final result = await _articleRepository.getArticleByCode(code);
        return result;
      },
      );
  }

  /// Refresh article data
  Future<void> refreshArticle(String code) async {
    if (state.isProcessing) return;

    final currentData = state.data;
    if (currentData == null) {
      await loadArticleByCode(code);
      return;
    }

    state = BaseState.refreshing(currentData);

    try {
      final result = await _articleRepository.getArticleByCode(code);

      result.fold(
        (failure) => state = BaseState.error(failure),
        (article) => state = BaseState.success(article),
      );
    } catch (e) {
      AppLogger.error('Unexpected error in refreshArticle', error: e);
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.internalError,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }
} 