// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'article_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$articleControllerHash() => r'66b2c2d6e6a41578106e29ac34bf5b99a57c47c6';

/// See also [ArticleController].
@ProviderFor(ArticleController)
final articleControllerProvider = AutoDisposeNotifierProvider<ArticleController,
    BaseState<ArticleEntity>>.internal(
  ArticleController.new,
  name: r'articleControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$articleControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ArticleController = AutoDisposeNotifier<BaseState<ArticleEntity>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
