import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:get_it/get_it.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';
// Presentation models
import 'package:sales_app/presentation/models/models.dart';

part 'ctv_registration_controller.g.dart';

/// Controller quản lý toàn bộ flow đăng ký CTV
///
/// Flow: identity_upload_page.dart → qr_scan_screen.dart → personal_info_confirmation_screen.dart
@riverpod
class CTVRegistrationController extends _$CTVRegistrationController
    with AutoDisposeBaseStateMixin<CTVRegistrationFlowData> {
  late final CTVRegistrationRepository _ctvRepository;
  late final AuthRepository _authRepository;

  @override
  BaseState<CTVRegistrationFlowData> build() {
    _ctvRepository = GetIt.I.get<CTVRegistrationRepository>();
    _authRepository = GetIt.I.get<AuthRepository>();

    // Khởi tạo với data mặc định
    const initialData = CTVRegistrationFlowData(
      currentStep: CTVRegistrationStep.documentUpload,
    );

    return const BaseState.success(initialData);
  }

  @override
  NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();

  // ==================== LOCAL DATA LOADING METHODS ====================

  /// Load data từ local storage (branch và referrer từ user profile)
  /// Gộp cả 2 method loadBranchFromProfile và loadReferrerFromProfile
  Future<void> loadDataFromLocalStorage() async {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Loading branch and referrer from user profile');

    try {
      // Lấy user info từ AuthRepository (sẽ lấy từ local storage)
      final userResult = await _authRepository.getCurrentUser();

      userResult.fold(
        (failure) {
          // Error loading - giữ nguyên data hiện tại
          AppLogger.error('Failed to load data from local storage', error: failure);
        },
        (user) {
          // Xử lý branch data
          BranchEntity? selectedBranch;
          if (user.branch != null) {
            selectedBranch = user.branch;
            AppLogger.info('Branch loaded from local storage: ${user.branch!.name}');
          } else {
            AppLogger.warning('User profile does not have branch information');
          }

          // Xử lý referrer data
          ReferrerEntity? selectedReferrer;
          if (user.name.isNotEmpty && user.id.isNotEmpty) {
            selectedReferrer = ReferrerEntity(
              referrerCode: user.id, // cifNo làm referrer code
              referrerName: user.name, // fullName làm referrer name
            );
            AppLogger.info('Referrer loaded from user profile', data: {
              'referrerCode': user.id,
              'referrerName': user.name,
            });
          } else {
            AppLogger.warning('User profile does not have valid name or id for referrer');
          }

          // Update data với branch và referrer từ local storage - giữ nguyên data khác
          final updatedData = currentData.copyWith(
            selectedBranch: selectedBranch,
            selectedReferrer: selectedReferrer,
          );

          AppLogger.info('Updated data with local storage data', data: {
            'currentStep': currentData.currentStep.name,
            'hasCCCDInfo': currentData.cccdInfo != null,
            'hasPersonalInfo': currentData.personalInfoConfirmation != null,
            'hasBranch': selectedBranch != null,
            'hasReferrer': selectedReferrer != null,
          });

          state = BaseState.success(updatedData);
        },
      );
    } catch (e) {
      // Exception - giữ nguyên data hiện tại
      AppLogger.error('Exception loading data from local storage', error: e);
    }
  }

  // ==================== LOCATION METHODS ====================

  /// Load danh sách tỉnh/thành phố
  Future<void> loadProvinces() async {
    // Kiểm tra nếu đang submitting thì không gọi lại
    if (state.isSubmitting) {
      AppLogger.info('Already loading provinces, skipping');
      return;
    }

    AppLogger.info('Loading provinces with submitData');

    await submitData(
      {}, // Empty data vì chỉ load, không submit gì
      (data) => _loadProvincesWithSubmit(),
    );
  }

  /// Load provinces với submitData pattern - giữ nguyên data cũ
  Future<Either<Failure, CTVRegistrationFlowData>>
      _loadProvincesWithSubmit() async {
    try {
      final result = await _ctvRepository.getProvinces();

      return result.fold(
        (failure) => Left(failure),
        (provinces) {
          final currentData = state.data;
          if (currentData == null) {
            // Tạo initial data nếu null
            AppLogger.info(
                'Current data is null, creating initial data with provinces');
            const initialData = CTVRegistrationFlowData(
              currentStep: CTVRegistrationStep.documentUpload,
            );
            final updatedData = initialData.copyWith(
              provinces: provinces,
            );
            return Right(updatedData);
          }

          // Update provinces vào data hiện tại - giữ nguyên data khác
          AppLogger.info('Updating provinces to existing data', data: {
            'currentStep': currentData.currentStep.name,
            'hasCCCDInfo': currentData.cccdInfo != null,
            'hasPersonalInfo': currentData.personalInfoConfirmation != null,
            'provincesCount': provinces.length,
          });

          final updatedData = currentData.copyWith(
            provinces: provinces,
          );
          return Right(updatedData);
        },
      );
    } catch (e) {
      AppLogger.error('Error loading provinces', error: e);
      return const Left(ServerFailure(ServerErrorType.unknown));
    }
  }

  /// Load danh sách chi nhánh theo tỉnh/thành phố
  Future<void> loadBranches(String provinceId) async {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Loading branches for province: $provinceId');

    // Set loading state riêng cho branches
    final loadingData = currentData.copyWith(isLoadingBranches: true);
    state = BaseState.success(loadingData);

    try {
      final result = await _ctvRepository.getBranches(provinceId);

      result.fold(
        (failure) {
          // Error loading branches - reset loading state và giữ nguyên data
          final errorData = currentData.copyWith(isLoadingBranches: false);
          state = BaseState.success(errorData);

          // Log error để BaseStateErrorHandler có thể handle
          AppLogger.error('Failed to load branches', error: failure);
        },
        (branches) {
          // Success - update branches và reset loading state
          final successData = currentData.copyWith(
            branches: branches,
            isLoadingBranches: false,
          );
          state = BaseState.success(successData);
        },
      );
    } catch (e) {
      // Exception - reset loading state và giữ nguyên data
      final errorData = currentData.copyWith(isLoadingBranches: false);
      state = BaseState.success(errorData);

      AppLogger.error('Exception loading branches', error: e);
    }
  }

  /// Chọn tỉnh/thành phố
  void selectProvince(ProvinceEntity province) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting province: ${province.name}');

    final updatedData = currentData.copyWith(
      selectedProvince: province,
      branches: const [], // Clear branches cũ
      isLoadingBranches: false, // Reset loading state
      clearSelectedBranch: true, // Reset branch khi chọn province mới
    );
    state = BaseState.success(updatedData);

    // Tự động load branches cho province đã chọn
    loadBranches(province.id);
  }

  /// Chọn chi nhánh
  void selectBranch(BranchEntity branch) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting branch: ${branch.name}');

    final updatedData = currentData.copyWith(
      selectedBranch: branch,
    );
    state = BaseState.success(updatedData);
  }

  /// Clear selection
  void clearLocationSelection() {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Clearing location selection');

    final updatedData = currentData.copyWith(
      selectedProvince: null,
      selectedBranch: null,
    );
    state = BaseState.success(updatedData);
  }

  // ==================== NAVIGATION METHODS ====================

  /// Chuyển đến bước tiếp theo
  void nextStep() {
    final currentData = state.data;
    if (currentData == null) return;

    final nextStep = _getNextStep(currentData.currentStep);
    final updatedData = currentData.copyWith(currentStep: nextStep);
    state = BaseState.success(updatedData);
  }

  /// Chuyển về bước trước và reset data của step hiện tại
  void previousStep() {
    final currentData = state.data;
    if (currentData == null) return;

    final previousStep = _getPreviousStep(currentData.currentStep);
    
    AppLogger.info('Moving to previous step and resetting current step data', data: {
      'currentStep': currentData.currentStep.name,
      'previousStep': previousStep.name,
    });

    // Reset data của step hiện tại khi quay lại bước trước
    final updatedData = _resetCurrentStepData(currentData, previousStep);
    state = BaseState.success(updatedData);
  }

  /// Chuyển đến bước cụ thể
  void goToStep(CTVRegistrationStep step) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(currentStep: step);
    state = BaseState.success(updatedData);
  }

  // ==================== DOCUMENT UPLOAD METHODS ====================

  /// Upload 2 mặt GTTT (tái sử dụng logic từ IdentityUploadController)
  Future<void> uploadIdentityDocuments({
    required DocumentType documentType,
    required File frontFile,
    required File backFile,
  }) async {
    if (state.isProcessing) return;

    AppLogger.info('Starting identity document upload for CTV registration',
        data: {
          'documentType': documentType.label,
          'frontFile': frontFile.path,
          'backFile': backFile.path,
        });

    // Validate files exist
    if (!await frontFile.exists() || !await backFile.exists()) {
      AppLogger.error('One or both files do not exist', data: {
        'frontExists': await frontFile.exists(),
        'backExists': await backFile.exists(),
      });
      state = const BaseState.error(
        ValidationFailure(ValidationErrorType.invalidFormat),
      );
      return;
    }

    // Resize ảnh trước khi upload
    final resizedFront = await resizeImage(frontFile, maxWidth: 1024);
    final resizedBack = await resizeImage(backFile, maxWidth: 1024);

    await submitData(
      {
        'documentType': documentType,
        'frontFile': resizedFront,
        'backFile': resizedBack,
      },
      (data) => _uploadBothDocuments(
        documentType: data['documentType'] as DocumentType,
        frontFile: data['frontFile'] as File,
        backFile: data['backFile'] as File,
      ),
    );
  }

  /// Upload cả 2 mặt GTTT và cập nhật flow data
  Future<Either<Failure, CTVRegistrationFlowData>> _uploadBothDocuments({
    required DocumentType documentType,
    required File frontFile,
    required File backFile,
  }) async {
    try {
      AppLogger.info('Starting identity document upload for CTV registration',
          data: {
            'documentType': documentType.label,
            'frontFile': frontFile.path,
            'backFile': backFile.path,
          });

      // Resize ảnh trước khi upload (phòng trường hợp gọi trực tiếp)
      final resizedFront = await resizeImage(frontFile, maxWidth: 1024);
      final resizedBack = await resizeImage(backFile, maxWidth: 1024);

      // Upload documents qua repository
      final uploadResult = await _ctvRepository.uploadIdentityDocuments(
        frontFile: resizedFront,
        backFile: resizedBack,
      );

      if (uploadResult.isLeft()) {
        final failure = uploadResult
            .swap()
            .getOrElse(() => const ServerFailure(ServerErrorType.unknown));
        AppLogger.error('Identity documents upload failed',
            data: {'failure': failure.toString()});
        return Left(failure);
      }

      final urls = uploadResult.getOrElse(() => {});
      final frontUrl = urls['frontUrl'] ?? '';
      final backUrl = urls['backUrl'] ?? '';

      final uploadResultData = IdentityUploadResult(
        documentType: documentType,
        frontImage: frontUrl,
        backImage: backUrl,
        uploadedAt: DateTime.now(),
      );

      // Cập nhật flow data với kết quả upload
      final currentData = state.data!;
      final updatedData = currentData.copyWith(
        identityUploadResult: uploadResultData,
        currentStep: CTVRegistrationStep.qrScan, // Chuyển đến bước QR scan
      );

      AppLogger.info('Identity document upload completed for CTV registration',
          data: {
            'documentType': documentType.label,
            'frontUrl': frontUrl,
            'backUrl': backUrl,
            'nextStep': CTVRegistrationStep.qrScan,
          });

      return Right(updatedData);
    } catch (e) {
      AppLogger.error('Error uploading identity documents for CTV registration',
          error: e);
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  /// Retry upload với data cuối cùng
  Future<void> retryUpload({
    required DocumentType documentType,
    required File frontFile,
    required File backFile,
  }) async {
    if (state.canRetry) {
      await uploadIdentityDocuments(
        documentType: documentType,
        frontFile: frontFile,
        backFile: backFile,
      );
    }
  }

  // ==================== QR SCAN METHODS ====================

  /// Bắt đầu quét QR (set loading)
  void startQRScan() {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Starting QR scan');

    final updatedData = currentData.copyWith(isLoadingQRScan: true);
    state = BaseState.success(updatedData);
  }

  /// Kết thúc quét QR (reset loading)
  void stopQRScan() {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Stopping QR scan');

    final updatedData = currentData.copyWith(isLoadingQRScan: false);
    state = BaseState.success(updatedData);
  }

  /// Cập nhật thông tin CCCD từ QR scan hoặc chỉnh sửa
  void updateCCCDInfo(CCCDInfoEntity cccdInfo) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating CCCD info from QR scan', data: {
      'fullName': cccdInfo.fullName,
      'idNumber': cccdInfo.idNumber,
    });

    // Đồng bộ các trường sang personalInfoConfirmation nếu khác
    final oldPersonal = currentData.personalInfoConfirmation ?? const PersonalInfoConfirmationData();
    final updatedPersonal = oldPersonal.copyWith(
      fullName: cccdInfo.fullName,
      documentNumber: cccdInfo.idNumber,
      address: cccdInfo.address,
    );

    final updatedData = currentData.copyWith(
      cccdInfo: cccdInfo,
      personalInfoConfirmation: updatedPersonal,
      isLoadingQRScan: false, // Reset loading sau khi thành công
      currentStep: CTVRegistrationStep.personalInfoConfirmation, // Chuyển đến bước xác nhận thông tin
    );
    state = BaseState.success(updatedData);
  }

  /// Clear thông tin CCCD (khi back về màn hình upload)
  void clearCCCDInfo() {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Clearing CCCD info when going back to upload screen');

    final updatedData = currentData.copyWith(
      clearCCCDInfo: true,
      clearPersonalInfoConfirmation: true, // Clear luôn personal info
      isLoadingQRScan: false,
    );
    state = BaseState.success(updatedData);
  }

  /// Skip QR scan (chuyển thẳng đến bước xác nhận thông tin)
  void skipQRScan() {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Skipping QR scan, moving to personal info confirmation');

    final updatedData = currentData.copyWith(
      isLoadingQRScan: false, // Reset loading khi skip
      currentStep: CTVRegistrationStep.personalInfoConfirmation,
    );
    state = BaseState.success(updatedData);
  }

  // ==================== PERSONAL INFO METHODS ====================

  /// Cập nhật thông tin cá nhân
  void updatePersonalInfo(PersonalInfoConfirmationData personalInfo) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating personal info', data: {
      'fullName': personalInfo.fullName,
      'phoneNumber': personalInfo.phoneNumber,
      'isValid': personalInfo.isPersonalInfoValid,
    });

    final updatedData = currentData.copyWith(
      personalInfoConfirmation: personalInfo,
    );
    state = BaseState.success(updatedData);
  }

  /// Submit đăng ký CTV
  Future<void> submitCTVRegistration() async {
    final currentData = state.data;
    if (currentData == null) return;

    if (!_canSubmit(currentData)) {
      AppLogger.warning('Cannot submit CTV registration - validation failed');
      return;
    }

    AppLogger.info('Submitting CTV registration', data: {
      'fullName': currentData.personalInfoConfirmation?.fullName,
      'phoneNumber': currentData.personalInfoConfirmation?.phoneNumber,
    });

    await submitData(
      currentData,
      (data) => _submitRegistration(data),
    );
  }

  /// Submit giới thiệu CTV (có gửi email thông báo)
  Future<void> submitCTVReferral() async {
    final currentData = state.data;
    if (currentData == null) return;

    if (!_canSubmit(currentData)) {
      AppLogger.warning('Cannot submit CTV referral - validation failed');
      return;
    }

    AppLogger.info('Submitting CTV referral', data: {
      'fullName': currentData.personalInfoConfirmation?.fullName,
      'phoneNumber': currentData.personalInfoConfirmation?.phoneNumber,
    });

    await submitData(
      currentData,
      (data) => _submitReferral(data),
    );
  }

  /// Submit registration data
  Future<Either<Failure, CTVRegistrationFlowData>> _submitRegistration(
    CTVRegistrationFlowData data,
  ) async {
    // Chuẩn bị data cho API call
    final fullName = data.personalInfoConfirmation?.fullName ??
        data.cccdInfo?.fullName ??
        '';
    final idCardType = data.identityUploadResult?.documentType;
    final idCardNo = data.personalInfoConfirmation?.documentNumber?.isNotEmpty == true
        ? data.personalInfoConfirmation!.documentNumber!
        : data.cccdInfo?.idNumber ?? '';
    final phoneNumber = data.personalInfoConfirmation?.phoneNumber ?? '';
    final permanentAddress =
        data.personalInfoConfirmation?.address ?? data.cccdInfo?.address ?? '';
    final provinceId = data.selectedProvince?.id ?? '';
    final branchId = data.selectedBranch?.id ?? '';
    final frontCardUrl = data.identityUploadResult?.frontImage ?? '';
    final backCardUrl = data.identityUploadResult?.backImage ?? '';
    final referrerCode = data.selectedReferrer?.referrerCode;
    final email = data.personalInfoConfirmation?.email;
    AppLogger.info('Calling CTV registration API', data: {
      'fullName': fullName,
      'idCardType': idCardType?.name,
      'phoneNumber': phoneNumber,
      'provinceId': provinceId,
      'branchId': branchId,
      'referrerCode': referrerCode,
    });

    // Call API đăng ký CTV
    final result = await _ctvRepository.registerCTV(
      fullName: fullName,
      documentType: idCardType,
      idCardNo: idCardNo,
      phoneNumber: phoneNumber,
      permanentAddress: permanentAddress,
      provinceId: provinceId,
      branchId: branchId,
      frontCardUrl: frontCardUrl,
      backCardUrl: backCardUrl,
      referrerCode: referrerCode,
      email: email,
    );

    // Return result từ repository và handle success case
    return result.fold(
      (failure) => Left(failure),
      (registrationResult) {
        AppLogger.info('CTV registration successful', data: {
          'fullName': registrationResult.fullName,
          'branchName': registrationResult.branchName,
          'positionName': registrationResult.positionName,
        });

        // Chuyển đến bước thành công
        final updatedData = data.copyWith(
          currentStep: CTVRegistrationStep.success,
        );
        return Right(updatedData);
      },
    );
  }

  /// Submit referral data (với email thông báo)
  Future<Either<Failure, CTVRegistrationFlowData>> _submitReferral(
    CTVRegistrationFlowData data,
  ) async {
    // Chuẩn bị data cho API call (tương tự registration)
    final fullName = data.personalInfoConfirmation?.fullName ??
        data.cccdInfo?.fullName ??
        '';
    final idCardType = data.identityUploadResult?.documentType;
    final idCardNo = data.personalInfoConfirmation?.documentNumber?.isNotEmpty == true
        ? data.personalInfoConfirmation!.documentNumber!
        : data.cccdInfo?.idNumber ?? '';
    final phoneNumber = data.personalInfoConfirmation?.phoneNumber ?? '';
    final permanentAddress =
        data.personalInfoConfirmation?.address ?? data.cccdInfo?.address ?? '';
    final provinceId = data.selectedProvince?.id ?? '';
    final branchId = data.selectedBranch?.id ?? '';
    final frontCardUrl = data.identityUploadResult?.frontImage ?? '';
    final backCardUrl = data.identityUploadResult?.backImage ?? '';
    final referrerCode = data.selectedReferrer?.referrerCode;
    final email = data.personalInfoConfirmation?.email;

    AppLogger.info('Calling CTV referral API', data: {
      'fullName': fullName,
      'idCardType': idCardType?.name,
      'phoneNumber': phoneNumber,
      'provinceId': provinceId,
      'branchId': branchId,
      'referrerCode': referrerCode,
    });

    // Call API giới thiệu CTV
    final result = await _ctvRepository.referCTV(
      fullName: fullName,
      documentType: idCardType,
      idCardNo: idCardNo,
      phoneNumber: phoneNumber,
      permanentAddress: permanentAddress,
      provinceId: provinceId,
      branchId: branchId,
      frontCardUrl: frontCardUrl,
      backCardUrl: backCardUrl,
      email: email,
    );

    // Return result từ repository và handle success case
    return result.fold(
      (failure) => Left(failure),
      (referralResult) {
        AppLogger.info('CTV referral successful', data: {
          'fullName': referralResult.fullName,
          'branchName': referralResult.branchName,
          'positionName': referralResult.positionName,
          'referrerCode': referralResult.referrerCode,
          'referrerName': referralResult.referrerName,
        });

        // Chuyển đến bước thành công
        final updatedData = data.copyWith(
          currentStep: CTVRegistrationStep.success,
        );
        return Right(updatedData);
      },
    );
  }

  // ==================== REFERRER METHODS ====================

  /// Load thông tin người giới thiệu theo mã
  Future<void> loadReferrerByCode(String referrerCode) async {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Loading referrer by code: $referrerCode');

    // Set loading state cho referrer
    final loadingData = currentData.copyWith(isLoadingReferrer: true);
    state = BaseState.success(loadingData);

    try {
      final result = await _ctvRepository.getReferrerByCode(referrerCode);

      result.fold(
        (failure) {
          // Error loading referrer - reset loading state và clear referrer cũ
          final errorData = currentData.copyWith(
            isLoadingReferrer: false,
            clearSelectedReferrer: true, // Clear referrer cũ khi có lỗi
          );
          
          // Set error state để BaseStateErrorHandler có thể bắt và hiển thị lỗi
          state = BaseState.error(failure, previousData: errorData);

          AppLogger.error('Failed to load referrer', error: failure, data: {
            'referrerCode': referrerCode,
            'failureType': failure.runtimeType.toString(),
          });
        },
        (referrer) {
          // Success - update referrer và reset loading state
          final successData = currentData.copyWith(
            selectedReferrer: referrer,
            isLoadingReferrer: false,
          );
          state = BaseState.success(successData);

          AppLogger.info(
              'Successfully loaded referrer: ${referrer.referrerName}');
        },
      );
    } catch (e) {
      // Exception - reset loading state và clear referrer cũ
      final errorData = currentData.copyWith(
        isLoadingReferrer: false,
        clearSelectedReferrer: true, // Clear referrer cũ khi có exception
      );
      
      // Tạo ServerFailure từ exception và set error state
      final failure = ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      );
      state = BaseState.error(failure, previousData: errorData);

      AppLogger.error('Exception loading referrer', error: e, data: {
        'referrerCode': referrerCode,
      });
    }
  }

  /// Xoá referrer đã chọn (nếu cần clear input)
  void clearReferrer() {
    final currentData = state.data;
    if (currentData == null) return;
    final updatedData = currentData.clearReferrer();
    state = BaseState.success(updatedData);
    
    AppLogger.info('Referrer cleared successfully');
  }

  // ==================== HELPER METHODS ====================

  /// Kiểm tra có thể submit không
  bool _canSubmit(CTVRegistrationFlowData data) {
    // Kiểm tra đã upload giấy tờ
    if (data.identityUploadResult == null) {
      return false;
    }

    // Kiểm tra đã có thông tin CCCD
    // if (data.cccdInfo == null) {
    //   print("----------------2222222");
    //
    //   return false;
    // }

    // Kiểm tra đã nhập số điện thoại
    if (data.personalInfoConfirmation?.phoneNumber?.trim().isEmpty ?? true) {
      return false;
    }

    // Kiểm tra đã chọn tỉnh và chi nhánh (chỉ cho luồng đăng ký, không cần cho giới thiệu)
    // TODO: Cần thêm tham số isCTVFlow để phân biệt
    // if (data.selectedProvince == null || data.selectedBranch == null) {
    //   return false;
    // }

    return true;
  }

  /// Lấy bước tiếp theo
  CTVRegistrationStep _getNextStep(CTVRegistrationStep currentStep) {
    switch (currentStep) {
      case CTVRegistrationStep.documentUpload:
        return CTVRegistrationStep.qrScan;
      case CTVRegistrationStep.qrScan:
        return CTVRegistrationStep.personalInfoConfirmation;
      case CTVRegistrationStep.personalInfoConfirmation:
        return CTVRegistrationStep.success;
      case CTVRegistrationStep.success:
        return CTVRegistrationStep.success;
    }
  }

  /// Lấy bước trước đó
  CTVRegistrationStep _getPreviousStep(CTVRegistrationStep currentStep) {
    switch (currentStep) {
      case CTVRegistrationStep.documentUpload:
        return CTVRegistrationStep.documentUpload;
      case CTVRegistrationStep.qrScan:
        return CTVRegistrationStep.documentUpload;
      case CTVRegistrationStep.personalInfoConfirmation:
        return CTVRegistrationStep.qrScan;
      case CTVRegistrationStep.success:
        return CTVRegistrationStep.personalInfoConfirmation;
    }
  }

  /// Reset data của step hiện tại khi quay lại bước trước
  CTVRegistrationFlowData _resetCurrentStepData(
    CTVRegistrationFlowData currentData,
    CTVRegistrationStep targetStep,
  ) {
    switch (currentData.currentStep) {
      case CTVRegistrationStep.documentUpload:
        // Không cần reset gì khi đang ở bước đầu
        return currentData.copyWith(currentStep: targetStep);
        
      case CTVRegistrationStep.qrScan:
        // Reset QR scan data khi quay lại document upload
        AppLogger.info('Resetting QR scan data (CCCD info)');
        return currentData.copyWith(
          currentStep: targetStep,
          cccdInfo: null, // Reset CCCD info
          isLoadingQRScan: false, // Reset loading state
        );
        
      case CTVRegistrationStep.personalInfoConfirmation:
        // Reset personal info data khi quay lại QR scan
        AppLogger.info('Resetting personal info data');
        return currentData.copyWith(
          currentStep: targetStep,
          personalInfoConfirmation: null, // Reset personal info
          clearSelectedProvince: true, // Reset province selection
          clearSelectedBranch: true, // Reset branch selection
          branches: const [], // Clear branches
          clearSelectedReferrer: true, // Reset referrer
          isLoadingBranches: false, // Reset loading states
          isLoadingReferrer: false,
        );
        
      case CTVRegistrationStep.success:
        // Reset success data khi quay lại personal info confirmation
        AppLogger.info('Resetting success data');
        return currentData.copyWith(
          currentStep: targetStep,
          // Không reset personal info vì đang quay lại bước đó
        );
    }
  }

  // ==================== GETTERS ====================

  /// Get kết quả upload
  IdentityUploadResult? get uploadResult => state.data?.identityUploadResult;

  /// Get thông tin CCCD
  CCCDInfoEntity? get cccdInfo => state.data?.cccdInfo;

  /// Get thông tin cá nhân
  PersonalInfoConfirmationData? get personalInfo =>
      state.data?.personalInfoConfirmation;

  /// Get bước hiện tại
  CTVRegistrationStep? get currentStep => state.data?.currentStep;

  /// Get provinces
  List<ProvinceEntity> get provinces => state.data?.provinces ?? [];

  /// Get branches
  List<BranchEntity> get branches => state.data?.branches ?? [];

  /// Get selected province
  ProvinceEntity? get selectedProvince => state.data?.selectedProvince;

  /// Get selected branch
  BranchEntity? get selectedBranch => state.data?.selectedBranch;

  /// Get selected referrer
  ReferrerEntity? get selectedReferrer => state.data?.selectedReferrer;

  /// Check upload thành công
  bool get isUploadSuccess => uploadResult != null;

  /// Check có lỗi
  bool get hasError => state.isError;

  /// Get error message
  String get errorMessage {
    if (state.failure == null) return '';
    return state.failure.toString();
  }

  /// Get URLs của ảnh đã upload
  String? get frontImageUrl => uploadResult?.frontImage;
  String? get backImageUrl => uploadResult?.backImage;

  /// Check đang loading
  bool get isLoading => state.isLoading;

  /// Check đang loading branches
  bool get isLoadingBranches => state.data?.isLoadingBranches ?? false;

  /// Check đang loading referrer
  bool get isLoadingReferrer => state.data?.isLoadingReferrer ?? false;

  /// Check đang loading QR scan
  bool get isLoadingQRScan => state.data?.isLoadingQRScan ?? false;

  /// Reset về trạng thái ban đầu
  @override
  void reset() {
    const initialData = CTVRegistrationFlowData(
      currentStep: CTVRegistrationStep.documentUpload,
      personalInfoConfirmation: PersonalInfoConfirmationData(),
    );
    state = const BaseState.success(initialData);
  }
}
