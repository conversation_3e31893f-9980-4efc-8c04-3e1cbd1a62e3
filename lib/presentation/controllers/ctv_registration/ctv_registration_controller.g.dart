// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ctv_registration_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cTVRegistrationControllerHash() =>
    r'290622d027abe88924b8af536fd8ed9f0d6948ca';

/// Controller quản lý toàn bộ flow đăng ký CTV
///
/// Flow: identity_upload_page.dart → qr_scan_screen.dart → personal_info_confirmation_screen.dart
///
/// Copied from [CTVRegistrationController].
@ProviderFor(CTVRegistrationController)
final cTVRegistrationControllerProvider = AutoDisposeNotifierProvider<
    CTVRegistrationController, BaseState<CTVRegistrationFlowData>>.internal(
  CTVRegistrationController.new,
  name: r'cTVRegistrationControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cTVRegistrationControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CTVRegistrationController
    = AutoDisposeNotifier<BaseState<CTVRegistrationFlowData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
