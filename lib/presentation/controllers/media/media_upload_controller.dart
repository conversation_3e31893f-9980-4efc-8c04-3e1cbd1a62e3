import 'dart:io';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:get_it/get_it.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';

part 'media_upload_controller.g.dart';

/// Controller sử dụng AutoDisposeBaseStateMixin cho @riverpod
/// 
/// Cách sử dụng 2 mixin:
/// 1. BaseStateMixin<T> - cho Notifier thường (non-auto-dispose)
/// 2. AutoDisposeBaseStateMixin<T> - cho AutoDisposeNotifier (@riverpod)
/// 
/// Ví dụ:
/// ```dart
/// // Non-auto-dispose với BaseStateMixin
/// class UserController extends Notifier<BaseState<User>> with BaseStateMixin<User> {
///   @override
///   BaseState<User> build() => const BaseState.initial();
///   
///   @override
///   NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();
/// }
/// 
/// // Auto-dispose với AutoDisposeBaseStateMixin  
/// @riverpod
/// class MediaUploadController extends _$MediaUploadController with AutoDisposeBaseStateMixin<MediaUploadResponse> {
///   @override
///   BaseState<MediaUploadResponse> build() => const BaseState.initial();
///   
///   @override
///   NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();
/// }
/// ```
@riverpod
class MediaUploadController extends _$MediaUploadController with AutoDisposeBaseStateMixin<String> {
  late final MediaRepository _mediaRepository;

  @override
  BaseState<String> build() {
    _mediaRepository = GetIt.I.get<MediaRepository>();
    return const BaseState.initial();
  }

  @override
  NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();

  /// Upload document file
  Future<void> uploadDocument(File file) async {
    // Resize ảnh trước khi upload
    final resizedFile = await resizeImage(file, maxWidth: 1024);
    await submitData(
      resizedFile,
      (File file) => _mediaRepository.uploadFile(
        file: file,
        fileType: FileType.idCard, // Sử dụng IDCARD cho document
      ),
    );
  }

  /// Upload multiple document files
  Future<void> uploadMultipleDocuments(List<File> files) async {
    if (state.isProcessing) return;

    state = const BaseState.loading();

    try {
      // Resize tất cả ảnh trước khi upload
      final resizedFiles = <File>[];
      for (final file in files) {
        final resized = await resizeImage(file, maxWidth: 1024);
        resizedFiles.add(resized);
      }
      final result = await _mediaRepository.uploadMultipleFiles(
        files: resizedFiles,
        fileType: FileType.idCard, // Sử dụng IDCARD cho document
      );

      result.fold(
        (failure) => state = BaseState.error(failure),
        (uploadResponses) {
          // Lấy response đầu tiên làm kết quả chính
          final firstResponse = uploadResponses.isNotEmpty 
              ? uploadResponses.first 
              : null;
          
          if (firstResponse != null) {
            state = BaseState.success(firstResponse);
          } else {
            state = const BaseState.error(
              ServerFailure(
                ServerErrorType.unknown,
                serverMessage: 'Không có file nào được upload thành công',
              ),
            );
          }
        },
      );
    } catch (e) {
      AppLogger.error('Unexpected error in uploadMultipleDocuments', error: e);
      state = BaseState.error(
        ServerFailure(
          ServerErrorType.unknown,
          serverMessage: 'Lỗi không xác định: ${e.toString()}',
        ),
      );
    }
  }

  /// Retry upload with last file
  Future<void> retryUpload(File file) async {
    if (state.canRetry) {
      await uploadDocument(file);
    }
  }

  /// Reset to initial state
  @override
  void reset() {
    state = const BaseState.initial();
  }

  /// Get current upload response
  String? get uploadResponse => state.data;

  /// Check if upload was successful
  bool get isSuccess => state.isSuccess;

  /// Check if there's an error
  bool get hasError => state.isError;

  /// Get file URL from upload response
  String? get fileUrl => uploadResponse;

  /// Get private file URL from upload response (not available with upload method)
  String? get privateFileUrl => null;

  /// Get file name from upload response (not available with upload method)
  String? get fileName => null;

  /// Get error message
  String get errorMessage {
    if (state.failure == null) return '';
    return state.failure.toString();
  }
} 