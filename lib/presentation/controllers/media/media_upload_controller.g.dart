// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_upload_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mediaUploadControllerHash() =>
    r'ea0545d21b35b24a3e9635ef1b0113f35f91518f';

/// Controller sử dụng AutoDisposeBaseStateMixin cho @riverpod
///
/// Cách sử dụng 2 mixin:
/// 1. BaseStateMixin<T> - cho Notifier thường (non-auto-dispose)
/// 2. AutoDisposeBaseStateMixin<T> - cho AutoDisposeNotifier (@riverpod)
///
/// Ví dụ:
/// ```dart
/// // Non-auto-dispose với BaseStateMixin
/// class UserController extends Notifier<BaseState<User>> with BaseStateMixin<User> {
///   @override
///   BaseState<User> build() => const BaseState.initial();
///
///   @override
///   NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();
/// }
///
/// // Auto-dispose với AutoDisposeBaseStateMixin
/// @riverpod
/// class MediaUploadController extends _$MediaUploadController with AutoDisposeBaseStateMixin<MediaUploadResponse> {
///   @override
///   BaseState<MediaUploadResponse> build() => const BaseState.initial();
///
///   @override
///   NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();
/// }
/// ```
///
/// Copied from [MediaUploadController].
@ProviderFor(MediaUploadController)
final mediaUploadControllerProvider = AutoDisposeNotifierProvider<
    MediaUploadController, BaseState<String>>.internal(
  MediaUploadController.new,
  name: r'mediaUploadControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mediaUploadControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MediaUploadController = AutoDisposeNotifier<BaseState<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
