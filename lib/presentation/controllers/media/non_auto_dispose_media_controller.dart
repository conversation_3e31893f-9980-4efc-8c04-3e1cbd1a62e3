import 'dart:io';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:get_it/get_it.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';

/// Controller này sẽ không tự động dispose khi không có listener
/// Phù hợp cho global state hoặc state cần persist
class NonAutoDisposeMediaController extends Notifier<BaseState<String>>
    with BaseStateMixin<String> {
  late final MediaRepository _mediaRepository;

  @override
  BaseState<String> build() {
    _mediaRepository = GetIt.I.get<MediaRepository>();
    return const BaseState.initial();
  }

  @override
  NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();

  /// Upload document file
  Future<void> uploadDocument(String filePath) async {
    // Resize file trước khi upload
    final file = File(filePath);
    final resizedFile = await resizeImage(file, maxWidth: 1024);
    await submitData(
      resizedFile.path,
      (String path) => _mediaRepository.uploadFile(
        file: File(path),
        fileType: FileType.idCard, // Sử dụng IDCARD cho document
      ),
    );
  }

  /// Reset to initial state
  @override
  void reset() {
    state = const BaseState.initial();
  }

  /// Get current upload response (previewUrl)
  String? get uploadResponse => state.data;

  /// Check if upload was successful
  bool get isSuccess => state.isSuccess;

  /// Check if there's an error
  bool get hasError => state.isError;

  /// Get file URL from upload response
  String? get fileUrl => uploadResponse;

  /// Get error message
  String get errorMessage {
    if (state.failure == null) return '';
    return state.failure.toString();
  }
}

/// Manual provider registration cho non-auto-dispose controller
/// 
/// Sử dụng NotifierProvider thay vì AutoDisposeNotifierProvider
/// Controller sẽ không tự động dispose khi không có listener
final nonAutoDisposeMediaControllerProvider = NotifierProvider<NonAutoDisposeMediaController, BaseState<String>>(
  () => NonAutoDisposeMediaController(),
); 