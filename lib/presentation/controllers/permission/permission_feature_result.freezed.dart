// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'permission_feature_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PermissionFeatureResult {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() granted,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        denied,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        permanentlyDenied,
    required TResult Function(String message, String feature) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? granted,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult? Function(String message, String feature)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? granted,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult Function(String message, String feature)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Granted value) granted,
    required TResult Function(_Denied value) denied,
    required TResult Function(_PermanentlyDenied value) permanentlyDenied,
    required TResult Function(_Error value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Granted value)? granted,
    TResult? Function(_Denied value)? denied,
    TResult? Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult? Function(_Error value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Granted value)? granted,
    TResult Function(_Denied value)? denied,
    TResult Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PermissionFeatureResultCopyWith<$Res> {
  factory $PermissionFeatureResultCopyWith(PermissionFeatureResult value,
          $Res Function(PermissionFeatureResult) then) =
      _$PermissionFeatureResultCopyWithImpl<$Res, PermissionFeatureResult>;
}

/// @nodoc
class _$PermissionFeatureResultCopyWithImpl<$Res,
        $Val extends PermissionFeatureResult>
    implements $PermissionFeatureResultCopyWith<$Res> {
  _$PermissionFeatureResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$GrantedImplCopyWith<$Res> {
  factory _$$GrantedImplCopyWith(
          _$GrantedImpl value, $Res Function(_$GrantedImpl) then) =
      __$$GrantedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GrantedImplCopyWithImpl<$Res>
    extends _$PermissionFeatureResultCopyWithImpl<$Res, _$GrantedImpl>
    implements _$$GrantedImplCopyWith<$Res> {
  __$$GrantedImplCopyWithImpl(
      _$GrantedImpl _value, $Res Function(_$GrantedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GrantedImpl implements _Granted {
  const _$GrantedImpl();

  @override
  String toString() {
    return 'PermissionFeatureResult.granted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$GrantedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() granted,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        denied,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        permanentlyDenied,
    required TResult Function(String message, String feature) error,
  }) {
    return granted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? granted,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult? Function(String message, String feature)? error,
  }) {
    return granted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? granted,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult Function(String message, String feature)? error,
    required TResult orElse(),
  }) {
    if (granted != null) {
      return granted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Granted value) granted,
    required TResult Function(_Denied value) denied,
    required TResult Function(_PermanentlyDenied value) permanentlyDenied,
    required TResult Function(_Error value) error,
  }) {
    return granted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Granted value)? granted,
    TResult? Function(_Denied value)? denied,
    TResult? Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult? Function(_Error value)? error,
  }) {
    return granted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Granted value)? granted,
    TResult Function(_Denied value)? denied,
    TResult Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (granted != null) {
      return granted(this);
    }
    return orElse();
  }
}

abstract class _Granted implements PermissionFeatureResult {
  const factory _Granted() = _$GrantedImpl;
}

/// @nodoc
abstract class _$$DeniedImplCopyWith<$Res> {
  factory _$$DeniedImplCopyWith(
          _$DeniedImpl value, $Res Function(_$DeniedImpl) then) =
      __$$DeniedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Map<PermissionType, PermissionStatus> permissions, String feature});
}

/// @nodoc
class __$$DeniedImplCopyWithImpl<$Res>
    extends _$PermissionFeatureResultCopyWithImpl<$Res, _$DeniedImpl>
    implements _$$DeniedImplCopyWith<$Res> {
  __$$DeniedImplCopyWithImpl(
      _$DeniedImpl _value, $Res Function(_$DeniedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? permissions = null,
    Object? feature = null,
  }) {
    return _then(_$DeniedImpl(
      permissions: null == permissions
          ? _value._permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as Map<PermissionType, PermissionStatus>,
      feature: null == feature
          ? _value.feature
          : feature // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeniedImpl implements _Denied {
  const _$DeniedImpl(
      {required final Map<PermissionType, PermissionStatus> permissions,
      required this.feature})
      : _permissions = permissions;

  final Map<PermissionType, PermissionStatus> _permissions;
  @override
  Map<PermissionType, PermissionStatus> get permissions {
    if (_permissions is EqualUnmodifiableMapView) return _permissions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_permissions);
  }

  @override
  final String feature;

  @override
  String toString() {
    return 'PermissionFeatureResult.denied(permissions: $permissions, feature: $feature)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeniedImpl &&
            const DeepCollectionEquality()
                .equals(other._permissions, _permissions) &&
            (identical(other.feature, feature) || other.feature == feature));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_permissions), feature);

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeniedImplCopyWith<_$DeniedImpl> get copyWith =>
      __$$DeniedImplCopyWithImpl<_$DeniedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() granted,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        denied,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        permanentlyDenied,
    required TResult Function(String message, String feature) error,
  }) {
    return denied(permissions, feature);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? granted,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult? Function(String message, String feature)? error,
  }) {
    return denied?.call(permissions, feature);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? granted,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult Function(String message, String feature)? error,
    required TResult orElse(),
  }) {
    if (denied != null) {
      return denied(permissions, feature);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Granted value) granted,
    required TResult Function(_Denied value) denied,
    required TResult Function(_PermanentlyDenied value) permanentlyDenied,
    required TResult Function(_Error value) error,
  }) {
    return denied(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Granted value)? granted,
    TResult? Function(_Denied value)? denied,
    TResult? Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult? Function(_Error value)? error,
  }) {
    return denied?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Granted value)? granted,
    TResult Function(_Denied value)? denied,
    TResult Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (denied != null) {
      return denied(this);
    }
    return orElse();
  }
}

abstract class _Denied implements PermissionFeatureResult {
  const factory _Denied(
      {required final Map<PermissionType, PermissionStatus> permissions,
      required final String feature}) = _$DeniedImpl;

  Map<PermissionType, PermissionStatus> get permissions;
  String get feature;

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeniedImplCopyWith<_$DeniedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PermanentlyDeniedImplCopyWith<$Res> {
  factory _$$PermanentlyDeniedImplCopyWith(_$PermanentlyDeniedImpl value,
          $Res Function(_$PermanentlyDeniedImpl) then) =
      __$$PermanentlyDeniedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Map<PermissionType, PermissionStatus> permissions, String feature});
}

/// @nodoc
class __$$PermanentlyDeniedImplCopyWithImpl<$Res>
    extends _$PermissionFeatureResultCopyWithImpl<$Res, _$PermanentlyDeniedImpl>
    implements _$$PermanentlyDeniedImplCopyWith<$Res> {
  __$$PermanentlyDeniedImplCopyWithImpl(_$PermanentlyDeniedImpl _value,
      $Res Function(_$PermanentlyDeniedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? permissions = null,
    Object? feature = null,
  }) {
    return _then(_$PermanentlyDeniedImpl(
      permissions: null == permissions
          ? _value._permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as Map<PermissionType, PermissionStatus>,
      feature: null == feature
          ? _value.feature
          : feature // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PermanentlyDeniedImpl implements _PermanentlyDenied {
  const _$PermanentlyDeniedImpl(
      {required final Map<PermissionType, PermissionStatus> permissions,
      required this.feature})
      : _permissions = permissions;

  final Map<PermissionType, PermissionStatus> _permissions;
  @override
  Map<PermissionType, PermissionStatus> get permissions {
    if (_permissions is EqualUnmodifiableMapView) return _permissions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_permissions);
  }

  @override
  final String feature;

  @override
  String toString() {
    return 'PermissionFeatureResult.permanentlyDenied(permissions: $permissions, feature: $feature)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PermanentlyDeniedImpl &&
            const DeepCollectionEquality()
                .equals(other._permissions, _permissions) &&
            (identical(other.feature, feature) || other.feature == feature));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_permissions), feature);

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PermanentlyDeniedImplCopyWith<_$PermanentlyDeniedImpl> get copyWith =>
      __$$PermanentlyDeniedImplCopyWithImpl<_$PermanentlyDeniedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() granted,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        denied,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        permanentlyDenied,
    required TResult Function(String message, String feature) error,
  }) {
    return permanentlyDenied(permissions, feature);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? granted,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult? Function(String message, String feature)? error,
  }) {
    return permanentlyDenied?.call(permissions, feature);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? granted,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult Function(String message, String feature)? error,
    required TResult orElse(),
  }) {
    if (permanentlyDenied != null) {
      return permanentlyDenied(permissions, feature);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Granted value) granted,
    required TResult Function(_Denied value) denied,
    required TResult Function(_PermanentlyDenied value) permanentlyDenied,
    required TResult Function(_Error value) error,
  }) {
    return permanentlyDenied(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Granted value)? granted,
    TResult? Function(_Denied value)? denied,
    TResult? Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult? Function(_Error value)? error,
  }) {
    return permanentlyDenied?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Granted value)? granted,
    TResult Function(_Denied value)? denied,
    TResult Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (permanentlyDenied != null) {
      return permanentlyDenied(this);
    }
    return orElse();
  }
}

abstract class _PermanentlyDenied implements PermissionFeatureResult {
  const factory _PermanentlyDenied(
      {required final Map<PermissionType, PermissionStatus> permissions,
      required final String feature}) = _$PermanentlyDeniedImpl;

  Map<PermissionType, PermissionStatus> get permissions;
  String get feature;

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PermanentlyDeniedImplCopyWith<_$PermanentlyDeniedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, String feature});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$PermissionFeatureResultCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? feature = null,
  }) {
    return _then(_$ErrorImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      feature: null == feature
          ? _value.feature
          : feature // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl({required this.message, required this.feature});

  @override
  final String message;
  @override
  final String feature;

  @override
  String toString() {
    return 'PermissionFeatureResult.error(message: $message, feature: $feature)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.feature, feature) || other.feature == feature));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, feature);

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() granted,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        denied,
    required TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)
        permanentlyDenied,
    required TResult Function(String message, String feature) error,
  }) {
    return error(message, feature);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? granted,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult? Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult? Function(String message, String feature)? error,
  }) {
    return error?.call(message, feature);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? granted,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        denied,
    TResult Function(
            Map<PermissionType, PermissionStatus> permissions, String feature)?
        permanentlyDenied,
    TResult Function(String message, String feature)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message, feature);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Granted value) granted,
    required TResult Function(_Denied value) denied,
    required TResult Function(_PermanentlyDenied value) permanentlyDenied,
    required TResult Function(_Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Granted value)? granted,
    TResult? Function(_Denied value)? denied,
    TResult? Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult? Function(_Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Granted value)? granted,
    TResult Function(_Denied value)? denied,
    TResult Function(_PermanentlyDenied value)? permanentlyDenied,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements PermissionFeatureResult {
  const factory _Error(
      {required final String message,
      required final String feature}) = _$ErrorImpl;

  String get message;
  String get feature;

  /// Create a copy of PermissionFeatureResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
