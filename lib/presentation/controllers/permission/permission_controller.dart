import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';
// DI barrel export
import 'package:sales_app/di/injection.dart';
// Permission state
import 'permission_state.dart';
// Permission feature result
import 'permission_feature_result.dart';

part 'permission_controller.g.dart';

@riverpod
class PermissionController extends _$PermissionController {
  late final PermissionService _permissionService;

  @override
  PermissionState build() {
    _permissionService = getIt<PermissionService>();
    return const PermissionState.initial();
  }

  /// Kiểm tra trạng thái của một permission
  Future<void> checkPermission(PermissionType permissionType) async {
    try {
      state = const PermissionState.loading();
      
      AppLogger.info('Checking permission: ${permissionType.key}');
      final status = await _permissionService.checkPermission(permissionType);
      
      state = PermissionState.checked({permissionType: status});
      
      AppLogger.info('Permission check completed: ${permissionType.key} = $status');
    } catch (e) {
      AppLogger.error('Error checking permission: ${permissionType.key}', error: e);
      state = const PermissionState.error('Lỗi kiểm tra quyền truy cập');
    }
  }

  /// Kiểm tra trạng thái của nhiều permission
  Future<void> checkPermissions(List<PermissionType> permissionTypes) async {
    try {
      state = const PermissionState.loading();
      
      AppLogger.info('Checking multiple permissions: ${permissionTypes.map((p) => p.key).join(', ')}');
      final results = await _permissionService.checkPermissions(permissionTypes);
      
      state = PermissionState.checked(results);
      
      AppLogger.info('Multiple permissions check completed: $results');
    } catch (e) {
      AppLogger.error('Error checking multiple permissions', error: e);
      state = const PermissionState.error('Lỗi kiểm tra quyền truy cập');
    }
  }

  /// Yêu cầu một permission
  Future<void> requestPermission(PermissionType permissionType) async {
    try {
      state = const PermissionState.loading();
      
      AppLogger.info('Requesting permission: ${permissionType.key}');
      final status = await _permissionService.requestPermission(permissionType);
      
      state = PermissionState.requested({permissionType: status});
      
      AppLogger.info('Permission request completed: ${permissionType.key} = $status');
    } catch (e) {
      AppLogger.error('Error requesting permission: ${permissionType.key}', error: e);
      state = const PermissionState.error('Lỗi yêu cầu quyền truy cập');
    }
  }

  /// Yêu cầu nhiều permission cùng lúc
  Future<void> requestPermissions(List<PermissionType> permissionTypes) async {
    try {
      state = const PermissionState.loading();
      
      AppLogger.info('Requesting multiple permissions: ${permissionTypes.map((p) => p.key).join(', ')}');
      final results = await _permissionService.requestPermissions(permissionTypes);
      
      state = PermissionState.requested(results);
      
      AppLogger.info('Multiple permissions request completed: $results');
    } catch (e) {
      AppLogger.error('Error requesting multiple permissions', error: e);
      state = const PermissionState.error('Lỗi yêu cầu quyền truy cập');
    }
  }

  /// Kiểm tra và request permission cho một tính năng cụ thể
  Future<void> requestPermissionsForFeature(String feature) async {
    try {
      state = const PermissionState.loading();
      
      AppLogger.info('Requesting permissions for feature: $feature');
      final results = await _permissionService.requestPermissionsForFeature(feature);
      
      state = PermissionState.requested(results);
      
      AppLogger.info('Feature permissions request completed: $results');
    } catch (e) {
      AppLogger.error('Error requesting feature permissions: $feature', error: e);
      state = const PermissionState.error('Lỗi yêu cầu quyền truy cập cho tính năng');
    }
  }

  /// Kiểm tra xem tất cả permission cần thiết cho tính năng có được cấp quyền không
  Future<bool> areFeaturePermissionsGranted(String feature) async {
    try {
      final granted = await _permissionService.areFeaturePermissionsGranted(feature);
      AppLogger.info('Feature permissions granted check: $feature = $granted');
      return granted;
    } catch (e) {
      AppLogger.error('Error checking feature permissions: $feature', error: e);
      return false;
    }
  }

  /// Mở cài đặt app
  Future<void> openSystemAppSettings() async {
    try {
      AppLogger.info('Opening app settings');
      final opened = await _permissionService.openSystemAppSettings();
      
      if (opened) {
        AppLogger.info('App settings opened successfully');
      } else {
        AppLogger.warning('Failed to open app settings');
      }
    } catch (e) {
      AppLogger.error('Error opening app settings', error: e);
    }
  }

  /// Mở cài đặt permission cụ thể
  Future<void> openPermissionSettings(PermissionType permissionType) async {
    try {
      AppLogger.info('Opening permission settings for: ${permissionType.key}');
      final opened = await _permissionService.openPermissionSettings(permissionType);
      
      if (opened) {
        AppLogger.info('Permission settings opened successfully');
      } else {
        AppLogger.warning('Failed to open permission settings');
      }
    } catch (e) {
      AppLogger.error('Error opening permission settings', error: e);
    }
  }

  /// Kiểm tra và request một permission cụ thể
  Future<PermissionFeatureResult> checkAndRequestPermission(PermissionType permissionType) async {
    try {
      AppLogger.info('Checking and requesting permission: ${permissionType.key}');
      
      // Kiểm tra trạng thái hiện tại
      final currentStatus = await _permissionService.checkPermission(permissionType);
      
      // Nếu đã được cấp quyền
      if (currentStatus == PermissionStatus.granted || currentStatus == PermissionStatus.limited) {
        AppLogger.info('Permission already granted: ${permissionType.key}');
        return const PermissionFeatureResult.granted();
      }
      
      // Nếu bị từ chối vĩnh viễn
      if (PermissionUtils.isPermanentlyDenied(currentStatus)) {
        AppLogger.info('Permission permanently denied: ${permissionType.key}');
        return PermissionFeatureResult.permanentlyDenied(
          permissions: {permissionType: currentStatus},
          feature: permissionType.key,
        );
      }
      
      // Request permission
      final requestedStatus = await _permissionService.requestPermission(permissionType);
      
      // Kiểm tra kết quả sau khi request
      if (requestedStatus == PermissionStatus.granted || requestedStatus == PermissionStatus.limited) {
        AppLogger.info('Permission granted after request: ${permissionType.key}');
        return const PermissionFeatureResult.granted();
      } else {
        AppLogger.info('Permission denied after request: ${permissionType.key}');
        return PermissionFeatureResult.denied(
          permissions: {permissionType: requestedStatus},
          feature: permissionType.key,
        );
      }
    } catch (e) {
      AppLogger.error('Error checking and requesting permission: ${permissionType.key}', error: e);
      return PermissionFeatureResult.error(
        message: 'Lỗi kiểm tra và yêu cầu quyền truy cập: ${permissionType.key}',
        feature: permissionType.key,
      );
    }
  }

  /// Kiểm tra và xử lý permission cho một tính năng
  Future<PermissionFeatureResult> handleFeaturePermissions(String feature) async {
    try {
      AppLogger.info('Handling permissions for feature: $feature');
      
      // Kiểm tra xem đã có quyền chưa
      final granted = await areFeaturePermissionsGranted(feature);
      if (granted) {
        AppLogger.info('Feature permissions already granted: $feature');
        return const PermissionFeatureResult.granted();
      }
      
      // Lấy danh sách permission cần thiết
      final requiredPermissions = _permissionService.getRequiredPermissionsForFeature(feature);
      if (requiredPermissions.isEmpty) {
        AppLogger.info('No permissions required for feature: $feature');
        return const PermissionFeatureResult.granted();
      }
      
      // Kiểm tra trạng thái hiện tại
      final currentStatuses = await _permissionService.checkPermissions(requiredPermissions);
      
      // Kiểm tra xem có permission nào bị từ chối vĩnh viễn không
      final permanentlyDenied = currentStatuses.values.any(PermissionUtils.isPermanentlyDenied);
      
      if (permanentlyDenied) {
        // Có permission bị từ chối vĩnh viễn
        AppLogger.info('Feature has permanently denied permissions: $feature');
        return PermissionFeatureResult.permanentlyDenied(
          permissions: currentStatuses,
          feature: feature,
        );
      }
      
      // Request permission
      await requestPermissionsForFeature(feature);
      
      // Kiểm tra lại sau khi request
      final finalGranted = await areFeaturePermissionsGranted(feature);
      AppLogger.info('Final feature permissions status: $feature = $finalGranted');
      
      if (finalGranted) {
        return const PermissionFeatureResult.granted();
      } else {
        return PermissionFeatureResult.denied(
          permissions: currentStatuses,
          feature: feature,
        );
      }
    } catch (e) {
      AppLogger.error('Error handling feature permissions: $feature', error: e);
      return PermissionFeatureResult.error(
        message: 'Lỗi xử lý quyền truy cập cho tính năng: $feature',
        feature: feature,
      );
    }
  }

  /// Reset state về initial
  void reset() {
    state = const PermissionState.initial();
  }
} 