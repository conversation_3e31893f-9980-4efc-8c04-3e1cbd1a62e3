import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:permission_handler/permission_handler.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';

part 'permission_state.freezed.dart';

/// State cho permission controller
@freezed
class PermissionState with _$PermissionState {
  /// Trạng thái ban đầu
  const factory PermissionState.initial() = _Initial;

  /// Đang loading
  const factory PermissionState.loading() = _Loading;

  /// Đã kiểm tra permission
  const factory PermissionState.checked(Map<PermissionType, PermissionStatus> permissions) = _Checked;

  /// Đã request permission
  const factory PermissionState.requested(Map<PermissionType, PermissionStatus> permissions) = _Requested;

  /// Lỗi
  const factory PermissionState.error(String message) = _Error;
} 