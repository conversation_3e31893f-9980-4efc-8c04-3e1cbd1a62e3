import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:permission_handler/permission_handler.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';

part 'permission_feature_result.freezed.dart';

/// Kết quả xử lý permission cho một tính năng
@freezed
class PermissionFeatureResult with _$PermissionFeatureResult {
  /// Permission đã được cấp
  const factory PermissionFeatureResult.granted() = _Granted;

  /// Permission bị từ chối thông thường
  const factory PermissionFeatureResult.denied({
    required Map<PermissionType, PermissionStatus> permissions,
    required String feature,
  }) = _Denied;

  /// Permission bị từ chối vĩnh viễn
  const factory PermissionFeatureResult.permanentlyDenied({
    required Map<PermissionType, PermissionStatus> permissions,
    required String feature,
  }) = _PermanentlyDenied;

  /// Có lỗi xảy ra
  const factory PermissionFeatureResult.error({
    required String message,
    required String feature,
  }) = _Error;
}

/// Extension để dễ dàng kiểm tra kết quả
extension PermissionFeatureResultExtension on PermissionFeatureResult {
  /// Kiểm tra xem permission có được cấp không
  bool get isGranted => this is _Granted;

  /// Kiểm tra xem permission có bị từ chối không
  bool get isDenied => this is _Denied;

  /// Kiểm tra xem permission có bị từ chối vĩnh viễn không
  bool get isPermanentlyDenied => this is _PermanentlyDenied;

  /// Kiểm tra xem có lỗi không
  bool get isError => this is _Error;

  /// Lấy danh sách permissions (nếu có)
  Map<PermissionType, PermissionStatus>? get permissions {
    return when(
      granted: () => null,
      denied: (permissions, feature) => permissions,
      permanentlyDenied: (permissions, feature) => permissions,
      error: (message, feature) => null,
    );
  }

  /// Lấy tên feature
  String? get feature {
    return when(
      granted: () => null,
      denied: (permissions, feature) => feature,
      permanentlyDenied: (permissions, feature) => feature,
      error: (message, feature) => feature,
    );
  }

  /// Lấy message lỗi (nếu có)
  String? get errorMessage {
    return when(
      granted: () => null,
      denied: (permissions, feature) => null,
      permanentlyDenied: (permissions, feature) => null,
      error: (message, feature) => message,
    );
  }
} 