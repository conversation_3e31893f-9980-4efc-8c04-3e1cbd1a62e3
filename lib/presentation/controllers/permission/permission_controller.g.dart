// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'permission_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$permissionControllerHash() =>
    r'5ffc4cfc8c2f34a0438021952338b9f6cfa9fe38';

/// See also [PermissionController].
@ProviderFor(PermissionController)
final permissionControllerProvider =
    AutoDisposeNotifierProvider<PermissionController, PermissionState>.internal(
  PermissionController.new,
  name: r'permissionControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$permissionControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PermissionController = AutoDisposeNotifier<PermissionState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
