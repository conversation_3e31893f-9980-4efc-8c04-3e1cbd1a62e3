import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:get_it/get_it.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';
// Presentation models
import 'package:sales_app/presentation/models/models.dart';

part 'create_loan_controller.g.dart';

/// Controller quản lý toàn bộ flow tạo khoản vay
///
/// Flow: identityDocument → borrowerInfo → coBorrowerDocument → coBorrowerInfo →
/// loanRequest → financialInfo → collateralInfo → collateralDetail → documentList →
/// loanConfirmation → success
@riverpod
class CreateLoanController extends _$CreateLoanController
    with AutoDisposeBaseStateMixin<CreateLoanFlowData> {
  final LocationRepository _locationRepository =
      GetIt.I.get<LocationRepository>();
  final LoanRepository _loanRepository = GetIt.I.get<LoanRepository>();
  final AuthRepository _authRepository = GetIt.I.get<AuthRepository>();

  // State để track QR scanner - đã chuyển vào CreateLoanFlowData
  bool get showQRScanner => state.data?.showQRScanner ?? false;

  @override
  BaseState<CreateLoanFlowData> build() {
    // Khởi tạo với data mặc định
    const initialData = CreateLoanFlowData(
      currentStep: LoanStep.identityDocument,
    );

    return const BaseState.success(initialData);
  }

  @override
  NetworkInfo get networkInfo => GetIt.I.get<NetworkInfo>();

  // ==================== STEP 1: IDENTITY DOCUMENT METHODS ====================

  /// Upload giấy tờ tùy thân, xử lý tác vụ trên UI
  Future<void> uploadIdentityDocuments({
    required DocumentType documentType,
    required File frontFile,
    File? backFile,
  }) async {
    if (state.isProcessing) return;

    AppLogger.info('Starting identity document upload for loan creation',
        data: {
          'documentType': documentType.label,
          'frontFile': frontFile.path,
          'backFile': backFile?.path ?? 'null',
          'isPassport': backFile == null,
        });

    // Validate files exist
    if (!await frontFile.exists()) {
      AppLogger.error('Front file does not exist');
      state = const BaseState.error(
        ValidationFailure(ValidationErrorType.invalidFormat),
      );
      return;
    }

    // Validate back file nếu có (cho CCCD)
    if (backFile != null && !await backFile.exists()) {
      AppLogger.error('Back file does not exist', data: {
        'frontExists': await frontFile.exists(),
        'backExists': await backFile.exists(),
      });
      state = const BaseState.error(
        ValidationFailure(ValidationErrorType.invalidFormat),
      );
      return;
    }

    // Resize ảnh trước khi upload
    final resizedFront = await resizeImage(frontFile, maxWidth: 1024);
    final resizedBack = backFile != null 
        ? await resizeImage(backFile, maxWidth: 1024)
        : null;

    await submitData(
      {
        'documentType': documentType,
        'frontFile': resizedFront,
        'backFile': resizedBack,
      },
      (data) => _uploadIdentityDocuments(
        documentType: data['documentType'] as DocumentType,
        frontFile: data['frontFile'] as File,
        backFile: data['backFile'] as File?,
      ),
    );
  }

  /// Upload giấy tờ tùy thân và cập nhật flow data
  Future<Either<Failure, CreateLoanFlowData>> _uploadIdentityDocuments({
    required DocumentType documentType,
    required File frontFile,
    File? backFile,
  }) async {
    try {
      AppLogger.info('Starting identity document upload for loan creation',
          data: {
            'documentType': documentType.label,
            'frontFile': frontFile.path,
            'backFile': backFile?.path ?? 'null',
            'isPassport': documentType == DocumentType.passport,
          });

      // Gọi API upload thực sự
      final uploadResult = await _loanRepository.uploadIdentityDocuments(
        frontFile: frontFile,
        backFile: backFile,
      );

      return uploadResult.fold(
        (failure) {
          AppLogger.error('Failed to upload identity documents',
              error: failure);
          return Left(failure);
        },
        (uploadUrls) {
          AppLogger.info('Identity documents uploaded successfully',
              data: uploadUrls);

          final step1Data = Step1DocumentData(
            documentType: documentType,
            frontImagePath: frontFile.path, // Local path cho preview
            backImagePath: backFile?.path, // Local path cho preview (có thể null cho passport)
            frontImageUrl: uploadUrls['frontUrl'], // Server URL từ API
            backImageUrl: uploadUrls['backUrl'], // Server URL từ API (có thể null cho passport)
          );

          // Cập nhật flow data với kết quả upload
          final currentData = state.data!;
          final updatedData = currentData.copyWith(
            step1Data: step1Data,
          );

          AppLogger.info('Identity document upload completed for loan creation',
              data: {
                'documentType': documentType.label,
                'frontUrl': uploadUrls['frontUrl'],
                'backUrl': uploadUrls['backUrl'],
                'nextStep': 'QR Scanner (sẽ được set trong handleContinue)',
              });

          return Right(updatedData);
        },
      );
    } catch (e) {
      AppLogger.error('Error uploading identity documents for loan creation',
          error: e);
      return const Left(ServerFailure(
        ServerErrorType.unknown,
      ));
    }
  }

  // ==================== QR SCANNER METHODS ====================

  /// Xử lý khi QR parse thành công (public method)
  void handleSuccessfulQRParse(CCCDInfoEntity cccdInfo) {
    AppLogger.info('QR Parse successful in loan flow', data: {
      'cccdInfo': cccdInfo.toJson(),
    });

    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Processing successful QR parse - current step: ${currentData.currentStep.name}');

    if (currentData.currentStep == LoanStep.identityDocument) {
      // Cập nhật CCCD info vào step1Data và chuyển sang step2
      final updatedStep1Data = currentData.step1Data?.copyWith(
        cccdInfo: cccdInfo,
      );
      final updatedData = currentData.copyWith(
        step1Data: updatedStep1Data,
        currentStep: LoanStep.borrowerInfo, // Chuyển sang step2
        showQRScanner: false, // Tắt QR scanner sau khi parse thành công
      );
      state = BaseState.success(updatedData);
      AppLogger.info(
          'QR parse success processed - new step: ${updatedData.currentStep.name}, showQRScanner: ${updatedData.showQRScanner}');
    } else if (currentData.currentStep == LoanStep.coBorrowerDocument) {
      // Cập nhật CCCD info vào step3Data và chuyển sang step4
      final updatedStep3Data = currentData.step3Data?.copyWith(
        cccdInfo: cccdInfo,
      );
      final updatedData = currentData.copyWith(
        step3Data: updatedStep3Data,
        currentStep: LoanStep.coBorrowerInfo, // Chuyển sang step4
        showQRScanner: false, // Tắt QR scanner sau khi parse thành công
      );
      state = BaseState.success(updatedData);
       }
  }

  /// Xử lý khi user skip QR scan
  void handleQRSkip() {
    AppLogger.info('QR Scan skipped in loan flow');

    final currentData = state.data;
    if (currentData == null) return;

    // Kiểm tra để tránh duplicate calls
    if (!currentData.showQRScanner) {
      AppLogger.warning(
          'handleQRSkip called but showQRScanner is already false');
      return;
    }

    AppLogger.info(
        'Processing QR skip - current step: ${currentData.currentStep.name}');

    if (currentData.currentStep == LoanStep.identityDocument) {
      // Chuyển sang step2 mà không có thông tin từ QR
      final updatedData = currentData.copyWith(
        currentStep: LoanStep.borrowerInfo, // Chuyển sang step2
        showQRScanner: false, // Tắt QR scanner khi skip
      );
      state = BaseState.success(updatedData);
      AppLogger.info(
          'QR skip processed - new step: ${updatedData.currentStep.name}, showQRScanner: ${updatedData.showQRScanner}');
    } else if (currentData.currentStep == LoanStep.coBorrowerDocument) {
      // Chuyển sang step4 mà không có thông tin từ QR
      final updatedData = currentData.copyWith(
        currentStep: LoanStep.coBorrowerInfo, // Chuyển sang step4
        showQRScanner: false, // Tắt QR scanner khi skip
      );
      state = BaseState.success(updatedData);
      AppLogger.info(
          'QR skip processed for co-borrower - new step: ${updatedData.currentStep.name}, showQRScanner: ${updatedData.showQRScanner}');
    }
  }

  /// Xử lý khi user back từ QR scanner
  void handleQRBack() {
    AppLogger.info('QR Scan back pressed in loan flow');

    final currentData = state.data;
    if (currentData == null) return;

    // Kiểm tra để tránh duplicate calls
    if (!currentData.showQRScanner) {
      AppLogger.warning(
          'handleQRBack called but showQRScanner is already false');
      return;
    }

    AppLogger.info(
        'Processing QR back - current step: ${currentData.currentStep.name}');

    if (currentData.currentStep == LoanStep.identityDocument) {
      // Quay lại step1 (upload giấy tờ) và tắt QR scanner
      final updatedData = currentData.copyWith(
        currentStep: LoanStep.identityDocument, // Quay lại step1
        showQRScanner: false, // Tắt QR scanner
      );
      state = BaseState.success(updatedData);
      AppLogger.info(
          'QR back processed - new step: ${updatedData.currentStep.name}, showQRScanner: ${updatedData.showQRScanner}');
    } else if (currentData.currentStep == LoanStep.coBorrowerDocument) {
      // Quay lại step3 (upload giấy tờ đồng vay) và tắt QR scanner
      final updatedData = currentData.copyWith(
        currentStep: LoanStep.coBorrowerDocument, // Quay lại step3
        showQRScanner: false, // Tắt QR scanner
      );
      state = BaseState.success(updatedData);
      AppLogger.info(
          'QR back processed for co-borrower - new step: ${updatedData.currentStep.name}, showQRScanner: ${updatedData.showQRScanner}');
    }
  }

  // ==================== STEP 2: BORROWER INFO METHODS ====================

  /// Cập nhật thông tin người vay chính
  void updateBorrowerInfo(Step2BorrowerData borrowerInfo) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating borrower info', data: {
      'fullName': borrowerInfo.fullName,
      'phoneNumber': borrowerInfo.phoneNumber,
      'hasCoBorrower': borrowerInfo.hasCoBorrower,
      'isValid': borrowerInfo.isComplete,
    });

    final updatedData = currentData.copyWith(
      step2Data: borrowerInfo,
    );
    state = BaseState.success(updatedData);
  }

  // ==================== STEP 3: CO-BORROWER DOCUMENT METHODS ====================

  /// Upload giấy tờ người đồng vay
  Future<void> uploadCoBorrowerDocuments({
    required DocumentType documentType,
    required File frontFile,
    File? backFile,
  }) async {
    if (state.isProcessing) return;

    AppLogger.info('Starting co-borrower document upload', data: {
      'documentType': documentType.label,
      'frontFile': frontFile.path,
      'backFile': backFile?.path ?? 'null',
      'isPassport': documentType == DocumentType.passport,
    });

    // Validate files exist
    if (!await frontFile.exists()) {
      AppLogger.error('Front file does not exist');
      state = const BaseState.error(
        ValidationFailure(ValidationErrorType.invalidFormat),
      );
      return;
    }

    // Validate back file nếu có (cho CCCD)
    if (backFile != null && !await backFile.exists()) {
      AppLogger.error('Back file does not exist', data: {
        'frontExists': await frontFile.exists(),
        'backExists': await backFile.exists(),
      });
      state = const BaseState.error(
        ValidationFailure(ValidationErrorType.invalidFormat),
      );
      return;
    }

    // Resize ảnh trước khi upload
    final resizedFront = await resizeImage(frontFile, maxWidth: 1024);
    final resizedBack = backFile != null 
        ? await resizeImage(backFile, maxWidth: 1024)
        : null;

    await submitData(
      {
        'documentType': documentType,
        'frontFile': resizedFront,
        'backFile': resizedBack,
      },
      (data) => _uploadCoBorrowerDocuments(
        documentType: data['documentType'] as DocumentType,
        frontFile: data['frontFile'] as File,
        backFile: data['backFile'] as File?,
      ),
    );
  }

  /// Upload giấy tờ người đồng vay và cập nhật flow data
  Future<Either<Failure, CreateLoanFlowData>> _uploadCoBorrowerDocuments({
    required DocumentType documentType,
    required File frontFile,
    File? backFile,
  }) async {
    try {
      AppLogger.info('Starting co-borrower document upload', data: {
        'documentType': documentType.label,
        'frontFile': frontFile.path,
        'backFile': backFile?.path ?? 'null',
        'isPassport': documentType == DocumentType.passport,
      });

      // Gọi API upload thực sự
      final uploadResult = await _loanRepository.uploadCoBorrowerDocuments(
        frontFile: frontFile,
        backFile: backFile,
      );

      return uploadResult.fold(
        (failure) {
          AppLogger.error('Failed to upload co-borrower documents', error: failure);
          return Left(failure);
        },
        (uploadUrls) {
          AppLogger.info('Co-borrower documents uploaded successfully', data: uploadUrls);

          final step3Data = Step3CoBorrowerDocumentData(
            documentType: documentType,
            frontImagePath: frontFile.path, // Local path cho preview
            backImagePath: backFile?.path, // Local path cho preview (có thể null cho passport)
            frontImageUrl: uploadUrls['frontUrl'], // Server URL từ API
            backImageUrl: uploadUrls['backUrl'], // Server URL từ API (có thể null cho passport)
          );

          // Cập nhật flow data với kết quả upload
          final currentData = state.data!;
          final updatedData = currentData.copyWith(
            step3Data: step3Data,
          );

      AppLogger.info('Co-borrower document upload completed', data: {
        'documentType': documentType.label,
        'frontUrl': frontFile.path, // Placeholder
        'backUrl': backFile?.path, // Placeholder
        'nextStep': LoanStep.coBorrowerInfo,
      });

          return Right(updatedData);
        },
      );
    } catch (e) {
      AppLogger.error('Error uploading co-borrower documents', error: e);
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định:  e.toString()',
      ));
    }
  }

  // ==================== STEP 4: CO-BORROWER INFO METHODS ====================

  /// Cập nhật thông tin người đồng vay
  void updateCoBorrowerInfo(Step4CoBorrowerInfoData coBorrowerInfo) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating co-borrower info', data: {
      'fullName': coBorrowerInfo.fullName,
      'phoneNumber': coBorrowerInfo.phoneNumber,
      'isValid': coBorrowerInfo.isComplete,
    });

    final updatedData = currentData.copyWith(
      step4Data: coBorrowerInfo,
    );
    state = BaseState.success(updatedData);
  }

  // ==================== STEP 5: LOAN REQUEST METHODS ====================

  /// Cập nhật thông tin yêu cầu vay
  void updateLoanRequest(Step5LoanRequestData loanRequestData) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating loan request', data: {
      'hasCollateral': loanRequestData.hasCollateral,
      'ownCapital': loanRequestData.ownCapital,
      'loanAmount': loanRequestData.loanAmount,
      'totalNeed': loanRequestData.totalNeed,
      'branchCode': loanRequestData.branchCode,
      'loanPurpose': loanRequestData.loanPurpose.name,
    });

    final updatedData = currentData.copyWith(
      step5Data: loanRequestData,
    );
    state = BaseState.success(updatedData);
  }

  // ==================== STEP 6: FINANCIAL INFO METHODS ====================

  /// Cập nhật thông tin tài chính
  void updateFinancialInfo(Step6FinancialInfoData financialData) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating financial info', data: {
      'incomeSource': financialData.incomeSource.name,
      'dailyIncome': financialData.dailyIncome,
      'dailyRevenue': financialData.dailyRevenue,
    });

    final updatedData = currentData.copyWith(
      step6Data: financialData,
    );
    state = BaseState.success(updatedData);
  }

  // ==================== STEP 7: COLLATERAL INFO METHODS ====================

  /// Cập nhật thông tin tài sản bảo đảm
  void updateCollateralInfo(Step7CollateralInfoData collateralData) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating collateral info', data: {
      'assetType': collateralData.assetType,
      'assetValue': collateralData.assetValue,
      'ownerName': collateralData.ownerName,
      'assetLocation': collateralData.assetLocation,
      'isValid': collateralData.isComplete,
    });

    // Update step 7 data and move to next step
    final updatedData = currentData.copyWith(
      step7Data: collateralData,
      currentStep: LoanStep.collateralDetail, // Chuyển đến bước chi tiết tài sản
    );
    state = BaseState.success(updatedData);
  }

  /// Get asset types và loading state cho step 7
  List<SystemConfigEntity> get assetTypes => state.data?.assetTypes ?? [];
  SystemConfigEntity? get selectedAssetType => state.data?.selectedAssetType;
  bool get isLoadingAssetTypes => state.data?.isLoadingAssetTypes ?? false;

  /// Chọn loại tài sản bảo đảm
  void selectAssetType(SystemConfigEntity? assetType) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting asset type: [${assetType?.label}]');

    final updatedData = currentData.copyWith(
      selectedAssetType: assetType,
    );
    state = BaseState.success(updatedData);
  }

  /// Load danh sách loại tài sản bảo đảm
  Future<void> loadAssetTypes() async {
    updateState((latest) => latest.copyWith(isLoadingAssetTypes: true));
    try {
      final result = await _loanRepository.getAssetTypes();
      result.fold(
        (failure) {
          updateState((latest) => latest.copyWith(isLoadingAssetTypes: false));
          AppLogger.error('Failed to load asset types', error: failure);
        },
        (assetTypes) {
          updateState((latest) => latest.copyWith(
            assetTypes: assetTypes,
            isLoadingAssetTypes: false,
          ));
          AppLogger.info('Loaded asset types successfully', data: {
            'count': assetTypes.length,
          });
        },
      );
    } catch (e) {
      updateState((latest) => latest.copyWith(isLoadingAssetTypes: false));
      AppLogger.error('Exception loading asset types', error: e);
    }
  }

  /// Get selected province/ward for step 7 (collateral location)
  ProvinceEntity? get selectedProvinceStep7 => state.data?.selectedProvinceStep7;
  WardEntity? get selectedWardStep7 => state.data?.selectedWardStep7;

  /// Chọn tỉnh/thành phố cho bước 7 (địa chỉ tài sản)
  void selectProvinceStep7(ProvinceEntity? province) {
    final currentData = state.data;
    if (currentData == null) return;
    AppLogger.info('Selecting province for step 7: [${province?.name}]');
    final updatedData = currentData.copyWith(
      selectedProvinceStep7: province,
      wards: const [], // Clear wards cũ nếu dùng chung danh sách
      isLoadingWards: false,
    );
    state = BaseState.success(updatedData);
    if (province != null) {
      loadWards(province.id);
    }
  }

  /// Chọn phường/xã cho bước 7 (địa chỉ tài sản)
  void selectWardStep7(WardEntity? ward) {
    final currentData = state.data;
    if (currentData == null) return;
    AppLogger.info('Selecting ward for step 7: [${ward?.name}]');
    final updatedData = currentData.copyWith(
      selectedWardStep7: ward,
    );
    state = BaseState.success(updatedData);
  }
  // ==================== STEP 8: COLLATERAL DETAIL METHODS ====================

  /// Cập nhật chi tiết tài sản bảo đảm
  void updateCollateralDetail(Step8CollateralDetailData collateralDetailData) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating collateral detail', data: {
      'licensePlate': collateralDetailData.licensePlate,
      'assetName': collateralDetailData.assetName,
      'totalAssetValue': collateralDetailData.totalAssetValue,
    });

    final updatedData = currentData.copyWith(
      step8Data: collateralDetailData,
      currentStep: LoanStep.documentList, // Chuyển đến bước danh sách tài liệu
    );
    state = BaseState.success(updatedData);
  }

  // ==================== STEP 9: DOCUMENT LIST METHODS ====================

  /// Cập nhật danh sách tài liệu
  void updateDocumentList(Step9DocumentListData documentListData) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating document list', data: {
      'marriageDocuments': documentListData.marriageDocuments.length,
      'residenceDocuments': documentListData.residenceDocuments.length,
      'vehicleAppraisalDocuments':
          documentListData.vehicleAppraisalDocuments.length,
      'vehicleRegistrationDocuments':
          documentListData.vehicleRegistrationDocuments.length,
      'businessLicenseDocuments':
          documentListData.businessLicenseDocuments.length,
    });

    final updatedData = currentData.copyWith(
      step9Data: documentListData,
      currentStep:
          LoanStep.loanConfirmation, // Chuyển đến bước xác nhận khoản vay
    );
    state = BaseState.success(updatedData);
  }

  // ==================== STEP 10: LOAN CONFIRMATION METHODS ====================

  /// Cập nhật xác nhận khoản vay
  void updateLoanConfirmation(Step10LoanConfirmationData confirmationData) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Updating loan confirmation', data: {
      'isConfirmed': confirmationData.isConfirmed,
    });

    final updatedData = currentData.copyWith(
      step10Data: confirmationData,
      currentStep: LoanStep.success, // Chuyển đến bước thành công
    );
    state = BaseState.success(updatedData);
  }

  // ==================== NAVIGATION METHODS ====================

  /// Chuyển đến bước tiếp theo
  void nextStep() {
    final currentData = state.data;
    if (currentData == null) return;

    final nextStep = _getNextStep(currentData.currentStep);
    final updatedData = currentData.copyWith(currentStep: nextStep);
    state = BaseState.success(updatedData);
  }

  /// Chuyển về bước trước và reset data của step hiện tại
  void previousStep() {
    final currentData = state.data;
    if (currentData == null) return;

    final previousStep = _getPreviousStep(currentData.currentStep);

    AppLogger.info('Moving to previous step and resetting current step data',
        data: {
          'currentStep': currentData.currentStep.name,
          'previousStep': previousStep.name,
        });

    // Reset data của step hiện tại khi quay lại bước trước
    final updatedData = _resetCurrentStepData(currentData, previousStep);
    state = BaseState.success(updatedData);
  }

  /// Chuyển đến bước cụ thể
  void goToStep(LoanStep step) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(currentStep: step);
    state = BaseState.success(updatedData);
  }

  // ==================== SUBMIT METHODS ====================

  /// Submit tạo khoản vay
  Future<void> submitLoanCreation() async {
    final currentData = state.data;
    if (currentData == null) return;

    if (!_canSubmit(currentData)) {
      AppLogger.warning('Cannot submit loan creation - validation failed');
      return;
    }

    AppLogger.info('Submitting loan creation', data: {
      'currentStep': currentData.currentStep.name,
      'hasStep1Data': currentData.step1Data != null,
      'hasStep2Data': currentData.step2Data != null,
    });

    await submitData(
      currentData,
      (data) => _submitLoanCreation(data),
    );
  }

  /// Submit tạo khoản vay
  Future<Either<Failure, CreateLoanFlowData>> _submitLoanCreation(
    CreateLoanFlowData data,
  ) async {
    try {
      AppLogger.info('Calling loan creation API', data: {
        'currentStep': data.currentStep.name,
      });

      // TODO: Implement loan creation API call
      // Call API tạo khoản vay qua repository
      // final result = await _loanRepository.createLoan(data);

      // Tạm thời return success để test flow
      AppLogger.info('Loan creation successful');

      // Chuyển đến bước thành công
      final updatedData = data.copyWith(
        currentStep: LoanStep.success,
      );
      return Right(updatedData);
    } catch (e) {
      AppLogger.error('Error creating loan', error: e);
      return Left(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  // ==================== HELPER METHODS ====================

  /// Kiểm tra có thể submit không
  bool _canSubmit(CreateLoanFlowData data) {
    // Kiểm tra đã upload giấy tờ
    if (data.step1Data == null) {
      return false;
    }

    // Kiểm tra đã có thông tin người vay
    if (data.step2Data == null) {
      return false;
    }

    // Kiểm tra đã có thông tin yêu cầu vay
    if (data.step5Data == null) {
      return false;
    }

    // Kiểm tra đã có thông tin tài chính
    if (data.step6Data == null) {
      return false;
    }

    // Kiểm tra có tài sản bảo đảm thì phải có thông tin tài sản
    final hasCollateral = data.step5Data?.hasCollateral ?? false;
    if (hasCollateral) {
      if (data.step7Data == null || data.step8Data == null) {
        return false;
      }
    }

    // Kiểm tra đã có danh sách tài liệu
    if (data.step9Data == null) {
      return false;
    }

    // Kiểm tra đã xác nhận
    if (data.step10Data?.isConfirmed != true) {
      return false;
    }

    return true;
  }

  /// Lấy bước tiếp theo
  LoanStep _getNextStep(LoanStep currentStep) {
    switch (currentStep) {
      case LoanStep.identityDocument:
        return LoanStep.borrowerInfo;
      case LoanStep.borrowerInfo:
        // Kiểm tra có người đồng vay không
        final hasCoBorrower = state.data?.step2Data?.hasCoBorrower ?? false;
        return hasCoBorrower
            ? LoanStep.coBorrowerDocument
            : LoanStep.loanRequest;
      case LoanStep.coBorrowerDocument:
        return LoanStep.coBorrowerInfo;
      case LoanStep.coBorrowerInfo:
        return LoanStep.loanRequest;
      case LoanStep.loanRequest:
        return LoanStep.financialInfo;
      case LoanStep.financialInfo:
        // Kiểm tra có tài sản bảo đảm không
        final hasCollateral = state.data?.step5Data?.hasCollateral ?? true;
        return hasCollateral ? LoanStep.collateralInfo : LoanStep.documentList;
      case LoanStep.collateralInfo:
        return LoanStep.collateralDetail;
      case LoanStep.collateralDetail:
        return LoanStep.documentList;
      case LoanStep.documentList:
        return LoanStep.loanConfirmation;
      case LoanStep.loanConfirmation:
        return LoanStep.success;
      case LoanStep.success:
        return LoanStep.success;
    }
  }

  /// Lấy bước trước đó
  LoanStep _getPreviousStep(LoanStep currentStep) {
    switch (currentStep) {
      case LoanStep.identityDocument:
        return LoanStep.identityDocument;
      case LoanStep.borrowerInfo:
        return LoanStep.identityDocument;
      case LoanStep.coBorrowerDocument:
        return LoanStep.borrowerInfo;
      case LoanStep.coBorrowerInfo:
        return LoanStep.coBorrowerDocument;
      case LoanStep.loanRequest:
        final hasCoBorrower = state.data?.step2Data?.hasCoBorrower ?? false;
        return hasCoBorrower ? LoanStep.coBorrowerInfo : LoanStep.borrowerInfo;
      case LoanStep.financialInfo:
        return LoanStep.loanRequest;
      case LoanStep.collateralInfo:
        return LoanStep.financialInfo;
      case LoanStep.collateralDetail:
        return LoanStep.collateralInfo;
      case LoanStep.documentList:
        final hasCollateral = state.data?.step5Data?.hasCollateral ?? ;
        return hasCollateral
            ? LoanStep.collateralDetail
            : LoanStep.financialInfo;
      case LoanStep.loanConfirmation:
        return LoanStep.documentList;
      case LoanStep.success:
        return LoanStep.loanConfirmation;
    }
  }

  /// Reset data của step hiện tại khi quay lại bước trước
  CreateLoanFlowData _resetCurrentStepData(
    CreateLoanFlowData currentData,
    LoanStep targetStep,
  ) {
    switch (currentData.currentStep) {
      case LoanStep.identityDocument:
        // Không cần reset gì khi đang ở bước đầu
        return currentData.copyWith(currentStep: targetStep);

      case LoanStep.borrowerInfo:
        // Reset borrower info và tất cả step phía sau khi quay lại identity document
        AppLogger.info('Resetting borrower info and subsequent steps');
        return currentData.copyWith(
          currentStep: targetStep,
          step2Data: null, // Reset borrower info
          step3Data: null, // Reset co-borrower document
          step4Data: null, // Reset co-borrower info
          step5Data: null, // Reset loan request
          step6Data: null, // Reset financial info
          step7Data: null, // Reset collateral info
          step8Data: null, // Reset collateral detail
          step9Data: null, // Reset document list
          step10Data: null, // Reset loan confirmation
        );

      case LoanStep.coBorrowerDocument:
        // Reset co-borrower document và tất cả step phía sau khi quay lại borrower info
        AppLogger.info('Resetting co-borrower document and subsequent steps');
        return currentData.copyWith(
          currentStep: targetStep,
          step3Data: null, // Reset co-borrower document
          step4Data: null, // Reset co-borrower info
          step5Data: null, // Reset loan request
          step6Data: null, // Reset financial info
          step7Data: null, // Reset collateral info
          step8Data: null, // Reset collateral detail
          step9Data: null, // Reset document list
          step10Data: null, // Reset loan confirmation
        );

      case LoanStep.coBorrowerInfo:
        // Reset co-borrower info và tất cả step phía sau khi quay lại co-borrower document
        AppLogger.info('Resetting co-borrower info and subsequent steps');
        return currentData.copyWith(
          currentStep: targetStep,
          step4Data: null, // Reset co-borrower info
          step5Data: null, // Reset loan request
          step6Data: null, // Reset financial info
          step7Data: null, // Reset collateral info
          step8Data: null, // Reset collateral detail
          step9Data: null, // Reset document list
          step10Data: null, // Reset loan confirmation
        );

      case LoanStep.loanRequest:
        // Reset loan request và tất cả step phía sau khi quay lại borrower info hoặc co-borrower info
        AppLogger.info('Resetting loan request and subsequent steps');
        return currentData.copyWith(
          currentStep: targetStep,
          step5Data: null, // Reset loan request
          step6Data: null, // Reset financial info
          step7Data: null, // Reset collateral info
          step8Data: null, // Reset collateral detail
          step9Data: null, // Reset document list
          step10Data: null, // Reset loan confirmation
        );

      case LoanStep.financialInfo:
        // Reset financial info và tất cả step phía sau khi quay lại loan request
        AppLogger.info('Resetting financial info and subsequent steps');
        return currentData.copyWith(
          currentStep: targetStep,
          step6Data: null, // Reset financial info
          step7Data: null, // Reset collateral info
          step8Data: null, // Reset collateral detail
          step9Data: null, // Reset document list
          step10Data: null, // Reset loan confirmation
        );

      case LoanStep.collateralInfo:
        // Reset collateral info và tất cả step phía sau khi quay lại financial info
        AppLogger.info('Resetting collateral info and subsequent steps');
        return currentData.copyWith(
          currentStep: targetStep,
          step7Data: null, // Reset collateral info
          step8Data: null, // Reset collateral detail
          step9Data: null, // Reset document list
          step10Data: null, // Reset loan confirmation
        );

      case LoanStep.collateralDetail:
        // Reset collateral detail và tất cả step phía sau khi quay lại collateral info
        AppLogger.info('Resetting collateral detail and subsequent steps');
        return currentData.copyWith(
          currentStep: targetStep,
          step8Data: null, // Reset collateral detail
          step9Data: null, // Reset document list
          step10Data: null, // Reset loan confirmation
        );

      case LoanStep.documentList:
        // Reset document list và tất cả step phía sau khi quay lại collateral detail hoặc financial info
        AppLogger.info('Resetting document list and subsequent steps');
        return currentData.copyWith(
          currentStep: targetStep,
          step9Data: null, // Reset document list
          step10Data: null, // Reset loan confirmation
        );

      case LoanStep.loanConfirmation:
        // Reset loan confirmation khi quay lại document list
        AppLogger.info('Resetting loan confirmation');
        return currentData.copyWith(
          currentStep: targetStep,
          step10Data: null, // Reset loan confirmation
        );

      case LoanStep.success:
        // Reset success data khi quay lại loan confirmation
        AppLogger.info('Resetting success data');
        return currentData.copyWith(
          currentStep: targetStep,
          // Không reset loan confirmation vì đang quay lại bước đó
        );
    }
  }

  // ==================== COMPATIBILITY METHODS FOR OLD UI ====================

  /// Update step 1 data (for compatibility with old UI)
  void updateStep1Data(Step1DocumentData step1Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step1Data: step1Data);
    state = BaseState.success(updatedData);
  }

  /// Update step 2 data (for compatibility with old UI)
  void updateStep2Data(Step2BorrowerData step2Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step2Data: step2Data);
    state = BaseState.success(updatedData);
  }

  /// Update step 3 data (for compatibility with old UI)
  void updateStep3Data(Step3CoBorrowerDocumentData step3Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step3Data: step3Data);
    state = BaseState.success(updatedData);
  }

  /// Update step 4 data (for compatibility with old UI)
  void updateStep4Data(Step4CoBorrowerInfoData step4Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step4Data: step4Data);
    state = BaseState.success(updatedData);
  }

  /// Update step 5 data (for compatibility with old UI)
  void updateStep5Data(Step5LoanRequestData step5Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step5Data: step5Data);
    state = BaseState.success(updatedData);
  }

  /// Update step 6 data (for compatibility with old UI)
  void updateStep6Data(Step6FinancialInfoData step6Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step6Data: step6Data);
    state = BaseState.success(updatedData);
  }

  /// Update step 7 data (for compatibility with old UI)
  void updateStep7Data(Step7CollateralInfoData step7Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step7Data: step7Data);
    state = BaseState.success(updatedData);
  }

  /// Update step 8 data (for compatibility with old UI)
  void updateStep8Data(Step8CollateralDetailData step8Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step8Data: step8Data);
    state = BaseState.success(updatedData);
  }

  /// Update step 9 data (for compatibility with old UI)
  void updateStep9Data(Step9DocumentListData step9Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step9Data: step9Data);
    state = BaseState.success(updatedData);
  }

  /// Update step 10 data (for compatibility with old UI)
  void updateStep10Data(Step10LoanConfirmationData step10Data) {
    final currentData = state.data;
    if (currentData == null) return;

    final updatedData = currentData.copyWith(step10Data: step10Data);
    state = BaseState.success(updatedData);
  }

  /// Go back method (for compatibility with old UI)
  void goBack() {
    previousStep();
  }

  /// Check if continue is enabled (for compatibility with old UI)
  bool isContinueEnabled() {
    final currentData = state.data;
    if (currentData == null) return false;

    switch (currentData.currentStep) {
      case LoanStep.identityDocument:
        final step1Data = currentData.step1Data;
        if (step1Data == null) return false;
        // Kiểm tra đã chọn đủ ảnh để có thể upload
        return step1Data.isReadyForUpload;
      case LoanStep.borrowerInfo:
        return true;
      case LoanStep.coBorrowerDocument:
        final step3Data = currentData.step3Data;
        if (step3Data == null) return false;
        // Kiểm tra đã chọn đủ ảnh để có thể upload
        return step3Data.isReadyForUpload;
      case LoanStep.coBorrowerInfo:
        return true;
      case LoanStep.loanRequest:
       return true;
      case LoanStep.financialInfo:
        return true;
      case LoanStep.collateralInfo:
        // Kiểm tra các điều kiện cơ bản để có thể tiếp tục
        final step7Data = currentData.step7Data;
        if (step7Data == null) return false;

        // Chỉ kiểm tra các trường cơ bản, không quá nghiêm ngặt
        return step7Data.assetValue > 0 &&
               step7Data.ownerName.trim().isNotEmpty;
      case LoanStep.collateralDetail:
        return currentData.step8Data?.isComplete ?? false;
      case LoanStep.documentList:
        return currentData.step9Data?.isComplete ?? false;
      case LoanStep.loanConfirmation:
        return currentData.step10Data?.isConfirmed ?? false;
      case LoanStep.success:
        return false; // Không có nút tiếp tục ở bước cuối
    }
  }

  /// Xử lý khi nhấn Continue button
  Future<void> handleContinue() async {
    AppLogger.info('handleContinue called');

    final currentData = state.data;
    if (currentData == null) {
      AppLogger.warning('handleContinue: currentData is null');
      return;
    }

    AppLogger.info('handleContinue: processing step', data: {
      'currentStep': currentData.currentStep.name,
      'hasStep1Data': currentData.step1Data != null,
    });

    try {
      switch (currentData.currentStep) {
        case LoanStep.identityDocument:
          AppLogger.info('handleContinue: calling uploadIdentityDocuments');
          // Upload ảnh và chuyển sang QR scanner
          final currentData = state.data;
          if (currentData?.step1Data != null &&
              currentData!.step1Data!.isReadyForUpload) {
            
            final documentType = currentData.step1Data!.documentType;
            final frontFile = File(currentData.step1Data!.frontImagePath!);
            final backFile = currentData.step1Data!.backImagePath != null 
                ? File(currentData.step1Data!.backImagePath!)
                : null;
            
            await uploadIdentityDocuments(
              documentType: documentType,
              frontFile: frontFile,
              backFile: backFile,
            );

            // Sau khi upload thành công, xử lý khác nhau cho passport và các loại khác
            if (!state.isError) {
              final finalData = state.data;
              if (finalData != null) {
                if (documentType == DocumentType.passport) {
                  // Passport không cần QR scanner, chuyển thẳng sang step2
                  final nextStep = _getNextStep(finalData.currentStep);
                  final updatedData = finalData.copyWith(
                    currentStep: nextStep,
                    showQRScanner: false,
                  );
                  state = BaseState.success(updatedData);
                  AppLogger.info(
                      'Step 1: Passport upload thành công, chuyển thẳng sang step2',
                      data: {
                        'nextStep': nextStep.name,
                        'showQRScanner': false,
                      });
                } else {
                  // Các loại khác cần QR scanner
                  final updatedData = finalData.copyWith(showQRScanner: true);
                  state = BaseState.success(updatedData);
                  AppLogger.info(
                      'Step 1: Upload thành công, chuyển sang QR scanner',
                      data: {
                        'showQRScanner': updatedData.showQRScanner,
                        'currentStep': updatedData.currentStep.name,
                      });
                }
              }
            }
          }
          break;
        case LoanStep.coBorrowerDocument:
          AppLogger.info('handleContinue: calling uploadCoBorrowerDocuments');
          final currentData = state.data;
          if (currentData?.step3Data != null &&
              currentData!.step3Data!.isReadyForUpload) {
            
            final documentType = currentData.step3Data!.documentType;
            final frontFile = File(currentData.step3Data!.frontImagePath!);
            final backFile = currentData.step3Data!.backImagePath != null 
                ? File(currentData.step3Data!.backImagePath!)
                : null;
            
            await uploadCoBorrowerDocuments(
              documentType: documentType,
              frontFile: frontFile,
              backFile: backFile,
            );

            // Sau khi upload thành công, xử lý khác nhau cho passport và các loại khác
            if (!state.isError) {
              final finalData = state.data;
              if (finalData != null) {
                if (documentType == DocumentType.passport) {
                  // Passport không cần QR scanner, chuyển thẳng sang step4
                  final nextStep = _getNextStep(finalData.currentStep);
                  final updatedData = finalData.copyWith(
                    currentStep: nextStep,
                    showQRScanner: false,
                  );
                  state = BaseState.success(updatedData);
                  AppLogger.info(
                      'Step 3: Passport upload thành công, chuyển thẳng sang step4',
                      data: {
                        'nextStep': nextStep.name,
                        'showQRScanner': false,
                      });
                } else {
                  // Các loại khác cần QR scanner
                  final updatedData = finalData.copyWith(showQRScanner: true);
                  state = BaseState.success(updatedData);
                  AppLogger.info(
                      'Step 3: Upload thành công, chuyển sang QR scanner',
                      data: {
                        'showQRScanner': updatedData.showQRScanner,
                        'currentStep': updatedData.currentStep.name,
                      });
                }
              }
            }
          }
          break;

        case LoanStep.borrowerInfo:
          // Chuyển sang step tiếp theo
          final nextStep = _getNextStep(currentData.currentStep);
          state =
              BaseState.success(currentData.copyWith(currentStep: nextStep));
          break;
        case LoanStep.coBorrowerInfo:
          // Chuyển sang step tiếp theo
          final nextStep = _getNextStep(currentData.currentStep);
          state =
              BaseState.success(currentData.copyWith(currentStep: nextStep));
          break;

        case LoanStep.loanRequest:
          // Chuyển sang step tiếp theo
          final nextStep = _getNextStep(currentData.currentStep);
          state =
              BaseState.success(currentData.copyWith(currentStep: nextStep));
          break;

        case LoanStep.financialInfo:
          // Chuyển sang step tiếp theo
          final nextStep = _getNextStep(currentData.currentStep);
          state =
              BaseState.success(currentData.copyWith(currentStep: nextStep));
          break;

        case LoanStep.collateralInfo:
          // Chuyển sang step tiếp theo
          final nextStep = _getNextStep(currentData.currentStep);
          state =
              BaseState.success(currentData.copyWith(currentStep: nextStep));
          break;

        case LoanStep.collateralDetail:
          // Chuyển sang step tiếp theo
          final nextStep = _getNextStep(currentData.currentStep);
          state =
              BaseState.success(currentData.copyWith(currentStep: nextStep));
          break;

        case LoanStep.documentList:
          // Chuyển sang step tiếp theo
          final nextStep = _getNextStep(currentData.currentStep);
          state =
              BaseState.success(currentData.copyWith(currentStep: nextStep));
          break;

        case LoanStep.loanConfirmation:
          // Chuyển sang step tiếp theo
          final nextStep = _getNextStep(currentData.currentStep);
          state =
              BaseState.success(currentData.copyWith(currentStep: nextStep));
          break;

        case LoanStep.success:
          // Không có bước tiếp theo ở bước cuối
          break;
      }
    } catch (e) {
      AppLogger.error('Error in handleContinue', error: e);
      state = BaseState.error(ServerFailure(
        ServerErrorType.unknown,
        serverMessage: 'Lỗi không xác định: ${e.toString()}',
      ));
    }
  }

  /// Get total steps (for compatibility with old UI)
  static int get totalSteps => LoanStep.totalSteps;

  // ==================== GETTERS ====================

  /// Get bước hiện tại
  LoanStep? get currentStep => state.data?.currentStep;

  /// Get step 1 data
  Step1DocumentData? get step1Data => state.data?.step1Data;

  /// Get step 2 data
  Step2BorrowerData? get step2Data => state.data?.step2Data;

  /// Get step 3 data
  Step3CoBorrowerDocumentData? get step3Data => state.data?.step3Data;

  /// Get step 4 data
  Step4CoBorrowerInfoData? get step4Data => state.data?.step4Data;

  /// Get step 5 data
  Step5LoanRequestData? get step5Data => state.data?.step5Data;

  /// Get step 6 data
  Step6FinancialInfoData? get step6Data => state.data?.step6Data;

  /// Get step 7 data
  Step7CollateralInfoData? get step7Data => state.data?.step7Data;

  /// Get step 8 data
  Step8CollateralDetailData? get step8Data => state.data?.step8Data;

  /// Get step 9 data
  Step9DocumentListData? get step9Data => state.data?.step9Data;

  /// Get step 10 data
  Step10LoanConfirmationData? get step10Data => state.data?.step10Data;

  /// Get validation attempt
  int get validationAttempt => state.data?.validationAttempt ?? 0;

  /// Check có lỗi
  bool get hasError => state.isError;

  /// Get error message
  String get errorMessage {
    if (state.failure == null) return '';
    return state.failure.toString();
  }

  /// Check đang loading
  bool get isLoading => state.isLoading;

  /// Check đang loading từng step
  bool get isLoadingStep1 => state.data?.isLoadingStep1 ?? false;
  bool get isLoadingStep2 => state.data?.isLoadingStep2 ?? false;
  bool get isLoadingStep3 => state.data?.isLoadingStep3 ?? false;
  bool get isLoadingStep4 => state.data?.isLoadingStep4 ?? false;
  bool get isLoadingStep5 => state.data?.isLoadingStep5 ?? false;
  bool get isLoadingStep6 => state.data?.isLoadingStep6 ?? false;
  bool get isLoadingStep7 => state.data?.isLoadingStep7 ?? false;
  bool get isLoadingStep8 => state.data?.isLoadingStep8 ?? false;
  bool get isLoadingStep9 => state.data?.isLoadingStep9 ?? false;
  bool get isLoadingStep10 => state.data?.isLoadingStep10 ?? false;

  /// Get provinces (chung cho cả người vay chính và đồng vay)
  List<ProvinceEntity> get provinces => state.data?.provinces ?? [];
  List<WardEntity> get wards => state.data?.wards ?? [];
  bool get isLoadingProvinces => state.data?.isLoadingProvinces ?? false;
  bool get isLoadingWards => state.data?.isLoadingWards ?? false;

  /// Get selected values cho người vay chính
  ProvinceEntity? get selectedProvince => state.data?.selectedProvince;
  WardEntity? get selectedWard => state.data?.selectedWard;

  /// Get selected values cho người đồng vay
  ProvinceEntity? get selectedCoBorrowerProvince => state.data?.selectedCoBorrowerProvince;
  WardEntity? get selectedCoBorrowerWard => state.data?.selectedCoBorrowerWard;

  /// Get marital statuses
  List<SystemConfigEntity> get maritalStatuses =>
      state.data?.maritalStatuses ?? [];

  /// Get selected marital status
  SystemConfigEntity? get selectedMaritalStatus =>
      state.data?.selectedMaritalStatus;

  /// Get selected co-borrower marital status
  SystemConfigEntity? get selectedCoBorrowerMaritalStatus =>
      state.data?.selectedCoBorrowerMaritalStatus;

  /// Get loan purposes
  List<SystemConfigEntity> get loanPurposes =>
      state.data?.loanPurposes ?? [];

  /// Get selected loan purpose
  SystemConfigEntity? get selectedLoanPurpose =>
      state.data?.selectedLoanPurpose;

  /// Get loading states
  bool get isLoadingMaritalStatuses =>
      state.data?.isLoadingMaritalStatuses ?? false;
  bool get isLoadingLoanPurposes =>
      state.data?.isLoadingLoanPurposes ?? false;

  /// Get loan terms
  List<SystemConfigEntity> get loanTerms =>
      state.data?.loanTerms ?? [];

  /// Get selected loan term
  SystemConfigEntity? get selectedLoanTerm =>
      state.data?.selectedLoanTerm;

  /// Get loading state for loan terms
  bool get isLoadingLoanTerms =>
      state.data?.isLoadingLoanTerms ?? false;

  /// Get payment accounts
  List<PaymentAccountEntity> get paymentAccounts => state.data?.paymentAccounts ?? [];
  bool get isLoadingPaymentAccounts => state.data?.isLoadingPaymentAccounts ?? false;

  /// Get income sources
  List<SystemConfigEntity> get incomeSources => state.data?.incomeSources ?? [];
  SystemConfigEntity? get selectedIncomeSource => state.data?.selectedIncomeSource;
  bool get isLoadingIncomeSources => state.data?.isLoadingIncomeSources ?? false;

  /// Get selected values cho bước 6 (tài chính)
  ProvinceEntity? get selectedProvinceStep6 => state.data?.selectedProvinceStep6;
  WardEntity? get selectedWardStep6 => state.data?.selectedWardStep6;


  /// Reset về trạng thái ban đầu
  @override
  void reset() {
    const initialData = CreateLoanFlowData(
      currentStep: LoanStep.identityDocument,
      showQRScanner: false, // Đảm bảo QR scanner được tắt khi reset
    );
    state = const BaseState.success(initialData);
  }

  void updateState(CreateLoanFlowData Function(CreateLoanFlowData) updater) {
    final latest = state.data;
    if (latest == null) return;
    state = BaseState.success(updater(latest));
  }

  // ==================== PROVINCES ====================
  Future<void> loadProvinces() async {
    updateState((latest) => latest.copyWith(isLoadingProvinces: true));
    try {
      final result = await _locationRepository.getProvinces();
      result.fold(
        (failure) {
          updateState((latest) => latest.copyWith(isLoadingProvinces: false));
          AppLogger.error('Failed to load provinces', error: failure);
        },
        (provinces) {
          updateState((latest) => latest.copyWith(
            provinces: provinces,
            isLoadingProvinces: false,
          ));
          AppLogger.info('Loaded provinces successfully', data: {
            'count': provinces.length,
          });
        },
      );
    } catch (e) {
      updateState((latest) => latest.copyWith(isLoadingProvinces: false));
      AppLogger.error('Exception loading provinces', error: e);
    }
  }

  // ==================== WARDS (chung) ====================
  Future<void> loadWards(String provinceId) async {
    final currentData = state.data;
    if (currentData == null) return;

    final loadingData = currentData.copyWith(isLoadingWards: true);
    state = BaseState.success(loadingData);

    try {
      final result = await _locationRepository.getWards(provinceId);
      result.fold(
        (failure) {
          final errorData = currentData.copyWith(isLoadingWards: false);
          state = BaseState.success(errorData);
          AppLogger.error('Failed to load wards', error: failure);
        },
        (wards) {
          final successData = currentData.copyWith(
            wards: wards,
            isLoadingWards: false,
          );
          state = BaseState.success(successData);
        },
      );
    } catch (e) {
      final errorData = currentData.copyWith(isLoadingWards: false);
      state = BaseState.success(errorData);
      AppLogger.error('Exception loading wards', error: e);
    }
  }

  /// Chọn tỉnh/thành phố cho người vay chính
  void selectProvince(ProvinceEntity? province) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting province for borrower: ${province?.name}');

    final updatedData = currentData.copyWith(
      selectedProvince: province,
      wards: const [], // Clear wards cũ
      isLoadingWards: false, // Reset loading state
    );
    state = BaseState.success(updatedData);

    // Tự động load wards cho province đã chọn
    if (province != null) {
      loadWards(province.id);
    }
  }

  /// Chọn phường/xã cho người vay chính
  void selectWard(WardEntity? ward) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting ward for borrower: ${ward?.name}');

    final updatedData = currentData.copyWith(
      selectedWard: ward,
    );
    state = BaseState.success(updatedData);
  }

  /// Chọn tỉnh/thành phố cho người đồng vay
  void selectCoBorrowerProvince(ProvinceEntity? province) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting province for co-borrower: ${province?.name}');

    final updatedData = currentData.copyWith(
      selectedCoBorrowerProvince: province,
      wards: const [], // Clear wards cũ
      isLoadingWards: false, // Reset loading state
    );
    state = BaseState.success(updatedData);

    // Tự động load wards cho province đã chọn
    if (province != null) {
      loadWards(province.id);
    }
  }

  /// Chọn phường/xã cho người đồng vay
  void selectCoBorrowerWard(WardEntity? ward) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting ward for co-borrower: ${ward?.name}');

    final updatedData = currentData.copyWith(
      selectedCoBorrowerWard: ward,
    );
    state = BaseState.success(updatedData);
  }


  /// Chọn tỉnh/thành phố cho bước 6 (tài chính)
  void selectProvinceStep6(ProvinceEntity? province) {
    final currentData = state.data;
    if (currentData == null) return;
    AppLogger.info('Selecting province for step 6:  [${province?.name}');
    final updatedData = currentData.copyWith(
      selectedProvinceStep6: province,
      wards: const [], // Clear wards cũ nếu dùng chung danh sách
      isLoadingWards: false,
    );
    state = BaseState.success(updatedData);
    if (province != null) {
      loadWards(province.id);
    }
  }

  /// Chọn phường/xã cho bước 6 (tài chính)
  void selectWardStep6(WardEntity? ward) {
    final currentData = state.data;
    if (currentData == null) return;
    AppLogger.info('Selecting ward for step 6:  [${ward?.name}');
    final updatedData = currentData.copyWith(
      selectedWardStep6: ward,
    );
    state = BaseState.success(updatedData);
  }
  // ==================== MARITAL STATUSES ====================
  Future<void> loadMaritalStatuses() async {
    updateState((latest) => latest.copyWith(isLoadingMaritalStatuses: true));
    try {
      final result = await _loanRepository.getMaritalStatuses();
      result.fold(
        (failure) {
          updateState((latest) => latest.copyWith(isLoadingMaritalStatuses: false));
          AppLogger.error('Failed to load marital statuses', error: failure);
        },
        (maritalStatuses) {
          updateState((latest) => latest.copyWith(
            maritalStatuses: maritalStatuses,
            isLoadingMaritalStatuses: false,
          ));
          AppLogger.info('Loaded marital statuses successfully', data: {
            'count': maritalStatuses.length,
          });
        },
      );
    } catch (e, stack) {
      updateState((latest) => latest.copyWith(isLoadingMaritalStatuses: false));
      AppLogger.error('Exception loading marital statuses', error: e, stackTrace: stack);
    }
  }

  /// Chọn tình trạng hôn nhân cho bước 1
  void selectMaritalStatus(SystemConfigEntity? maritalStatus) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting marital status: ${maritalStatus?.label}');

    final updatedData = currentData.copyWith(
      selectedMaritalStatus: maritalStatus,
    );
    state = BaseState.success(updatedData);
  }

   /// Chọn tình trạng hôn nhân cho bước 4 - người đồng vay
  void selectMaritalStatusCoBorrower(SystemConfigEntity? maritalStatus) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info(
        'Selecting marital status for co-borrower: ${maritalStatus?.label}');

    final updatedData = currentData.copyWith(
      selectedCoBorrowerMaritalStatus: maritalStatus,
    );
    state = BaseState.success(updatedData);
  }

  // ==================== LOAN TERMS ====================
  Future<void> loadLoanTerms() async {
    updateState((latest) => latest.copyWith(isLoadingLoanTerms: true));
    try {
      final result = await _loanRepository.getLoanTerms();
      result.fold(
        (failure) {
          updateState((latest) => latest.copyWith(isLoadingLoanTerms: false));
          AppLogger.error('Failed to load loan terms', error: failure);
        },
        (loanTerms) {
          updateState((latest) => latest.copyWith(
            loanTerms: loanTerms,
            isLoadingLoanTerms: false,
          ));
          AppLogger.info('Loaded loan terms successfully', data: {
            'count': loanTerms.length,
          });
        },
      );
    } catch (e) {
      updateState((latest) => latest.copyWith(isLoadingLoanTerms: false));
      AppLogger.error('Exception loading loan terms', error: e);
    }
  }

    void selectLoanTerm(SystemConfigEntity? loanTerm) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting loan purpose: ${loanTerm?.label}');

    final updatedData = currentData.copyWith(
      selectedLoanTerm: loanTerm,
    );
    state = BaseState.success(updatedData);
  }
  // ==================== LOAN PURPOSES ====================
  Future<void> loadLoanPurposes() async {
    updateState((latest) => latest.copyWith(isLoadingLoanPurposes: true));
    try {
      final result = await _loanRepository.getLoanPurposes();
      result.fold(
        (failure) {
          updateState((latest) => latest.copyWith(isLoadingLoanPurposes: false));
          AppLogger.error('Failed to load loan purposes', error: failure);
        },
        (loanPurposes) {
          updateState((latest) => latest.copyWith(
            loanPurposes: loanPurposes,
            isLoadingLoanPurposes: false,
          ));
          AppLogger.info('Loaded loan purposes successfully', data: {
            'count': loanPurposes.length,
          });
        },
      );
    } catch (e) {
      updateState((latest) => latest.copyWith(isLoadingLoanPurposes: false));
      AppLogger.error('Exception loading loan purposes', error: e);
    }
  }

  /// Chọn mục đích sử dụng vốn
  void selectLoanPurpose(SystemConfigEntity? loanPurpose) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting loan purpose: ${loanPurpose?.label}');

    final updatedData = currentData.copyWith(
      selectedLoanPurpose: loanPurpose,
    );
    state = BaseState.success(updatedData);
  }

  // ==================== PAYMENT ACCOUNTS ====================
  Future<void> loadPaymentAccounts(String idNumber) async {
    final currentData = state.data;
    if (currentData == null) return;

    final loadingData = currentData.copyWith(isLoadingPaymentAccounts: true);
    state = BaseState.success(loadingData);

    try {
      final result = await _loanRepository.getPaymentAccounts(idNumber);
      result.fold(
        (failure) {
          final errorData = currentData.copyWith(isLoadingPaymentAccounts: false);
          state = BaseState.success(errorData);
          AppLogger.error('Failed to load payment accounts', error: failure);
        },
        (accounts) {
          final successData = currentData.copyWith(
            paymentAccounts: accounts,
            isLoadingPaymentAccounts: false,
          );
          state = BaseState.success(successData);
          AppLogger.info('Loaded payment accounts successfully', data: {
            'count': accounts.length,
          });
        },
      );
    } catch (e) {
      final errorData = currentData.copyWith(isLoadingPaymentAccounts: false);
      state = BaseState.success(errorData);
      AppLogger.error('Exception loading payment accounts', error: e);
    }
  }

  /// Chọn tài khoản nhận tiền
  void selectPaymentAccount(String? accountNumber) {
    final currentData = state.data;
    if (currentData == null) return;
    final step5Data = currentData.step5Data?.copyWith(accountNumber: accountNumber);
    final updatedData = currentData.copyWith(step5Data: step5Data);
    state = BaseState.success(updatedData);
  }

  // ==================== INCOME SOURCES ====================
  Future<void> loadIncomeSources() async {
    updateState((latest) => latest.copyWith(isLoadingIncomeSources: true));
    try {
      final result = await _loanRepository.getIncomeSources();
      result.fold(
        (failure) {
          updateState((latest) => latest.copyWith(isLoadingIncomeSources: false));
          AppLogger.error('Failed to load income sources', error: failure);
        },
        (incomeSources) {
          updateState((latest) => latest.copyWith(
            incomeSources: incomeSources,
            isLoadingIncomeSources: false,
          ));
          AppLogger.info('Loaded income sources successfully', data: {
            'count': incomeSources.length,
          });
        },
      );
    } catch (e) {
      updateState((latest) => latest.copyWith(isLoadingIncomeSources: false));
      AppLogger.error('Exception loading income sources', error: e);
    }
  }

  /// Chọn nguồn thu
  void selectIncomeSource(SystemConfigEntity? incomeSource) {
    final currentData = state.data;
    if (currentData == null) return;

    AppLogger.info('Selecting income source:  [${incomeSource?.label}');

    final updatedData = currentData.copyWith(
      selectedIncomeSource: incomeSource,
    );
    state = BaseState.success(updatedData);
  }

  /// Lấy thông tin chi nhánh (branch) của user hiện tại
  Future<BranchEntity?> getCurrentBranch() async {
    try {
      final userResult = await _authRepository.getCurrentUser();
      return userResult.fold(
        (failure) {
          AppLogger.error('Failed to get user for branch', error: failure);
          return null;
        },
        (user) {
          if (user.branch != null) {
            AppLogger.info('Branch loaded from user: ${user.branch!.name}');
            return user.branch;
          } else {
            AppLogger.warning('User does not have branch info');
            return null;
          }
        },
      );
    } catch (e) {
      AppLogger.error('Exception getting branch from user', error: e);
      return null;
    }
  }

  /// Check if step 7 is valid and complete
  bool isStep7Valid() {
    final currentData = state.data;
    if (currentData == null) return false;

    // Check if we have selected asset type
    if (currentData.selectedAssetType == null) return false;

    // Check if we have selected location
    if (currentData.selectedProvinceStep7 == null ||
        currentData.selectedWardStep7 == null) {
      return false;
    }

    // Check if step 7 data is complete
    final step7Data = currentData.step7Data;
    if (step7Data == null) return false;

    return step7Data.isComplete &&
           step7Data.hasBasicInfo &&
           step7Data.hasOwnerInfo &&
           step7Data.hasLocationInfo &&
           step7Data.hasReasonableValue;
  }

  /// Handle validation for step 7
  void validateStep7() {
    final currentData = state.data;
    if (currentData == null) return;

    final isValid = isStep7Valid();
    if (!isValid) {
      // Increment validation attempt to show errors
      final updatedData = currentData.copyWith(
        validationAttempt: currentData.validationAttempt + 1,
      );
      state = BaseState.success(updatedData);
      return;
    }

    // If valid, update state and move to next step
    final updatedData = currentData.copyWith(
      currentStep: LoanStep.collateralDetail,
    );
    state = BaseState.success(updatedData);
  }

  /// Kiểm tra có thể chuyển từ step 7 sang step 8
  bool canMoveToCollateralDetail() {
    final currentData = state.data;
    if (currentData == null) return false;

    // 1. Check asset type is selected
    if (currentData.selectedAssetType == null) {
      AppLogger.warning('Cannot move to step 8: Asset type not selected');
      return false;
    }

    // 2. Check location is selected
    if (currentData.selectedProvinceStep7 == null || 
        currentData.selectedWardStep7 == null) {
      AppLogger.warning('Cannot move to step 8: Location not fully selected');
      return false;
    }

    // 3. Check step 7 data is valid and complete
    final step7Data = currentData.step7Data;
    if (step7Data == null) {
      AppLogger.warning('Cannot move to step 8: No step 7 data');
      return false;
    }

    if (!step7Data.isComplete) {
      AppLogger.warning('Cannot move to step 8: Step 7 data incomplete');
      return false;
    }

    if (!step7Data.hasBasicInfo) {
      AppLogger.warning('Cannot move to step 8: Basic asset info missing');
      return false;
    }

    if (!step7Data.hasOwnerInfo) {
      AppLogger.warning('Cannot move to step 8: Owner info missing');
      return false;
    }

    if (!step7Data.hasLocationInfo) {
      AppLogger.warning('Cannot move to step 8: Location info missing');
      return false;
    }

    if (!step7Data.hasReasonableValue) {
      AppLogger.warning('Cannot move to step 8: Asset value unreasonable');
      return false;
    }

    AppLogger.info('Can move to step 8: All validations passed');
    return true;
  }

  /// Handle transition from step 7 to step 8
  void handleCollateralInfoCompletion(Step7CollateralInfoData collateralData) {
    final currentData = state.data;
    if (currentData == null) return;

    // Update the collateral info data
    updateCollateralInfo(collateralData);

    // Check if we can move to next step
    if (canMoveToCollateralDetail()) {
      final updatedData = currentData.copyWith(
        step7Data: collateralData,
        currentStep: LoanStep.collateralDetail,
      );
      state = BaseState.success(updatedData);
      AppLogger.info('Moving to step 8 (collateral detail)');
    } else {
      // Stay on current step but increment validation attempt to show errors
      final updatedData = currentData.copyWith(
        step7Data: collateralData,
        validationAttempt: currentData.validationAttempt + 1,
      );
      state = BaseState.success(updatedData);
      AppLogger.warning('Cannot move to step 8 - validation failed');
    }
  }
}

final currentBranchProvider = FutureProvider<BranchEntity?>((ref) async {
  final controller = ref.read(createLoanControllerProvider.notifier);
  return await controller.getCurrentBranch();
});
