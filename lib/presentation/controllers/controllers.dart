/// Presentation Controllers Master Barrel Export
/// Single import point for all Riverpod controllers in the presentation layer
/// 
/// This file provides centralized access to all controller categories:
/// - Auth controllers (authentication, login, register)
/// - Article controllers (content management)
/// - CTV Registration controllers (CTV registration flow)
/// - Identity controllers (identity verification)
/// - Loan controllers (loan management)
/// - Media controllers (file upload, image handling)
/// - Network controllers (network monitoring)
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/controllers/controllers.dart';
/// 
/// // Access any controller from any category:
/// 
/// // Auth controllers
/// final authController = ref.watch(authControllerProvider);
/// 
/// // Article controllers
/// final articlesController = ref.watch(articleControllerProvider);
/// 
/// // CTV Registration controllers
/// final ctvController = ref.watch(ctvRegistrationControllerProvider);
/// 
/// // Identity controllers
/// final identityController = ref.watch(identityUploadControllerProvider);
/// 
/// // Loan controllers
/// final loanController = ref.watch(createLoanControllerProvider);
/// 
/// // Media controllers
/// final mediaController = ref.watch(mediaUploadControllerProvider);
/// 
/// // Network controllers
/// final networkController = ref.watch(networkMonitorControllerProvider);
/// ```
/// 
/// ## Controller Organization:
/// - **auth/**: Authentication and user management controllers
/// - **article/**: Content and article management controllers
/// - **ctv_registration/**: CTV registration flow controllers
/// - **identity/**: Identity verification controllers
/// - **loan/**: Loan application and management controllers
/// - **media/**: File upload and media handling controllers
/// - **network/**: Network monitoring and connectivity controllers
library controllers;

// Auth controllers - authentication and user management
export 'auth/auth_controller.dart';
export 'auth/auth_state.dart';

// Article controllers - content management
export 'article/article_controller.dart';

// CTV Registration controllers - CTV registration flow
export 'ctv_registration/ctv_registration_controller.dart';

// Identity controllers - identity verification
// Loan controllers - loan management
export 'loan/create_loan_controller.dart';

// Media controllers - file upload and media handling
export 'media/media_upload_controller.dart';
export 'media/non_auto_dispose_media_controller.dart';

// Network controllers - network monitoring
export 'network/network_monitor_controller.dart';

// Permission controllers - permission management
export 'permission/permission_controller.dart';
export 'permission/permission_state.dart';
export 'permission/permission_feature_result.dart'; 