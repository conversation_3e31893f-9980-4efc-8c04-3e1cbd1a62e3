import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Direct import for events to avoid naming conflicts
import 'package:sales_app/core/events/app_events.dart' as events;
// Domain barrel export
import 'package:sales_app/domain/domain.dart';
// DI barrel export
import 'package:sales_app/di/injection.dart';
// Auth state
import 'auth_state.dart';

part 'auth_controller.g.dart';

@riverpod
class AuthController extends _$AuthController {
  late final AuthRepository _authRepository;
  late final NetworkInfo _networkInfo;
  late final AppEventBus _eventBus;

  StreamSubscription<events.AuthEvent>? _authEventSubscription;

  @override
  AuthState build() {
    _authRepository = getIt<AuthRepository>();
    _networkInfo = getIt<NetworkInfo>();
    _eventBus = getIt<AppEventBus>();

    // Listen to auth events for session expiry
    _listenToAuthEvents();

    // Setup automatic cleanup when provider is disposed
    ref.onDispose(() {
      _authEventSubscription?.cancel();
      AppLogger.info('AuthController: Auto-disposed auth event subscription');
    });

    // Check initial login status
    _checkInitialLoginStatus();

    return const AuthState.initial();
  }

  /// Listen to auth events from the event bus
  void _listenToAuthEvents() {
    _authEventSubscription = _eventBus.authEvents.listen((authEvent) {
      authEvent.when(
        sessionExpired: (reason) => _handleSessionExpired(reason),
        forceLogout: (reason) => _handleForceLogout(reason),
        tokenRefreshFailed: (reason) => _handleTokenRefreshFailed(reason),
      );
    });

    AppLogger.info('AuthController: Started listening to auth events');
  }

  /// Handle session expired event
  void _handleSessionExpired(String? reason) {
    AppLogger.info('AuthController: Session expired event received', data: {
      'reason': reason,
      'currentState': state.toString(),
    });

    // Clear authentication state
    state = AuthState.unauthenticated(
      failure: AuthFailure(
        AuthErrorType.tokenExpired,
        serverMessage: reason ?? 'Session expired',
      ),
    );
  }

  /// Handle force logout event
  void _handleForceLogout(String? reason) async {
    AppLogger.info('AuthController: Force logout event received', data: {
      'reason': reason,
    });

    // For session expiry scenarios, show dialog with custom message
    // For manual logout, just clear state silently
    if (reason != null) {
      // Session expired scenario - show dialog
      state = AuthState.unauthenticated(
        failure: AuthFailure(
          AuthErrorType.tokenExpired,
          serverMessage: reason,
        ),
      );
    } else {
      // Manual logout or other scenarios - silent logout
      state = const AuthState.unauthenticated();
    }
  }

  /// Handle token refresh failed event (for logging/analytics)
  void _handleTokenRefreshFailed(String? reason) {
    AppLogger.warning('AuthController: Token refresh failed', data: {
      'reason': reason,
    });
    // This is mainly for logging, no state change needed
  }



  /// Check if user is already logged in
  Future<void> _checkInitialLoginStatus() async {
    try {
      // ✅ Kiểm tra token validity trước
      final isLoggedIn = await _authRepository.isLoggedIn();
      
      if (isLoggedIn) {
        // ✅ Lấy user từ repository thay vì storage trực tiếp
        final userResult = await _authRepository.getCurrentUser();
        
        userResult.fold(
          (failure) {
            AppLogger.warning('User logged in but failed to get user info: ${failure.toString()}');
            state = const AuthState.unauthenticated();
          },
          (user) {
            AppLogger.info('User found, setting authenticated state', data: {
              'userId': user.id,
              'email': user.email,
            });
            state = AuthState.authenticated(user);
          },
        );
      } else {
        AppLogger.info('No valid authentication found');
        state = const AuthState.unauthenticated();
      }
    } catch (e) {
      AppLogger.error('Error checking login status', error: e);
      state = const AuthState.unauthenticated();
    }
  }

  /// Login with email and password
  Future<void> login({
    required String email,
    required String password,
  }) async {
    // Validate inputs
    if (!_isValidEmail(email)) {
      state = const AuthState.error(ValidationFailure(ValidationErrorType.invalidEmail));
      return;
    }

    if (!_isValidPassword(password)) {
      state = const AuthState.error(ValidationFailure(ValidationErrorType.invalidPassword));
      return;
    }

    // Check network connectivity
    if (!await _networkInfo.isConnected) {
      state = const AuthState.error(NetworkFailure(NetworkErrorType.noConnection));
      return;
    }

    // Start loading
    state = const AuthState.loading();

    try {
      AppLogger.info('Attempting login', data: {
        'email': email,
        'timestamp': DateTime.now().toIso8601String(),
      });

      final result = await _authRepository.login(
        email: email,
        password: password,
      );

      result.fold(
        (failure) {
          AppLogger.error('Login failed', error: failure.toString());
          state = AuthState.error(failure);
        },
        (user) async {
          AppLogger.info('Login successful, fetching user profile', data: {
            'userId': user.id,
            'email': user.email,
          });
          
          // ✅ Gọi API getProfile để lấy thông tin chi tiết user
          await _fetchAndUpdateUserProfile(user);
        },
      );
    } catch (e) {
      AppLogger.error('Unexpected login error', error: e);
      state = const AuthState.error(ServerFailure(ServerErrorType.unknown));
    }
  }

  /// Fetch user profile from API and update user information
  Future<void> _fetchAndUpdateUserProfile(User user) async {
    try {
      AppLogger.info('🔍 Starting profile fetch via repository');
      
      // Gọi repository để lấy profile thay vì gọi API trực tiếp
      final profileResult = await _authRepository.getProfile();
      
      profileResult.fold(
        (failure) {
          AppLogger.error('❌ Profile fetch failed - login cannot proceed', data: {
            'failure': failure.toString(),
          });
          
          // Nếu profile API lỗi, login thất bại
          AppLogger.warning('🚫 Login failed due to profile API error');
          state = AuthState.error(failure);
        },
        (updatedUser) {
          AppLogger.info('✅ Profile fetched and updated successfully', data: {
            'userId': updatedUser.id,
            'email': updatedUser.email,
            'fullName': updatedUser.name,
            'phone': updatedUser.phone,
            'originalName': user.name,
            'originalPhone': user.phone,
          });
          
          // Chỉ set authenticated khi profile API thành công
          AppLogger.info('🎯 Setting authenticated state with updated user profile');
          state = AuthState.authenticated(updatedUser);
        },
      );
    } catch (e) {
      AppLogger.error('❌ Unexpected error in profile fetch', error: e);
      
      // Nếu có lỗi không mong đợi, login thất bại
      AppLogger.warning('🚫 Login failed due to unexpected profile error');
      state = const AuthState.error(ServerFailure(ServerErrorType.unknown));
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      state = const AuthState.loading();

      await _authRepository.logout();

      AppLogger.info('User logged out successfully');
      state = const AuthState.unauthenticated();
    } catch (e) {
      AppLogger.error('Logout error', error: e);
      // Even if logout fails, clear local state
      state = const AuthState.unauthenticated();
    }
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    return email.isNotEmpty; // ✅ Đã sửa từ (email??'').isNotEmpty
    // return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate password strength
  bool _isValidPassword(String password) {
    return password.length >= 6;
  }

  /// Update email validation state
  void updateEmailValidation(String email) {
    _isValidEmail(email);
    // For now, we'll just store validation state internally
    // TODO: Implement proper state management for validation
  }

  /// Update password validation state
  void updatePasswordValidation(String password) {
    _isValidPassword(password);
    // For now, we'll just store validation state internally
    // TODO: Implement proper state management for validation
  }

  /// Clear error message
  void clearError() {
    // Clear error by setting to unauthenticated state
    if (state.hasError) {
      state = const AuthState.unauthenticated();
    }
  }

  /// Biometric login (placeholder)
  Future<void> biometricLogin() async {
    AppLogger.info('Biometric login attempted');
    // TODO: Implement biometric authentication
    state = const AuthState.error(ServerFailure(ServerErrorType.unknown));
  }

  /// 🧪 DEBUG: Force session expired for testing
  /// This method simulates a session expired scenario for testing purposes
  void debugForceSessionExpired() {
    AppLogger.warning('DEBUG: Forcing session expired for testing');
    _eventBus.emitAuthEvent(const events.AuthEvent.sessionExpired(
      reason: 'DEBUG: Manually triggered session expired for testing',
    ));
  }

  /// 🧪 DEBUG: Set expired refresh token to trigger real refresh failure
  /// This method sets an expired refresh token to test the actual refresh flow
  Future<void> debugSetExpiredRefreshToken() async {
    try {
      final storageService = getIt<StorageService>();
      await storageService.saveRefreshToken('mock_refresh_token_expired_for_testing');
      AppLogger.warning('DEBUG: Set expired refresh token for testing');
    } catch (e) {
      AppLogger.error('DEBUG: Failed to set expired refresh token', error: e);
    }
  }

  /// 🧪 DEBUG: Force logout for testing
  /// This method simulates a force logout scenario (admin logout, security violation, etc.)
  void debugForceLogout() {
    AppLogger.warning('DEBUG: Forcing logout for testing');
    _eventBus.emitAuthEvent(const events.AuthEvent.forceLogout(
      reason: 'Phiên đăng nhập hết hạn, vui lòng đăng nhập lại',
    ));
  }
}
