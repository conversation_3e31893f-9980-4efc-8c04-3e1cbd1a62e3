import 'package:freezed_annotation/freezed_annotation.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Domain barrel export
import 'package:sales_app/domain/domain.dart';

part 'auth_state.freezed.dart';

@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default(false) bool isLoading,
    @Default(false) bool isLoggedIn,
    User? user,
    Failure? failure,
    @Default(false) bool isEmailValid,
    @Default(false) bool isPasswordValid,
  }) = _AuthState;

  const factory AuthState.initial() = _Initial;
  const factory AuthState.loading() = _Loading;
  const factory AuthState.authenticated(User user) = _Authenticated;
  const factory AuthState.unauthenticated({Failure? failure}) = _Unauthenticated;
  const factory AuthState.error(Failure failure) = _Error;
}

extension AuthStateX on AuthState {
  bool get canLogin => when(
    (isLoading, isLoggedIn, user, failure, isEmailValid, isPasswordValid) =>
        isEmailValid && isPasswordValid && !isLoading,
    initial: () => false,
    loading: () => false,
    authenticated: (user) => false,
    unauthenticated: (failure) => true, // Allow login when unauthenticated
    error: (failure) => true, // Allow retry when error
  );

  bool get hasError => when(
    (isLoading, isLoggedIn, user, failure, isEmailValid, isPasswordValid) =>
        failure != null,
    initial: () => false,
    loading: () => false,
    authenticated: (user) => false,
    unauthenticated: (failure) => failure != null,
    error: (failure) => true,
  );
}
