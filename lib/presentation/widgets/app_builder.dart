import 'package:flutter/material.dart';

// Widget exports
import 'auth/auth.dart';
import 'network/network.dart';

/// Combined app builder that wraps the app with all necessary global wrappers
/// 
/// This builder combines multiple global wrappers in the correct order:
/// 1. NetworkAwareApp - for network monitoring and notifications
/// 2. AuthAwareApp - for authentication monitoring and session handling
/// 
/// Usage:
/// ```dart
/// MaterialApp.router(
///   builder: AppBuilder.build,
///   // ... other properties
/// );
/// ```
class AppBuilder {
  /// Build method that wraps the child with all necessary global wrappers
  static Widget build(BuildContext context, Widget? child) {
    if (child == null) {
      return const SizedBox.shrink();
    }

    // Wrap with network awareness first, then auth awareness
    return NetworkAwareAppBuilder.build(
      context,
      AuthAwareAppBuilder.build(context, child),
    );
  }
}
