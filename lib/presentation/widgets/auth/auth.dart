/// Auth Widgets Barrel Export
/// Authentication-related widgets and components
/// 
/// This file provides centralized access to all authentication-related widgets:
/// - Global auth monitoring and session handling
/// - Authentication state management widgets
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/widgets/auth/auth.dart';
/// 
/// // Use AuthAwareApp for global session monitoring
/// MaterialApp.router(
///   builder: AuthAwareAppBuilder.build,
///   // ... other properties
/// );
/// ```

library auth_widgets;

// Global auth monitoring
export 'auth_aware_app.dart';
