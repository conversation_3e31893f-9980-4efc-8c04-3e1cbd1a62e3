import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Presentation controllers
import 'package:sales_app/presentation/controllers/auth/auth_controller.dart';
import 'package:sales_app/presentation/controllers/auth/auth_state.dart';



/// Global auth-aware app wrapper
/// Provides authentication monitoring and session expired handling throughout the entire app
class AuthAwareApp extends ConsumerStatefulWidget {
  final Widget child;

  const AuthAwareApp({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<AuthAwareApp> createState() => _AuthAwareAppState();
}

class _AuthAwareAppState extends ConsumerState<AuthAwareApp> {
  bool _sessionExpiredDialogShown = false;

  @override
  void initState() {
    super.initState();
    AppLogger.info('AuthAwareApp initialized');
  }

  @override
  void dispose() {
    AppLogger.info('AuthAwareApp disposed');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes for session expiry
    ref.listen<AuthState>(authControllerProvider, (previous, next) {
      _handleAuthStateChange(context, previous, next);
    });

    return widget.child;
  }

  /// Handle authentication state changes
  void _handleAuthStateChange(
    BuildContext context,
    AuthState? previous,
    AuthState next,
  ) {
    AppLogger.info('AuthAwareApp: Auth state changed', data: {
      'previousState': previous?.toString(),
      'nextState': next.toString(),
    });

    next.when(
      (isLoading, isLoggedIn, user, failure, isEmailValid, isPasswordValid) {
        // Handle default state - check for session expired failure
        AppLogger.debug('AuthAwareApp: Default state', data: {
          'isLoading': isLoading,
          'isLoggedIn': isLoggedIn,
          'hasFailure': failure != null,
          'failureType': failure?.runtimeType.toString(),
        });
        if (failure != null) {
          _checkAndShowSessionExpiredDialog(context, failure);
        }
      },
      initial: () {
        AppLogger.debug('AuthAwareApp: Initial state');
      },
      loading: () {
        AppLogger.debug('AuthAwareApp: Loading state');
      },
      authenticated: (user) {
        AppLogger.debug('AuthAwareApp: Authenticated state', data: {
          'userId': user.id,
          'userEmail': user.email,
        });
        // Reset dialog flag when user is authenticated
        _sessionExpiredDialogShown = false;
      },
      unauthenticated: (failure) {
        AppLogger.debug('AuthAwareApp: Unauthenticated state', data: {
          'hasFailure': failure != null,
          'failureType': failure?.runtimeType.toString(),
        });
        if (failure != null) {
          _checkAndShowSessionExpiredDialog(context, failure);
        }
      },
      error: (failure) {
        AppLogger.debug('AuthAwareApp: Error state', data: {
          'failureType': failure.runtimeType.toString(),
        });
        _checkAndShowSessionExpiredDialog(context, failure);
      },
    );
  }

  /// Check if failure is session expired and show dialog
  void _checkAndShowSessionExpiredDialog(BuildContext context, Failure failure) {
    AppLogger.info('AuthAwareApp: Checking for session expired dialog', data: {
      'failureType': failure.runtimeType.toString(),
      'dialogAlreadyShown': _sessionExpiredDialogShown,
    });

    // Only show dialog once per session expiry
    if (_sessionExpiredDialogShown) {
      AppLogger.debug('AuthAwareApp: Dialog already shown, skipping');
      return;
    }

    failure.when(
      server: (errorType, serverMessage, serverErrorCode) {
        AppLogger.debug('AuthAwareApp: Server failure - not session expired');
      },
      network: (errorType) {
        AppLogger.debug('AuthAwareApp: Network failure - not session expired');
      },
      cache: (errorType) {
        AppLogger.debug('AuthAwareApp: Cache failure - not session expired');
      },
      auth: (errorType, serverMessage, serverErrorCode) {
        AppLogger.info('AuthAwareApp: Auth failure detected', data: {
          'errorType': errorType.toString(),
          'serverMessage': serverMessage,
          'isTokenExpired': errorType == AuthErrorType.tokenExpired,
        });

        // Check if this is a token expired error (session expired)
        if (errorType == AuthErrorType.tokenExpired) {
          AppLogger.info('AuthAwareApp: Token expired detected, showing dialog');
          _showSessionExpiredDialog(context, serverMessage);
        }
      },
      validation: (errorType, serverMessage, serverErrorCode) {
        AppLogger.debug('AuthAwareApp: Validation failure - not session expired');
      },
    );
  }

  /// Show session expired dialog
  void _showSessionExpiredDialog(BuildContext context, String? reason) {
    if (_sessionExpiredDialogShown) return;

    _sessionExpiredDialogShown = true;

    AppLogger.info('AuthAwareApp: Session expired, navigating to login with message', data: {
      'reason': reason,
    });

    // Use post frame callback to ensure the widget tree is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Navigate to login screen and show snackbar with session expired message
        _navigateToLoginWithMessage(reason);
      }
    });
  }

  /// Navigate to login screen and show session expired message
  void _navigateToLoginWithMessage(String? reason) {
    try {
      // Use global navigator helper method
      // If reason is provided, use it; otherwise use default localized message
      if (reason != null && reason.isNotEmpty) {
        GlobalNavigator.showSessionExpiredMessage(reason);
      } else {
        GlobalNavigator.showSessionExpiredMessageWithDefault();
      }

      AppLogger.info('AuthAwareApp: Successfully triggered session expired flow');
    } catch (e) {
      AppLogger.error('AuthAwareApp: Error in session expired flow', error: e);
      // Reset flag so we can try again later
      _sessionExpiredDialogShown = false;
    }
  }
}

/// Builder function for easy integration with MaterialApp.router
class AuthAwareAppBuilder {
  /// Build method that wraps the child with AuthAwareApp
  static Widget build(BuildContext context, Widget? child) {
    if (child == null) {
      return const SizedBox.shrink();
    }

    return AuthAwareApp(child: child);
  }
}
