import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'adaptive_loading_overlay.dart';

/// Mixin to easily add loading overlay functionality to any widget
mixin LoadingMixin {
  /// Build loading overlay based on loading state
  Widget buildLoadingOverlay({
    required bool isLoading,
    String? message,
    Color? backgroundColor,
    double opacity = 0.5,
    bool showMessage = true,
    TextStyle? messageStyle,
  }) {
    if (!isLoading) {
      return const SizedBox.shrink();
    }
    
    return AdaptiveLoadingOverlay(
      message: message,
      backgroundColor: backgroundColor,
      opacity: opacity,
      showMessage: showMessage,
      messageStyle: messageStyle,
    );
  }
  
  /// Build loading overlay with predefined configurations
  Widget buildConfiguredLoadingOverlay({
    required bool isLoading,
    required LoadingType type,
    String? customMessage,
  }) {
    if (!isLoading) {
      return const SizedBox.shrink();
    }
    
    // If customMessage is provided, use it. Otherwise, show no message (silent loading)
    if (customMessage != null) {
      return AdaptiveLoadingOverlay(
        message: customMessage,
        showMessage: true,
      );
    }
    
    // Default: silent loading (no message) for all types
    return const AdaptiveLoadingOverlay(
      showMessage: false,
    );
  }
}

/// Enum for different loading types
enum LoadingType {
  login,
  logout,
  loading,
  saving,
  deleting,
  silent,
}

/// Base widget class with loading functionality
abstract class BaseLoadingConsumerWidget extends ConsumerWidget with LoadingMixin {
  const BaseLoadingConsumerWidget({super.key});
  
  /// Override this to define when loading should be shown
  bool isLoading(WidgetRef ref);
  
  /// Override this to define loading message
  String? getLoadingMessage(WidgetRef ref) => null;
  
  /// Override this to define loading type
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.loading;
  
  /// Build the main content (without loading overlay)
  Widget buildContent(BuildContext context, WidgetRef ref);
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        buildContent(context, ref),
        buildConfiguredLoadingOverlay(
          isLoading: isLoading(ref),
          type: getLoadingType(ref),
          customMessage: getLoadingMessage(ref),
        ),
      ],
    );
  }
}

/// Hook-based loading widget
abstract class BaseLoadingHookWidget extends HookConsumerWidget with LoadingMixin {
  const BaseLoadingHookWidget({super.key});
  
  /// Override this to define when loading should be shown
  bool isLoading(WidgetRef ref);
  
  /// Override this to define loading message
  String? getLoadingMessage(WidgetRef ref) => null;
  
  /// Override this to define loading type
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.loading;
  
  /// Build the main content (without loading overlay)
  Widget buildContent(BuildContext context, WidgetRef ref);
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        buildContent(context, ref),
        buildConfiguredLoadingOverlay(
          isLoading: isLoading(ref),
          type: getLoadingType(ref),
          customMessage: getLoadingMessage(ref),
        ),
      ],
    );
  }
}
