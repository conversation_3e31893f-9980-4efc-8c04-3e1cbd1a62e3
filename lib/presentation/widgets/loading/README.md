# Adaptive Loading System

## 📁 File Structure
```
lib/presentation/widgets/loading/
├── adaptive_loading_overlay.dart    # Core overlay widget
├── loading_mixin.dart              # Mixin & base classes
├── examples/                       # Usage examples
│   └── example_screens.dart
└── README.md                       # This documentation
```

## 🎯 Overview

Reusable, adaptive loading overlay system that works across iOS and Android with consistent UX.

### ✅ Key Features
- **Adaptive Design**: iOS (CupertinoActivityIndicator) / Android (CircularProgressIndicator)
- **Silent by Default**: No messages unless explicitly provided
- **Reusable Components**: Use across all screens with consistent UX
- **Multiple Usage Patterns**: BaseWidget, Mixin, Extension, Direct usage
- **Type-safe**: Compile-time checking with enums
- **Customizable**: Flexible configuration options

## 🔄 Behavior Update (v2.0)

### ❌ Before (Auto Messages)
```dart
LoadingType.login → "Đang đăng nhập..." (automatic)
LoadingType.logout → "Đang đăng xuất..." (automatic)
```

### ✅ After (Silent by Default)
```dart
LoadingType.login → Only spinner (silent)
LoadingType.logout → Only spinner (silent)

// Messages only when explicitly provided:
@override
String? getLoadingMessage(WidgetRef ref) => 'Custom message';
```

## 📱 Usage Patterns

### 1. BaseLoadingHookWidget (Recommended)

#### Silent Loading (Default)
```dart
class DataScreen extends BaseLoadingHookWidget {
  @override
  bool isLoading(WidgetRef ref) {
    return ref.watch(dataProvider).maybeWhen(
      null, // $default parameter
      loading: () => true,
      orElse: () => false,
    );
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.loading;
  
  // No getLoadingMessage() = silent loading (no text)

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('Data')),
      body: YourContent(),
    );
  }
}
```

#### Loading with Message
```dart
class LoginScreen extends BaseLoadingHookWidget {
  @override
  bool isLoading(WidgetRef ref) {
    return ref.watch(authProvider).maybeWhen(
      null,
      loading: () => true,
      orElse: () => false,
    );
  }

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.login;

  @override
  String? getLoadingMessage(WidgetRef ref) => 'Đang đăng nhập...'; // ✅ Show message

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: LoginForm(),
    );
  }
}
```

#### Dynamic Messages
```dart
class SettingsScreen extends BaseLoadingHookWidget {
  @override
  bool isLoading(WidgetRef ref) => ref.watch(settingsProvider).isLoading;

  @override
  LoadingType getLoadingType(WidgetRef ref) => LoadingType.saving;

  @override
  String? getLoadingMessage(WidgetRef ref) {
    final operation = ref.watch(settingsProvider).currentOperation;
    
    switch (operation) {
      case 'saving': return 'Đang lưu cài đặt...';
      case 'syncing': return 'Đang đồng bộ...';
      case 'clearing': return 'Đang xóa cache...';
      default: return null; // Silent loading
    }
  }

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return SettingsForm();
  }
}
```

### 2. Extension Method (Simple Cases)
```dart
class SimpleScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = ref.watch(someProvider).isLoading;
    
    return Scaffold(
      body: YourContent(),
    ).withLoadingOverlay(
      isLoading: isLoading,
      message: 'Processing...', // Optional message
    );
  }
}
```

### 3. LoadingMixin (Custom Control)
```dart
class CustomScreen extends ConsumerWidget with LoadingMixin {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = ref.watch(someProvider).isLoading;
    
    return Scaffold(
      body: Stack(
        children: [
          YourMainContent(),
          buildConfiguredLoadingOverlay(
            isLoading: isLoading,
            type: LoadingType.saving,
            customMessage: 'Custom message...', // Optional
          ),
        ],
      ),
    );
  }
}
```

### 4. Direct Widget Usage
```dart
class DirectUsageScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        YourMainContent(),
        if (isLoading)
          const AdaptiveLoadingOverlay(
            message: 'Custom loading message...',
            backgroundColor: Colors.blue,
            opacity: 0.3,
          ),
      ],
    );
  }
}
```

## 🎨 Customization Options

### AdaptiveLoadingOverlay Parameters
```dart
AdaptiveLoadingOverlay(
  message: 'Custom message',           // Loading text
  backgroundColor: Colors.black,       // Overlay background
  opacity: 0.5,                       // Background opacity (0.0 - 1.0)
  showMessage: true,                   // Show/hide message
  messageStyle: TextStyle(...),       // Custom text style
)
```

### LoadingOverlayConfig
```dart
// Silent loading (DEFAULT)
LoadingOverlayConfig.silent()

// Custom message
LoadingOverlayConfig.withMessage('Custom message...')

// Legacy methods with predefined messages
LoadingOverlayConfig.loginWithMessage()     // "Đang đăng nhập..."
LoadingOverlayConfig.logoutWithMessage()    // "Đang đăng xuất..."
LoadingOverlayConfig.savingWithMessage()    // "Đang lưu..."
LoadingOverlayConfig.deletingWithMessage()  // "Đang xóa..."
LoadingOverlayConfig.loadingWithMessage()   // "Đang tải..."
```

## 📊 Platform-Specific Indicators

### iOS
```dart
// Automatically uses CupertinoActivityIndicator
if (Platform.isIOS) {
  return const CupertinoActivityIndicator(radius: 16);
}
```

### Android
```dart
// Automatically uses CircularProgressIndicator
return CircularProgressIndicator(
  valueColor: AlwaysStoppedAnimation<Color>(
    Theme.of(context).colorScheme.primary,
  ),
);
```

## 🔄 Migration Guide

### From Old Loading System
```dart
// ❌ OLD WAY
Stack(
  children: [
    MainContent(),
    if (isLoading)
      Container(
        color: Colors.black.withOpacity(0.5),
        child: Center(child: CircularProgressIndicator()),
      ),
  ],
)

// ✅ NEW WAY
class MyScreen extends BaseLoadingHookWidget {
  @override
  bool isLoading(WidgetRef ref) => /* loading state */;
  
  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return MainContent(); // Clean & simple!
  }
}
```

### Adding Messages to Existing Screens
```dart
// Want to keep current messages?
@override
String? getLoadingMessage(WidgetRef ref) => 'Your message here';

// Want silent loading?
// Do nothing - it's the default!
```

## 📊 Recommended Usage

| Screen Type | Recommendation | Example |
|-------------|----------------|---------|
| **Data Loading** | ✅ Silent | List refresh, pagination |
| **Quick Actions** | ✅ Silent | Like, favorite, bookmark |
| **Important Operations** | 📝 With Message | Login, payment, upload |
| **Long Operations** | 📝 With Message | Sync, backup, export |
| **Background Tasks** | ✅ Silent | Auto-save, cache update |

## 🎯 Benefits

### ✅ Developer Experience
- **Easy Integration**: Extend BaseLoadingHookWidget
- **Type Safety**: LoadingType enum
- **Consistent API**: Same pattern across all screens
- **No Boilerplate**: Automatic Stack management

### ✅ User Experience
- **Platform Native**: iOS/Android specific indicators
- **Consistent**: Same loading experience across app
- **Professional**: Modern overlay design
- **Accessible**: Clear loading messages when needed
- **Clean**: Minimal UI by default

### ✅ Performance
- **Efficient**: SizedBox.shrink() when not loading
- **Memory Safe**: Automatic cleanup
- **Optimized**: Platform-specific widgets
- **Lightweight**: No text rendering when not needed

## 🎉 Summary

**Key Features:**
- ✅ **Silent by default**: Clean, minimal loading experience
- ✅ **Explicit messaging**: Use `getLoadingMessage()` when needed
- ✅ **Adaptive**: iOS/Android platform-specific indicators
- ✅ **Reusable**: Consistent across all screens
- ✅ **Flexible**: Multiple usage patterns
- ✅ **Type-safe**: Compile-time checking
- ✅ **Professional**: Modern UX patterns

**Migration is simple:**
- **Want messages?** Add `getLoadingMessage()` method
- **Want silent?** Do nothing - it's the default!
- **Want custom control?** Use LoadingMixin or extension methods

This loading system provides a modern, flexible, and consistent loading experience across your entire Flutter application.
