/// Adaptive Loading System
///
/// A comprehensive loading overlay system that provides:
/// - Adaptive design for iOS and Android
/// - Silent loading by default
/// - Multiple usage patterns
/// - Type-safe configuration
///
/// Usage:
/// ```dart
/// // Import everything
/// import 'package:sales_app/presentation/widgets/loading/loading.dart';
///
/// // Use BaseLoadingHookWidget
/// class MyScreen extends BaseLoadingHookWidget {
///   @override
///   bool isLoading(WidgetRef ref) => /* your loading state */;
///
///   @override
///   Widget buildContent(BuildContext context, WidgetRef ref) {
///     return YourContent();
///   }
/// }
/// ```
library loading;

// Core components
export 'adaptive_loading_overlay.dart';
export 'loading_mixin.dart';

// Re-export commonly used Flutter packages for convenience
export 'package:flutter/material.dart';
export 'package:hooks_riverpod/hooks_riverpod.dart';
