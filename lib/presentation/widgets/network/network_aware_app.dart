import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
    // Network widgets barrel export
import 'package:sales_app/presentation/widgets/network/network.dart';

/// Global network-aware app wrapper
/// Provides network monitoring and notifications throughout the entire app
class NetworkAwareApp extends ConsumerStatefulWidget {
  final Widget child;
  final bool showOverlay;
  final bool showNotifications;
  final VoidCallback? onNetworkRetry;

  const NetworkAwareApp({
    super.key,
    required this.child,
    this.showOverlay = true,
    this.showNotifications = true,
    this.onNetworkRetry,
  });

  @override
  ConsumerState<NetworkAwareApp> createState() => _NetworkAwareAppState();
}

class _NetworkAwareAppState extends ConsumerState<NetworkAwareApp>
    with WidgetsBindingObserver {
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // Initialize network monitoring
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeNetworkServices();
    });
    
    AppLogger.info('NetworkAwareApp initialized');
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    NetworkNotificationService.dispose();
    AppLogger.info('NetworkAwareApp disposed');
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    // Check network when app resumes
    if (state == AppLifecycleState.resumed) {
      _checkNetworkOnResume();
    }
  }

  /// Initialize network services
  void _initializeNetworkServices() {
    try {
      // Initialize notification service with current ScaffoldMessenger
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      NetworkNotificationService.initialize(scaffoldMessenger);
      
      AppLogger.info('Network services initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize network services', error: e);
    }
  }

  /// Check network when app resumes from background
  void _checkNetworkOnResume() {
    AppLogger.info('App resumed, checking network status');
    
    // Delay to ensure proper app state
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        ref.read(networkMonitorControllerProvider.notifier).checkConnection();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main app content with network notifications
        widget.showNotifications
            ? widget.child.withNetworkNotifications()
            : widget.child,
        
        // Network warning overlay (only for disconnected state)
        if (widget.showOverlay)
          Consumer(
            builder: (context, ref, child) {
              final networkStatus = ref.watch(networkMonitorControllerProvider);

              // Only show overlay for disconnected state
              // Unstable state will be handled by notifications
              if (networkStatus.isDisconnected) {
                return NetworkWarningOverlay(
                  onRetry: widget.onNetworkRetry,
                );
              }

              return const SizedBox.shrink();
            },
          ),
      ],
    );
  }
}

/// Builder function for easy integration with MaterialApp
class NetworkAwareAppBuilder {
  /// Build network-aware app with default configuration
  static Widget build(BuildContext context, Widget? child) {
    if (child == null) return const SizedBox.shrink();
    
    return NetworkAwareApp(
      child: child,
      onNetworkRetry: () {
        AppLogger.info('Network retry triggered from global overlay');
        // Note: Context will be available in the overlay widget itself
      },
    );
  }

  /// Build network-aware app with custom configuration
  static Widget buildCustom(
    BuildContext context,
    Widget? child, {
    bool showOverlay = true,
    bool showNotifications = true,
    VoidCallback? onNetworkRetry,
  }) {
    if (child == null) return const SizedBox.shrink();
    
    return NetworkAwareApp(
      showOverlay: showOverlay,
      showNotifications: showNotifications,
      onNetworkRetry: onNetworkRetry,
      child: child,
    );
  }
}

/// Extension for easy integration
extension NetworkAwareAppExtension on Widget {
  /// Wrap widget with network awareness
  Widget withNetworkAwareness({
    bool showOverlay = true,
    bool showNotifications = true,
    VoidCallback? onNetworkRetry,
  }) {
    return NetworkAwareApp(
      showOverlay: showOverlay,
      showNotifications: showNotifications,
      onNetworkRetry: onNetworkRetry,
      child: this,
    );
  }
}
