/// Network Monitoring System (Simplified Architecture)
///
/// A clean and efficient network monitoring system that provides:
/// - Real-time network connectivity monitoring
/// - Automatic overlay for disconnected state
/// - SnackBar notifications for reconnection and unstable connections
/// - Global network awareness throughout the app
/// - Stability tracking and unstable connection detection
/// - Simple enum-based architecture without mapping complexity
///
/// ## Features:
/// - **Simplified Architecture**: Direct enum usage, no mapping overhead
/// - **Better Performance**: Enum operations faster than Freezed classes
/// - **Adaptive Design**: Works natively on both iOS and Android
/// - **Smart Detection**: Distinguishes between disconnected and unstable connections
/// - **Non-intrusive**: Overlay only for disconnected, notifications for other states
/// - **Customizable**: Configurable overlay and notification behavior
/// - **Clean Architecture**: Core enum shared across all layers
///
/// ## Usage:
/// ```dart
/// // 1. Wrap your MaterialApp (Already integrated)
/// MaterialApp.router(
///   builder: NetworkAwareAppBuilder.build,
///   // ... other configs
/// )
///
/// // 2. Access network status in widgets
/// class MyWidget extends ConsumerWidget {
///   @override
///   Widget build(BuildContext context, WidgetRef ref) {
///     final networkStatus = ref.watch(networkMonitorControllerProvider);
///
///     // Simple switch statement
///     switch (networkStatus) {
///       case NetworkConnectionStatus.connected:
///         return OnlineContent();
///       case NetworkConnectionStatus.disconnected:
///         return OfflineContent();
///       case NetworkConnectionStatus.unstable:
///         return UnstableWarning();
///       case NetworkConnectionStatus.checking:
///         return LoadingIndicator();
///     }
///   }
/// }
///
/// // 3. Use extension methods
/// if (networkStatus.isConnected) {
///   return OnlineFeatures();
/// }
///
/// // 4. Get localized messages
/// final message = NetworkStateLocalizer.getStatusMessage(context, networkStatus);
/// final icon = networkStatus.statusIcon; // ✅, ❌, ⚠️, 🔄
///
/// // 5. Manual network check
/// ref.read(networkMonitorControllerProvider.notifier).checkConnection();
/// ```
///
/// ## Network Connection Status (Simple Enum):
/// - **Connected**: Normal internet connection
/// - **Disconnected**: No internet connection (shows overlay)
/// - **Unstable**: Frequent disconnections (shows notifications)
/// - **Checking**: Currently checking connection status
///
/// ## Simplified Architecture:
/// - **NetworkConnectionStatus**: Core enum with extension methods
/// - **NetworkMonitorController**: Riverpod controller for state management
/// - **NetworkMonitoringService**: Domain service for business logic
/// - **NetworkWarningOverlay**: Full-screen overlay for disconnected state
/// - **NetworkNotificationService**: SnackBar notifications for status changes
/// - **NetworkAwareApp**: Global wrapper for the entire app
/// - **NetworkStateLocalizer**: Localization helper for UI messages
///
library network;

// Core enum and controller
export 'package:sales_app/core/enums/network_connection_status.dart';
export 'package:sales_app/presentation/controllers/network/network_monitor_controller.dart';

// UI components
export 'network_warning_overlay.dart';
export 'network_notification_service.dart';
export 'network_aware_app.dart';

// Convenience exports for common use cases
export 'package:sales_app/core/enums/network_connection_status.dart'
    show NetworkConnectionStatus, NetworkConnectionStatusExtension;
export 'package:sales_app/presentation/controllers/network/network_monitor_controller.dart'
    show NetworkMonitorController, networkMonitorControllerProvider;
export 'network_aware_app.dart'
    show NetworkAwareAppBuilder, NetworkAwareAppExtension;
export 'network_warning_overlay.dart'
    show NetworkWarningOverlayExtension;
export 'network_notification_service.dart'
    show NetworkNotificationExtension;
