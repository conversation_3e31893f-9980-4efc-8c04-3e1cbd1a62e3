# Network Monitoring System

> **🌐 Simplified & Clean**: <PERSON><PERSON> thống giám sát mạng tự động với architecture đơn giản và hiệu quả

## 📋 Overview

Hệ thống Network Monitoring cung cấp giám sát kết nối mạng real-time với:
- **Overlay tự động** khi mất kết nối
- **SnackBar notifications** cho reconnection và unstable connection
- **Smart detection** phân biệt disconnected vs unstable
- **Global integration** hoạt động trên toàn app
- **Simple enum-based** architecture - không có mapping complexity

## 🏗️ Architecture (Simplified)

```
NetworkMonitorController (Riverpod)
├── NetworkConnectionStatus (Core Enum)
├── NetworkWarningOverlay (UI)
├── NetworkNotificationService (SnackBar)
└── NetworkAwareApp (Global Wrapper)
```

## 🎯 Features

### ✅ Network Connection Status (Simple Enum)
- **Connected**: Kết nối bình thường
- **Disconnected**: M<PERSON>t kết nối (hiển thị overlay)
- **Unstable**: Kết nối không ổn định (hiển thị notification)
- **Checking**: Đang kiểm tra kết nối

### ✅ Smart Detection
- **Stability Tracking**: Theo dõi số lần disconnect trong 2 phút
- **Unstable Threshold**: 3+ disconnections = unstable
- **Auto Recovery**: Tự động reset sau 5 phút stable

### ✅ UI Components
- **Full-screen Overlay**: Cho disconnected state
- **SnackBar Notifications**: Cho reconnection/unstable
- **Adaptive Design**: iOS/Android native indicators
- **Customizable**: Colors, messages, behavior

### ✅ Simplified Architecture
- **No mapping complexity**: Direct enum usage
- **Single source of truth**: NetworkConnectionStatus
- **Better performance**: No object conversion overhead
- **Easier maintenance**: Less boilerplate code

## 🚀 Usage

### 1. Basic Setup (Already Integrated)

```dart
// main.dart - Đã được tích hợp
MaterialApp.router(
  builder: NetworkAwareAppBuilder.build,
  // ... other configs
)
```

### 2. Access Network Status

```dart
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkStatus = ref.watch(networkMonitorControllerProvider);

    // Simple switch or if-else
    switch (networkStatus) {
      case NetworkConnectionStatus.connected:
        return OnlineContent();
      case NetworkConnectionStatus.disconnected:
        return OfflineMessage();
      case NetworkConnectionStatus.unstable:
        return UnstableWarning();
      case NetworkConnectionStatus.checking:
        return CheckingIndicator();
    }

    // Or use extension methods
    if (networkStatus.isConnected) {
      return OnlineContent();
    } else {
      return OfflineContent();
    }
  }
}
```

### 3. Manual Network Check

```dart
// Trigger manual check
ref.read(networkMonitorControllerProvider.notifier).checkConnection();

// Force connected state (for testing)
ref.read(networkMonitorControllerProvider.notifier).forceConnected();
```

### 4. Localized Messages

```dart
// Get localized status messages
final message = NetworkStateLocalizer.getStatusMessage(context, networkStatus);
final description = NetworkStateLocalizer.getDescription(context, networkStatus);
final retryText = NetworkStateLocalizer.getRetryButtonText(context, networkStatus);

// Use built-in status icon
final icon = networkStatus.statusIcon; // ✅, ❌, ⚠️, 🔄
```

### 5. Custom Integration

```dart
// Custom wrapper with specific config
MaterialApp.router(
  builder: (context, child) => NetworkAwareAppBuilder.buildCustom(
    context,
    child,
    showOverlay: true,        // Show overlay for disconnected
    showNotifications: true,  // Show SnackBar notifications
    onNetworkRetry: () {
      // Custom retry logic
    },
  ),
)

// Widget-level integration
Widget myWidget = MyContent().withNetworkAwareness(
  showOverlay: false,  // Disable overlay for this widget
);
```

## 📱 UI Behavior

### Disconnected State
- **Full-screen overlay** với retry button
- **Blocking interaction** until reconnected
- **"Continue" button** để force bypass (emergency)

### Unstable State  
- **SnackBar warning** không block UI
- **Background monitoring** tiếp tục
- **Auto-clear** sau 5 phút stable

### Reconnection
- **Success SnackBar** với green color
- **Auto-hide overlay** nếu đang hiển thị
- **Reset stability tracking**

## 🔧 Configuration

### Network Detection Settings

```dart
// In NetworkMonitoringConfig (Domain Layer)
static const NetworkMonitoringConfig defaultConfig = NetworkMonitoringConfig(
  unstableThreshold: 3,                           // 3 disconnections
  unstableWindow: Duration(minutes: 2),           // in 2 minutes
  checkDelay: Duration(seconds: 2),               // Check delay
  stabilizationDuration: Duration(minutes: 5),   // Clear unstable after
);
```

### UI Customization

```dart
// Custom overlay colors
NetworkWarningOverlay(
  backgroundColor: Colors.red,
  opacity: 0.9,
  messageStyle: TextStyle(fontSize: 18),
  onRetry: () => customRetryLogic(),
)

// Custom notification service
NetworkNotificationService.initialize(scaffoldMessenger);
```

## 🧪 Testing

### Manual Testing

```dart
// Access current network status
final networkStatus = ref.read(networkMonitorControllerProvider);
print('Current status: $networkStatus'); // NetworkConnectionStatus.connected

// Manual connection check
await ref.read(networkMonitorControllerProvider.notifier).checkConnection();

// Force connected state (for testing/emergency)
ref.read(networkMonitorControllerProvider.notifier).forceConnected();

// Test with different statuses
switch (networkStatus) {
  case NetworkConnectionStatus.connected:
    print('Online features available');
  case NetworkConnectionStatus.disconnected:
    print('Show offline UI');
  case NetworkConnectionStatus.unstable:
    print('Show warning message');
  case NetworkConnectionStatus.checking:
    print('Show loading indicator');
}
```

### Integration Testing

```dart
testWidgets('Network overlay shows on disconnection', (tester) async {
  // Setup
  await tester.pumpWidget(MyApp());

  // Mock network status change
  final container = ProviderScope.containerOf(tester.element(find.byType(MyApp)));

  // Simulate disconnection by updating the provider state
  // (This would typically be done through the domain service)

  await tester.pump();

  // Verify overlay is shown for disconnected status
  expect(find.byType(NetworkWarningOverlay), findsOneWidget);

  // Verify localized text is shown
  expect(find.text('Mất kết nối mạng'), findsOneWidget);
});

// Test enum properties
test('NetworkConnectionStatus should have correct properties', () {
  expect(NetworkConnectionStatus.connected.isConnected, true);
  expect(NetworkConnectionStatus.disconnected.isDisconnected, true);
  expect(NetworkConnectionStatus.unstable.isUnstable, true);
  expect(NetworkConnectionStatus.checking.isChecking, true);

  expect(NetworkConnectionStatus.connected.statusIcon, '✅');
  expect(NetworkConnectionStatus.disconnected.statusIcon, '❌');
});
```

## 📊 Performance

### Resource Usage
- **Minimal CPU**: Chỉ listen connectivity stream
- **Low Memory**: Simple enum-based state management
- **Efficient UI**: Conditional rendering, no unnecessary rebuilds
- **No Mapping Overhead**: Direct enum usage, no object conversion
- **Better Performance**: Enum operations faster than Freezed classes

### Battery Impact
- **Optimized**: Sử dụng system connectivity APIs
- **Smart Polling**: Không poll liên tục
- **Background Aware**: Pause monitoring khi app background

## 🔍 Debugging

### Logging
```dart
// Enable detailed logging
AppLogger.info('Network state changed', data: {
  'previous': previousState.toString(),
  'current': currentState.toString(),
  'timestamp': DateTime.now().toIso8601String(),
});
```

### Debug Tools
```dart
// Check current status
final status = ref.read(networkMonitorControllerProvider);
print('Current network status: $status'); // NetworkConnectionStatus.connected

// Check status properties
print('Is connected: ${status.isConnected}');
print('Status icon: ${status.statusIcon}');

// Manual status changes (for testing)
ref.read(networkMonitorControllerProvider.notifier).forceConnected();
ref.read(networkMonitorControllerProvider.notifier).checkConnection();
```

## 🚨 Error Handling

### Graceful Degradation
- **Service unavailable**: Fallback to manual check
- **Permission denied**: Show appropriate message
- **Stream errors**: Auto-retry with exponential backoff

### Error Recovery
```dart
// Auto-recovery on stream errors
_connectivity.onConnectivityChanged.listen(
  (result) => _handleConnectivityChange(result),
  onError: (error) {
    AppLogger.error('Connectivity stream error', error: error);
    // Fallback to manual checking
    _startManualChecking();
  },
);
```

## 📈 Future Enhancements

### Planned Features
- [ ] **Connection Quality**: Measure speed/latency
- [ ] **Offline Mode**: Cache management integration  
- [ ] **Custom Retry Logic**: Per-screen retry strategies
- [ ] **Analytics**: Network usage tracking
- [ ] **Bandwidth Detection**: Adapt UI based on connection speed

### Integration Points
- [ ] **API Interceptors**: Auto-retry on network errors
- [ ] **Cache System**: Offline data management
- [ ] **Push Notifications**: Network-aware delivery
- [ ] **Background Sync**: Queue operations for later
