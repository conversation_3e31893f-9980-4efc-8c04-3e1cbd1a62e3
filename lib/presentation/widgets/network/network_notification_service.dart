import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Presentation utils barrel export
import 'package:sales_app/presentation/utils/utils.dart';

import 'network.dart';

/// Service for showing network-related notifications via SnackBar
/// Handles reconnection notifications and unstable connection warnings
class NetworkNotificationService {
  static ScaffoldMessengerState? _scaffoldMessenger;

  /// Initialize the notification service with ScaffoldMessenger
  static void initialize(ScaffoldMessengerState scaffoldMessenger) {
    _scaffoldMessenger = scaffoldMessenger;
    AppLogger.info('Network notification service initialized');
  }

  /// Handle network connection status changes and show appropriate notifications
  static void handleNetworkStatusChange(
    BuildContext context,
    NetworkConnectionStatus previousStatus,
    NetworkConnectionStatus currentStatus,
  ) {
    if (_scaffoldMessenger == null) {
      AppLogger.warning('ScaffoldMessenger not initialized for network notifications');
      return;
    }

    AppLogger.info('Handling network status change', data: {
      'previous': previousStatus.toString(),
      'current': currentStatus.toString(),
    });

    // Hide any existing network-related snackbars
    _hideCurrentNetworkSnackBar();

    // Handle specific status transitions
    if (previousStatus.isDisconnected && currentStatus.isConnected) {
      _showReconnectionNotification(context);
    } else if (previousStatus.isDisconnected && currentStatus.isUnstable) {
      _showUnstableConnectionNotification(context);
    } else if (currentStatus.isUnstable && !previousStatus.isUnstable) {
      _showUnstableConnectionWarning(context);
    }
  }

  /// Show reconnection success notification
  static void _showReconnectionNotification(BuildContext context) {
    _scaffoldMessenger?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.wifi,
              color: Colors.white,
              size: 20,
            ),
            SizedBox(width: AppDimens.spacingSM),
            Expanded(
              child: Text(
                NetworkStateLocalizer.getReconnectionMessage(context),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
        shape: RoundedRectangleBorder(
          borderRadius: AppDimens.borderRadius8,
        ),
        margin: EdgeInsets.all(AppDimens.spacingMD),
      ),
    );

    AppLogger.info('Showed reconnection notification');
  }

  /// Show unstable connection notification (when reconnected but unstable)
  static void _showUnstableConnectionNotification(BuildContext context) {
    _scaffoldMessenger?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.signal_wifi_bad,
              color: Colors.white,
              size: 20,
            ),
            SizedBox(width: AppDimens.spacingSM),
            Expanded(
              child: Text(
                NetworkStateLocalizer.getUnstableWarningMessage(context),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.warning,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
        shape: RoundedRectangleBorder(
          borderRadius: AppDimens.borderRadius8,
        ),
        margin: EdgeInsets.all(AppDimens.spacingMD),
        action: SnackBarAction(
          label: 'Kiểm tra',
          textColor: Colors.white,
          onPressed: () {
            // This will be handled by the global network listener
            AppLogger.info('User requested network check from notification');
          },
        ),
      ),
    );

    AppLogger.info('Showed unstable connection notification');
  }

  /// Show unstable connection warning (when connection becomes unstable)
  static void _showUnstableConnectionWarning(BuildContext context) {
    _scaffoldMessenger?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.warning,
              color: Colors.white,
              size: 20,
            ),
            SizedBox(width: AppDimens.spacingSM),
            Expanded(
              child: Text(
                NetworkStateLocalizer.getUnstableDescriptionMessage(context),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.warning,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 5),
        shape: RoundedRectangleBorder(
          borderRadius: AppDimens.borderRadius8,
        ),
        margin: EdgeInsets.all(AppDimens.spacingMD),
      ),
    );

    AppLogger.info('Showed unstable connection warning');
  }

  /// Hide current network-related snackbar
  static void _hideCurrentNetworkSnackBar() {
    _scaffoldMessenger?.hideCurrentSnackBar();
  }

  /// Show manual retry notification
  static void showRetryNotification(BuildContext context) {
    if (_scaffoldMessenger == null) return;

    _scaffoldMessenger?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.refresh,
              color: Colors.white,
              size: 20,
            ),
            SizedBox(width: AppDimens.spacingSM),
            Expanded(
              child: Text(
                NetworkStateLocalizer.getCheckingConnectionMessage(context),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.primaryColor,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
        shape: RoundedRectangleBorder(
          borderRadius: AppDimens.borderRadius8,
        ),
        margin: EdgeInsets.all(AppDimens.spacingMD),
      ),
    );

    AppLogger.info('Showed retry notification');
  }

  /// Dispose the service
  static void dispose() {
    _scaffoldMessenger = null;
    AppLogger.info('Network notification service disposed');
  }
}

/// Widget that listens to network state changes and shows notifications
class NetworkNotificationListener extends ConsumerWidget {
  final Widget child;

  const NetworkNotificationListener({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to network connection status changes
    ref.listen<NetworkConnectionStatus>(
      networkMonitorControllerProvider,
      (previous, next) {
        if (previous != null) {
          NetworkNotificationService.handleNetworkStatusChange(context, previous, next);
        }
      },
    );

    return child;
  }
}

/// Extension to easily add network notifications to any widget
extension NetworkNotificationExtension on Widget {
  /// Wrap widget with network notification listener
  Widget withNetworkNotifications() {
    return NetworkNotificationListener(child: this);
  }
}
