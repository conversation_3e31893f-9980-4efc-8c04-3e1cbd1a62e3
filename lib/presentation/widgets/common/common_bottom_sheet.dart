import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/di/injection.dart';
import 'package:sales_app/presentation/screens/image_capture/document_camera_screen.dart';

/// Common bottom sheet widgets để tái sử dụng
class CommonBottomSheet {
  /// Hi<PERSON>n thị bottom sheet chọn hình thức upload
  static void showUploadOptions({
    required BuildContext context,
    required Function(List<String>) onImagesSelected,
    int maxImages = 10,
  }) {
    final filePickerService = getIt<FilePickerService>();
    
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimens.borderRadius16.topLeft.x),
        ),
      ),
      builder: (context) => Container(
        padding: AppDimens.paddingAllLg,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.lightGray,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            AppDimens.h16,
            
            Text(
              'Chọn hình thức tải lên',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppDimens.h16,
            
            // Chụp ảnh
            ListTile(
              leading: const Icon(Icons.camera_alt, color: AppColors.primaryColor),
              title: const Text('Chụp ảnh'),
              subtitle: const Text('Mở camera để chụp nhiều ảnh'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => DocumentCameraScreen(
                      title: 'Chụp chứng từ',
                      maxImages: maxImages,
                      onImagesSelected: onImagesSelected,
                    ),
                  ),
                );
              },
            ),
            
            // Chọn ảnh từ thư viện
            ListTile(
              leading: const Icon(Icons.photo_library, color: AppColors.primaryColor),
              title: const Text('Chọn ảnh trong thư viện'),
              subtitle: const Text('Chọn nhiều ảnh từ thư viện'),
              onTap: () async {
                Navigator.pop(context);
                
                final imagePaths = await filePickerService.pickImages(
                  maxSizeInMB: 5,
                  allowMultiple: true,
                );
                
                if (imagePaths != null && imagePaths.isNotEmpty) {
                  onImagesSelected(imagePaths);
                }
              },
            ),
            
            // Chọn tệp tin
            ListTile(
              leading: const Icon(Icons.attach_file, color: AppColors.primaryColor),
              title: const Text('Chọn tệp tin'),
              subtitle: const Text('PNG, JPG, PDF (tối đa 5MB)'),
              onTap: () async {
                Navigator.pop(context);
                
                final filePaths = await filePickerService.pickDocuments(
                  maxSizeInMB: 5,
                  allowMultiple: true,
                );
                
                if (filePaths != null && filePaths.isNotEmpty) {
                  onImagesSelected(filePaths);
                }
              },
            ),
            
            AppDimens.h8,
            
            // Hủy bỏ
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Hủy bỏ'),
            ),
          ],
        ),
      ),
    );
  }

  /// Hiển thị bottom sheet chọn options
  static void showOptionsBottomSheet({
    required BuildContext context,
    required String title,
    required List<BottomSheetOption> options,
  }) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimens.borderRadius16.topLeft.x),
        ),
      ),
      builder: (context) => Container(
        padding: AppDimens.paddingAllLg,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.lightGray,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            AppDimens.h16,
            
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppDimens.h16,
            
            // Options
            ...options.map((option) => ListTile(
              leading: option.icon != null 
                ? Icon(option.icon, color: option.iconColor ?? AppColors.primaryColor)
                : null,
              title: Text(option.title),
              subtitle: option.subtitle != null ? Text(option.subtitle!) : null,
              onTap: () {
                Navigator.pop(context);
                option.onTap();
              },
            )),
            
            AppDimens.h8,
            
            // Hủy bỏ
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Hủy bỏ'),
            ),
          ],
        ),
      ),
    );
  }

  /// Hiển thị bottom sheet thông tin
  static void showInfoBottomSheet({
    required BuildContext context,
    required String title,
    required Widget content,
    List<Widget>? actions,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimens.borderRadius16.topLeft.x),
        ),
      ),
      builder: (context) => Container(
        padding: AppDimens.paddingAllLg,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.lightGray,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            AppDimens.h16,
            
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppDimens.h16,
            
            // Content
            Flexible(child: content),
            
            // Actions
            if (actions != null) ...[
              AppDimens.h16,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: actions,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Model cho bottom sheet option
class BottomSheetOption {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Color? iconColor;
  final VoidCallback onTap;

  BottomSheetOption({
    required this.title,
    this.subtitle,
    this.icon,
    this.iconColor,
    required this.onTap,
  });
}
