import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
import 'package:sales_app/generated/l10n.dart';

enum DropdownState {
  loading,
  empty,
  normal,
}

class CommonDropdown<T> extends HookWidget {
  final List<T> items;
  final T? value;
  final String? label;
  final String? hint;
  final bool enabled;
  final Widget? prefixIcon;
  final Widget? trailingIcon;
  final bool isRequired;
  final String Function(T item)? labelBuilder;
  final Widget Function(T item)? itemBuilder;
  final Widget Function(T selected)? selectedItemBuilder;
  final void Function(T)? onChanged;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? labelColor;
  final Color? iconColor;
  final Color? borderColor;
  final bool isDense;
  final String? Function(T?)? validator;
  final String? errorText;

  // Replace boolean flags with DropdownState
  final DropdownState state;
  final String? emptyMessage;
  final VoidCallback? onRetry;

  const CommonDropdown({
    super.key,
    required this.items,
    this.value,
    this.label,
    this.hint,
    this.enabled = true,
    this.prefixIcon,
    this.trailingIcon,
    this.isRequired = false,
    this.labelBuilder,
    this.itemBuilder,
    this.selectedItemBuilder,
    this.onChanged,
    this.backgroundColor,
    this.textColor,
    this.labelColor,
    this.iconColor,
    this.borderColor,
    this.isDense = false,
    this.validator,
    this.errorText,
    this.state = DropdownState.normal,
    this.emptyMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    switch (state) {
      case DropdownState.loading:
        return const Center(
          child: SizedBox(
            width: 32,
            height: 32,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        );
      case DropdownState.empty:
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surfaceColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.grey300),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: AppDimens.spacingSM),
                  Expanded(
                    child: Text(
                      emptyMessage ?? '',
                      style: TextStyle(
                        fontSize: AppDimens.fontMD,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
              AppDimens.h12,
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: onRetry ?? () {},
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primaryColor,
                    side: const BorderSide(color: AppColors.primaryColor),
                    padding: EdgeInsets.symmetric(vertical: AppDimens.spacingSM),
                  ),
                  child: Text(S.of(context).try_again),
                ),
              ),
            ],
          ),
        );
      case DropdownState.normal:
        break;
    }

    final hasFocus = useState(false);
    final focusNode = useFocusNode();

    final defaultTextColor = textColor ?? AppColors.textPrimary;
    final defaultLabelColor = labelColor ?? AppColors.textSecondary;
    final defaultIconColor =
        iconColor ?? AppColors.primaryColor.withValues(alpha: 0.7);
    final defaultBorderColor = borderColor ?? AppColors.primaryColor;

    // Listen to focus changes
    useEffect(() {
      void onFocusChange() {
        hasFocus.value = focusNode.hasFocus;
      }

      focusNode.addListener(onFocusChange);
      return () => focusNode.removeListener(onFocusChange);
    }, [focusNode]);

    String getItemLabel(T item) {
      if (labelBuilder != null) {
        return labelBuilder!(item);
      }
      return item.toString();
    }

    Widget buildSelectedItem(T item) {
      if (selectedItemBuilder != null) {
        return selectedItemBuilder!(item);
      }
      return Text(
        getItemLabel(item),
        style: theme.textTheme.bodyLarge?.copyWith(
          color: defaultTextColor,
          fontSize: AppDimens.fontLG,
        ),
      );
    }

    Widget buildItem(T item) {
      if (itemBuilder != null) {
        return itemBuilder!(item);
      }
      return Padding(
        padding: EdgeInsets.symmetric(
          vertical: AppDimens.spacingMD,
          horizontal: AppDimens.spacingLG,
        ),
        child: Text(
          getItemLabel(item),
          style: theme.textTheme.bodyLarge?.copyWith(
            color: defaultTextColor,
          ),
        ),
      );
    }

    Future<void> showDropdown() async {
      if (!enabled) return;

      focusNode.requestFocus();

      await showModalBottomSheet(
        context: context,
        backgroundColor: backgroundColor ?? theme.colorScheme.surface,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(16),
          ),
        ),
        builder: (context) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: AppDimens.paddingVerticalMd,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: theme.dividerColor,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(width: 48),
                    Text(
                      label ?? '',
                      style: theme.textTheme.titleMedium,
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: items.length,
                  itemBuilder: (context, index) {
                    final item = items[index];
                    final isSelected = value == item;
                    return Column(
                      children: [
                        InkWell(
                          onTap: () {
                            Navigator.pop(context);
                            onChanged?.call(item);
                          },
                          child: Container(
                            padding: AppDimens.paddingVerticalMd,
                            color: isSelected
                                ? defaultBorderColor.withValues(alpha: 0.1)
                                : null,
                            child: Row(
                              children: [
                                Expanded(child: buildItem(item)),
                                if (isSelected)
                                  Padding(
                                    padding: AppDimens.paddingHorizontalMd,
                                    child: Icon(
                                      AppIcons.check,
                                      color: AppColors.primaryColor,
                                      size: AppDimens.iconMD,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                        if (index < items.length - 1)
                          Divider(
                            height: 0,
                            thickness: 0.5,
                            color: theme.dividerColor,
                            indent: AppDimens.spacingLG,
                            endIndent: AppDimens.spacingLG,
                          ),
                      ],
                    );
                  },
                ),
              ),
            ],
          );
        },
      );

      focusNode.unfocus();
    }

    return FormField<T>(
      validator: validator,
      builder: (formState) {
        final error = formState.errorText;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: showDropdown,
              child: Container(
                decoration: BoxDecoration(
                  color: backgroundColor ??
                      (hasFocus.value
                          ? defaultBorderColor.withValues(alpha: 0.05)
                          : AppColors.backgroundColor),
                  borderRadius: AppDimens.inputBorderRadius,
                  border: Border.all(
                    color: (error != null && error.isNotEmpty)
                        ? theme.colorScheme.error
                        : hasFocus.value
                        ? defaultBorderColor
                        : defaultBorderColor.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                        left: isDense ? AppDimens.spacingLG : AppDimens.spacingXL,
                        right: isDense ? AppDimens.spacingLG : AppDimens.spacingXL,
                        top: isDense ? AppDimens.spacingSM : AppDimens.spacingMD,
                        bottom: isDense ? AppDimens.spacingSM : AppDimens.spacingMD,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          if (prefixIcon != null) ...[
                            IconTheme(
                              data: IconThemeData(
                                color: defaultIconColor,
                                size: AppDimens.iconMD,
                              ),
                              child: prefixIcon!,
                            ),
                            AppDimens.w8,
                          ],
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                if (label != null) ...[
                                  Padding(
                                    padding: EdgeInsets.only(bottom: AppDimens.spacingXS),
                                    child: Text(
                                      label!,
                                      style: theme.inputDecorationTheme.labelStyle?.copyWith(
                                        fontSize: AppDimens.fontSM,
                                        color: value != null
                                            ? (hasFocus.value
                                            ? defaultBorderColor
                                            : (theme.inputDecorationTheme.labelStyle?.color ?? defaultLabelColor))
                                            : (theme.inputDecorationTheme.hintStyle?.color ?? defaultLabelColor.withValues(alpha: 0.5)),
                                      ),
                                    ),
                                  ),
                                ],
                                value != null
                                    ? Padding(
                                  padding: EdgeInsets.only(top: AppDimens.spacingXXS),
                                  child: buildSelectedItem(value as T),
                                )
                                    : Padding(
                                  padding: EdgeInsets.only(top: AppDimens.spacingXXS),
                                  child: Text(
                                    hint ?? '',
                                    style: theme.inputDecorationTheme.hintStyle?.copyWith(
                                      fontSize: AppDimens.fontLG,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconTheme(
                            data: IconThemeData(
                              color: defaultIconColor,
                              size: AppDimens.iconMD,
                            ),
                            child: trailingIcon ?? const Icon(AppIcons.chevronDown),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (error != null && error.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only( top: 4),
                child: Text(
                  error,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.error,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
