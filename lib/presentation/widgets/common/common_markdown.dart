import 'package:flutter/material.dart';
import 'package:markdown_widget/markdown_widget.dart';
import 'package:sales_app/core/core.dart';


/// Common Markdown Widget - Tái sử dụng trong toàn app
/// Hỗ trợ custom style, image, và responsive design
class CommonMarkdown extends StatelessWidget {
  final String data;
  final EdgeInsetsGeometry? padding;
  final BoxDecoration? decoration;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final VoidCallback? onTap;
  final bool showScrollbar;

  const CommonMarkdown({
    super.key,
    required this.data,
    this.padding,
    this.decoration,
    this.shrinkWrap = true,
    this.physics,
    this.onTap,
    this.showScrollbar = false,
  });

  @override
  Widget build(BuildContext context) {
    // Set màu text và header
    const pinkColor = AppColors.textPrimary;
    final config = MarkdownConfig(
      configs: [
        const PConfig(
          textStyle: TextStyle(color: pinkColor, fontSize: 14),

        ),
      ],
    );

    Widget markdownWidget = MarkdownWidget(
      data: data,
      shrinkWrap: shrinkWrap,
      physics: physics,
      config: config,
    );

    // Wrap with GestureDetector if onTap is provided
    if (onTap != null) {
      markdownWidget = GestureDetector(
        onTap: onTap,
        child: markdownWidget,
      );
    }

    // Wrap with Container if decoration or padding is provided
    if (decoration != null || padding != null) {
      markdownWidget = Container(
        padding: padding,
        decoration: decoration,
        child: markdownWidget,
      );
    }

    // Wrap with Scrollbar if needed
    if (showScrollbar) {
      markdownWidget = Scrollbar(
        child: markdownWidget,
      );
    }

    return markdownWidget;
  }
}

/// Predefined markdown configurations for common use cases
class MarkdownConfigs {
  /// Default markdown with card style
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: AppColors.backgroundColor,
    borderRadius: AppDimens.borderRadius24,
    border: Border.all(color: AppColors.primaryColor.withValues(alpha: 0.1)),
    boxShadow: [
      BoxShadow(
        color: AppColors.primaryColor.withValues(alpha: 0.03),
        blurRadius: AppDimens.blurRadiusXL,
        offset: AppDimens.offsetMedium,
      ),
    ],
  );

  /// Compact markdown style
  static BoxDecoration get compactDecoration => BoxDecoration(
    color: AppColors.backgroundColor,
    borderRadius: AppDimens.borderRadius16,
    border: Border.all(color: AppColors.textSecondary.withValues(alpha: 0.1)),
  );

  /// Minimal markdown style (no decoration)
  static BoxDecoration? get minimalDecoration => null;

  /// Article content style
  static BoxDecoration get articleDecoration => BoxDecoration(
    color: AppColors.backgroundColor,
    borderRadius: AppDimens.borderRadius24,
    border: Border.all(color: AppColors.primaryColor.withValues(alpha: 0.08)),
  );
}

/// Extension methods for easy markdown usage
extension MarkdownExtensions on String {
  /// Convert string to markdown widget with default card style
  Widget toMarkdownCard({
    Key? key,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    bool shrinkWrap = true,
  }) {
    return CommonMarkdown(
      key: key,
      data: this,
      padding: padding ?? AppDimens.paddingAllXl,
      decoration: MarkdownConfigs.cardDecoration,
      onTap: onTap,
      shrinkWrap: shrinkWrap,
    );
  }

  /// Convert string to markdown widget with compact style
  Widget toMarkdownCompact({
    Key? key,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    bool shrinkWrap = true,
  }) {
    return CommonMarkdown(
      key: key,
      data: this,
      padding: padding ?? AppDimens.paddingAllLg,
      decoration: MarkdownConfigs.compactDecoration,
      onTap: onTap,
      shrinkWrap: shrinkWrap,
    );
  }

  /// Convert string to markdown widget with minimal style
  Widget toMarkdownMinimal({
    Key? key,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    bool shrinkWrap = true,
  }) {
    return CommonMarkdown(
      key: key,
      data: this,
      padding: padding,
      decoration: MarkdownConfigs.minimalDecoration,
      onTap: onTap,
      shrinkWrap: shrinkWrap,
    );
  }
} 