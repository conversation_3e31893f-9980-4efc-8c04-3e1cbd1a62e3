import 'package:flutter/material.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';
// Presentation barrel exports
import 'package:sales_app/presentation/widgets/widgets.dart';

/// Màn hình test permission
class PermissionTestScreen extends StatelessWidget {
  const PermissionTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Permission Test'),
      ),
      body: Padding(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Test PermissionExample
            Card(
              child: Padding(
                padding: AppDimens.paddingAllMd,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Permission Example (Riverpod)',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    AppDimens.h8,
                    const Text('Test với PermissionController và Riverpod'),
                    AppDimens.h8,
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const PermissionExample(),
                          ),
                        );
                      },
                      child: const Text('Mở Permission Example'),
                    ),
                  ],
                ),
              ),
            ),
            
            AppDimens.h16,
            
            // Test PermissionDebug
            Card(
              child: Padding(
                padding: AppDimens.paddingAllMd,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Permission Debug (Direct)',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    AppDimens.h8,
                    const Text('Test trực tiếp với permission_handler'),
                    AppDimens.h8,
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const PermissionDebug(),
                          ),
                        );
                      },
                      child: const Text('Mở Permission Debug'),
                    ),
                  ],
                ),
              ),
            ),
            
            AppDimens.h16,
            
            // Info
            Card(
              child: Padding(
                padding: AppDimens.paddingAllMd,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Hướng dẫn Debug',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    AppDimens.h8,
                    const Text('1. Mở Permission Debug để test trực tiếp'),
                    const Text('2. Kiểm tra log console khi request permission'),
                    const Text('3. Đảm bảo test trên thiết bị thật, không phải simulator'),
                    const Text('4. Kiểm tra manifest/plist đã có đủ quyền'),
                    AppDimens.h8,
                    Text(
                      'Platform: ${Theme.of(context).platform.name}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 