/// CommonCheckbox - Checkbox tuỳ biến chuẩn dự án
///
/// <PERSON><PERSON> dụng cho các tr<PERSON>ờng hợp cần checkbox với label, trạng thái enabled/disabled, màu sắc tuỳ chỉnh.
///
/// Ví dụ:
/// ```dart
/// CommonCheckbox(
///   label: S.of(context).accept_terms,
///   value: isChecked,
///   onChanged: (v) => setState(() => isChecked = v ?? false),
/// )
/// ```
library common_checkbox;

import 'package:flutter/material.dart';
// Core barrel export
import 'package:sales_app/core/core.dart';

class CommonCheckbox extends StatelessWidget {
  final String? label;
  final bool value;
  final ValueChanged<bool?>? onChanged;
  final bool enabled;
  final Color? activeColor;
  final Color? checkColor;
  final Color? textColor;
  final TextStyle? textStyle;

  const CommonCheckbox({
    super.key,
    this.label,
    required this.value,
    this.onChanged,
    this.enabled = true,
    this.activeColor,
    this.checkColor,
    this.textColor,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: enabled ? () => onChanged?.call(!value) : null,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // Căn giữa checkbox và text
          children: [
            Checkbox(
              value: value,
              onChanged: enabled ? onChanged : null,
              activeColor: activeColor ?? AppColors.primaryColor,
              checkColor: checkColor ?? Colors.white,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
            ),
            if (label != null) ...[
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label!,
                  style: textStyle ?? theme.textTheme.bodyMedium?.copyWith(
                    color: enabled 
                        ? (textColor ?? AppColors.textPrimary)
                        : AppColors.textSecondary.withValues(alpha: 0.5),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
