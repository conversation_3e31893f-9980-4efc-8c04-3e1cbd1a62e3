import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';
// Presentation barrel exports
import 'package:sales_app/presentation/controllers/controllers.dart';
import 'package:sales_app/presentation/widgets/widgets.dart';
// Permission state
import 'package:sales_app/presentation/controllers/permission/permission_state.dart';

/// Ví dụ sử dụng permission system
class PermissionExample extends ConsumerWidget {
  const PermissionExample({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final permissionState = ref.watch(permissionControllerProvider);
    final permissionController = ref.read(permissionControllerProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Permission Example'),
      ),
      body: Padding(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Hi<PERSON>n thị trạng thái hiện tại
            _buildStatusCard(context, permissionState),
            
            AppDimens.h16,
            
            // Các button để test permission
            _buildPermissionButtons(context, ref, permissionController),
            
            AppDimens.h16,
            
            // Button test feature permission
            _buildFeaturePermissionButton(context, permissionController),
          ],
        ),
      ),
    );
  }

  /// Widget hiển thị trạng thái permission
  Widget _buildStatusCard(BuildContext context, PermissionState state) {
    return Card(
      child: Padding(
        padding: AppDimens.paddingAllMd,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trạng thái Permission',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppDimens.h8,
            state.when(
              initial: () => const Text('Chưa kiểm tra'),
              loading: () => const Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('Đang kiểm tra...'),
                ],
              ),
              checked: (permissions) => _buildPermissionsList(context, permissions),
              requested: (permissions) => _buildPermissionsList(context, permissions),
              error: (message) => Text(
                'Lỗi: $message',
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Widget hiển thị danh sách permissions
  Widget _buildPermissionsList(BuildContext context, Map<PermissionType, PermissionStatus> permissions) {
    if (permissions.isEmpty) {
      return const Text('Không có permission nào');
    }

    return Column(
      children: permissions.entries.map((entry) {
        final permissionType = entry.key;
        final status = entry.value;
        
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _getStatusColor(status).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getStatusColor(status).withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                _getPermissionIcon(permissionType),
                color: _getStatusColor(status),
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      permissionType.displayName,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    Text(
                      PermissionUtils.getStatusDisplayName(status),
                      style: TextStyle(
                        color: _getStatusColor(status),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                _getStatusIcon(status),
                color: _getStatusColor(status),
                size: 16,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// Widget hiển thị các button test permission
  Widget _buildPermissionButtons(BuildContext context, WidgetRef ref, PermissionController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        CommonButton(
          title: 'Kiểm tra Camera',
          onPressed: () => controller.checkPermission(PermissionType.camera),
        ),
        AppDimens.h8,
        CommonButton(
          title: 'Yêu cầu Camera (Smart)',
          onPressed: () => _requestPermissionSmart(context, ref, controller, PermissionType.camera),
        ),
        AppDimens.h8,
        CommonButton(
          title: 'Kiểm tra Thư viện ảnh',
          onPressed: () => controller.checkPermission(PermissionType.photos),
        ),
        AppDimens.h8,
        CommonButton(
          title: 'Yêu cầu Thư viện ảnh (Smart)',
          onPressed: () => _requestPermissionSmart(context, ref, controller, PermissionType.photos),
        ),
        AppDimens.h8,
        CommonButton(
          title: 'Kiểm tra nhiều Permission',
          onPressed: () => controller.checkPermissions([
            PermissionType.camera,
            PermissionType.photos,
            PermissionType.microphone,
          ]),
        ),
        AppDimens.h8,
        CommonButton(
          title: 'Yêu cầu nhiều Permission (Smart)',
          onPressed: () => _requestMultiplePermissionsSmart(context, ref, controller, [
            PermissionType.camera,
            PermissionType.photos,
            PermissionType.microphone,
          ]),
        ),
        AppDimens.h8,
        CommonButton(
          title: 'Mở cài đặt App',
          onPressed: () => controller.openSystemAppSettings(),
        ),
      ],
    );
  }

  /// Widget hiển thị button test feature permission
  Widget _buildFeaturePermissionButton(BuildContext context, PermissionController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        CommonButton(
          title: 'Test QR Scan Permission',
          onPressed: () => _testFeaturePermission(context, controller, 'qr_scan'),
        ),
        AppDimens.h8,
        CommonButton(
          title: 'Test Identity Verification Permission',
          onPressed: () => _testFeaturePermission(context, controller, 'identity_verification'),
        ),
        AppDimens.h8,
        CommonButton(
          title: 'Test Camera Permission',
          onPressed: () => _testFeaturePermission(context, controller, 'camera'),
        ),
        AppDimens.h8,
        CommonButton(
          title: 'Test Gallery Permission',
          onPressed: () => _testFeaturePermission(context, controller, 'gallery'),
        ),
      ],
    );
  }

  /// Test feature permission
  Future<void> _testFeaturePermission(
    BuildContext context,
    PermissionController controller,
    String feature,
  ) async {
    final result = await controller.handleFeaturePermissions(feature);

    result.when(
      granted: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã cấp quyền cho tính năng: $feature'),
            backgroundColor: Colors.green,
          ),
        );
      },
      denied: (permissions, featureName) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chưa cấp quyền cho tính năng: $featureName'),
            backgroundColor: Colors.orange,
          ),
        );
      },
      permanentlyDenied: (permissions, featureName) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Quyền bị từ chối vĩnh viễn cho tính năng: $featureName'),
            backgroundColor: Colors.red,
          ),
        );
      },
      error: (message, featureName) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi: $message'),
            backgroundColor: Colors.red,
          ),
        );
      },
    );
  }

  /// Request permission với logic thông minh
  Future<void> _requestPermissionSmart(
    BuildContext context,
    WidgetRef ref,
    PermissionController controller,
    PermissionType permissionType,
  ) async {
    try {
      // Kiểm tra trạng thái hiện tại
      await controller.checkPermission(permissionType);
      
      // Đợi một chút để state cập nhật
      await Future.delayed(const Duration(milliseconds: 100));
      
      // Lấy trạng thái hiện tại từ state
      final currentState = ref.watch(permissionControllerProvider);
      PermissionStatus? currentStatus;
      
      currentState.when(
        initial: () => currentStatus = null,
        loading: () => currentStatus = null,
        checked: (permissions) => currentStatus = permissions[permissionType],
        requested: (permissions) => currentStatus = permissions[permissionType],
        error: (message) => currentStatus = null,
      );
      
      if (currentStatus == null) {
        // Nếu không có trạng thái, request bình thường
        await controller.requestPermission(permissionType);
        return;
      }
      
      // Xử lý theo trạng thái
      if (currentStatus?.isGranted == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Quyền ${permissionType.displayName} đã được cấp'),
            backgroundColor: Colors.green,
          ),
        );
        return;
      }
      
      if (currentStatus?.isPermanentlyDenied == true) {
        // Hiển thị dialog để vào settings
        final shouldOpenSettings = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Quyền ${permissionType.displayName} bị từ chối vĩnh viễn'),
            content: Text(
              'Quyền ${permissionType.displayName} đã bị từ chối vĩnh viễn. '
              'Bạn cần vào cài đặt để cấp quyền thủ công.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Hủy'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Vào cài đặt'),
              ),
            ],
          ),
        );
        
        if (shouldOpenSettings == true) {
          await controller.openSystemAppSettings();
        }
        return;
      }
      
      if (currentStatus?.isDenied == true) {
        // Request permission bình thường
        await controller.requestPermission(permissionType);
        
        // Đợi kết quả
        await Future.delayed(const Duration(milliseconds: 500));
        
        // Kiểm tra lại trạng thái
        await controller.checkPermission(permissionType);
        
        // Đợi state cập nhật
        await Future.delayed(const Duration(milliseconds: 100));
        
        final newState = ref.watch(permissionControllerProvider);
        newState.when(
          initial: () {},
          loading: () {},
          checked: (permissions) {
            final newStatus = permissions[permissionType];
            if (newStatus?.isGranted == true) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Đã cấp quyền ${permissionType.displayName}'),
                  backgroundColor: Colors.green,
                ),
              );
            } else if (newStatus?.isPermanentlyDenied == true) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Quyền ${permissionType.displayName} bị từ chối vĩnh viễn'),
                  backgroundColor: Colors.red,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Quyền ${permissionType.displayName} bị từ chối'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          },
          requested: (permissions) {
            final newStatus = permissions[permissionType];
            if (newStatus?.isGranted == true) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Đã cấp quyền ${permissionType.displayName}'),
                  backgroundColor: Colors.green,
                ),
              );
            } else if (newStatus?.isPermanentlyDenied == true) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Quyền ${permissionType.displayName} bị từ chối vĩnh viễn'),
                  backgroundColor: Colors.red,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Quyền ${permissionType.displayName} bị từ chối'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          },
          error: (message) {},
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi khi xử lý quyền: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Request nhiều permission với logic thông minh
  Future<void> _requestMultiplePermissionsSmart(
    BuildContext context,
    WidgetRef ref,
    PermissionController controller,
    List<PermissionType> permissionTypes,
  ) async {
    try {
      // Kiểm tra trạng thái hiện tại
      await controller.checkPermissions(permissionTypes);
      
      // Đợi state cập nhật
      await Future.delayed(const Duration(milliseconds: 100));
      
      // Lấy trạng thái hiện tại
      final currentState = ref.watch(permissionControllerProvider);
      Map<PermissionType, PermissionStatus>? currentStatuses;
      
      currentState.when(
        initial: () => currentStatuses = null,
        loading: () => currentStatuses = null,
        checked: (permissions) => currentStatuses = permissions,
        requested: (permissions) => currentStatuses = permissions,
        error: (message) => currentStatuses = null,
      );
      
      if (currentStatuses == null) {
        // Request bình thường nếu không có trạng thái
        await controller.requestPermissions(permissionTypes);
        return;
      }
      
      // Kiểm tra xem có permission nào bị permanently denied không
      final permanentlyDenied = currentStatuses?.values.any((status) => status.isPermanentlyDenied) ?? false;
      
      if (permanentlyDenied) {
        // Hiển thị dialog với option vào settings
        final shouldOpenSettings = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Một số quyền bị từ chối vĩnh viễn'),
            content: const Text(
              'Một số quyền đã bị từ chối vĩnh viễn. '
              'Bạn cần vào cài đặt để cấp quyền thủ công.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Hủy'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Vào cài đặt'),
              ),
            ],
          ),
        );
        
        if (shouldOpenSettings == true) {
          await controller.openSystemAppSettings();
        }
        return;
      }
      
      // Request permissions bình thường
      await controller.requestPermissions(permissionTypes);
      
      // Đợi kết quả
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Kiểm tra lại trạng thái
      await controller.checkPermissions(permissionTypes);
      
      // Hiển thị kết quả
      final newState = ref.watch(permissionControllerProvider);
      newState.when(
        initial: () {},
        loading: () {},
        checked: (newStatuses) {
          final grantedCount = newStatuses.values.where((status) => status.isGranted).length;
          final totalCount = permissionTypes.length;
          
          if (grantedCount == totalCount) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Đã cấp tất cả $totalCount quyền'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Đã cấp $grantedCount/$totalCount quyền'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        },
        requested: (newStatuses) {
          final grantedCount = newStatuses.values.where((status) => status.isGranted).length;
          final totalCount = permissionTypes.length;
          
          if (grantedCount == totalCount) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Đã cấp tất cả $totalCount quyền'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Đã cấp $grantedCount/$totalCount quyền'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        },
        error: (message) {},
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi khi xử lý quyền: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Lấy icon cho permission
  IconData _getPermissionIcon(PermissionType permissionType) {
    switch (permissionType) {
      case PermissionType.camera:
        return Icons.camera_alt;
      case PermissionType.photos:
      case PermissionType.photoLibrary:
        return Icons.photo_library;
      case PermissionType.microphone:
        return Icons.mic;
      case PermissionType.location:
      case PermissionType.locationWhenInUse:
      case PermissionType.locationAlways:
        return Icons.location_on;
      case PermissionType.storage:
        return Icons.storage;
      default:
        return Icons.security;
    }
  }

  /// Lấy icon cho status
  IconData _getStatusIcon(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return Icons.check_circle;
      case PermissionStatus.denied:
        return Icons.cancel;
      case PermissionStatus.permanentlyDenied:
        return Icons.block;
      case PermissionStatus.restricted:
        return Icons.warning;
      case PermissionStatus.limited:
        return Icons.info;
      case PermissionStatus.provisional:
        return Icons.schedule;
      default:
        return Icons.help;
    }
  }

  /// Lấy màu cho status
  Color _getStatusColor(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return Colors.green;
      case PermissionStatus.denied:
        return Colors.orange;
      case PermissionStatus.permanentlyDenied:
        return Colors.red;
      case PermissionStatus.restricted:
        return Colors.purple;
      case PermissionStatus.limited:
        return Colors.blue;
      case PermissionStatus.provisional:
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }
} 