/// CommonRadioGroup - Nhóm radio button tuỳ biến chuẩn dự án
///
/// <PERSON><PERSON> dụng cho các trường hợp cần chọn 1 trong nhiều lựa chọn, hỗ trợ vertical/horizontal, label, trạng thái enabled/disabled.
///
/// Ví dụ:
/// ```dart
/// CommonRadioGroup<String>(
///   label: S.of(context).gender,
///   items: ['Nam', 'Nữ'],
///   value: selected,
///   onChanged: (v) => setState(() => selected = v),
///   labelBuilder: (v) => v,
/// )
/// ```
library common_radio_group;

import 'package:flutter/material.dart';
// Core barrel export
import 'package:sales_app/core/core.dart';

class CommonRadioGroup<T> extends StatelessWidget {
  final String? label;
  final List<T> items;
  final T? value;
  final ValueChanged<T?>? onChanged;
  final String Function(T) labelBuilder;
  final bool isRequired;
  final bool enabled;
  final Color? activeColor;
  final Color? textColor;
  final Color? labelColor;
  final MainAxisAlignment alignment;
  final bool isVertical;

  const CommonRadioGroup({
    super.key,
    this.label,
    required this.items,
    this.value,
    this.onChanged,
    required this.labelBuilder,
    this.isRequired = false,
    this.enabled = true,
    this.activeColor,
    this.textColor,
    this.labelColor,
    this.alignment = MainAxisAlignment.start,
    this.isVertical = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultTextColor = textColor ?? AppColors.textPrimary;
    final defaultLabelColor = labelColor ?? AppColors.textSecondary;
    final defaultActiveColor = activeColor ?? AppColors.primaryColor;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Row(
            children: [
              Text(
                label!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: defaultLabelColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        if (isVertical)
          Column(
            children: items.map((item) => _buildRadioTile(
              context,
              item,
              defaultTextColor,
              defaultActiveColor,
            )).toList(),
          )
        else
          Row(
            mainAxisAlignment: alignment,
            children: items.map((item) => Expanded(
              child: _buildRadioTile(
                context,
                item,
                defaultTextColor,
                defaultActiveColor,
              ),
            )).toList(),
          ),
      ],
    );
  }

  Widget _buildRadioTile(
    BuildContext context,
    T item,
    Color textColor,
    Color activeColor,
  ) {
    return InkWell(
      onTap: enabled ? () => onChanged?.call(item) : null,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Radio<T>(
              value: item,
              groupValue: value,
              onChanged: enabled ? onChanged : null,
              activeColor: activeColor,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                labelBuilder(item),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: enabled 
                      ? textColor
                      : textColor.withValues(alpha: 0.5),
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
