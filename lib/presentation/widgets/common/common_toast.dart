import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';

/// Common Toast widgets để hiển thị thông báo overlay
class CommonToast {
  static OverlayEntry? _currentToast;

  /// Hi<PERSON>n thị success toast
  static void showSuccess({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 2),
  }) {
    _showToast(
      context: context,
      message: message,
      backgroundColor: AppColors.success,
      icon: Icons.check_circle,
      duration: duration,
    );
  }

  /// Hiển thị error toast
  static void showError({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    _showToast(
      context: context,
      message: message,
      backgroundColor: AppColors.error,
      icon: Icons.error,
      duration: duration,
    );
  }

  /// Hiển thị warning toast
  static void showWarning({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 2),
  }) {
    _showToast(
      context: context,
      message: message,
      backgroundColor: AppColors.warning,
      icon: Icons.warning,
      duration: duration,
    );
  }

  /// Hi<PERSON>n thị info toast
  static void showInfo({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 2),
  }) {
    _showToast(
      context: context,
      message: message,
      backgroundColor: AppColors.info,
      icon: Icons.info,
      duration: duration,
    );
  }

  /// Hiển thị custom toast
  static void showCustom({
    required BuildContext context,
    required String message,
    required Color backgroundColor,
    IconData? icon,
    Duration duration = const Duration(seconds: 2),
  }) {
    _showToast(
      context: context,
      message: message,
      backgroundColor: backgroundColor,
      icon: icon,
      duration: duration,
    );
  }

  /// Private method để hiển thị toast
  static void _showToast({
    required BuildContext context,
    required String message,
    required Color backgroundColor,
    IconData? icon,
    Duration duration = const Duration(seconds: 2),
  }) {
    // Đóng toast hiện tại nếu có
    hide();

    final overlay = Overlay.of(context);
    
    _currentToast = OverlayEntry(
      builder: (context) => _ToastWidget(
        message: message,
        backgroundColor: backgroundColor,
        icon: icon,
        duration: duration,
        onDismiss: hide,
      ),
    );

    overlay.insert(_currentToast!);

    // Tự động đóng sau duration
    Future.delayed(duration, () {
      hide();
    });
  }

  /// Đóng toast hiện tại
  static void hide() {
    _currentToast?.remove();
    _currentToast = null;
  }
}

/// Widget hiển thị toast
class _ToastWidget extends StatefulWidget {
  final String message;
  final Color backgroundColor;
  final IconData? icon;
  final Duration duration;
  final VoidCallback onDismiss;

  const _ToastWidget({
    required this.message,
    required this.backgroundColor,
    this.icon,
    required this.duration,
    required this.onDismiss,
  });

  @override
  State<_ToastWidget> createState() => _ToastWidgetState();
}

class _ToastWidgetState extends State<_ToastWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();

    // Bắt đầu animation đóng trước khi hết duration
    Future.delayed(widget.duration - const Duration(milliseconds: 300), () {
      if (mounted) {
        _animationController.reverse();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + AppDimens.spacingMD,
      left: AppDimens.spacingMD,
      right: AppDimens.spacingMD,
      child: SlideTransition(
        position: _slideAnimation,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: AppDimens.paddingAllMd,
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: AppDimens.borderRadius8,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: AppColors.white,
                      size: 20,
                    ),
                    AppDimens.w12,
                  ],
                  Expanded(
                    child: Text(
                      widget.message,
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: AppDimens.fontMD,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: widget.onDismiss,
                    child: const Icon(
                      Icons.close,
                      color: AppColors.white,
                      size: 18,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Extension để dễ dàng sử dụng Toast
extension ToastExtension on BuildContext {
  /// Hiển thị success toast
  void showSuccessToast(String message) {
    CommonToast.showSuccess(context: this, message: message);
  }

  /// Hiển thị error toast
  void showErrorToast(String message) {
    CommonToast.showError(context: this, message: message);
  }

  /// Hiển thị warning toast
  void showWarningToast(String message) {
    CommonToast.showWarning(context: this, message: message);
  }

  /// Hiển thị info toast
  void showInfoToast(String message) {
    CommonToast.showInfo(context: this, message: message);
  }
}
