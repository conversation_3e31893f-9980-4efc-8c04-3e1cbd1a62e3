import 'package:flutter/material.dart';

/// Widget wrapper để đóng bàn phím khi nhấn vào bất cứ đâu trong màn hình
/// 
/// Sử dụng widget này để wrap body của các màn hình có form input
/// để cải thiện trải nghiệm người dùng
class KeyboardDismisser extends StatelessWidget {
  final Widget child;
  final bool dismissOnTap;

  const KeyboardDismisser({
    super.key,
    required this.child,
    this.dismissOnTap = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!dismissOnTap) {
      return child;
    }

    return GestureDetector(
      onTap: () {
        // Đóng bàn phím khi nhấn vào bất cứ đâu
        FocusScopeNode currentFocus = FocusScope.of(context);

        if (!currentFocus.hasPrimaryFocus &&
            currentFocus.focusedChild != null) {
          FocusManager.instance.primaryFocus?.unfocus();
        }
      },
      // Đ<PERSON>m bảo gesture detector không ảnh hưởng đến các widget con
      behavior: HitTestBehavior.translucent,
      child: child,
    );
  }
} 