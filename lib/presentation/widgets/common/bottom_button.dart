import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'common_button.dart';
import 'package:sales_app/core/constants/app_dimens.dart';

class BottomButton extends StatelessWidget {
  final String? title;
  final Widget? child;
  final VoidCallback? onPressed;
  final bool enabled;
  final Color? backgroundColor;
  final Color? disabledColor;
  final Color? textColor;
  final EdgeInsets padding;

  // Props cho 2 button
  final String? primaryTitle;
  final VoidCallback? primaryOnPressed;
  final Color? primaryColor;
  final String? secondaryTitle;
  final VoidCallback? secondaryOnPressed;
  final Color? secondaryColor;

  const BottomButton({
    super.key,
    this.title,
    this.child,
    this.onPressed,
    this.enabled = true,
    this.backgroundColor,
    this.disabledColor,
    this.textColor,
    this.padding = const EdgeInsets.fromLTRB(16, 12, 16, 12),
    // 2 button
    this.primaryTitle,
    this.primaryOnPressed,
    this.primaryColor,
    this.secondaryTitle,
    this.secondaryOnPressed,
    this.secondaryColor,
  }) : assert(
          (title != null || child != null) ||
              (primaryTitle != null && secondaryTitle != null),
          'Phải cung cấp title/child hoặc cả primaryTitle và secondaryTitle',
        );

  @override
  Widget build(BuildContext context) {
    final isDoubleButton = primaryTitle != null && secondaryTitle != null;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Divider(height: 1),
        SafeArea(
          child: Padding(
            padding: padding,
            child: isDoubleButton
                ? Row(
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: AppDimens.buttonHeight,
                          child: OutlinedButton(
                            onPressed: secondaryOnPressed,
                            style: OutlinedButton.styleFrom(
                              foregroundColor:
                                  Theme.of(context).colorScheme.primary,
                              side: BorderSide(
                                  color: Theme.of(context).colorScheme.primary),
                              shape: RoundedRectangleBorder(
                                borderRadius: AppDimens.borderRadius12,
                              ),
                              padding: AppDimens.paddingHorizontalMd,
                            ),
                            child: Text(
                              secondaryTitle ?? '',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: CommonButton(
                          title: primaryTitle,
                          onPressed: primaryOnPressed,
                          backgroundColor: primaryColor ?? backgroundColor,
                          textColor: textColor,
                          height: AppDimens.buttonHeight,
                        ),
                      ),
                    ],
                  )
                : CommonButton(
                    title: title,
                    onPressed: onPressed,
                    enabled: enabled,
                    backgroundColor: backgroundColor,
                    disabledColor: disabledColor,
                    textColor: textColor,
                    child: child,
                  ),
          ),
        ),
      ],
    );
  }
}
