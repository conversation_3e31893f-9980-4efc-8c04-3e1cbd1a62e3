import 'package:flutter/material.dart';

/// Widget success tái sử dụng với RefreshIndicator
class BaseSuccessWidget<T> extends StatelessWidget {
  final T data;
  final Widget Function(T data) builder;
  final VoidCallback? onRefresh;
  
  const BaseSuccessWidget({
    super.key,
    required this.data,
    required this.builder,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh != null 
          ? () async => onRefresh!()
          : () async {},
      child: builder(data),
    );
  }
} 