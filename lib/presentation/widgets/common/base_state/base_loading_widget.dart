import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';

/// Widget loading tái sử dụng với message và size tùy chỉnh
/// Adaptive cho Android (CircularProgressIndicator) và iOS (CupertinoActivityIndicator)
class BaseLoadingWidget extends StatelessWidget {
  final String? message;
  final double? size;
  final bool useAdaptive;
  
  const BaseLoadingWidget({
    super.key,
    this.message,
    this.size,
    this.useAdaptive = true,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size ?? AppDimens.iconLG,
            height: size ?? AppDimens.iconLG,
            child: _buildLoadingIndicator(),
          ),
          if (message != null) ...[
            AppDimens.h16,
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    if (!useAdaptive) {
      return const CircularProgressIndicator();
    }

    // Adaptive loading indicator
    return const CupertinoActivityIndicator();
  }
} 