import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';

/// Widget refreshing với loading indicator overlay
class BaseRefreshingWidget<T> extends StatelessWidget {
  final T data;
  final Widget Function(T data) builder;

  const BaseRefreshingWidget({
    super.key,
    required this.data,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        builder(data),
        Positioned(
          top: AppDimens.spacingLG,
          right: AppDimens.spacingLG,
          child: Container(
            padding: EdgeInsets.all(AppDimens.spacingSM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: AppDimens.borderRadius16,
            ),
            child: SizedBox(
              width: AppDimens.iconXS,
              height: AppDimens.iconXS,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppColors.white),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
