import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';
// Presentation utils barrel export
import 'package:sales_app/presentation/utils/utils.dart';

/// Widget error tái sử dụng với retry functionality
class BaseErrorWidget extends StatelessWidget {
  final Failure failure;
  final VoidCallback? onRetry;
  final String? customMessage;
  
  const BaseErrorWidget({
    super.key,
    required this.failure,
    this.onRetry,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    final errorMessage = customMessage ?? 
        ErrorMessageMapper.getErrorMessage(context, failure);
    
    return Center(
      child: Padding(
        padding: AppDimens.paddingAllLg,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: AppDimens.iconXXL,
              color: Theme.of(context).colorScheme.error,
            ),
            AppDimens.h16,
            Text(
              errorMessage,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              AppDimens.h16,
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(S.of(context).networkRetry),
              ),
            ],
          ],
        ),
      ),
    );
  }
} 