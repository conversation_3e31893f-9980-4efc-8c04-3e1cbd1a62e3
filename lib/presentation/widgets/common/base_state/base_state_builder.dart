import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Presentation widgets barrel export
import 'package:sales_app/presentation/widgets/common/common.dart';
// Presentation utils barrel export
import 'package:sales_app/presentation/utils/utils.dart';

/// Helper widget để handle tất cả states một cách gọn gàng
class BaseStateBuilder<T> extends StatelessWidget {
  final BaseState<T> state;
  final Widget Function(T data) successBuilder;
  final Widget Function(Failure failure, VoidCallback? onRetry)? errorBuilder;
  final Widget? loadingBuilder;
  final Widget? emptyBuilder;
  final Widget? initialBuilder;
  final Widget? submittedBuilder;
  final Widget? submittingOverlay;
  final Widget Function(T data)? refreshingBuilder;
  final VoidCallback? onRetry;
  final bool showContentWhileSubmitting;
  final bool showErrorWidgetOnSubmittingError;
  final bool showErrorWidgetOnLoadingError;
  
  const BaseStateBuilder({
    super.key,
    required this.state,
    required this.successBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.initialBuilder,
    this.submittedBuilder,
    this.submittingOverlay,
    this.refreshingBuilder,
    this.onRetry,
    this.showContentWhileSubmitting = false,
    this.showErrorWidgetOnSubmittingError = false,
    this.showErrorWidgetOnLoadingError = true,
  });

  @override
  Widget build(BuildContext context) {
    return state.when(
      initial: () => initialBuilder ?? const SizedBox.shrink(),
      loading: () => loadingBuilder ?? const BaseLoadingWidget(),
      success: (data) => successBuilder(data),
      error: (failure, previousData) => _buildErrorState(failure, previousData),
      submitting: (previousData) => _buildSubmittingState(previousData),
      submitted: (data) => _buildSubmittedState(data),
      empty: () => emptyBuilder ?? const BaseEmptyWidget(),
      refreshing: (data) => refreshingBuilder?.call(data) ?? 
          BaseRefreshingWidget(data: data, builder: successBuilder),
    );
  }

  Widget _buildErrorState(Failure failure, T? previousData) {
    // Case 1: Từ loading/initial sang error
    if (previousData == null) {
      if (!showErrorWidgetOnLoadingError) {
        // Không show error widget, có thể trả về empty/initial hoặc SizedBox
        return initialBuilder ?? emptyBuilder ?? const SizedBox.shrink();
      }
      return errorBuilder?.call(failure, onRetry) ?? 
          BaseErrorWidget(failure: failure, onRetry: onRetry);
    }
    // Case 2: Từ submitting/success sang error
    if (!showErrorWidgetOnSubmittingError) {
      // Giữ lại content và để BaseStateErrorHandler xử lý error
      return successBuilder(previousData);
    }
    return errorBuilder?.call(failure, onRetry) ?? 
        BaseErrorWidget(failure: failure, onRetry: onRetry);
  }

  Widget _buildSubmittingState(T? previousData) {
    // Nếu muốn show content while submitting
    if (showContentWhileSubmitting) {
      // Nếu có previousData, sử dụng successBuilder
      if (previousData != null) {
      return Stack(
        children: [
          // Hiển thị content bình thường
          successBuilder(previousData),

            // Overlay loading
            submittingOverlay ?? const DefaultSubmittingOverlay(),
          ],
        );
      }
      
      // Nếu không có previousData, sử dụng initialBuilder hoặc loadingBuilder
      final contentWidget = initialBuilder ?? 
                           loadingBuilder ?? 
                           const SizedBox.shrink();
      
      return Stack(
        children: [
          // Hiển thị content
          contentWidget,

          // Overlay loading
          submittingOverlay ?? const DefaultSubmittingOverlay(),
        ],
      );
    }

    // Fallback: hiển thị full loading (case không có data - như login form)
    return const DefaultSubmittingWidget();
  }

  Widget _buildSubmittedState(T? data) {
    // Nếu có custom submitted widget
    if (submittedBuilder != null) {
      return submittedBuilder!;
    }

    // Nếu có data, hiển thị như success state
    if (data != null) {
      return successBuilder(data);
    }

    // Fallback: hiển thị empty state (case không có data)
    return emptyBuilder ?? const BaseEmptyWidget();
  }
}

/// BaseStateBuilder với Riverpod Consumer - dành cho widget cần access WidgetRef
class BaseStateConsumerBuilder<T> extends ConsumerWidget {
  final ProviderBase<BaseState<T>> stateProvider;
  final Widget Function(T data, WidgetRef ref) successBuilder;
  final Widget Function(Failure failure, VoidCallback? onRetry, WidgetRef ref)? errorBuilder;
  final Widget Function(WidgetRef ref)? loadingBuilder;
  final Widget Function(WidgetRef ref)? emptyBuilder;
  final Widget Function(WidgetRef ref)? initialBuilder;
  final Widget Function(WidgetRef ref)? submittedBuilder;
  final Widget? submittingOverlay;
  final Widget Function(T data, WidgetRef ref)? refreshingBuilder;
  final VoidCallback? onRetry;
  final bool showContentWhileSubmitting;
  final bool showErrorWidgetOnSubmittingError;
  final bool showErrorWidgetOnLoadingError;
  
  const BaseStateConsumerBuilder({
    super.key,
    required this.stateProvider,
    required this.successBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.initialBuilder,
    this.submittedBuilder,
    this.submittingOverlay,
    this.refreshingBuilder,
    this.onRetry,
    this.showContentWhileSubmitting = false,
    this.showErrorWidgetOnSubmittingError = false,
    this.showErrorWidgetOnLoadingError = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(stateProvider);
    
    return state.when(
      initial: () => initialBuilder?.call(ref) ?? const SizedBox.shrink(),
      loading: () => loadingBuilder?.call(ref) ?? const BaseLoadingWidget(),
      success: (data) => successBuilder(data, ref),
      error: (failure, previousData) => _buildErrorState(failure, previousData, ref),
      submitting: (previousData) => _buildSubmittingState(previousData, ref),
      submitted: (data) => _buildSubmittedState(data, ref),
      empty: () => emptyBuilder?.call(ref) ?? const BaseEmptyWidget(),
      refreshing: (data) => refreshingBuilder?.call(data, ref) ?? 
          BaseRefreshingWidget(data: data, builder: (data) => successBuilder(data, ref)),
    );
  }

  Widget _buildErrorState(Failure failure, T? previousData, WidgetRef ref) {
    // Case 1: Từ loading/initial sang error
    if (previousData == null) {
      if (!showErrorWidgetOnLoadingError) {
        return initialBuilder?.call(ref) ?? emptyBuilder?.call(ref) ?? const SizedBox.shrink();
      }
      return errorBuilder?.call(failure, onRetry, ref) ?? 
          BaseErrorWidget(failure: failure, onRetry: onRetry);
    }
    // Case 2: Từ submitting/success sang error
    if (!showErrorWidgetOnSubmittingError) {
      return successBuilder(previousData, ref);
    }
    return errorBuilder?.call(failure, onRetry, ref) ?? 
        BaseErrorWidget(failure: failure, onRetry: onRetry);
  }

  Widget _buildSubmittingState(T? previousData, WidgetRef ref) {
    // Nếu muốn show content while submitting
    if (showContentWhileSubmitting) {
      // Nếu có previousData, sử dụng successBuilder
      if (previousData != null) {
      return Stack(
        children: [
          // Hiển thị content bình thường
          successBuilder(previousData, ref),

            // Overlay loading
            submittingOverlay ?? const DefaultSubmittingWidget(),
          ],
        );
      }
      
      // Nếu không có previousData, sử dụng initialBuilder hoặc loadingBuilder
      final contentWidget = initialBuilder?.call(ref) ?? 
                           loadingBuilder?.call(ref) ?? 
                           const SizedBox.shrink();
      
      return Stack(
        children: [
          // Hiển thị content
          contentWidget,

          // Overlay loading
          submittingOverlay ?? const DefaultSubmittingOverlay(),
        ],
      );
    }

    // Fallback: hiển thị full loading (case không có data - như login form)
    return const DefaultSubmittingWidget();
  }

  Widget _buildSubmittedState(T? data, WidgetRef ref) {
    // Nếu có custom submitted widget
    if (submittedBuilder != null) {
      return submittedBuilder!(ref);
    }

    // Nếu có data, hiển thị như success state
    if (data != null) {
      return successBuilder(data, ref);
    }

    // Fallback: hiển thị empty state (case không có data)
    return emptyBuilder?.call(ref) ?? const BaseEmptyWidget();
  }
}

/// BaseStateBuilder với Hooks - dành cho widget cần sử dụng hooks
class BaseStateHookBuilder<T> extends HookConsumerWidget {
  final ProviderBase<BaseState<T>> stateProvider;
  final Widget Function(T data, WidgetRef ref) successBuilder;
  final Widget Function(Failure failure, VoidCallback? onRetry, WidgetRef ref)? errorBuilder;
  final Widget Function(WidgetRef ref)? loadingBuilder;
  final Widget Function(WidgetRef ref)? emptyBuilder;
  final Widget Function(WidgetRef ref)? initialBuilder;
  final Widget Function(WidgetRef ref)? submittedBuilder;
  final Widget? submittingOverlay;
  final Widget Function(T data, WidgetRef ref)? refreshingBuilder;
  final VoidCallback? onRetry;
  final bool showContentWhileSubmitting;
  final bool showErrorWidgetOnSubmittingError;
  final bool showErrorWidgetOnLoadingError;
  
  const BaseStateHookBuilder({
    super.key,
    required this.stateProvider,
    required this.successBuilder,
    this.errorBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.initialBuilder,
    this.submittedBuilder,
    this.submittingOverlay,
    this.refreshingBuilder,
    this.onRetry,
    this.showContentWhileSubmitting = false,
    this.showErrorWidgetOnSubmittingError = false,
    this.showErrorWidgetOnLoadingError = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(stateProvider);
    
    return state.when(
      initial: () => initialBuilder?.call(ref) ?? const SizedBox.shrink(),
      loading: () => loadingBuilder?.call(ref) ?? const BaseLoadingWidget(),
      success: (data) => successBuilder(data, ref),
      error: (failure, previousData) => _buildErrorState(failure, previousData, ref),
      submitting: (previousData) => _buildSubmittingState(previousData, ref),
      submitted: (data) => _buildSubmittedState(data, ref),
      empty: () => emptyBuilder?.call(ref) ?? const BaseEmptyWidget(),
      refreshing: (data) => refreshingBuilder?.call(data, ref) ?? 
          BaseRefreshingWidget(data: data, builder: (data) => successBuilder(data, ref)),
    );
  }

  Widget _buildErrorState(Failure failure, T? previousData, WidgetRef ref) {
    // Case 1: Từ loading/initial sang error
    if (previousData == null) {
      if (!showErrorWidgetOnLoadingError) {
        return initialBuilder?.call(ref) ?? emptyBuilder?.call(ref) ?? const SizedBox.shrink();
      }
      return errorBuilder?.call(failure, onRetry, ref) ?? 
          BaseErrorWidget(failure: failure, onRetry: onRetry);
    }
    // Case 2: Từ submitting/success sang error
    if (!showErrorWidgetOnSubmittingError) {
      return successBuilder(previousData, ref);
    }
    return errorBuilder?.call(failure, onRetry, ref) ?? 
        BaseErrorWidget(failure: failure, onRetry: onRetry);
  }

  Widget _buildSubmittingState(T? previousData, WidgetRef ref) {
    // Nếu muốn show content while submitting
    if (showContentWhileSubmitting) {
      // Nếu có previousData, sử dụng successBuilder
      if (previousData != null) {
      return Stack(
        children: [
          // Hiển thị content bình thường
          successBuilder(previousData, ref),

            // Overlay loading
            submittingOverlay ?? const DefaultSubmittingWidget(),
          ],
        );
      }
      
      // Nếu không có previousData, sử dụng initialBuilder hoặc loadingBuilder
      final contentWidget = initialBuilder?.call(ref) ?? 
                           loadingBuilder?.call(ref) ?? 
                           const SizedBox.shrink();
      
      return Stack(
        children: [
          // Hiển thị content
          contentWidget,

          // Overlay loading
          submittingOverlay ?? const DefaultSubmittingOverlay(),
        ],
      );
    }

    // Fallback: hiển thị full loading (case không có data - như login form)
    return const DefaultSubmittingWidget();
  }

  Widget _buildSubmittedState(T? data, WidgetRef ref) {
    // Nếu có custom submitted widget
    if (submittedBuilder != null) {
      return submittedBuilder!(ref);
    }

    // Nếu có data, hiển thị như success state
    if (data != null) {
      return successBuilder(data, ref);
    }

    // Fallback: hiển thị empty state (case không có data)
    return emptyBuilder?.call(ref) ?? const BaseEmptyWidget();
  }
}

/// Widget helper để tự động handle error từ provider và hiển thị dialog/snackbar
class BaseStateErrorHandler<T> extends ConsumerWidget {
  final ProviderBase<BaseState<T>> stateProvider;
  final Widget child;
  final ErrorDisplayType displayType;
  final VoidCallback? onError;
  final bool showErrorSnackBar;
  final bool showErrorDialog;
  final String? customErrorTitle;
  final String? customErrorMessage;
  
  const BaseStateErrorHandler({
    super.key,
    required this.stateProvider,
    required this.child,
    this.displayType = ErrorDisplayType.dialog,
    this.onError,
    this.showErrorSnackBar = false,
    this.showErrorDialog = true,
    this.customErrorTitle,
    this.customErrorMessage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to state changes and handle errors
    ref.listen<BaseState<T>>(stateProvider, (previous, next) {
      // Chỉ handle error state
      if (next.isError && previous?.isError != true) {
        final failure = next.failure;
        if (failure != null) {
          _handleError(context, failure);
        }
      }
    });

    return child;
  }

  void _handleError(BuildContext context, Failure failure) {
    // Call custom error handler if provided
    onError?.call();

    // Get error message
    final errorMessage = customErrorMessage ?? 
        ErrorMessageMapper.getErrorMessage(context, failure);

    // Show error based on display type
    switch (displayType) {
      case ErrorDisplayType.snackBar:
        if (showErrorSnackBar) {
          _showErrorSnackBar(context, errorMessage);
        }
        break;
      case ErrorDisplayType.dialog:
        if (showErrorDialog) {
          _showErrorDialog(context, errorMessage);
        }
        break;
      case ErrorDisplayType.both:
        if (showErrorSnackBar) {
          _showErrorSnackBar(context, errorMessage);
        }
        if (showErrorDialog) {
          _showErrorDialog(context, errorMessage);
        }
        break;
      case ErrorDisplayType.none:
        // Do nothing
        break;
    }
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: AppDimens.borderRadius8,
        ),
        margin: EdgeInsets.all(AppDimens.spacingMD),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    CommonDialog.showErrorDialog(
      context: context,
      title: customErrorTitle,
      message: message,
    );
  }
}

/// Enum để chọn cách hiển thị error
enum ErrorDisplayType {
  snackBar,    // Chỉ hiển thị SnackBar
  dialog,      // Chỉ hiển thị Dialog
  both,        // Hiển thị cả SnackBar và Dialog
  none,        // Không hiển thị gì
}

/// Extension để dễ dàng thêm error handling vào widget
extension BaseStateErrorHandlerExtension on Widget {
  /// Wrap widget với error handler cho provider
  Widget withErrorHandler<T>(
    ProviderBase<BaseState<T>> stateProvider, {
    ErrorDisplayType displayType = ErrorDisplayType.dialog,
    VoidCallback? onError,
    bool showErrorSnackBar = false,
    bool showErrorDialog = true,
    String? customErrorTitle,
    String? customErrorMessage,
  }) {
    return BaseStateErrorHandler<T>(
      stateProvider: stateProvider,
      displayType: displayType,
      onError: onError,
      showErrorSnackBar: showErrorSnackBar,
      showErrorDialog: showErrorDialog,
      customErrorTitle: customErrorTitle,
      customErrorMessage: customErrorMessage,
      child: this,
    );
  }
} 