# Base State Widgets

Th<PERSON> mục này chứa các widget tái sử dụng để xử lý các trạng thái khác nhau của dữ liệu trong ứng dụng.

## 📁 C<PERSON>u trúc

```
base_state/
├── base_state.dart              # Barrel export cho tất cả widgets
├── base_state_builder.dart      # Core state builder widgets
├── base_loading_widget.dart     # Loading state widget
├── base_error_widget.dart       # Error state widget
├── base_empty_widget.dart       # Empty state widget
├── base_success_widget.dart     # Success state widget
├── base_submitting_widget.dart  # Submitting state widget
└── base_refreshing_widget.dart  # Refreshing state widget
```

## 🚀 Cách sử dụng

### Import
```dart
// Import tất cả base state widgets
import 'package:sales_app/presentation/widgets/common/base_state/base_state.dart';

// Hoặc import từ common.dart
import 'package:sales_app/presentation/widgets/common/common.dart';
```

### BaseStateBuilder - Widget chính
```dart
BaseStateBuilder<MyData>(
  state: myState,
  successBuilder: (data) => MyWidget(data: data),
  errorBuilder: (failure, onRetry) => CustomErrorWidget(failure: failure),
  loadingBuilder: () => CustomLoadingWidget(),
  emptyBuilder: () => CustomEmptyWidget(),
)
```

### BaseStateConsumerBuilder - Với Riverpod
```dart
BaseStateConsumerBuilder<MyData>(
  stateProvider: myStateProvider,
  successBuilder: (data, ref) => MyWidget(data: data),
  errorBuilder: (failure, onRetry, ref) => CustomErrorWidget(failure: failure),
)
```

### BaseStateHookBuilder - Với Hooks
```dart
BaseStateHookBuilder<MyData>(
  stateProvider: myStateProvider,
  successBuilder: (data, ref) => MyWidget(data: data),
)
```

## 🎯 Các Widget Cơ Bản

### BaseLoadingWidget
```dart
BaseLoadingWidget(
  message: 'Đang tải...',
  size: 40.0,
)
```

### BaseErrorWidget
```dart
BaseErrorWidget(
  failure: failure,
  onRetry: () => retryAction(),
  customMessage: 'Lỗi tùy chỉnh',
)
```

### BaseEmptyWidget
```dart
BaseEmptyWidget(
  message: 'Không có dữ liệu',
  icon: Icons.inbox_outlined,
  onAction: () => addNewItem(),
  actionLabel: 'Thêm mới',
)
```

### BaseSubmittingWidget
```dart
// Overlay submitting
DefaultSubmittingOverlay(
  message: 'Đang gửi...',
  showMessage: true,
)

// Full screen submitting
DefaultSubmittingWidget(
  message: 'Đang xử lý...',
)
```

## 🔧 Error Handling

### BaseStateErrorHandler
```dart
BaseStateErrorHandler<MyData>(
  stateProvider: myStateProvider,
  displayType: ErrorDisplayType.snackBar,
  child: MyWidget(),
)
```

### Extension Method
```dart
MyWidget().withErrorHandler<MyData>(
  myStateProvider,
  displayType: ErrorDisplayType.dialog,
  onError: () => customErrorHandler(),
)
```

## 📋 ErrorDisplayType

- `snackBar`: Hiển thị SnackBar
- `dialog`: Hiển thị Dialog
- `both`: Hiển thị cả SnackBar và Dialog
- `none`: Không hiển thị gì

## 🎨 Tùy chỉnh

### Custom Loading
```dart
BaseStateBuilder<MyData>(
  state: myState,
  successBuilder: (data) => MyWidget(data: data),
  loadingBuilder: () => CustomLoadingWidget(),
)
```

### Custom Error
```dart
BaseStateBuilder<MyData>(
  state: myState,
  successBuilder: (data) => MyWidget(data: data),
  errorBuilder: (failure, onRetry) => CustomErrorWidget(
    failure: failure,
    onRetry: onRetry,
  ),
)
```

### Show Content While Submitting
```dart
BaseStateBuilder<MyData>(
  state: myState,
  successBuilder: (data) => MyWidget(data: data),
  showContentWhileSubmitting: true,
  submittingOverlay: CustomSubmittingOverlay(),
)
```

## 🔄 State Flow

```
initial → loading → success
    ↓        ↓        ↓
  error ← error ← error
    ↓        ↓        ↓
submitting → submitted
    ↓
  refreshing
```

## 📝 Lưu ý

1. **Import**: Luôn sử dụng import từ `base_state.dart` hoặc `common.dart`
2. **Type Safety**: Luôn chỉ định generic type `<T>` cho BaseStateBuilder
3. **Error Handling**: Sử dụng BaseStateErrorHandler để tự động xử lý lỗi
4. **Customization**: Có thể tùy chỉnh từng state widget theo nhu cầu
5. **Performance**: Sử dụng `const` constructor khi có thể 