/// Base State Widgets Barrel Export
/// 
/// This file provides a single import point for all base state widgets that handle
/// different states of data (loading, error, success, empty, submitting, refreshing).
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/widgets/common/base_state/base_state.dart';
/// 
/// // Now you can use any base state widget:
/// BaseStateBuilder<MyData>(
///   state: myState,
///   successBuilder: (data) => MyWidget(data: data),
/// )
/// 
/// BaseLoadingWidget(message: 'Loading...')
/// BaseErrorWidget(failure: failure, onRetry: () {})
/// ```
/// 
/// ## Widget Categories:
/// - **State Builders**: BaseStateBuilder, BaseStateConsumerBuilder, BaseStateHookBuilder
/// - **State Widgets**: BaseLoadingWidget, BaseErrorWidget, BaseEmptyWidget, BaseSuccessWidget
/// - **Special States**: BaseSubmittingWidget, BaseRefreshingWidget
/// - **Error Handling**: BaseStateErrorHandler with ErrorDisplayType
library base_state_widgets;

// Core state builder widgets
export 'base_state_builder.dart';

// Individual state widgets
export 'base_loading_widget.dart';
export 'base_error_widget.dart';
export 'base_empty_widget.dart';
export 'base_success_widget.dart';
export 'base_submitting_widget.dart';
export 'base_refreshing_widget.dart'; 