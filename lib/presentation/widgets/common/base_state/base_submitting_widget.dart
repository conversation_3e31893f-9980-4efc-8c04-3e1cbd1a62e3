import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';

/// Default submitting overlay widget
class DefaultSubmittingOverlay extends StatelessWidget {
  const DefaultSubmittingOverlay({
    super.key,
    this.message,
    this.backgroundColor,
    this.opacity = 0.5,
    this.showMessage = true,
    this.messageStyle,
  });

  /// Submitting message to display
  final String? message;

  /// Background color of the overlay
  final Color? backgroundColor;

  /// Opacity of the overlay background
  final double opacity;

  /// Whether to show the submitting message
  final bool showMessage;

  /// Custom style for the submitting message
  final TextStyle? messageStyle;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: (backgroundColor ?? Colors.black).withValues(alpha: opacity),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Adaptive loading indicator
              _buildAdaptiveLoadingIndicator(context),

              if (showMessage && message != null && message!.isNotEmpty) ...[
                AppDimens.h16,
                Text(
                  message!,
                  style: messageStyle ??
                    Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdaptiveLoadingIndicator(BuildContext context) {
    // Use platform-specific loading indicators
    if (Platform.isIOS) {
      return CupertinoActivityIndicator(
        radius: AppDimens.loadingIndicatorRadius,
      );
    } else {
      return CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(
          Theme.of(context).colorScheme.primary,
        ),
      );
    }
  }
}

/// Default full-screen submitting widget (for cases without previous data)
/// Similar to login screen loading with background overlay
class DefaultSubmittingWidget extends StatelessWidget {
  final String? message;
  final Color? backgroundColor;
  final double opacity;

  const DefaultSubmittingWidget({
    super.key,
    this.message,
    this.backgroundColor,
    this.opacity = 0.5,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: (backgroundColor ?? Colors.black).withValues(alpha: opacity),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
      child: Column(
            mainAxisSize: MainAxisSize.min,
        children: [
          // Adaptive loading indicator
          _buildAdaptiveLoadingIndicator(context),
          
              if (message != null && message!.isNotEmpty) ...[
            AppDimens.h16,
            Text(
              message!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdaptiveLoadingIndicator(BuildContext context) {
    // Use platform-specific loading indicators
    if (Platform.isIOS) {
      return CupertinoActivityIndicator(
        radius: AppDimens.loadingIndicatorRadius,
      );
    } else {
      return CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(
          Theme.of(context).colorScheme.primary,
        ),
      );
    }
  }
}

/// Predefined submitting widget configurations
class SubmittingWidgetConfig {
  /// Silent submitting overlay (no message) - DEFAULT for all types
  static DefaultSubmittingOverlay silent() => const DefaultSubmittingOverlay(
    showMessage: false,
  );

  /// Submitting overlay with custom message
  static DefaultSubmittingOverlay withMessage(String message) => DefaultSubmittingOverlay(
    message: message,
    showMessage: true,
  );

  /// Full-screen submitting widget with custom message
  static DefaultSubmittingWidget fullScreen(String message) => DefaultSubmittingWidget(
    message: message,
  );

  /// Full-screen silent submitting widget
  static DefaultSubmittingWidget fullScreenSilent() => const DefaultSubmittingWidget();

  // Common submitting scenarios
  /// Login submitting overlay
  static DefaultSubmittingOverlay login() => DefaultSubmittingOverlay(
    message: S.current.submitting_login,
    showMessage: true,
  );

  /// Register submitting overlay
  static DefaultSubmittingOverlay register() => DefaultSubmittingOverlay(
    message: S.current.submitting_register,
    showMessage: true,
  );

  /// Save data submitting overlay
  static DefaultSubmittingOverlay saving() => DefaultSubmittingOverlay(
    message: S.current.submitting_save,
    showMessage: true,
  );

  /// Delete data submitting overlay
  static DefaultSubmittingOverlay deleting() => DefaultSubmittingOverlay(
    message: S.current.submitting_delete,
    showMessage: true,
  );

  /// Update data submitting overlay
  static DefaultSubmittingOverlay updating() => DefaultSubmittingOverlay(
    message: S.current.submitting_update,
    showMessage: true,
  );

  /// Submit form overlay
  static DefaultSubmittingOverlay submitForm() => DefaultSubmittingOverlay(
    message: S.current.submitting_form,
    showMessage: true,
  );
} 