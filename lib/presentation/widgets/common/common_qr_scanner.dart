import 'dart:async';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:get_it/get_it.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';
// Widgets barrel export
import 'package:sales_app/presentation/widgets/widgets.dart';
// Mixins barrel export
import 'package:sales_app/presentation/mixins/mixins.dart';


/// Generic QR Scanner Widget that can be used across different modules
/// 
/// Features:
/// - Camera lifecycle management
/// - Internal loading state
/// - Customizable UI elements
/// - Raw QR data callback (string)
/// - No generic type complexity
class CommonQRScanner extends ConsumerStatefulWidget {
  /// Callback when QR code is detected (returns raw string data)
  final void Function(String qrData) onQRCodeDetected;
  
  /// Callback when skip button is pressed
  final VoidCallback? onSkip;
  
  /// Callback when back button is pressed
  final VoidCallback? onBack;
  
  /// Callback when camera permission is denied permanently
  /// Return true if user wants to continue without camera, false to go back
  final Future<bool> Function()? onCameraPermissionDenied;
  
  /// Callback when camera permission is denied temporarily
  /// Return true if user wants to retry, false to go back
  final Future<bool> Function()? onCameraPermissionDeniedTemporarily;
  
  /// Custom title for the scanner
  final String? customTitle;
  
  /// Custom guidance text
  final String? customGuidanceText;
  
  /// Whether to show skip button
  final bool showSkipButton;
  
  /// Whether to show gallery button
  final bool showGalleryButton;
  
  /// Whether to show flash button
  final bool showFlashButton;
  
  /// Custom error message when QR parsing fails
  final String? customErrorMessage;
  
  /// Whether to auto-stop camera after QR detection
  /// Default: false - let caller decide when to stop
  final bool autoStopAfterDetection;

  const CommonQRScanner({
    super.key,
    required this.onQRCodeDetected,
    this.onSkip,
    this.onBack,
    this.onCameraPermissionDenied,
    this.onCameraPermissionDeniedTemporarily,
    this.customTitle,
    this.customGuidanceText,
    this.showSkipButton = true,
    this.showGalleryButton = true,
    this.showFlashButton = true,
    this.customErrorMessage,
    this.autoStopAfterDetection = false,
  });

  @override
  ConsumerState<CommonQRScanner> createState() => _CommonQRScannerState();
}

class _CommonQRScannerState extends ConsumerState<CommonQRScanner>
    with WidgetsBindingObserver, CameraPermissionMixin {
  // Mobile Scanner Controller với autoStart: false theo docs
  final MobileScannerController _controller = MobileScannerController(
    autoStart: false,
  );
  
  // StreamSubscription để lắng nghe barcode events
  StreamSubscription<BarcodeCapture>? _subscription;
  
  bool _isFlashOn = false;
  bool _isInitialized = false;
  bool _isRestarting = false; // Flag để tránh restart liên tục
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    AppLogger.info('CommonQRScanner: initState called');
    
    // Start listening to lifecycle changes.
    WidgetsBinding.instance.addObserver(this);

    // Initialize camera sau khi widget được mount
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeCamera();
      }
    });
  }

  Future<void> _initializeCamera() async {
    if (_isInitialized || _isRestarting) return;
    _isRestarting = true;
    
    try {
      AppLogger.info('CommonQRScanner: Initializing camera...');
      
      // Sử dụng mixin để request camera permission
      final permissionResult = await requestCameraPermission(
        maxRetries: 1,
        showDialogOnPermanentDenial: true,
        showDialogOnTemporaryDenial: true,
        onGranted: () {
          AppLogger.info('CommonQRScanner: Camera permission granted, starting camera...');
          _startCamera();
        },
        onDenied: () {
          AppLogger.warning('CommonQRScanner: Camera permission denied');
          // Không làm gì, dialog đã được hiển thị bởi mixin
        },
        onPermanentlyDenied: () {
          AppLogger.warning('CommonQRScanner: Camera permission permanently denied');
          // Không làm gì, dialog đã được hiển thị bởi mixin
        },
        onError: (errorMessage) {
          AppLogger.error('CommonQRScanner: Camera permission error: $errorMessage');
          if (!mounted) return;
          CommonDialog.showErrorDialog(
            context: context,
            message: errorMessage,
          );
        },
        onBack: () {
          AppLogger.info('CommonQRScanner: User chose to go back');
          if (mounted && widget.onBack != null) {
            widget.onBack!();
          }
        },
      );
      
      // Xử lý kết quả nếu cần
      if (permissionResult.isGranted) {
        _startCamera();
      }
      
    } catch (e) {
      AppLogger.error('CommonQRScanner: Error initializing camera', error: e);
      if (!mounted) return;
      CommonDialog.showErrorDialog(
        context: context,
        message: 'Lỗi khởi tạo camera: ${e.toString()}',
      );
    } finally {
      _isRestarting = false;
    }
  }
  
  /// Khởi động camera sau khi có permission
  Future<void> _startCamera() async {
    try {
      _subscription = _controller.barcodes.listen(_handleBarcode);
      await _controller.start();
      _isInitialized = true;
      AppLogger.info('CommonQRScanner: Camera initialized successfully');
    } catch (e) {
      AppLogger.error('CommonQRScanner: Error starting camera', error: e);
      if (!mounted) return;
      CommonDialog.showErrorDialog(
        context: context,
        message: 'Lỗi khởi động camera: ${e.toString()}',
      );
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // If the controller is not ready, do not try to start or stop it.
    // Permission dialogs can trigger lifecycle changes before the controller is ready.
    if (!_controller.value.isInitialized) {
      return;
    }

    AppLogger.info('CommonQRScanner: App lifecycle changed', data: {
      'state': state.toString(),
      'isInitialized': _controller.value.isInitialized,
    });

    switch (state) {
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
      case AppLifecycleState.paused:
        return;
      case AppLifecycleState.resumed:
        // Restart the scanner when the app is resumed.
        // Don't forget to resume listening to the barcode events.
        if (_isInitialized) {
          _subscription = _controller.barcodes.listen(_handleBarcode);
          unawaited(_controller.start());
        }
        break;
      case AppLifecycleState.inactive:
        // Stop the scanner when the app is paused.
        // Also stop the barcode events subscription.
        unawaited(_subscription?.cancel());
        _subscription = null;
        unawaited(_controller.stop());
        break;
    }
  }

  @override
  Future<void> dispose() async {
    AppLogger.info('CommonQRScanner: dispose called');
    
    // Stop listening to lifecycle changes.
    WidgetsBinding.instance.removeObserver(this);
    
    // Stop listening to the barcode events.
    unawaited(_subscription?.cancel());
    _subscription = null;
    
    // Dispose the widget itself.
    super.dispose();
    
    // Finally, dispose of the controller.
    await _controller.dispose();
  }

  void _handleBarcode(BarcodeCapture capture) {
    AppLogger.info('CommonQRScanner: _handleBarcode called', data: {
      'isLoading': _isLoading,
      'barcodesCount': capture.barcodes.length,
    });

    if (_isLoading) {
      AppLogger.info('CommonQRScanner: Skipping barcode processing - loading');
      return;
    }

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isEmpty) {
      AppLogger.debug('CommonQRScanner: No barcodes detected in capture');
      return;
    }

    final Barcode barcode = barcodes.first;
    AppLogger.info('CommonQRScanner: Barcode found', data: {
      'format': barcode.format.name,
      'rawValue': barcode.rawValue,
      'displayValue': barcode.displayValue,
    });

    if (barcode.rawValue == null) {
      AppLogger.warning('CommonQRScanner: Barcode rawValue is null');
      return;
    }

    AppLogger.info('CommonQRScanner: Barcode detected', data: {
      'barcodeValue': barcode.rawValue,
    });

    _processQRCode(barcode.rawValue!);
  }

  Future<void> _processQRCode(String qrData) async {
    try {
      setState(() => _isLoading = true);
      
      AppLogger.info('CommonQRScanner: Processing QR code', data: {
        'qrData': qrData,
      });
      
      // Simply pass the raw QR data to callback
      // Let the caller handle parsing
      await Future.delayed(const Duration(milliseconds: 500));
      if (!mounted) return;

      // Gọi callback với raw QR data
      widget.onQRCodeDetected(qrData);
      
      // Stop camera nếu được cấu hình auto-stop
      if (widget.autoStopAfterDetection) {
        unawaited(_controller.stop());
      }
      
      setState(() => _isLoading = false);
    } catch (e) {
      AppLogger.error('CommonQRScanner: Error processing QR code', error: e);
      
      if (!mounted) return;

      setState(() => _isLoading = false);
      CommonDialog.showErrorDialog(
        context: context,
        message: 'Lỗi xử lý mã QR: ${e.toString()}',
        onPressed: () {
          // Restart camera sau khi tắt dialog lỗi
          _restartCamera();
        },
      );
    }
  }

  Future<void> _handleGalleryPick() async {
    if (_isLoading) return;
    try {
      AppLogger.info('CommonQRScanner: Gallery pick started');
      
      final imagePicker = GetIt.I<ImagePickerService>();
      final path = await imagePicker.pickFromGallery(
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 90,
      );
      if (path == null) return;

      _processQRCode(path);
    } catch (e) {
      AppLogger.error('CommonQRScanner: Gallery pick error', error: e);
      if (!mounted) return;
      setState(() => _isLoading = false);
      CommonDialog.showErrorDialog(
        context: context,
        message: 'Lỗi xử lý ảnh: ${e.toString()}',
      );
    }
  }

  void _toggleFlash() {
    AppLogger.info('CommonQRScanner: Toggle flash', data: {
      'currentFlashState': _isFlashOn,
    });
    
    final newFlashState = !_isFlashOn;
    setState(() {
      _isFlashOn = newFlashState;
    });
    _controller.toggleTorch();
  }

  Future<void> _restartCamera() async {
    if (!mounted || _isRestarting) return;
    
    try {
      AppLogger.info('CommonQRScanner: Restarting camera after error dialog');
      
      // Stop current camera nếu đang chạy
      await _controller.stop();
      
      // Cancel current subscription
      await _subscription?.cancel();
      _subscription = null;
      
      // Restart camera
      _subscription = _controller.barcodes.listen(_handleBarcode);
      await _controller.start();
      
      AppLogger.info('CommonQRScanner: Camera restarted successfully');
    } catch (e) {
      AppLogger.error('CommonQRScanner: Error restarting camera', error: e);
    }
  }

  /// Stop camera manually - can be called by parent widget
  Future<void> stopCamera() async {
    if (!mounted) return;
    
    try {
      AppLogger.info('CommonQRScanner: Stopping camera manually');
      await _controller.stop();
      await _subscription?.cancel();
      _subscription = null;
    } catch (e) {
      AppLogger.error('CommonQRScanner: Error stopping camera', error: e);
    }
  }

  void _handleSkipQRScan() {
    if (_isLoading) return;

    AppLogger.info('CommonQRScanner: Skip QR scan');
    if (widget.onSkip != null) widget.onSkip!();
  }




  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final screenHeight = size.height - MediaQuery.of(context).padding.top - kToolbarHeight;

    AppLogger.info('CommonQRScanner: build called', data: {
      'isLoading': _isLoading,
      'isInitialized': _isInitialized,
    });

    return Scaffold(
      backgroundColor: Colors.black,
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Scanner view đơn giản - không cần onDetect vì đã có subscription
            MobileScanner(
              controller: _controller,
            ),

            // Overlay với khung scan
            Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: Center(
                child: Container(
                  width: size.width * 0.7,
                  height: size.width * 0.7,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white, width: 2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),

            // Top bar
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: _isLoading ? null : () {
                        AppLogger.info('CommonQRScanner: Back button pressed');
                        if (widget.onBack != null) widget.onBack!();
                      },
                      icon: const Icon(
                        Icons.arrow_back_ios_new_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    Image.asset(
                      'assets/images/kienlongbank_logo.png',
                      height: 32,
                      color: Colors.white,
                    ),
                    const SizedBox(width: 40),
                  ],
                ),
              ),
            ),

            // Guidance text
            Positioned(
              top: screenHeight * 0.25,
              left: 0,
              right: 0,
              child: Container(
                margin: AppDimens.marginHorizontalLg,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      widget.customGuidanceText ?? 'Vui lòng đưa QR vào khung hình',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Hoặc chọn ảnh từ thư viện',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                    ),
                  ],
                ),
              ),
            ),

            // Bottom buttons
            Positioned(
              left: 0,
              right: 0,
              bottom: 48,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      if (widget.showFlashButton)
                        _ActionButton(
                          onPressed: _isLoading ? null : _toggleFlash,
                          icon: _isFlashOn ? Icons.flash_on : Icons.flash_off,
                          label: _isFlashOn ? 'Tắt đèn' : 'Bật đèn',
                        ),
                      if (widget.showGalleryButton)
                        _ActionButton(
                          onPressed: _isLoading ? null : _handleGalleryPick,
                          icon: Icons.photo_library_outlined,
                          label: 'Thư viện',
                        ),
                    ],
                  ),
                  if (widget.showSkipButton) ...[
                    const SizedBox(height: 24),
                    TextButton(
                      onPressed: _isLoading ? null : _handleSkipQRScan,
                      child: Text(
                        S.of(context).skip,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback? onPressed;

  const _ActionButton({
    required this.icon,
    required this.label,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: onPressed,
            icon: Icon(
              icon,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
} 