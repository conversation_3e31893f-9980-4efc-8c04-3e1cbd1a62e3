/// Common Widgets Barrel Export
/// Reusable widgets used across the entire application
/// 
/// This file provides a single import point for all common widgets that are used
/// throughout multiple features and screens in the app.
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/widgets/common/common.dart';
/// 
/// // Now you can use any common widget:
/// CommonButton(
///   text: 'Login',
///   onPressed: () {},
/// )
/// 
/// CommonTextField(
///   label: 'Email',
///   controller: emailController,
/// )
/// 
/// CommonDropdown<String>(
///   items: ['Option 1', 'Option 2'],
///   onChanged: (value) {},
/// )
/// ```
/// 
/// ## Widget Categories:
/// - **Base Components**: BaseScreen for consistent screen structure
/// - **Form Components**: CommonTextField, CommonDropdown, CommonButton
/// - **Layout Components**: BottomButton for consistent bottom actions
/// - **Navigation Components**: CustomAppBar for consistent app bar
/// - **Utility Components**: PhotoSourcePicker for image selection
library common_widgets;

export 'base_screen.dart';
export 'bottom_button.dart';
export 'common_bottom_sheet.dart';
export 'common_button.dart';
export 'common_checkbox.dart';
export 'common_date_picker.dart';
export 'common_dialog.dart';
export 'common_dropdown.dart';
export 'common_radio_group.dart';
export 'common_text_field.dart';
export 'common_toast.dart';
export 'common_toggle.dart';
export 'custom_app_bar.dart';
export 'photo_source_picker.dart';
export 'common_markdown.dart';
export 'document_image_picker.dart';
export 'keyboard_dismisser.dart';
export 'permission_dialog.dart';
export 'permission_example.dart';
export 'permission_debug.dart';
export 'permission_test_screen.dart';
export 'common_qr_scanner.dart';
export 'common_icon_button.dart';
export 'app_image.dart';
export 'loan_progress_indicator.dart';

// Base state components
export 'base_state/base_state.dart'; 
