import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';

/// Widget đơn giản để hiển thị hình ảnh từ assets
/// Tự động xử lý PNG và SVG
class AppImage extends StatelessWidget {
  final String imagePath;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Color? color;

  const AppImage(
    this.imagePath, {
    super.key,
    this.width,
    this.height,
    this.fit,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    if (AppImages.isSvg(imagePath)) {
      return SvgPicture.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit ?? BoxFit.contain,
        colorFilter: color != null
            ? ColorFilter.mode(color!, BlendMode.srcIn)
            : null,
      );
    } else {
      return Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        color: color,
      );
    }
  }
}

/// Extension để tạo AppImage từ String path
extension AppImageExtension on String {
  /// Tạo AppImage widget
  AppImage toImage({
    double? width,
    double? height,
    BoxFit? fit,
    Color? color,
  }) {
    return AppImage(
      this,
      width: width,
      height: height,
      fit: fit,
      color: color,
    );
  }

  /// Tạo AppImage với kích thước vuông
  AppImage toSquareImage({
    required double size,
    BoxFit? fit,
    Color? color,
  }) {
    return AppImage(
      this,
      width: size,
      height: size,
      fit: fit,
      color: color,
    );
  }
}
