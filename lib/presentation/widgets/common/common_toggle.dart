/// CommonToggle - Switch/Toggle tuỳ biến chuẩn dự án
///
/// <PERSON><PERSON> dụng cho các trường hợp cần bật/tắt trạng thái, hỗ trợ label, trạng thái enabled/disabled, màu sắc tuỳ chỉnh.
///
/// Ví dụ:
/// ```dart
/// CommonToggle(
///   label: S.of(context).receive_notification,
///   value: isOn,
///   onChanged: (v) => setState(() => isOn = v),
/// )
/// ```
library common_toggle;

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
// Core barrel export
import 'package:sales_app/core/core.dart';

class CommonToggle extends StatelessWidget {
  final String? label;
  final bool value;
  final ValueChanged<bool>? onChanged;
  final bool enabled;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color? textColor;
  final TextStyle? textStyle;
  final bool isRequired;

  const CommonToggle({
    super.key,
    this.label,
    required this.value,
    this.onChanged,
    this.enabled = true,
    this.activeColor,
    this.inactiveColor,
    this.textColor,
    this.textStyle,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: enabled ? () => onChanged?.call(!value) : null,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            if (label != null) ...[
              Expanded(
                child: Row(
                  children: [
                    Text(
                      label!,
                      style: textStyle ?? theme.textTheme.bodyMedium?.copyWith(
                        color: enabled 
                            ? (textColor ?? AppColors.textPrimary)
                            : AppColors.textSecondary.withValues(alpha: 0.5),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
            ],
            Transform.scale(
              scale: 0.95, // tuỳ chỉnh nhỏ lại nếu muốn
              child: CupertinoSwitch(
                value: value,
                onChanged: enabled ? onChanged : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
