import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Core barrel export
import 'package:sales_app/core/core.dart';
// Generated localization
import 'package:sales_app/generated/l10n.dart';
// Presentation utils barrel export
import 'package:sales_app/presentation/utils/utils.dart';

/// Fixed Step Navigator - Always visible step navigation component with expand/collapse
class FixedStepNavigator extends StatefulWidget {
  final LoanStep currentStep;
  final List<LoanStep> enabledSteps;
  final Function(LoanStep) onStepTap;
  final Color primaryColor;
  final Color backgroundColor;
  final bool showProgressBar;
  final bool showHeader;

  const FixedStepNavigator({
    super.key,
    required this.currentStep,
    required this.enabledSteps,
    required this.onStepTap,
    this.primaryColor = const Color(0xFF1E88E5),
    this.backgroundColor = Colors.white,
    this.showProgressBar = true,
    this.showHeader = true,
  });

  @override
  State<FixedStepNavigator> createState() => _FixedStepNavigatorState();
}

class _FixedStepNavigatorState extends State<FixedStepNavigator>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(_animationController);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  int get _currentIndex => widget.enabledSteps.indexOf(widget.currentStep);
  int get _totalSteps => widget.enabledSteps.length;
  double get _progress => _totalSteps > 0 ? (_currentIndex + 1) / _totalSteps : 0.0;

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _expandAnimation,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: widget.backgroundColor,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 6,
                offset: const Offset(0, 2),
                spreadRadius: -2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildCompactHeader(),
              if (_isExpanded) _buildExpandedContent(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCompactHeader() {
    return InkWell(
      onTap: _toggleExpanded,
      borderRadius: BorderRadius.circular(16.r),
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Row(
              children: [
                // Progress circle
                SizedBox(
                  width: 40.w,
                  height: 40.w,
                  child: Stack(
                    children: [
                      // Background circle
                      Container(
                        width: 40.w,
                        height: 40.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: widget.primaryColor.withValues(alpha: 0.1),
                        ),
                      ),
                      // Progress circle
                      CircularProgressIndicator(
                        value: _progress,
                        backgroundColor: widget.primaryColor.withValues(alpha: 0.2),
                        valueColor: AlwaysStoppedAnimation<Color>(widget.primaryColor),
                        strokeWidth: 3,
                      ),
                      // Step number
                      Center(
                        child: Text(
                          '${_currentIndex + 1}',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                            color: widget.primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                SizedBox(width: 12.w),
                
                // Step info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${S.of(context).step} ${_currentIndex + 1}/$_totalSteps',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        LoanStepLocalizer.getStepTitle(context, widget.currentStep),
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[800],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                
                // Expand/Collapse icon
                RotationTransition(
                  turns: _rotationAnimation,
                  child: Icon(
                    Icons.expand_more,
                    color: Colors.grey[600],
                    size: 20.w,
                  ),
                ),
              ],
            ),
            
            if (widget.showProgressBar) ...[
              SizedBox(height: 12.h),
              
              // Progress bar
              Container(
                height: 4.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2.r),
                  color: widget.primaryColor.withValues(alpha: 0.2),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _progress,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2.r),
                      color: widget.primaryColor,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildExpandedContent() {
    return AnimatedBuilder(
      animation: _expandAnimation,
      builder: (context, child) {
        return SizeTransition(
          sizeFactor: _expandAnimation,
          child: Container(
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                SizedBox(height: 12.h),
                
                // Steps list
                for (final entry in widget.enabledSteps.asMap().entries) ...[
                  () {
                    final index = entry.key;
                    final step = entry.value;
                    final isActive = step == widget.currentStep;
                    final isCompleted = index < _currentIndex;
                    final isEnabled = index <= _currentIndex;
                    
                    return _buildStepItem(
                      step: step,
                      stepIndex: index,
                      isActive: isActive,
                      isCompleted: isCompleted,
                      isEnabled: isEnabled,
                    );
                  }(),
                ],
                
                SizedBox(height: 8.h),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStepItem({
    required LoanStep step,
    required int stepIndex,
    required bool isActive,
    required bool isCompleted,
    required bool isEnabled,
  }) {
    return InkWell(
      onTap: isEnabled ? () => widget.onStepTap(step) : null,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: isActive ? widget.primaryColor.withValues(alpha: 0.1) : null,
          border: isActive ? Border.all(
            color: widget.primaryColor.withValues(alpha: 0.3),
            width: 1,
          ) : null,
        ),
        child: Row(
          children: [
            // Step indicator
            Container(
              width: 24.w,
              height: 24.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isCompleted 
                    ? widget.primaryColor 
                    : isActive 
                        ? widget.primaryColor.withValues(alpha: 0.2)
                        : Colors.grey[300],
                border: isActive && !isCompleted ? Border.all(
                  color: widget.primaryColor,
                  width: 2,
                ) : null,
              ),
              child: Center(
                child: isCompleted
                    ? Icon(
                        Icons.check,
                        size: 14.w,
                        color: Colors.white,
                      )
                    : Text(
                        '${stepIndex + 1}',
                        style: TextStyle(
                          fontSize: 11.sp,
                          fontWeight: FontWeight.w600,
                          color: isActive 
                              ? widget.primaryColor 
                              : isEnabled
                                  ? Colors.grey[700]
                                  : Colors.grey[500],
                        ),
                      ),
              ),
            ),
            
            SizedBox(width: 12.w),
            
            // Step title
            Expanded(
              child: Text(
                LoanStepLocalizer.getStepTitle(context, step),
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                  color: isActive 
                      ? widget.primaryColor 
                      : isEnabled
                          ? Colors.grey[800]
                          : Colors.grey[500],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // Status icon
            if (isCompleted)
              Icon(
                Icons.check_circle,
                size: 16.w,
                color: widget.primaryColor,
              )
            else if (isActive)
              Icon(
                Icons.radio_button_checked,
                size: 16.w,
                color: widget.primaryColor,
              )
            else if (isEnabled)
              Icon(
                Icons.radio_button_unchecked,
                size: 16.w,
                color: Colors.grey[400],
              )
            else
              Icon(
                Icons.lock_outline,
                size: 16.w,
                color: Colors.grey[400],
              ),
          ],
        ),
      ),
    );
  }
} 