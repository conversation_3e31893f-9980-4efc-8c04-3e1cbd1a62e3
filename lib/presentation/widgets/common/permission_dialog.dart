import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';
// Presentation barrel exports
import 'package:sales_app/presentation/widgets/widgets.dart';

/// Dialog hiển thị thông tin permission và cho phép user cấp quyền
class PermissionDialog extends StatelessWidget {
  final PermissionType permissionType;
  final PermissionStatus status;
  final VoidCallback? onGrantPermission;
  final VoidCallback? onOpenSettings;
  final VoidCallback? onCancel;
  final String? title;
  final String? message;
  final String? grantButtonText;
  final String? settingsButtonText;
  final String? cancelButtonText;

  const PermissionDialog({
    super.key,
    required this.permissionType,
    required this.status,
    this.onGrantPermission,
    this.onOpenSettings,
    this.onCancel,
    this.title,
    this.message,
    this.grantButtonText,
    this.settingsButtonText,
    this.cancelButtonText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: AppDimens.borderRadius16,
      ),
      title: Row(
        children: [
          Icon(
            _getPermissionIcon(),
            color: _getPermissionColor(),
            size: 24,
          ),
          AppDimens.w8,
          Expanded(
            child: Text(
              title ?? 'Quyền truy cập ${permissionType.displayName}',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            message ?? PermissionUtils.getPermissionMessage(permissionType, status),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          AppDimens.h12,
          _buildStatusInfo(context),
        ],
      ),
      actions: _buildActions(context),
    );
  }

  /// Xây dựng thông tin trạng thái permission
  Widget _buildStatusInfo(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Container(
      padding: AppDimens.paddingAllMd,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: AppDimens.borderRadius12,
        border: Border.all(
          color: _getPermissionColor().withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getStatusIcon(),
            color: _getPermissionColor(),
            size: 20,
          ),
          AppDimens.w8,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Trạng thái: ${PermissionUtils.getStatusDisplayName(status)}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  PermissionUtils.getStatusDescription(status),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.surfaceContainerHighest,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Xây dựng các action buttons
  List<Widget> _buildActions(BuildContext context) {
    final actions = <Widget>[];
    
    // Button hủy
    if (cancelButtonText != null || onCancel != null) {
      actions.add(
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            onCancel?.call();
          },
          child: Text(cancelButtonText ?? 'Hủy'),
        ),
      );
    }
    
    // Button cấp quyền hoặc vào cài đặt
    if (PermissionUtils.canRequestAgain(status)) {
      actions.add(
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            onGrantPermission?.call();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: _getPermissionColor(),
            foregroundColor: Colors.white,
          ),
          child: Text(grantButtonText ?? 'Cấp quyền'),
        ),
      );
    } else if (PermissionUtils.shouldOpenAppSettings(status)) {
      actions.add(
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            onOpenSettings?.call();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: _getPermissionColor(),
            foregroundColor: Colors.white,
          ),
          child: Text(settingsButtonText ?? 'Vào cài đặt'),
        ),
      );
    }
    
    return actions;
  }

  /// Lấy icon cho permission
  IconData _getPermissionIcon() {
    switch (permissionType) {
      case PermissionType.camera:
        return Icons.camera_alt;
      case PermissionType.photos:
      case PermissionType.photoLibrary:
        return Icons.photo_library;
      case PermissionType.microphone:
        return Icons.mic;
      case PermissionType.location:
      case PermissionType.locationWhenInUse:
      case PermissionType.locationAlways:
        return Icons.location_on;
      case PermissionType.storage:
        return Icons.storage;
      case PermissionType.videos:
        return Icons.video_library;
      case PermissionType.audio:
        return Icons.audiotrack;
      case PermissionType.bluetooth:
      case PermissionType.bluetoothConnect:
      case PermissionType.bluetoothScan:
      case PermissionType.bluetoothAdvertise:
        return Icons.bluetooth;
      case PermissionType.calendar:
        return Icons.calendar_today;
      case PermissionType.contacts:
        return Icons.contacts;
      case PermissionType.phone:
        return Icons.phone;
      case PermissionType.sensors:
        return Icons.sensors;
      case PermissionType.sms:
        return Icons.sms;
      case PermissionType.speech:
        return Icons.record_voice_over;
      case PermissionType.notification:
        return Icons.notifications;
      default:
        return Icons.security;
    }
  }

  /// Lấy icon cho status
  IconData _getStatusIcon() {
    switch (status) {
      case PermissionStatus.granted:
        return Icons.check_circle;
      case PermissionStatus.denied:
        return Icons.cancel;
      case PermissionStatus.permanentlyDenied:
        return Icons.block;
      case PermissionStatus.restricted:
        return Icons.warning;
      case PermissionStatus.limited:
        return Icons.info;
      case PermissionStatus.provisional:
        return Icons.schedule;
      default:
        return Icons.help;
    }
  }

  /// Lấy màu cho permission
  Color _getPermissionColor() {
    switch (status) {
      case PermissionStatus.granted:
        return Colors.green;
      case PermissionStatus.denied:
        return Colors.orange;
      case PermissionStatus.permanentlyDenied:
        return Colors.red;
      case PermissionStatus.restricted:
        return Colors.purple;
      case PermissionStatus.limited:
        return Colors.blue;
      case PermissionStatus.provisional:
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }
}

/// Dialog hiển thị nhiều permission cùng lúc
class MultiPermissionDialog extends StatelessWidget {
  final Map<PermissionType, PermissionStatus> permissions;
  final VoidCallback? onGrantPermissions;
  final VoidCallback? onOpenSettings;
  final VoidCallback? onCancel;
  final String? title;
  final String? message;

  const MultiPermissionDialog({
    super.key,
    required this.permissions,
    this.onGrantPermissions,
    this.onOpenSettings,
    this.onCancel,
    this.title,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final hasCriticalPermissions = PermissionUtils.hasCriticalPermissions(
      permissions.values.toList(),
    );
    
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: AppDimens.borderRadius16,
      ),
      title: Row(
        children: [
          Icon(
            hasCriticalPermissions ? Icons.warning : Icons.security,
            color: hasCriticalPermissions ? Colors.orange : Colors.blue,
            size: 24,
          ),
          AppDimens.w8,
          Expanded(
            child: Text(
              title ?? 'Quyền truy cập cần thiết',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message != null) ...[
            Text(
              message!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            AppDimens.h12,
          ],
          _buildPermissionsList(context),
        ],
      ),
      actions: _buildActions(context),
    );
  }

  /// Xây dựng danh sách permissions
  Widget _buildPermissionsList(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Column(
      children: permissions.entries.map((entry) {
        final permissionType = entry.key;
        final status = entry.value;
        
        return Container(
          margin: EdgeInsets.only(bottom: AppDimens.spacingSM),
          padding: AppDimens.paddingAllMd,
          decoration: BoxDecoration(
            color: colorScheme.surfaceVariant.withOpacity(0.3),
            borderRadius: AppDimens.borderRadius12,
            border: Border.all(
              color: _getPermissionColor(status).withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                _getPermissionIcon(permissionType),
                color: _getPermissionColor(status),
                size: 20,
              ),
              AppDimens.w8,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      permissionType.displayName,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      PermissionUtils.getStatusDisplayName(status),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.surfaceContainerHighest,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                _getStatusIcon(status),
                color: _getPermissionColor(status),
                size: 16,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// Xây dựng các action buttons
  List<Widget> _buildActions(BuildContext context) {
    final actions = <Widget>[];
    
    // Button hủy
    actions.add(
      TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          onCancel?.call();
        },
        child: const Text('Hủy'),
      ),
    );
    
    // Kiểm tra xem có permission nào cần request không
    final canRequestAny = permissions.values.any(PermissionUtils.canRequestAgain);
    final hasPermanentlyDenied = permissions.values.any(PermissionUtils.isPermanentlyDenied);
    
    if (canRequestAny) {
      actions.add(
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            onGrantPermissions?.call();
          },
          child: const Text('Cấp quyền'),
        ),
      );
    }
    
    if (hasPermanentlyDenied) {
      actions.add(
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            onOpenSettings?.call();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
          child: const Text('Vào cài đặt'),
        ),
      );
    }
    
    return actions;
  }

  /// Lấy icon cho permission
  IconData _getPermissionIcon(PermissionType permissionType) {
    switch (permissionType) {
      case PermissionType.camera:
        return Icons.camera_alt;
      case PermissionType.photos:
      case PermissionType.photoLibrary:
        return Icons.photo_library;
      case PermissionType.microphone:
        return Icons.mic;
      case PermissionType.location:
      case PermissionType.locationWhenInUse:
      case PermissionType.locationAlways:
        return Icons.location_on;
      case PermissionType.storage:
        return Icons.storage;
      default:
        return Icons.security;
    }
  }

  /// Lấy icon cho status
  IconData _getStatusIcon(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return Icons.check_circle;
      case PermissionStatus.denied:
        return Icons.cancel;
      case PermissionStatus.permanentlyDenied:
        return Icons.block;
      case PermissionStatus.restricted:
        return Icons.warning;
      case PermissionStatus.limited:
        return Icons.info;
      case PermissionStatus.provisional:
        return Icons.schedule;
      default:
        return Icons.help;
    }
  }

  /// Lấy màu cho permission
  Color _getPermissionColor(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return Colors.green;
      case PermissionStatus.denied:
        return Colors.orange;
      case PermissionStatus.permanentlyDenied:
        return Colors.red;
      case PermissionStatus.restricted:
        return Colors.purple;
      case PermissionStatus.limited:
        return Colors.blue;
      case PermissionStatus.provisional:
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }
} 