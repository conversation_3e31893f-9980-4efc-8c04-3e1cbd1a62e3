import 'package:flutter/material.dart';
import 'package:sales_app/core/core.dart';

/// Common Icon Button Widget
/// 
/// Widget button với icon đ<PERSON><PERSON><PERSON> sử dụng phổ biến trong ứng dụng
/// Sử dụng AppIcons để đảm bảo tính nhất quán
class CommonIconButton extends StatelessWidget {
  const CommonIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.size = 24.0,
    this.color,
    this.backgroundColor,
    this.padding = const EdgeInsets.all(8.0),
    this.tooltip,
    this.isCircular = false,
  });

  /// IconData từ AppIcons
  final IconData icon;
  
  /// Callback khi button được nhấn
  final VoidCallback? onPressed;
  
  /// Kích thước icon
  final double size;
  
  /// Màu của icon
  final Color? color;
  
  /// Màu nền của button
  final Color? backgroundColor;
  
  /// Padding của button
  final EdgeInsetsGeometry padding;
  
  /// Tooltip hiển thị khi hover
  final String? tooltip;
  
  /// Có phải button hình tròn không
  final bool isCircular;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final iconColor = color ?? theme.iconTheme.color;
    final bgColor = backgroundColor ?? theme.colorScheme.surface;

    Widget iconWidget = Icon(
      icon,
      size: size,
      color: iconColor,
    );

    if (tooltip != null) {
      iconWidget = Tooltip(
        message: tooltip!,
        child: iconWidget,
      );
    }

    if (isCircular) {
      return Container(
        decoration: BoxDecoration(
          color: bgColor,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(size + padding.horizontal / 2),
            child: Padding(
              padding: padding,
              child: iconWidget,
            ),
          ),
        ),
      );
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: AppDimens.borderRadius8,
        child: Padding(
          padding: padding,
          child: iconWidget,
        ),
      ),
    );
  }
}

/// Common Icon Button với các preset phổ biến
class CommonIconButtons {
  CommonIconButtons._();

  /// Button Back với icon mũi tên trái
  static Widget back({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.back,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Quay lại',
    );
  }

  /// Button Close với icon X
  static Widget close({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.close,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Đóng',
    );
  }

  /// Button Menu với icon menu
  static Widget menu({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.menu,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Menu',
    );
  }

  /// Button Search với icon tìm kiếm
  static Widget search({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.search,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Tìm kiếm',
    );
  }

  /// Button Add với icon dấu cộng
  static Widget add({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
    bool isCircular = true,
  }) {
    return CommonIconButton(
      icon: AppIcons.add,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Thêm',
      isCircular: isCircular,
    );
  }

  /// Button Edit với icon chỉnh sửa
  static Widget edit({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.edit,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Chỉnh sửa',
    );
  }

  /// Button Delete với icon thùng rác
  static Widget delete({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.delete,
      onPressed: onPressed,
      size: size,
      color: color ?? Colors.red,
      tooltip: tooltip ?? 'Xóa',
    );
  }

  /// Button Camera với icon camera
  static Widget camera({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.camera,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Chụp ảnh',
    );
  }

  /// Button Gallery với icon thư viện ảnh
  static Widget gallery({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.gallery,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Thư viện ảnh',
    );
  }

  /// Button Share với icon chia sẻ
  static Widget share({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.share,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Chia sẻ',
    );
  }

  /// Button Refresh với icon làm mới
  static Widget refresh({
    required VoidCallback? onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
  }) {
    return CommonIconButton(
      icon: AppIcons.refresh,
      onPressed: onPressed,
      size: size,
      color: color,
      tooltip: tooltip ?? 'Làm mới',
    );
  }
} 