import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

// Core barrel exports
import 'package:sales_app/core/core.dart';

/// Widget debug để test permission trực tiếp
class PermissionDebug extends StatefulWidget {
  const PermissionDebug({super.key});

  @override
  State<PermissionDebug> createState() => _PermissionDebugState();
}

class _PermissionDebugState extends State<PermissionDebug> {
  PermissionStatus? _cameraStatus;
  PermissionStatus? _photosStatus;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Permission Debug'),
      ),
      body: Padding(
        padding: AppDimens.paddingAllLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Camera Permission
            Card(
              child: Padding(
                padding: AppDimens.paddingAllMd,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Camera Permission',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    AppDimens.h8,
                    Text('Status: ${_cameraStatus?.name ?? 'Unknown'}'),
                    AppDimens.h8,
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _isLoading ? null : _checkCameraPermission,
                          child: const Text('Check'),
                        ),
                        AppDimens.w8,
                        ElevatedButton(
                          onPressed: _isLoading ? null : _requestCameraPermission,
                          child: const Text('Request'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            AppDimens.h16,
            
            // Photos Permission
            Card(
              child: Padding(
                padding: AppDimens.paddingAllMd,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Photos Permission',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    AppDimens.h8,
                    Text('Status: ${_photosStatus?.name ?? 'Unknown'}'),
                    AppDimens.h8,
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _isLoading ? null : _checkPhotosPermission,
                          child: const Text('Check'),
                        ),
                        AppDimens.w8,
                        ElevatedButton(
                          onPressed: _isLoading ? null : _requestPhotosPermission,
                          child: const Text('Request'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            AppDimens.h16,
            
            // Logs
            Card(
              child: Padding(
                padding: AppDimens.paddingAllMd,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Debug Info',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    AppDimens.h8,
                    Text('Platform: ${Theme.of(context).platform.name}'),
                    Text('Is Loading: $_isLoading'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkCameraPermission() async {
    setState(() => _isLoading = true);
    
    try {
      AppLogger.info('=== DEBUG: Checking camera permission ===');
      final status = await Permission.camera.status;
      AppLogger.info('Camera status: $status');
      AppLogger.info('Is granted: ${status.isGranted}');
      AppLogger.info('Is denied: ${status.isDenied}');
      AppLogger.info('Is permanently denied: ${status.isPermanentlyDenied}');
      
      setState(() {
        _cameraStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error('Error checking camera permission', error: e);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _requestCameraPermission() async {
    setState(() => _isLoading = true);
    
    try {
      AppLogger.info('=== DEBUG: Requesting camera permission ===');
      final status = await Permission.camera.request();
      AppLogger.info('Camera request result: $status');
      AppLogger.info('Is granted: ${status.isGranted}');
      AppLogger.info('Is denied: ${status.isDenied}');
      AppLogger.info('Is permanently denied: ${status.isPermanentlyDenied}');
      
      setState(() {
        _cameraStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error('Error requesting camera permission', error: e);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _checkPhotosPermission() async {
    setState(() => _isLoading = true);
    
    try {
      AppLogger.info('=== DEBUG: Checking photos permission ===');
      final status = await Permission.photos.status;
      AppLogger.info('Photos status: $status');
      AppLogger.info('Is granted: ${status.isGranted}');
      AppLogger.info('Is denied: ${status.isDenied}');
      AppLogger.info('Is permanently denied: ${status.isPermanentlyDenied}');
      
      setState(() {
        _photosStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error('Error checking photos permission', error: e);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _requestPhotosPermission() async {
    setState(() => _isLoading = true);
    
    try {
      AppLogger.info('=== DEBUG: Requesting photos permission ===');
      final status = await Permission.photos.request();
      AppLogger.info('Photos request result: $status');
      AppLogger.info('Is granted: ${status.isGranted}');
      AppLogger.info('Is denied: ${status.isDenied}');
      AppLogger.info('Is permanently denied: ${status.isPermanentlyDenied}');
      
      setState(() {
        _photosStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error('Error requesting photos permission', error: e);
      setState(() => _isLoading = false);
    }
  }
} 