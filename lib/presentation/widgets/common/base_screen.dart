import 'package:flutter/material.dart';
import 'custom_app_bar.dart';

class BaseScreen extends StatelessWidget {
  final dynamic title;
  final Widget body;
  final List<Widget>? actions;
  final bool centerTitle;
  final PreferredSizeWidget? bottom;
  final bool automaticallyImplyLeading;
  final Widget? leading;

  const BaseScreen({
    super.key,
    required this.title,
    required this.body,
    this.actions,
    this.centerTitle = true,
    this.bottom,
    this.automaticallyImplyLeading = true,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: title,
        actions: actions,
        centerTitle: centerTitle,
        bottom: bottom,
        automaticallyImplyLeading: automaticallyImplyLeading,
        leading: leading,
      ),
      body: Container(
        color: Colors.white,
        child: body,
      ),
    );
  }
} 