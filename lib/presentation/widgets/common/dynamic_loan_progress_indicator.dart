import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sales_app/core/core.dart';
import 'package:sales_app/generated/l10n.dart';

/// Dynamic loan progress indicator that can handle variable steps
class DynamicLoanProgressIndicator extends StatelessWidget {
  final LoanStep currentStep;
  final List<LoanStep> enabledSteps; // <PERSON>h s<PERSON>ch các bước đ<PERSON>c k<PERSON>ch hoạt
  final String stepTitle;
  final Color primaryColor;
  final Color backgroundColor;

  const DynamicLoanProgressIndicator({
    super.key,
    required this.currentStep,
    required this.enabledSteps,
    required this.stepTitle,
    this.primaryColor = const Color(0xFF1E88E5),
    this.backgroundColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    final currentIndex = enabledSteps.indexOf(currentStep);
    final totalSteps = enabledSteps.length;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStepIndicator(currentIndex, totalSteps),
                SizedBox(width: 16.w),
                _buildPercentageIndicator(currentIndex, totalSteps),
              ],
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            stepTitle,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Text(
            '${S.of(context).step} ${currentIndex + 1}/$totalSteps',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int currentIndex, int totalSteps) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(totalSteps, (index) {
        final isActive = index == currentIndex;
        final isCompleted = index < currentIndex;
        
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 24.w,
              height: 24.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isCompleted ? primaryColor : Colors.grey[200],
                border: Border.all(
                  color: isActive ? primaryColor : Colors.grey[300]!,
                  width: 2,
                ),
                boxShadow: isActive ? [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ] : null,
              ),
              child: Center(
                child: isCompleted
                    ? Icon(Icons.check, size: 16.w, color: Colors.white)
                    : Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: isActive ? primaryColor : Colors.grey[600],
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
            if (index < totalSteps - 1)
              Container(
                width: 32.w,
                height: 2,
                color: isCompleted ? primaryColor : Colors.grey[300],
              ),
          ],
        );
      }),
    );
  }

  Widget _buildPercentageIndicator(int currentIndex, int totalSteps) {
    final percentage = totalSteps > 0 ? ((currentIndex + 1) / totalSteps * 100).toInt() : 0;
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Text(
        '$percentage%',
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
          color: primaryColor,
        ),
      ),
    );
  }
} 