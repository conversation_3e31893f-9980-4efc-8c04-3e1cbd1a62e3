import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'package:sales_app/core/core.dart';

class CommonDatePicker extends HookWidget {
  final String? label;
  final String? hintText;
  final DateTime? selectedDate;
  final ValueChanged<DateTime?>? onDateSelected;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final bool enabled;
  final bool isRequired;
  final FormFieldValidator<DateTime>? validator;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? labelColor;
  final Color? iconColor;
  final Color? borderColor;
  final bool isDense;
  final bool filled;

  const CommonDatePicker({
    super.key,
    this.label,
    this.hintText,
    this.selectedDate,
    this.onDateSelected,
    this.firstDate,
    this.lastDate,
    this.enabled = true,
    this.isRequired = false,
    this.validator,
    this.backgroundColor,
    this.textColor,
    this.labelColor,
    this.iconColor,
    this.borderColor,
    this.isDense = false,
    this.filled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasFocus = useState(false);

    final defaultTextColor = textColor ?? AppColors.textPrimary;
    final defaultLabelColor = labelColor ?? AppColors.textSecondary;
    final defaultIconColor =
        iconColor ?? AppColors.primaryColor.withValues(alpha: 0.7);
    final defaultBorderColor = borderColor ?? AppColors.primaryColor;
    final borderRadius = BorderRadius.circular(8);

    String getDisplayText() {
      if (selectedDate != null) {
        return DateFormat('dd/MM/yyyy').format(selectedDate!);
      }
      return '';
    }

    return FormField<DateTime>(
      validator: validator,
      builder: (formState) {
        Future<void> selectDate() async {
          if (!enabled) return;

          hasFocus.value = true;

          final DateTime? picked = await showDatePicker(
            context: context,
            initialDate: selectedDate ?? DateTime.now(),
            firstDate: firstDate ?? DateTime(1900),
            lastDate: lastDate ?? DateTime(2100, 12, 31),
            locale: const Locale('vi', 'VN'),
            builder: (context, child) {
              return Theme(
                data: theme.copyWith(
                  colorScheme: theme.colorScheme.copyWith(
                    primary: AppColors.primaryColor,
                  ),
                ),
                child: child!,
              );
            },
          );

          hasFocus.value = false;

          if (picked != null && picked != selectedDate) {
            formState.didChange(picked); // Cập nhật giá trị cho FormField
            onDateSelected?.call(picked);
          }
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                color: filled
                    ? (backgroundColor ??
                        (hasFocus.value
                            ? defaultBorderColor.withValues(alpha: 0.05)
                            : AppColors.backgroundColor))
                    : Colors.transparent,
                borderRadius: borderRadius,
                border: Border.all(
                  color: formState.hasError
                      ? theme.colorScheme.error
                      : (hasFocus.value
                          ? defaultBorderColor
                          : defaultBorderColor.withValues(alpha: 0.2)),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: borderRadius,
                child: InkWell(
                  onTap: selectDate,
                  borderRadius: borderRadius,
                  child: Container(
                    // Sử dụng cùng padding như CommonTextField
                    // padding: EdgeInsets.symmetric(
                    //   horizontal: isDense ? 12 : 16,
                    //   // vertical: 10,
                    // ),
                    padding: EdgeInsets.fromLTRB(
                        isDense ? 12 : 16, 0, isDense ? 12 : 16, 10),
                    child: Stack(
                      children: [
                        // Floating label với style giống CommonTextField
                        if (label != null)
                          AnimatedPositioned(
                            duration: const Duration(milliseconds: 200),
                            left: 0,
                            // Điều chỉnh vị trí để label không bị cắt
                            top:
                                selectedDate != null || hasFocus.value ? -6 : 8,
                            child: AnimatedDefaultTextStyle(
                              duration: const Duration(milliseconds: 200),
                              style: TextStyle(
                                color: formState.hasError
                                    ? theme.colorScheme.error
                                    : (hasFocus.value
                                        ? defaultBorderColor
                                        : defaultLabelColor),
                                // Sử dụng cùng font size như CommonTextField
                                fontSize: selectedDate != null || hasFocus.value
                                    ? AppDimens.fontSM // 12sp
                                    : AppDimens.fontMD, // 14sp
                                fontWeight: FontWeight.w400,
                              ),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 4, vertical: 12),
                                color: Colors.transparent,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(label??'',),
                                    // Đã bỏ dấu * đỏ khi isRequired
                                  ],
                                ),
                              ),
                            ),
                          ),
                        // Content row với padding giống CommonTextField
                        Padding(
                          padding: EdgeInsets.only(
                            top: label != null ? 20 : 10,
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Text(
                                  getDisplayText().isEmpty
                                      ? (hintText ?? '')
                                      : getDisplayText(),
                                  style: theme.textTheme.bodyLarge?.copyWith(
                                    color: selectedDate != null
                                        ? defaultTextColor
                                        : defaultLabelColor.withValues(
                                            alpha: 0.5),
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                              Icon(
                                Icons.calendar_today,
                                color: defaultIconColor,
                                size: 20,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            if (formState.hasError && formState.errorText != null) ...[
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  formState.errorText!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.error,
                    height: 1.2,
                  ),
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}
