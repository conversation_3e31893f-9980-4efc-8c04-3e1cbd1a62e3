import 'package:flutter/material.dart';
import 'package:sales_app/core/constants/app_images.dart';
import 'package:sales_app/presentation/widgets/common/app_image.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final dynamic title;
  final List<Widget>? actions;
  final bool centerTitle;
  final PreferredSizeWidget? bottom;
  final bool automaticallyImplyLeading;
  final Widget? leading;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.centerTitle = true,
    this.bottom,
    this.automaticallyImplyLeading = true,
    this.leading,
  });

  @override
Widget build(BuildContext context) {
  final Widget titleWidget = title is String
      ? Text(
          title as String,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
              ),
        )
      : title as Widget;

  return AppBar(
    title: titleWidget,
    actions: actions,
    centerTitle: centerTitle,
    bottom: bottom,
    automaticallyImplyLeading: automaticallyImplyLeading,
    elevation: 0,
    foregroundColor: Colors.white,
    leading: leading,
    flexibleSpace: AppImages.backgroundTitle.toImage(
      width: double.infinity,
      height: double.infinity,
    ),
  );
}

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

} 


