import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:io';

// Core barrel export
import 'package:sales_app/core/core.dart';

// Generated localization
import 'package:sales_app/generated/l10n.dart';

/// Common dialog widgets để tái sử dụng
/// Adaptive cho cả Android và iOS với responsive design
class CommonDialog {
  /// Hiển thị confirm dialog
  static Future<void> showConfirmDialog({
    required BuildContext context,
    required String message,
    String? title,
    String? confirmText,
    String? cancelText,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    bool isDangerous = false,
  }) async {
    final s = S.of(context);
    
    if (Platform.isIOS) {
      return showCupertinoDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(title ?? AppConfig.title),
            content: Text(message),
            actions: [
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  onCancel?.call();
                },
                child: Text(cancelText ?? s.close),
              ),
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  onConfirm();
                },
                isDestructiveAction: isDangerous,
                child: Text(confirmText ?? s.confirm),
              ),
            ],
          );
        },
      );
    } else {
      // Android và các platform khác
      final theme = Theme.of(context);
      
      return showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: AppDimens.borderRadius12,
            ),
            title: Text(
              title ?? AppConfig.title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: AppDimens.fontLG,
              ),
            ),
            content: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontSize: AppDimens.fontMD,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onCancel?.call();
                },
                child: Text(
                  cancelText ?? s.close,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                    fontSize: AppDimens.fontMD,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onConfirm();
                },
                child: Text(
                  confirmText ?? s.confirm,
                  style: TextStyle(
                    color: isDangerous ? theme.colorScheme.error : AppColors.primaryColor,
                    fontWeight: FontWeight.w600,
                    fontSize: AppDimens.fontMD,
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
  }

  /// Hiển thị confirm dialog với kết quả trả về
  static Future<bool> showConfirmDialogWithResult({
    required BuildContext context,
    required String message,
    String? title,
    String? confirmText,
    String? cancelText,
    bool isDangerous = false,
  }) async {
    final s = S.of(context);
    bool? result;
    
    if (Platform.isIOS) {
      await showCupertinoDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(title ?? AppConfig.title),
            content: Text(message),
            actions: [
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  result = false;
                },
                child: Text(cancelText ?? s.close),
              ),
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  result = true;
                },
                isDestructiveAction: isDangerous,
                child: Text(confirmText ?? s.confirm),
              ),
            ],
          );
        },
      );
    } else {
      // Android và các platform khác
      final theme = Theme.of(context);
      
      await showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: AppDimens.borderRadius12,
            ),
            title: Text(
              title ?? AppConfig.title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: AppDimens.fontLG,
              ),
            ),
            content: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontSize: AppDimens.fontMD,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  result = false;
                },
                child: Text(
                  cancelText ?? s.close,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                    fontSize: AppDimens.fontMD,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  result = true;
                },
                child: Text(
                  confirmText ?? s.confirm,
                  style: TextStyle(
                    color: isDangerous ? theme.colorScheme.error : AppColors.primaryColor,
                    fontWeight: FontWeight.w600,
                    fontSize: AppDimens.fontMD,
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
    
    return result ?? false;
  }

  /// Hiển thị info dialog
  static Future<void> showInfoDialog({
    required BuildContext context,
    required String message,
    String? title,
    String? buttonText,
    VoidCallback? onPressed,
  }) async {
    final s = S.of(context);
    
    if (Platform.isIOS) {
      return showCupertinoDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(title ?? AppConfig.title),
            content: Text(message),
            actions: [
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  onPressed?.call();
                },
                child: Text(buttonText ?? s.close),
              ),
            ],
          );
        },
      );
    } else {
      // Android và các platform khác
      final theme = Theme.of(context);
      
      return showDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: AppDimens.borderRadius12,
            ),
            title: Text(
              title ?? AppConfig.title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: AppDimens.fontLG,
              ),
            ),
            content: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontSize: AppDimens.fontMD,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onPressed?.call();
                },
                child: Text(
                  buttonText ?? s.close,
                  style: TextStyle(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w600,
                    fontSize: AppDimens.fontMD,
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
  }

  /// Hiển thị loading dialog
  static void showLoadingDialog({
    required BuildContext context,
    String? message,
    String? title,
  }) {
    final s = S.of(context);
    
    if (Platform.isIOS) {
      showCupertinoDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(title ?? AppConfig.title),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CupertinoActivityIndicator(),
                const SizedBox(height: 16),
                Text(message ?? s.submitting_form),
              ],
            ),
          );
        },
      );
    } else {
      // Android và các platform khác
      final theme = Theme.of(context);
      
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: AppDimens.borderRadius12,
            ),
            title: Text(
              title ?? AppConfig.title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: AppDimens.fontLG,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: AppDimens.iconLG,
                  height: AppDimens.iconLG,
                  child: const CircularProgressIndicator(
                    color: AppColors.primaryColor,
                    strokeWidth: 3.0,
                  ),
                ),
                AppDimens.h16,
                Text(
                  message ?? s.submitting_form,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontSize: AppDimens.fontMD,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      );
    }
  }

  /// Đóng loading dialog
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// Hiển thị error dialog
  static Future<void> showErrorDialog({
    required BuildContext context,
    required String message,
    String? title,
    String? buttonText,
    VoidCallback? onPressed,
  }) async {
    final s = S.of(context);
    
    if (Platform.isIOS) {
      return showCupertinoDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(title ?? AppConfig.title),
            content: Text(message),
            actions: [
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  onPressed?.call();
                },
                isDestructiveAction: true,
                child: Text(buttonText ?? s.close),
              ),
            ],
          );
        },
      );
    } else {
      // Android và các platform khác
      final theme = Theme.of(context);
      
      return showDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: AppDimens.borderRadius12,
            ),
            title: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: theme.colorScheme.error,
                  size: AppDimens.iconMD,
                ),
                AppDimens.w8,
                Expanded(
                  child: Text(
                    title ?? AppConfig.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.error,
                      fontSize: AppDimens.fontLG,
                    ),
                  ),
                ),
              ],
            ),
            content: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontSize: AppDimens.fontMD,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onPressed?.call();
                },
                child: Text(
                  buttonText ?? s.close,
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.w600,
                    fontSize: AppDimens.fontMD,
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
  }

  /// Hiển thị success dialog
  static Future<void> showSuccessDialog({
    required BuildContext context,
    required String message,
    String? title,
    String? buttonText,
    VoidCallback? onPressed,
  }) async {
    final s = S.of(context);
    
    if (Platform.isIOS) {
      return showCupertinoDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(title ?? AppConfig.title),
            content: Text(message),
            actions: [
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.of(context).pop();
                  onPressed?.call();
                },
                child: Text(buttonText ?? s.close),
              ),
            ],
          );
        },
      );
    } else {
      // Android và các platform khác
      final theme = Theme.of(context);
      
      return showDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: AppDimens.borderRadius12,
            ),
            title: Row(
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: AppColors.success,
                  size: AppDimens.iconMD,
                ),
                AppDimens.w8,
                Expanded(
                  child: Text(
                    title ?? AppConfig.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.success,
                      fontSize: AppDimens.fontLG,
                    ),
                  ),
                ),
              ],
            ),
            content: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontSize: AppDimens.fontMD,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onPressed?.call();
                },
                child: Text(
                  buttonText ?? s.close,
                  style: TextStyle(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                    fontSize: AppDimens.fontMD,
                  ),
                ),
              ),
            ],
          );
        },
      );
    }
  }
}
