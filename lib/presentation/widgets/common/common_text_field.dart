import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:sales_app/core/core.dart';

class CommonTextField extends HookWidget {
  final String? label;
  final String? hintText;
  final String? helperText;
  final TextEditingController controller;
  final bool isSensitive;
  final bool allowClear;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? suffixText;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final List<TextInputFormatter>? inputFormatters;
  final bool enabled;
  final bool isDropdown;
  final List<String>? items;
  final String? value;
  final Function(String?)? onDropdownChanged;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? labelColor;
  final Color? iconColor;
  final Color? borderColor;
  final bool isDense;
  final bool filled;
  final int maxLines;
  final FocusNode? focusNode;
  final FocusNode? nextFocusNode;
  final ValueChanged<String>? onFieldSubmitted;
  final ValueChanged<String>? onBlur;

  static const String kCurrencySuffixText = 'VND';

  const CommonTextField({
    super.key,
    this.label,
    this.hintText,
    this.helperText,
    required this.controller,
    this.isSensitive = false,
    this.allowClear = false,
    this.prefixIcon,
    this.suffixIcon,
    this.suffixText,
    this.keyboardType,
    this.textInputAction,
    this.validator,
    this.onChanged,
    this.inputFormatters,
    this.enabled = true,
    this.isDropdown = false,
    this.items,
    this.value,
    this.onDropdownChanged,
    this.backgroundColor,
    this.textColor,
    this.labelColor,
    this.iconColor,
    this.borderColor,
    this.isDense = false,
    this.filled = true,
    this.maxLines = 1,
    this.focusNode,
    this.nextFocusNode,
    this.onFieldSubmitted,
    this.onBlur,
  });

  /// Named constructor for currency input fields with "Đồng" suffix
  const CommonTextField.currency({
    super.key,
    this.label,
    this.hintText,
    this.helperText,
    required this.controller,
    this.validator,
    this.onChanged,
    this.inputFormatters,
    this.enabled = true,
    this.backgroundColor,
    this.textColor,
    this.labelColor,
    this.iconColor,
    this.borderColor,
    this.isDense = false,
    this.filled = true,
    this.textInputAction = TextInputAction.next,
    this.maxLines = 1,
    this.focusNode,
    this.nextFocusNode,
    this.onFieldSubmitted,
    this.onBlur,
  })  : isSensitive = false,
        allowClear = false,
        prefixIcon = null,
        suffixIcon = null,
        suffixText = CommonTextField.kCurrencySuffixText,
        keyboardType = TextInputType.number,
        isDropdown = false,
        items = null,
        value = null,
        onDropdownChanged = null;

  @override
  Widget build(BuildContext context) {
    useListenable(controller);
    final theme = Theme.of(context);
    final isObscure = useState(isSensitive);
    final hasFocus = useState(false);
    final localFocusNode = focusNode ?? useFocusNode();

    final defaultTextColor = textColor ?? AppColors.textPrimary;
    final defaultLabelColor = labelColor ?? AppColors.textSecondary;
    final defaultIconColor =
        iconColor ?? AppColors.primaryColor.withValues(alpha: 0.7);
    final defaultBorderColor = borderColor ?? AppColors.primaryColor;
    final borderRadius = AppDimens.inputBorderRadius;

    final effectiveBackgroundColor = backgroundColor ??
        (!enabled ? AppColors.surfaceColor : AppColors.white);

    // Listen to focus changes
    useEffect(() {
      void onFocusChange() {
        final wasFocused = hasFocus.value;
        final isNowFocused = localFocusNode.hasFocus;

        hasFocus.value = isNowFocused;

        // Gọi onBlur khi field mất focus
        if (wasFocused && !isNowFocused && onBlur != null) {
          onBlur!(controller.text);
        }
      }

      localFocusNode.addListener(onFocusChange);
      return () => localFocusNode.removeListener(onFocusChange);
    }, [localFocusNode, onBlur]);

    // Xử lý suffix icons và text
    Widget? suffixWidget;
    if (suffixIcon != null) {
      suffixWidget = suffixIcon;
    } else {
      final List<Widget> suffixElements = [];

      // Suffix text (như "Đồng")
      if (suffixText != null) {
        suffixElements.add(
          Padding(
            padding: AppDimens.paddingHorizontalMd,
            child: Center(
              widthFactor: 1.0,
              child: Text(
                suffixText!,
                style: theme.inputDecorationTheme.labelStyle?.copyWith(
                  color: AppColors.textSecondary,
                  fontSize: AppDimens.fontSM,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        );
      }

      // Icon xóa text
      if (allowClear && controller.text.isNotEmpty && hasFocus.value) {
        if (suffixElements.isNotEmpty) {
          suffixElements.add(
            Container(
              width: 1,
              height: AppDimens.iconSM + 4,
              color: (textColor == AppColors.white)
                  ? Colors.white
                  : theme.dividerColor,
            ),
          );
        }
        suffixElements.add(
          IconButton(
            icon: Icon(
              Icons.clear,
              size: AppDimens.iconSM,
              color: defaultIconColor,
            ),
            onPressed: () {
              controller.clear();
              if (onChanged != null) {
                onChanged!("");
              }
            },
          ),
        );
      }

      // Icon ẩn/hiện text nhạy cảm
      if (isSensitive) {
        if (suffixElements.isNotEmpty) {
          suffixElements.add(
            Container(
              width: 1,
              height: AppDimens.iconSM + 4,
              color: (textColor == AppColors.white)
                  ? Colors.white
                  : theme.dividerColor,
            ),
          );
        }
        suffixElements.add(
          IconButton(
            icon: Icon(
              isObscure.value ? Icons.visibility : Icons.visibility_off,
              size: AppDimens.iconSM,
              color: defaultIconColor,
            ),
            onPressed: () => isObscure.value = !isObscure.value,
          ),
        );
      }

      if (suffixElements.isNotEmpty) {
        suffixWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: suffixElements,
        );
      }
    }

    return FormField<String>(
      validator: validator,
      builder: (formState) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                color: filled ? effectiveBackgroundColor : Colors.transparent,
                borderRadius: borderRadius,
                border: Border.all(
                  color: formState.hasError
                      ? theme.colorScheme.error
                      : (hasFocus.value
                          ? defaultBorderColor
                          : defaultBorderColor.withValues(alpha: 0.2)),
                ),
              ),
              child: ClipRRect(
                borderRadius: borderRadius,
                child: TextFormField(
                  controller: controller,
                  focusNode: localFocusNode,
                  obscureText: isSensitive && isObscure.value,
                  keyboardType: keyboardType,
                  textInputAction: textInputAction,
                  onChanged: (value) {
                    formState.didChange(value);
                    if (onChanged != null) {
                      onChanged!(value);
                    }
                  },
                  enabled: enabled,
                  inputFormatters: inputFormatters,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: defaultTextColor,
                    fontSize: AppDimens.fontLG,
                  ),
                  maxLines: maxLines,
                  onFieldSubmitted: (value) {
                    if (nextFocusNode != null) {
                      FocusScope.of(context).requestFocus(nextFocusNode);
                    } else if (onFieldSubmitted != null) {
                      onFieldSubmitted!(value);
                    }
                  },
                  decoration: InputDecoration(
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal:
                          isDense ? AppDimens.spacingLG : AppDimens.spacingXL,
                      vertical:
                          isDense ? AppDimens.spacingSM : AppDimens.spacingMD,
                    ),
                    filled: filled,
                    fillColor: Colors.transparent,
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    errorStyle: const TextStyle(height: 0, fontSize: 0),
                    labelText: label,
                    labelStyle: theme.inputDecorationTheme.labelStyle?.copyWith(
                      color: formState.hasError
                          ? theme.colorScheme.error
                          : (hasFocus.value
                              ? defaultBorderColor
                              : defaultLabelColor),
                    ),
                    hintText: hintText,
                    hintStyle: theme.inputDecorationTheme.hintStyle?.copyWith(
                      color: defaultLabelColor.withValues(alpha: 0.5),
                    ),
                    prefixIcon: prefixIcon != null
                        ? IconTheme(
                            data: IconThemeData(
                              color: defaultIconColor,
                              size: AppDimens.iconSM,
                            ),
                            child: prefixIcon!,
                          )
                        : null,
                    suffixIcon: suffixWidget,
                  ),
                ),
              ),
            ),
            if (formState.hasError && formState.errorText != null) ...[
              Padding(
                padding: AppDimens.paddingVerticalXS,
                child: Text(
                  formState.errorText!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.error,
                    height: 1.2,
                  ),
                ),
              ),
            ] else if (helperText != null) ...[
              Padding(
                padding: AppDimens.paddingVerticalXS,
                child: Text(
                  helperText!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: defaultLabelColor,
                    height: 1.2,
                  ),
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}
