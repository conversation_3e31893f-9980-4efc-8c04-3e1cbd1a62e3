/// Presentation Widgets Master Barrel Export
/// Single import point for all widget components in the presentation layer
/// 
/// This file provides centralized access to all widget categories:
/// - Common widgets (globally reusable)
/// - Shared widgets (cross-feature)
/// - Loading system (adaptive loading)
/// - Network monitoring (network awareness)
/// 
/// Usage:
/// ```dart
/// import 'package:sales_app/presentation/widgets/widgets.dart';
/// 
/// // Access any widget from any category:
/// 
/// // Common widgets
/// CommonButton(text: 'Submit', onPressed: () {})
/// CommonTextField(label: 'Email')
/// 
/// // Shared widgets
/// UserAvatar(imageUrl: user.avatar)
/// 
/// // Loading system
/// class MyScreen extends BaseLoadingHookWidget {
///   // Implementation
/// }
/// 
/// // Network monitoring
/// final networkStatus = ref.watch(networkMonitorControllerProvider);
/// ```
/// 
/// ## Widget Organization:
/// - **common/**: Globally reusable widgets used throughout the app
/// - **shared/**: Cross-feature widgets used by 2-3 features
/// - **loading/**: Adaptive loading overlay system
/// - **network/**: Network monitoring and awareness system
library widgets;

// Auth widgets - authentication monitoring and session handling
export 'auth/auth.dart';

// Common widgets - globally reusable
export 'common/common.dart';

// Loading system - adaptive loading overlays
export 'loading/loading.dart';

// Network monitoring - network awareness system
export 'network/network.dart';

// Shared widgets - cross-feature components
export 'shared/shared.dart';

// Combined app builder
export 'app_builder.dart';
