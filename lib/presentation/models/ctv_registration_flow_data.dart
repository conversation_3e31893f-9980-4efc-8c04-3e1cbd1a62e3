  import 'package:sales_app/domain/domain.dart';

import 'personal_info_confirmation_data.dart';

/// Enum cho các bước trong flow đăng ký CTV
enum CTVRegistrationStep {
  documentUpload,      // identity_upload_page.dart
  qrScan,             // qr_scan_screen.dart  
  personalInfoConfirmation, // personal_info_confirmation_screen.dart
  success,
}

/// Model ch<PERSON>h chứa tất cả data và navigation state cho flow đăng ký CTV
class CTVRegistrationFlowData {
  // Navigation
  final CTVRegistrationStep currentStep;
  
  // Screen-specific data
  final IdentityUploadResult? identityUploadResult; // Từ identity_upload_page.dart
  final CCCDInfoEntity? cccdInfo; // Từ qr_scan_screen.dart
  final PersonalInfoConfirmationData? personalInfoConfirmation; // Từ personal_info_confirmation_screen.dart

  // Location data
  final List<ProvinceEntity> provinces;
  final List<BranchEntity> branches;
  final ProvinceEntity? selectedProvince;
  final BranchEntity? selectedBranch;
  final bool isLoadingBranches; // Track loading state riêng cho branches

  // Referrer data
  final ReferrerEntity? selectedReferrer;
  final bool isLoadingReferrer; // Track loading state cho referrer

  // QR Scan data
  final bool isLoadingQRScan; // Track loading state cho QR scan

  const CTVRegistrationFlowData({
    this.currentStep = CTVRegistrationStep.documentUpload,
    this.identityUploadResult,
    this.cccdInfo,
     this.personalInfoConfirmation,
    this.provinces = const [],
    this.branches = const [],
    this.selectedProvince,
    this.selectedBranch,
    this.isLoadingBranches = false,
    this.selectedReferrer,
    this.isLoadingReferrer = false,
    this.isLoadingQRScan = false,
  });

  CTVRegistrationFlowData copyWith({
    CTVRegistrationStep? currentStep,
    IdentityUploadResult? identityUploadResult,
    CCCDInfoEntity? cccdInfo,
    PersonalInfoConfirmationData? personalInfoConfirmation,
    List<ProvinceEntity>? provinces,
    List<BranchEntity>? branches,
    ProvinceEntity? selectedProvince,
    BranchEntity? selectedBranch,
    bool? isLoadingBranches,
    ReferrerEntity? selectedReferrer,
    bool? isLoadingReferrer,
    bool? isLoadingQRScan,
    // Flags để phân biệt giữa không thay đổi và set null
    bool clearSelectedReferrer = false,
    bool clearSelectedProvince = false,
    bool clearSelectedBranch = false,
    bool clearCCCDInfo = false,
    bool clearPersonalInfoConfirmation = false,
  }) {
    return CTVRegistrationFlowData(
      currentStep: currentStep ?? this.currentStep,
      identityUploadResult: identityUploadResult ?? this.identityUploadResult,
      cccdInfo: clearCCCDInfo ? null : (cccdInfo ?? this.cccdInfo),
      personalInfoConfirmation: clearPersonalInfoConfirmation ? null : (personalInfoConfirmation ?? this.personalInfoConfirmation),
      provinces: provinces ?? this.provinces,
      branches: branches ?? this.branches,
      selectedProvince: clearSelectedProvince ? null : (selectedProvince ?? this.selectedProvince),
      selectedBranch: clearSelectedBranch ? null : (selectedBranch ?? this.selectedBranch),
      isLoadingBranches: isLoadingBranches ?? this.isLoadingBranches,
      selectedReferrer: clearSelectedReferrer ? null : (selectedReferrer ?? this.selectedReferrer),
      isLoadingReferrer: isLoadingReferrer ?? this.isLoadingReferrer,
      isLoadingQRScan: isLoadingQRScan ?? this.isLoadingQRScan,
    );
  }

  /// Convenience method để clear referrer
  CTVRegistrationFlowData clearReferrer() {
    return copyWith(clearSelectedReferrer: true);
  }

  /// Convenience method để clear province
  CTVRegistrationFlowData clearProvince() {
    return copyWith(clearSelectedProvince: true);
  }

  /// Convenience method để clear branch  
  CTVRegistrationFlowData clearBranch() {
    return copyWith(clearSelectedBranch: true);
  }

  @override
  String toString() {
    return 'CTVRegistrationFlowData(currentStep: $currentStep, identityUploadResult: $identityUploadResult, cccdInfo: $cccdInfo, personalInfoConfirmation: $personalInfoConfirmation, provinces: ${provinces.length}, branches: ${branches.length}, selectedProvince: ${selectedProvince?.name}, selectedBranch: ${selectedBranch?.name}, isLoadingBranches: $isLoadingBranches, selectedReferrer: ${selectedReferrer?.referrerName}, isLoadingReferrer: $isLoadingReferrer, isLoadingQRScan: $isLoadingQRScan)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CTVRegistrationFlowData &&
        other.currentStep == currentStep &&
        other.identityUploadResult == identityUploadResult &&
        other.cccdInfo == cccdInfo &&
        other.personalInfoConfirmation == personalInfoConfirmation &&
        other.provinces == provinces &&
        other.branches == branches &&
        other.selectedProvince == selectedProvince &&
        other.selectedBranch == selectedBranch &&
        other.isLoadingBranches == isLoadingBranches &&
        other.selectedReferrer == selectedReferrer &&
        other.isLoadingReferrer == isLoadingReferrer &&
        other.isLoadingQRScan == isLoadingQRScan;
  }

  @override
  int get hashCode {
    return currentStep.hashCode ^
        identityUploadResult.hashCode ^
        cccdInfo.hashCode ^
        personalInfoConfirmation.hashCode ^
        provinces.hashCode ^
        branches.hashCode ^
        selectedProvince.hashCode ^
        selectedBranch.hashCode ^
        isLoadingBranches.hashCode ^
        selectedReferrer.hashCode ^
        isLoadingReferrer.hashCode ^
        isLoadingQRScan.hashCode;
  }
} 