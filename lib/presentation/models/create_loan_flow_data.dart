import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/core/enums/enums.dart';
import 'package:sales_app/domain/domain.dart';
import 'package:sales_app/presentation/models/loan/steps/steps.dart';

part 'create_loan_flow_data.freezed.dart';

@freezed
class CreateLoanFlowData with _$CreateLoanFlowData {
  const factory CreateLoanFlowData({
    @Default(LoanStep.identityDocument) LoanStep currentStep,
    Step1DocumentData? step1Data,
    Step2BorrowerData? step2Data,
    Step3CoBorrowerDocumentData? step3Data,
    Step4CoBorrowerInfoData? step4Data,
    Step5LoanRequestData? step5Data,
    Step6FinancialInfoData? step6Data,
    Step7CollateralInfoData? step7Data,
    Step8CollateralDetailData? step8Data,
    Step9DocumentListData? step9Data,
    Step10LoanConfirmationData? step10Data,
    // Loading states cho các step
    @Default(false) bool isLoadingStep1,
    @Default(false) bool isLoadingStep2,
    @Default(false) bool isLoadingStep3,
    @Default(false) bool isLoadingStep4,
    @Default(false) bool isLoadingStep5,
    @Default(false) bool isLoadingStep6,
    @Default(false) bool isLoadingStep7,
    @Default(false) bool isLoadingStep8,
    @Default(false) bool isLoadingStep9,
    @Default(false) bool isLoadingStep10,
    // Location data (chung cho cả người vay chính và đồng vay)
    @Default([]) List<ProvinceEntity> provinces,
    @Default(false) bool isLoadingProvinces,
    @Default([]) List<WardEntity> wards,
    @Default(false) bool isLoadingWards,
    // Selected values cho người vay chính
    ProvinceEntity? selectedProvince,
    WardEntity? selectedWard,
    // Selected values cho người đồng vay (tách biệt)
    ProvinceEntity? selectedCoBorrowerProvince,
    WardEntity? selectedCoBorrowerWard,
    // Selected values cho bước 6 (tài chính)
    ProvinceEntity? selectedProvinceStep6,
    WardEntity? selectedWardStep6,
    // System config data (marital status)
    @Default([]) List<SystemConfigEntity> maritalStatuses,
    SystemConfigEntity? selectedMaritalStatus,
    SystemConfigEntity? selectedCoBorrowerMaritalStatus,
    @Default(false) bool isLoadingMaritalStatuses,
    // System config data (loan purposes)
    @Default([]) List<SystemConfigEntity> loanPurposes,
    SystemConfigEntity? selectedLoanPurpose,
    @Default(false) bool isLoadingLoanPurposes,
    // System config data (loan terms)
    @Default([]) List<SystemConfigEntity> loanTerms,
    SystemConfigEntity? selectedLoanTerm,
    @Default(false) bool isLoadingLoanTerms,
    // System config data (income sources)
    @Default([]) List<SystemConfigEntity> incomeSources,
    SystemConfigEntity? selectedIncomeSource,
    @Default(false) bool isLoadingIncomeSources,
    // QR Scanner state
    @Default(false) bool showQRScanner,
    // Validation
    @Default(0) int validationAttempt,
    // Payment accounts
    @Default([]) List<PaymentAccountEntity> paymentAccounts,
    @Default(false) bool isLoadingPaymentAccounts,
    // Step 7 location data
    ProvinceEntity? selectedProvinceStep7,
    WardEntity? selectedWardStep7,
    // Step 7 asset types
    @Default([]) List<SystemConfigEntity> assetTypes,
    SystemConfigEntity? selectedAssetType,
    @Default(false) bool isLoadingAssetTypes,
  }) = _CreateLoanFlowData;
}
