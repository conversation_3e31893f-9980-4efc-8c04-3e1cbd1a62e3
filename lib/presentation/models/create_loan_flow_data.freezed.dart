// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_loan_flow_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreateLoanFlowData {
  LoanStep get currentStep => throw _privateConstructorUsedError;
  Step1DocumentData? get step1Data => throw _privateConstructorUsedError;
  Step2BorrowerData? get step2Data => throw _privateConstructorUsedError;
  Step3CoBorrowerDocumentData? get step3Data =>
      throw _privateConstructorUsedError;
  Step4CoBorrowerInfoData? get step4Data => throw _privateConstructorUsedError;
  Step5LoanRequestData? get step5Data => throw _privateConstructorUsedError;
  Step6FinancialInfoData? get step6Data => throw _privateConstructorUsedError;
  Step7CollateralInfoData? get step7Data => throw _privateConstructorUsedError;
  Step8CollateralDetailData? get step8Data =>
      throw _privateConstructorUsedError;
  Step9DocumentListData? get step9Data => throw _privateConstructorUsedError;
  Step10LoanConfirmationData? get step10Data =>
      throw _privateConstructorUsedError; // Loading states cho các step
  bool get isLoadingStep1 => throw _privateConstructorUsedError;
  bool get isLoadingStep2 => throw _privateConstructorUsedError;
  bool get isLoadingStep3 => throw _privateConstructorUsedError;
  bool get isLoadingStep4 => throw _privateConstructorUsedError;
  bool get isLoadingStep5 => throw _privateConstructorUsedError;
  bool get isLoadingStep6 => throw _privateConstructorUsedError;
  bool get isLoadingStep7 => throw _privateConstructorUsedError;
  bool get isLoadingStep8 => throw _privateConstructorUsedError;
  bool get isLoadingStep9 => throw _privateConstructorUsedError;
  bool get isLoadingStep10 =>
      throw _privateConstructorUsedError; // Location data (chung cho cả người vay chính và đồng vay)
  List<ProvinceEntity> get provinces => throw _privateConstructorUsedError;
  bool get isLoadingProvinces => throw _privateConstructorUsedError;
  List<WardEntity> get wards => throw _privateConstructorUsedError;
  bool get isLoadingWards =>
      throw _privateConstructorUsedError; // Selected values cho người vay chính
  ProvinceEntity? get selectedProvince => throw _privateConstructorUsedError;
  WardEntity? get selectedWard =>
      throw _privateConstructorUsedError; // Selected values cho người đồng vay (tách biệt)
  ProvinceEntity? get selectedCoBorrowerProvince =>
      throw _privateConstructorUsedError;
  WardEntity? get selectedCoBorrowerWard =>
      throw _privateConstructorUsedError; // Selected values cho bước 6 (tài chính)
  ProvinceEntity? get selectedProvinceStep6 =>
      throw _privateConstructorUsedError;
  WardEntity? get selectedWardStep6 =>
      throw _privateConstructorUsedError; // System config data (marital status)
  List<SystemConfigEntity> get maritalStatuses =>
      throw _privateConstructorUsedError;
  SystemConfigEntity? get selectedMaritalStatus =>
      throw _privateConstructorUsedError;
  SystemConfigEntity? get selectedCoBorrowerMaritalStatus =>
      throw _privateConstructorUsedError;
  bool get isLoadingMaritalStatuses =>
      throw _privateConstructorUsedError; // System config data (loan purposes)
  List<SystemConfigEntity> get loanPurposes =>
      throw _privateConstructorUsedError;
  SystemConfigEntity? get selectedLoanPurpose =>
      throw _privateConstructorUsedError;
  bool get isLoadingLoanPurposes =>
      throw _privateConstructorUsedError; // System config data (loan terms)
  List<SystemConfigEntity> get loanTerms => throw _privateConstructorUsedError;
  SystemConfigEntity? get selectedLoanTerm =>
      throw _privateConstructorUsedError;
  bool get isLoadingLoanTerms =>
      throw _privateConstructorUsedError; // System config data (income sources)
  List<SystemConfigEntity> get incomeSources =>
      throw _privateConstructorUsedError;
  SystemConfigEntity? get selectedIncomeSource =>
      throw _privateConstructorUsedError;
  bool get isLoadingIncomeSources =>
      throw _privateConstructorUsedError; // QR Scanner state
  bool get showQRScanner => throw _privateConstructorUsedError; // Validation
  int get validationAttempt =>
      throw _privateConstructorUsedError; // Payment accounts
  List<PaymentAccountEntity> get paymentAccounts =>
      throw _privateConstructorUsedError;
  bool get isLoadingPaymentAccounts =>
      throw _privateConstructorUsedError; // Step 7 location data
  ProvinceEntity? get selectedProvinceStep7 =>
      throw _privateConstructorUsedError;
  WardEntity? get selectedWardStep7 =>
      throw _privateConstructorUsedError; // Step 7 asset types
  List<SystemConfigEntity> get assetTypes => throw _privateConstructorUsedError;
  SystemConfigEntity? get selectedAssetType =>
      throw _privateConstructorUsedError;
  bool get isLoadingAssetTypes => throw _privateConstructorUsedError;

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateLoanFlowDataCopyWith<CreateLoanFlowData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateLoanFlowDataCopyWith<$Res> {
  factory $CreateLoanFlowDataCopyWith(
          CreateLoanFlowData value, $Res Function(CreateLoanFlowData) then) =
      _$CreateLoanFlowDataCopyWithImpl<$Res, CreateLoanFlowData>;
  @useResult
  $Res call(
      {LoanStep currentStep,
      Step1DocumentData? step1Data,
      Step2BorrowerData? step2Data,
      Step3CoBorrowerDocumentData? step3Data,
      Step4CoBorrowerInfoData? step4Data,
      Step5LoanRequestData? step5Data,
      Step6FinancialInfoData? step6Data,
      Step7CollateralInfoData? step7Data,
      Step8CollateralDetailData? step8Data,
      Step9DocumentListData? step9Data,
      Step10LoanConfirmationData? step10Data,
      bool isLoadingStep1,
      bool isLoadingStep2,
      bool isLoadingStep3,
      bool isLoadingStep4,
      bool isLoadingStep5,
      bool isLoadingStep6,
      bool isLoadingStep7,
      bool isLoadingStep8,
      bool isLoadingStep9,
      bool isLoadingStep10,
      List<ProvinceEntity> provinces,
      bool isLoadingProvinces,
      List<WardEntity> wards,
      bool isLoadingWards,
      ProvinceEntity? selectedProvince,
      WardEntity? selectedWard,
      ProvinceEntity? selectedCoBorrowerProvince,
      WardEntity? selectedCoBorrowerWard,
      ProvinceEntity? selectedProvinceStep6,
      WardEntity? selectedWardStep6,
      List<SystemConfigEntity> maritalStatuses,
      SystemConfigEntity? selectedMaritalStatus,
      SystemConfigEntity? selectedCoBorrowerMaritalStatus,
      bool isLoadingMaritalStatuses,
      List<SystemConfigEntity> loanPurposes,
      SystemConfigEntity? selectedLoanPurpose,
      bool isLoadingLoanPurposes,
      List<SystemConfigEntity> loanTerms,
      SystemConfigEntity? selectedLoanTerm,
      bool isLoadingLoanTerms,
      List<SystemConfigEntity> incomeSources,
      SystemConfigEntity? selectedIncomeSource,
      bool isLoadingIncomeSources,
      bool showQRScanner,
      int validationAttempt,
      List<PaymentAccountEntity> paymentAccounts,
      bool isLoadingPaymentAccounts,
      ProvinceEntity? selectedProvinceStep7,
      WardEntity? selectedWardStep7,
      List<SystemConfigEntity> assetTypes,
      SystemConfigEntity? selectedAssetType,
      bool isLoadingAssetTypes});

  $Step1DocumentDataCopyWith<$Res>? get step1Data;
  $Step2BorrowerDataCopyWith<$Res>? get step2Data;
  $Step3CoBorrowerDocumentDataCopyWith<$Res>? get step3Data;
  $Step4CoBorrowerInfoDataCopyWith<$Res>? get step4Data;
  $Step5LoanRequestDataCopyWith<$Res>? get step5Data;
  $Step6FinancialInfoDataCopyWith<$Res>? get step6Data;
  $Step7CollateralInfoDataCopyWith<$Res>? get step7Data;
  $Step8CollateralDetailDataCopyWith<$Res>? get step8Data;
  $Step9DocumentListDataCopyWith<$Res>? get step9Data;
  $Step10LoanConfirmationDataCopyWith<$Res>? get step10Data;
}

/// @nodoc
class _$CreateLoanFlowDataCopyWithImpl<$Res, $Val extends CreateLoanFlowData>
    implements $CreateLoanFlowDataCopyWith<$Res> {
  _$CreateLoanFlowDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? step1Data = freezed,
    Object? step2Data = freezed,
    Object? step3Data = freezed,
    Object? step4Data = freezed,
    Object? step5Data = freezed,
    Object? step6Data = freezed,
    Object? step7Data = freezed,
    Object? step8Data = freezed,
    Object? step9Data = freezed,
    Object? step10Data = freezed,
    Object? isLoadingStep1 = null,
    Object? isLoadingStep2 = null,
    Object? isLoadingStep3 = null,
    Object? isLoadingStep4 = null,
    Object? isLoadingStep5 = null,
    Object? isLoadingStep6 = null,
    Object? isLoadingStep7 = null,
    Object? isLoadingStep8 = null,
    Object? isLoadingStep9 = null,
    Object? isLoadingStep10 = null,
    Object? provinces = null,
    Object? isLoadingProvinces = null,
    Object? wards = null,
    Object? isLoadingWards = null,
    Object? selectedProvince = freezed,
    Object? selectedWard = freezed,
    Object? selectedCoBorrowerProvince = freezed,
    Object? selectedCoBorrowerWard = freezed,
    Object? selectedProvinceStep6 = freezed,
    Object? selectedWardStep6 = freezed,
    Object? maritalStatuses = null,
    Object? selectedMaritalStatus = freezed,
    Object? selectedCoBorrowerMaritalStatus = freezed,
    Object? isLoadingMaritalStatuses = null,
    Object? loanPurposes = null,
    Object? selectedLoanPurpose = freezed,
    Object? isLoadingLoanPurposes = null,
    Object? loanTerms = null,
    Object? selectedLoanTerm = freezed,
    Object? isLoadingLoanTerms = null,
    Object? incomeSources = null,
    Object? selectedIncomeSource = freezed,
    Object? isLoadingIncomeSources = null,
    Object? showQRScanner = null,
    Object? validationAttempt = null,
    Object? paymentAccounts = null,
    Object? isLoadingPaymentAccounts = null,
    Object? selectedProvinceStep7 = freezed,
    Object? selectedWardStep7 = freezed,
    Object? assetTypes = null,
    Object? selectedAssetType = freezed,
    Object? isLoadingAssetTypes = null,
  }) {
    return _then(_value.copyWith(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as LoanStep,
      step1Data: freezed == step1Data
          ? _value.step1Data
          : step1Data // ignore: cast_nullable_to_non_nullable
              as Step1DocumentData?,
      step2Data: freezed == step2Data
          ? _value.step2Data
          : step2Data // ignore: cast_nullable_to_non_nullable
              as Step2BorrowerData?,
      step3Data: freezed == step3Data
          ? _value.step3Data
          : step3Data // ignore: cast_nullable_to_non_nullable
              as Step3CoBorrowerDocumentData?,
      step4Data: freezed == step4Data
          ? _value.step4Data
          : step4Data // ignore: cast_nullable_to_non_nullable
              as Step4CoBorrowerInfoData?,
      step5Data: freezed == step5Data
          ? _value.step5Data
          : step5Data // ignore: cast_nullable_to_non_nullable
              as Step5LoanRequestData?,
      step6Data: freezed == step6Data
          ? _value.step6Data
          : step6Data // ignore: cast_nullable_to_non_nullable
              as Step6FinancialInfoData?,
      step7Data: freezed == step7Data
          ? _value.step7Data
          : step7Data // ignore: cast_nullable_to_non_nullable
              as Step7CollateralInfoData?,
      step8Data: freezed == step8Data
          ? _value.step8Data
          : step8Data // ignore: cast_nullable_to_non_nullable
              as Step8CollateralDetailData?,
      step9Data: freezed == step9Data
          ? _value.step9Data
          : step9Data // ignore: cast_nullable_to_non_nullable
              as Step9DocumentListData?,
      step10Data: freezed == step10Data
          ? _value.step10Data
          : step10Data // ignore: cast_nullable_to_non_nullable
              as Step10LoanConfirmationData?,
      isLoadingStep1: null == isLoadingStep1
          ? _value.isLoadingStep1
          : isLoadingStep1 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep2: null == isLoadingStep2
          ? _value.isLoadingStep2
          : isLoadingStep2 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep3: null == isLoadingStep3
          ? _value.isLoadingStep3
          : isLoadingStep3 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep4: null == isLoadingStep4
          ? _value.isLoadingStep4
          : isLoadingStep4 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep5: null == isLoadingStep5
          ? _value.isLoadingStep5
          : isLoadingStep5 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep6: null == isLoadingStep6
          ? _value.isLoadingStep6
          : isLoadingStep6 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep7: null == isLoadingStep7
          ? _value.isLoadingStep7
          : isLoadingStep7 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep8: null == isLoadingStep8
          ? _value.isLoadingStep8
          : isLoadingStep8 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep9: null == isLoadingStep9
          ? _value.isLoadingStep9
          : isLoadingStep9 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep10: null == isLoadingStep10
          ? _value.isLoadingStep10
          : isLoadingStep10 // ignore: cast_nullable_to_non_nullable
              as bool,
      provinces: null == provinces
          ? _value.provinces
          : provinces // ignore: cast_nullable_to_non_nullable
              as List<ProvinceEntity>,
      isLoadingProvinces: null == isLoadingProvinces
          ? _value.isLoadingProvinces
          : isLoadingProvinces // ignore: cast_nullable_to_non_nullable
              as bool,
      wards: null == wards
          ? _value.wards
          : wards // ignore: cast_nullable_to_non_nullable
              as List<WardEntity>,
      isLoadingWards: null == isLoadingWards
          ? _value.isLoadingWards
          : isLoadingWards // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedProvince: freezed == selectedProvince
          ? _value.selectedProvince
          : selectedProvince // ignore: cast_nullable_to_non_nullable
              as ProvinceEntity?,
      selectedWard: freezed == selectedWard
          ? _value.selectedWard
          : selectedWard // ignore: cast_nullable_to_non_nullable
              as WardEntity?,
      selectedCoBorrowerProvince: freezed == selectedCoBorrowerProvince
          ? _value.selectedCoBorrowerProvince
          : selectedCoBorrowerProvince // ignore: cast_nullable_to_non_nullable
              as ProvinceEntity?,
      selectedCoBorrowerWard: freezed == selectedCoBorrowerWard
          ? _value.selectedCoBorrowerWard
          : selectedCoBorrowerWard // ignore: cast_nullable_to_non_nullable
              as WardEntity?,
      selectedProvinceStep6: freezed == selectedProvinceStep6
          ? _value.selectedProvinceStep6
          : selectedProvinceStep6 // ignore: cast_nullable_to_non_nullable
              as ProvinceEntity?,
      selectedWardStep6: freezed == selectedWardStep6
          ? _value.selectedWardStep6
          : selectedWardStep6 // ignore: cast_nullable_to_non_nullable
              as WardEntity?,
      maritalStatuses: null == maritalStatuses
          ? _value.maritalStatuses
          : maritalStatuses // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedMaritalStatus: freezed == selectedMaritalStatus
          ? _value.selectedMaritalStatus
          : selectedMaritalStatus // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      selectedCoBorrowerMaritalStatus: freezed ==
              selectedCoBorrowerMaritalStatus
          ? _value.selectedCoBorrowerMaritalStatus
          : selectedCoBorrowerMaritalStatus // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingMaritalStatuses: null == isLoadingMaritalStatuses
          ? _value.isLoadingMaritalStatuses
          : isLoadingMaritalStatuses // ignore: cast_nullable_to_non_nullable
              as bool,
      loanPurposes: null == loanPurposes
          ? _value.loanPurposes
          : loanPurposes // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedLoanPurpose: freezed == selectedLoanPurpose
          ? _value.selectedLoanPurpose
          : selectedLoanPurpose // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingLoanPurposes: null == isLoadingLoanPurposes
          ? _value.isLoadingLoanPurposes
          : isLoadingLoanPurposes // ignore: cast_nullable_to_non_nullable
              as bool,
      loanTerms: null == loanTerms
          ? _value.loanTerms
          : loanTerms // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedLoanTerm: freezed == selectedLoanTerm
          ? _value.selectedLoanTerm
          : selectedLoanTerm // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingLoanTerms: null == isLoadingLoanTerms
          ? _value.isLoadingLoanTerms
          : isLoadingLoanTerms // ignore: cast_nullable_to_non_nullable
              as bool,
      incomeSources: null == incomeSources
          ? _value.incomeSources
          : incomeSources // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedIncomeSource: freezed == selectedIncomeSource
          ? _value.selectedIncomeSource
          : selectedIncomeSource // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingIncomeSources: null == isLoadingIncomeSources
          ? _value.isLoadingIncomeSources
          : isLoadingIncomeSources // ignore: cast_nullable_to_non_nullable
              as bool,
      showQRScanner: null == showQRScanner
          ? _value.showQRScanner
          : showQRScanner // ignore: cast_nullable_to_non_nullable
              as bool,
      validationAttempt: null == validationAttempt
          ? _value.validationAttempt
          : validationAttempt // ignore: cast_nullable_to_non_nullable
              as int,
      paymentAccounts: null == paymentAccounts
          ? _value.paymentAccounts
          : paymentAccounts // ignore: cast_nullable_to_non_nullable
              as List<PaymentAccountEntity>,
      isLoadingPaymentAccounts: null == isLoadingPaymentAccounts
          ? _value.isLoadingPaymentAccounts
          : isLoadingPaymentAccounts // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedProvinceStep7: freezed == selectedProvinceStep7
          ? _value.selectedProvinceStep7
          : selectedProvinceStep7 // ignore: cast_nullable_to_non_nullable
              as ProvinceEntity?,
      selectedWardStep7: freezed == selectedWardStep7
          ? _value.selectedWardStep7
          : selectedWardStep7 // ignore: cast_nullable_to_non_nullable
              as WardEntity?,
      assetTypes: null == assetTypes
          ? _value.assetTypes
          : assetTypes // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedAssetType: freezed == selectedAssetType
          ? _value.selectedAssetType
          : selectedAssetType // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingAssetTypes: null == isLoadingAssetTypes
          ? _value.isLoadingAssetTypes
          : isLoadingAssetTypes // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step1DocumentDataCopyWith<$Res>? get step1Data {
    if (_value.step1Data == null) {
      return null;
    }

    return $Step1DocumentDataCopyWith<$Res>(_value.step1Data!, (value) {
      return _then(_value.copyWith(step1Data: value) as $Val);
    });
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step2BorrowerDataCopyWith<$Res>? get step2Data {
    if (_value.step2Data == null) {
      return null;
    }

    return $Step2BorrowerDataCopyWith<$Res>(_value.step2Data!, (value) {
      return _then(_value.copyWith(step2Data: value) as $Val);
    });
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step3CoBorrowerDocumentDataCopyWith<$Res>? get step3Data {
    if (_value.step3Data == null) {
      return null;
    }

    return $Step3CoBorrowerDocumentDataCopyWith<$Res>(_value.step3Data!,
        (value) {
      return _then(_value.copyWith(step3Data: value) as $Val);
    });
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step4CoBorrowerInfoDataCopyWith<$Res>? get step4Data {
    if (_value.step4Data == null) {
      return null;
    }

    return $Step4CoBorrowerInfoDataCopyWith<$Res>(_value.step4Data!, (value) {
      return _then(_value.copyWith(step4Data: value) as $Val);
    });
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step5LoanRequestDataCopyWith<$Res>? get step5Data {
    if (_value.step5Data == null) {
      return null;
    }

    return $Step5LoanRequestDataCopyWith<$Res>(_value.step5Data!, (value) {
      return _then(_value.copyWith(step5Data: value) as $Val);
    });
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step6FinancialInfoDataCopyWith<$Res>? get step6Data {
    if (_value.step6Data == null) {
      return null;
    }

    return $Step6FinancialInfoDataCopyWith<$Res>(_value.step6Data!, (value) {
      return _then(_value.copyWith(step6Data: value) as $Val);
    });
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step7CollateralInfoDataCopyWith<$Res>? get step7Data {
    if (_value.step7Data == null) {
      return null;
    }

    return $Step7CollateralInfoDataCopyWith<$Res>(_value.step7Data!, (value) {
      return _then(_value.copyWith(step7Data: value) as $Val);
    });
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step8CollateralDetailDataCopyWith<$Res>? get step8Data {
    if (_value.step8Data == null) {
      return null;
    }

    return $Step8CollateralDetailDataCopyWith<$Res>(_value.step8Data!, (value) {
      return _then(_value.copyWith(step8Data: value) as $Val);
    });
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step9DocumentListDataCopyWith<$Res>? get step9Data {
    if (_value.step9Data == null) {
      return null;
    }

    return $Step9DocumentListDataCopyWith<$Res>(_value.step9Data!, (value) {
      return _then(_value.copyWith(step9Data: value) as $Val);
    });
  }

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Step10LoanConfirmationDataCopyWith<$Res>? get step10Data {
    if (_value.step10Data == null) {
      return null;
    }

    return $Step10LoanConfirmationDataCopyWith<$Res>(_value.step10Data!,
        (value) {
      return _then(_value.copyWith(step10Data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreateLoanFlowDataImplCopyWith<$Res>
    implements $CreateLoanFlowDataCopyWith<$Res> {
  factory _$$CreateLoanFlowDataImplCopyWith(_$CreateLoanFlowDataImpl value,
          $Res Function(_$CreateLoanFlowDataImpl) then) =
      __$$CreateLoanFlowDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoanStep currentStep,
      Step1DocumentData? step1Data,
      Step2BorrowerData? step2Data,
      Step3CoBorrowerDocumentData? step3Data,
      Step4CoBorrowerInfoData? step4Data,
      Step5LoanRequestData? step5Data,
      Step6FinancialInfoData? step6Data,
      Step7CollateralInfoData? step7Data,
      Step8CollateralDetailData? step8Data,
      Step9DocumentListData? step9Data,
      Step10LoanConfirmationData? step10Data,
      bool isLoadingStep1,
      bool isLoadingStep2,
      bool isLoadingStep3,
      bool isLoadingStep4,
      bool isLoadingStep5,
      bool isLoadingStep6,
      bool isLoadingStep7,
      bool isLoadingStep8,
      bool isLoadingStep9,
      bool isLoadingStep10,
      List<ProvinceEntity> provinces,
      bool isLoadingProvinces,
      List<WardEntity> wards,
      bool isLoadingWards,
      ProvinceEntity? selectedProvince,
      WardEntity? selectedWard,
      ProvinceEntity? selectedCoBorrowerProvince,
      WardEntity? selectedCoBorrowerWard,
      ProvinceEntity? selectedProvinceStep6,
      WardEntity? selectedWardStep6,
      List<SystemConfigEntity> maritalStatuses,
      SystemConfigEntity? selectedMaritalStatus,
      SystemConfigEntity? selectedCoBorrowerMaritalStatus,
      bool isLoadingMaritalStatuses,
      List<SystemConfigEntity> loanPurposes,
      SystemConfigEntity? selectedLoanPurpose,
      bool isLoadingLoanPurposes,
      List<SystemConfigEntity> loanTerms,
      SystemConfigEntity? selectedLoanTerm,
      bool isLoadingLoanTerms,
      List<SystemConfigEntity> incomeSources,
      SystemConfigEntity? selectedIncomeSource,
      bool isLoadingIncomeSources,
      bool showQRScanner,
      int validationAttempt,
      List<PaymentAccountEntity> paymentAccounts,
      bool isLoadingPaymentAccounts,
      ProvinceEntity? selectedProvinceStep7,
      WardEntity? selectedWardStep7,
      List<SystemConfigEntity> assetTypes,
      SystemConfigEntity? selectedAssetType,
      bool isLoadingAssetTypes});

  @override
  $Step1DocumentDataCopyWith<$Res>? get step1Data;
  @override
  $Step2BorrowerDataCopyWith<$Res>? get step2Data;
  @override
  $Step3CoBorrowerDocumentDataCopyWith<$Res>? get step3Data;
  @override
  $Step4CoBorrowerInfoDataCopyWith<$Res>? get step4Data;
  @override
  $Step5LoanRequestDataCopyWith<$Res>? get step5Data;
  @override
  $Step6FinancialInfoDataCopyWith<$Res>? get step6Data;
  @override
  $Step7CollateralInfoDataCopyWith<$Res>? get step7Data;
  @override
  $Step8CollateralDetailDataCopyWith<$Res>? get step8Data;
  @override
  $Step9DocumentListDataCopyWith<$Res>? get step9Data;
  @override
  $Step10LoanConfirmationDataCopyWith<$Res>? get step10Data;
}

/// @nodoc
class __$$CreateLoanFlowDataImplCopyWithImpl<$Res>
    extends _$CreateLoanFlowDataCopyWithImpl<$Res, _$CreateLoanFlowDataImpl>
    implements _$$CreateLoanFlowDataImplCopyWith<$Res> {
  __$$CreateLoanFlowDataImplCopyWithImpl(_$CreateLoanFlowDataImpl _value,
      $Res Function(_$CreateLoanFlowDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? step1Data = freezed,
    Object? step2Data = freezed,
    Object? step3Data = freezed,
    Object? step4Data = freezed,
    Object? step5Data = freezed,
    Object? step6Data = freezed,
    Object? step7Data = freezed,
    Object? step8Data = freezed,
    Object? step9Data = freezed,
    Object? step10Data = freezed,
    Object? isLoadingStep1 = null,
    Object? isLoadingStep2 = null,
    Object? isLoadingStep3 = null,
    Object? isLoadingStep4 = null,
    Object? isLoadingStep5 = null,
    Object? isLoadingStep6 = null,
    Object? isLoadingStep7 = null,
    Object? isLoadingStep8 = null,
    Object? isLoadingStep9 = null,
    Object? isLoadingStep10 = null,
    Object? provinces = null,
    Object? isLoadingProvinces = null,
    Object? wards = null,
    Object? isLoadingWards = null,
    Object? selectedProvince = freezed,
    Object? selectedWard = freezed,
    Object? selectedCoBorrowerProvince = freezed,
    Object? selectedCoBorrowerWard = freezed,
    Object? selectedProvinceStep6 = freezed,
    Object? selectedWardStep6 = freezed,
    Object? maritalStatuses = null,
    Object? selectedMaritalStatus = freezed,
    Object? selectedCoBorrowerMaritalStatus = freezed,
    Object? isLoadingMaritalStatuses = null,
    Object? loanPurposes = null,
    Object? selectedLoanPurpose = freezed,
    Object? isLoadingLoanPurposes = null,
    Object? loanTerms = null,
    Object? selectedLoanTerm = freezed,
    Object? isLoadingLoanTerms = null,
    Object? incomeSources = null,
    Object? selectedIncomeSource = freezed,
    Object? isLoadingIncomeSources = null,
    Object? showQRScanner = null,
    Object? validationAttempt = null,
    Object? paymentAccounts = null,
    Object? isLoadingPaymentAccounts = null,
    Object? selectedProvinceStep7 = freezed,
    Object? selectedWardStep7 = freezed,
    Object? assetTypes = null,
    Object? selectedAssetType = freezed,
    Object? isLoadingAssetTypes = null,
  }) {
    return _then(_$CreateLoanFlowDataImpl(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as LoanStep,
      step1Data: freezed == step1Data
          ? _value.step1Data
          : step1Data // ignore: cast_nullable_to_non_nullable
              as Step1DocumentData?,
      step2Data: freezed == step2Data
          ? _value.step2Data
          : step2Data // ignore: cast_nullable_to_non_nullable
              as Step2BorrowerData?,
      step3Data: freezed == step3Data
          ? _value.step3Data
          : step3Data // ignore: cast_nullable_to_non_nullable
              as Step3CoBorrowerDocumentData?,
      step4Data: freezed == step4Data
          ? _value.step4Data
          : step4Data // ignore: cast_nullable_to_non_nullable
              as Step4CoBorrowerInfoData?,
      step5Data: freezed == step5Data
          ? _value.step5Data
          : step5Data // ignore: cast_nullable_to_non_nullable
              as Step5LoanRequestData?,
      step6Data: freezed == step6Data
          ? _value.step6Data
          : step6Data // ignore: cast_nullable_to_non_nullable
              as Step6FinancialInfoData?,
      step7Data: freezed == step7Data
          ? _value.step7Data
          : step7Data // ignore: cast_nullable_to_non_nullable
              as Step7CollateralInfoData?,
      step8Data: freezed == step8Data
          ? _value.step8Data
          : step8Data // ignore: cast_nullable_to_non_nullable
              as Step8CollateralDetailData?,
      step9Data: freezed == step9Data
          ? _value.step9Data
          : step9Data // ignore: cast_nullable_to_non_nullable
              as Step9DocumentListData?,
      step10Data: freezed == step10Data
          ? _value.step10Data
          : step10Data // ignore: cast_nullable_to_non_nullable
              as Step10LoanConfirmationData?,
      isLoadingStep1: null == isLoadingStep1
          ? _value.isLoadingStep1
          : isLoadingStep1 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep2: null == isLoadingStep2
          ? _value.isLoadingStep2
          : isLoadingStep2 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep3: null == isLoadingStep3
          ? _value.isLoadingStep3
          : isLoadingStep3 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep4: null == isLoadingStep4
          ? _value.isLoadingStep4
          : isLoadingStep4 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep5: null == isLoadingStep5
          ? _value.isLoadingStep5
          : isLoadingStep5 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep6: null == isLoadingStep6
          ? _value.isLoadingStep6
          : isLoadingStep6 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep7: null == isLoadingStep7
          ? _value.isLoadingStep7
          : isLoadingStep7 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep8: null == isLoadingStep8
          ? _value.isLoadingStep8
          : isLoadingStep8 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep9: null == isLoadingStep9
          ? _value.isLoadingStep9
          : isLoadingStep9 // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingStep10: null == isLoadingStep10
          ? _value.isLoadingStep10
          : isLoadingStep10 // ignore: cast_nullable_to_non_nullable
              as bool,
      provinces: null == provinces
          ? _value._provinces
          : provinces // ignore: cast_nullable_to_non_nullable
              as List<ProvinceEntity>,
      isLoadingProvinces: null == isLoadingProvinces
          ? _value.isLoadingProvinces
          : isLoadingProvinces // ignore: cast_nullable_to_non_nullable
              as bool,
      wards: null == wards
          ? _value._wards
          : wards // ignore: cast_nullable_to_non_nullable
              as List<WardEntity>,
      isLoadingWards: null == isLoadingWards
          ? _value.isLoadingWards
          : isLoadingWards // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedProvince: freezed == selectedProvince
          ? _value.selectedProvince
          : selectedProvince // ignore: cast_nullable_to_non_nullable
              as ProvinceEntity?,
      selectedWard: freezed == selectedWard
          ? _value.selectedWard
          : selectedWard // ignore: cast_nullable_to_non_nullable
              as WardEntity?,
      selectedCoBorrowerProvince: freezed == selectedCoBorrowerProvince
          ? _value.selectedCoBorrowerProvince
          : selectedCoBorrowerProvince // ignore: cast_nullable_to_non_nullable
              as ProvinceEntity?,
      selectedCoBorrowerWard: freezed == selectedCoBorrowerWard
          ? _value.selectedCoBorrowerWard
          : selectedCoBorrowerWard // ignore: cast_nullable_to_non_nullable
              as WardEntity?,
      selectedProvinceStep6: freezed == selectedProvinceStep6
          ? _value.selectedProvinceStep6
          : selectedProvinceStep6 // ignore: cast_nullable_to_non_nullable
              as ProvinceEntity?,
      selectedWardStep6: freezed == selectedWardStep6
          ? _value.selectedWardStep6
          : selectedWardStep6 // ignore: cast_nullable_to_non_nullable
              as WardEntity?,
      maritalStatuses: null == maritalStatuses
          ? _value._maritalStatuses
          : maritalStatuses // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedMaritalStatus: freezed == selectedMaritalStatus
          ? _value.selectedMaritalStatus
          : selectedMaritalStatus // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      selectedCoBorrowerMaritalStatus: freezed ==
              selectedCoBorrowerMaritalStatus
          ? _value.selectedCoBorrowerMaritalStatus
          : selectedCoBorrowerMaritalStatus // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingMaritalStatuses: null == isLoadingMaritalStatuses
          ? _value.isLoadingMaritalStatuses
          : isLoadingMaritalStatuses // ignore: cast_nullable_to_non_nullable
              as bool,
      loanPurposes: null == loanPurposes
          ? _value._loanPurposes
          : loanPurposes // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedLoanPurpose: freezed == selectedLoanPurpose
          ? _value.selectedLoanPurpose
          : selectedLoanPurpose // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingLoanPurposes: null == isLoadingLoanPurposes
          ? _value.isLoadingLoanPurposes
          : isLoadingLoanPurposes // ignore: cast_nullable_to_non_nullable
              as bool,
      loanTerms: null == loanTerms
          ? _value._loanTerms
          : loanTerms // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedLoanTerm: freezed == selectedLoanTerm
          ? _value.selectedLoanTerm
          : selectedLoanTerm // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingLoanTerms: null == isLoadingLoanTerms
          ? _value.isLoadingLoanTerms
          : isLoadingLoanTerms // ignore: cast_nullable_to_non_nullable
              as bool,
      incomeSources: null == incomeSources
          ? _value._incomeSources
          : incomeSources // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedIncomeSource: freezed == selectedIncomeSource
          ? _value.selectedIncomeSource
          : selectedIncomeSource // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingIncomeSources: null == isLoadingIncomeSources
          ? _value.isLoadingIncomeSources
          : isLoadingIncomeSources // ignore: cast_nullable_to_non_nullable
              as bool,
      showQRScanner: null == showQRScanner
          ? _value.showQRScanner
          : showQRScanner // ignore: cast_nullable_to_non_nullable
              as bool,
      validationAttempt: null == validationAttempt
          ? _value.validationAttempt
          : validationAttempt // ignore: cast_nullable_to_non_nullable
              as int,
      paymentAccounts: null == paymentAccounts
          ? _value._paymentAccounts
          : paymentAccounts // ignore: cast_nullable_to_non_nullable
              as List<PaymentAccountEntity>,
      isLoadingPaymentAccounts: null == isLoadingPaymentAccounts
          ? _value.isLoadingPaymentAccounts
          : isLoadingPaymentAccounts // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedProvinceStep7: freezed == selectedProvinceStep7
          ? _value.selectedProvinceStep7
          : selectedProvinceStep7 // ignore: cast_nullable_to_non_nullable
              as ProvinceEntity?,
      selectedWardStep7: freezed == selectedWardStep7
          ? _value.selectedWardStep7
          : selectedWardStep7 // ignore: cast_nullable_to_non_nullable
              as WardEntity?,
      assetTypes: null == assetTypes
          ? _value._assetTypes
          : assetTypes // ignore: cast_nullable_to_non_nullable
              as List<SystemConfigEntity>,
      selectedAssetType: freezed == selectedAssetType
          ? _value.selectedAssetType
          : selectedAssetType // ignore: cast_nullable_to_non_nullable
              as SystemConfigEntity?,
      isLoadingAssetTypes: null == isLoadingAssetTypes
          ? _value.isLoadingAssetTypes
          : isLoadingAssetTypes // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CreateLoanFlowDataImpl implements _CreateLoanFlowData {
  const _$CreateLoanFlowDataImpl(
      {this.currentStep = LoanStep.identityDocument,
      this.step1Data,
      this.step2Data,
      this.step3Data,
      this.step4Data,
      this.step5Data,
      this.step6Data,
      this.step7Data,
      this.step8Data,
      this.step9Data,
      this.step10Data,
      this.isLoadingStep1 = false,
      this.isLoadingStep2 = false,
      this.isLoadingStep3 = false,
      this.isLoadingStep4 = false,
      this.isLoadingStep5 = false,
      this.isLoadingStep6 = false,
      this.isLoadingStep7 = false,
      this.isLoadingStep8 = false,
      this.isLoadingStep9 = false,
      this.isLoadingStep10 = false,
      final List<ProvinceEntity> provinces = const [],
      this.isLoadingProvinces = false,
      final List<WardEntity> wards = const [],
      this.isLoadingWards = false,
      this.selectedProvince,
      this.selectedWard,
      this.selectedCoBorrowerProvince,
      this.selectedCoBorrowerWard,
      this.selectedProvinceStep6,
      this.selectedWardStep6,
      final List<SystemConfigEntity> maritalStatuses = const [],
      this.selectedMaritalStatus,
      this.selectedCoBorrowerMaritalStatus,
      this.isLoadingMaritalStatuses = false,
      final List<SystemConfigEntity> loanPurposes = const [],
      this.selectedLoanPurpose,
      this.isLoadingLoanPurposes = false,
      final List<SystemConfigEntity> loanTerms = const [],
      this.selectedLoanTerm,
      this.isLoadingLoanTerms = false,
      final List<SystemConfigEntity> incomeSources = const [],
      this.selectedIncomeSource,
      this.isLoadingIncomeSources = false,
      this.showQRScanner = false,
      this.validationAttempt = 0,
      final List<PaymentAccountEntity> paymentAccounts = const [],
      this.isLoadingPaymentAccounts = false,
      this.selectedProvinceStep7,
      this.selectedWardStep7,
      final List<SystemConfigEntity> assetTypes = const [],
      this.selectedAssetType,
      this.isLoadingAssetTypes = false})
      : _provinces = provinces,
        _wards = wards,
        _maritalStatuses = maritalStatuses,
        _loanPurposes = loanPurposes,
        _loanTerms = loanTerms,
        _incomeSources = incomeSources,
        _paymentAccounts = paymentAccounts,
        _assetTypes = assetTypes;

  @override
  @JsonKey()
  final LoanStep currentStep;
  @override
  final Step1DocumentData? step1Data;
  @override
  final Step2BorrowerData? step2Data;
  @override
  final Step3CoBorrowerDocumentData? step3Data;
  @override
  final Step4CoBorrowerInfoData? step4Data;
  @override
  final Step5LoanRequestData? step5Data;
  @override
  final Step6FinancialInfoData? step6Data;
  @override
  final Step7CollateralInfoData? step7Data;
  @override
  final Step8CollateralDetailData? step8Data;
  @override
  final Step9DocumentListData? step9Data;
  @override
  final Step10LoanConfirmationData? step10Data;
// Loading states cho các step
  @override
  @JsonKey()
  final bool isLoadingStep1;
  @override
  @JsonKey()
  final bool isLoadingStep2;
  @override
  @JsonKey()
  final bool isLoadingStep3;
  @override
  @JsonKey()
  final bool isLoadingStep4;
  @override
  @JsonKey()
  final bool isLoadingStep5;
  @override
  @JsonKey()
  final bool isLoadingStep6;
  @override
  @JsonKey()
  final bool isLoadingStep7;
  @override
  @JsonKey()
  final bool isLoadingStep8;
  @override
  @JsonKey()
  final bool isLoadingStep9;
  @override
  @JsonKey()
  final bool isLoadingStep10;
// Location data (chung cho cả người vay chính và đồng vay)
  final List<ProvinceEntity> _provinces;
// Location data (chung cho cả người vay chính và đồng vay)
  @override
  @JsonKey()
  List<ProvinceEntity> get provinces {
    if (_provinces is EqualUnmodifiableListView) return _provinces;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_provinces);
  }

  @override
  @JsonKey()
  final bool isLoadingProvinces;
  final List<WardEntity> _wards;
  @override
  @JsonKey()
  List<WardEntity> get wards {
    if (_wards is EqualUnmodifiableListView) return _wards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_wards);
  }

  @override
  @JsonKey()
  final bool isLoadingWards;
// Selected values cho người vay chính
  @override
  final ProvinceEntity? selectedProvince;
  @override
  final WardEntity? selectedWard;
// Selected values cho người đồng vay (tách biệt)
  @override
  final ProvinceEntity? selectedCoBorrowerProvince;
  @override
  final WardEntity? selectedCoBorrowerWard;
// Selected values cho bước 6 (tài chính)
  @override
  final ProvinceEntity? selectedProvinceStep6;
  @override
  final WardEntity? selectedWardStep6;
// System config data (marital status)
  final List<SystemConfigEntity> _maritalStatuses;
// System config data (marital status)
  @override
  @JsonKey()
  List<SystemConfigEntity> get maritalStatuses {
    if (_maritalStatuses is EqualUnmodifiableListView) return _maritalStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_maritalStatuses);
  }

  @override
  final SystemConfigEntity? selectedMaritalStatus;
  @override
  final SystemConfigEntity? selectedCoBorrowerMaritalStatus;
  @override
  @JsonKey()
  final bool isLoadingMaritalStatuses;
// System config data (loan purposes)
  final List<SystemConfigEntity> _loanPurposes;
// System config data (loan purposes)
  @override
  @JsonKey()
  List<SystemConfigEntity> get loanPurposes {
    if (_loanPurposes is EqualUnmodifiableListView) return _loanPurposes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_loanPurposes);
  }

  @override
  final SystemConfigEntity? selectedLoanPurpose;
  @override
  @JsonKey()
  final bool isLoadingLoanPurposes;
// System config data (loan terms)
  final List<SystemConfigEntity> _loanTerms;
// System config data (loan terms)
  @override
  @JsonKey()
  List<SystemConfigEntity> get loanTerms {
    if (_loanTerms is EqualUnmodifiableListView) return _loanTerms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_loanTerms);
  }

  @override
  final SystemConfigEntity? selectedLoanTerm;
  @override
  @JsonKey()
  final bool isLoadingLoanTerms;
// System config data (income sources)
  final List<SystemConfigEntity> _incomeSources;
// System config data (income sources)
  @override
  @JsonKey()
  List<SystemConfigEntity> get incomeSources {
    if (_incomeSources is EqualUnmodifiableListView) return _incomeSources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_incomeSources);
  }

  @override
  final SystemConfigEntity? selectedIncomeSource;
  @override
  @JsonKey()
  final bool isLoadingIncomeSources;
// QR Scanner state
  @override
  @JsonKey()
  final bool showQRScanner;
// Validation
  @override
  @JsonKey()
  final int validationAttempt;
// Payment accounts
  final List<PaymentAccountEntity> _paymentAccounts;
// Payment accounts
  @override
  @JsonKey()
  List<PaymentAccountEntity> get paymentAccounts {
    if (_paymentAccounts is EqualUnmodifiableListView) return _paymentAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_paymentAccounts);
  }

  @override
  @JsonKey()
  final bool isLoadingPaymentAccounts;
// Step 7 location data
  @override
  final ProvinceEntity? selectedProvinceStep7;
  @override
  final WardEntity? selectedWardStep7;
// Step 7 asset types
  final List<SystemConfigEntity> _assetTypes;
// Step 7 asset types
  @override
  @JsonKey()
  List<SystemConfigEntity> get assetTypes {
    if (_assetTypes is EqualUnmodifiableListView) return _assetTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_assetTypes);
  }

  @override
  final SystemConfigEntity? selectedAssetType;
  @override
  @JsonKey()
  final bool isLoadingAssetTypes;

  @override
  String toString() {
    return 'CreateLoanFlowData(currentStep: $currentStep, step1Data: $step1Data, step2Data: $step2Data, step3Data: $step3Data, step4Data: $step4Data, step5Data: $step5Data, step6Data: $step6Data, step7Data: $step7Data, step8Data: $step8Data, step9Data: $step9Data, step10Data: $step10Data, isLoadingStep1: $isLoadingStep1, isLoadingStep2: $isLoadingStep2, isLoadingStep3: $isLoadingStep3, isLoadingStep4: $isLoadingStep4, isLoadingStep5: $isLoadingStep5, isLoadingStep6: $isLoadingStep6, isLoadingStep7: $isLoadingStep7, isLoadingStep8: $isLoadingStep8, isLoadingStep9: $isLoadingStep9, isLoadingStep10: $isLoadingStep10, provinces: $provinces, isLoadingProvinces: $isLoadingProvinces, wards: $wards, isLoadingWards: $isLoadingWards, selectedProvince: $selectedProvince, selectedWard: $selectedWard, selectedCoBorrowerProvince: $selectedCoBorrowerProvince, selectedCoBorrowerWard: $selectedCoBorrowerWard, selectedProvinceStep6: $selectedProvinceStep6, selectedWardStep6: $selectedWardStep6, maritalStatuses: $maritalStatuses, selectedMaritalStatus: $selectedMaritalStatus, selectedCoBorrowerMaritalStatus: $selectedCoBorrowerMaritalStatus, isLoadingMaritalStatuses: $isLoadingMaritalStatuses, loanPurposes: $loanPurposes, selectedLoanPurpose: $selectedLoanPurpose, isLoadingLoanPurposes: $isLoadingLoanPurposes, loanTerms: $loanTerms, selectedLoanTerm: $selectedLoanTerm, isLoadingLoanTerms: $isLoadingLoanTerms, incomeSources: $incomeSources, selectedIncomeSource: $selectedIncomeSource, isLoadingIncomeSources: $isLoadingIncomeSources, showQRScanner: $showQRScanner, validationAttempt: $validationAttempt, paymentAccounts: $paymentAccounts, isLoadingPaymentAccounts: $isLoadingPaymentAccounts, selectedProvinceStep7: $selectedProvinceStep7, selectedWardStep7: $selectedWardStep7, assetTypes: $assetTypes, selectedAssetType: $selectedAssetType, isLoadingAssetTypes: $isLoadingAssetTypes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateLoanFlowDataImpl &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.step1Data, step1Data) ||
                other.step1Data == step1Data) &&
            (identical(other.step2Data, step2Data) ||
                other.step2Data == step2Data) &&
            (identical(other.step3Data, step3Data) ||
                other.step3Data == step3Data) &&
            (identical(other.step4Data, step4Data) ||
                other.step4Data == step4Data) &&
            (identical(other.step5Data, step5Data) ||
                other.step5Data == step5Data) &&
            (identical(other.step6Data, step6Data) ||
                other.step6Data == step6Data) &&
            (identical(other.step7Data, step7Data) ||
                other.step7Data == step7Data) &&
            (identical(other.step8Data, step8Data) ||
                other.step8Data == step8Data) &&
            (identical(other.step9Data, step9Data) ||
                other.step9Data == step9Data) &&
            (identical(other.step10Data, step10Data) ||
                other.step10Data == step10Data) &&
            (identical(other.isLoadingStep1, isLoadingStep1) ||
                other.isLoadingStep1 == isLoadingStep1) &&
            (identical(other.isLoadingStep2, isLoadingStep2) ||
                other.isLoadingStep2 == isLoadingStep2) &&
            (identical(other.isLoadingStep3, isLoadingStep3) ||
                other.isLoadingStep3 == isLoadingStep3) &&
            (identical(other.isLoadingStep4, isLoadingStep4) ||
                other.isLoadingStep4 == isLoadingStep4) &&
            (identical(other.isLoadingStep5, isLoadingStep5) ||
                other.isLoadingStep5 == isLoadingStep5) &&
            (identical(other.isLoadingStep6, isLoadingStep6) ||
                other.isLoadingStep6 == isLoadingStep6) &&
            (identical(other.isLoadingStep7, isLoadingStep7) ||
                other.isLoadingStep7 == isLoadingStep7) &&
            (identical(other.isLoadingStep8, isLoadingStep8) ||
                other.isLoadingStep8 == isLoadingStep8) &&
            (identical(other.isLoadingStep9, isLoadingStep9) ||
                other.isLoadingStep9 == isLoadingStep9) &&
            (identical(other.isLoadingStep10, isLoadingStep10) ||
                other.isLoadingStep10 == isLoadingStep10) &&
            const DeepCollectionEquality()
                .equals(other._provinces, _provinces) &&
            (identical(other.isLoadingProvinces, isLoadingProvinces) ||
                other.isLoadingProvinces == isLoadingProvinces) &&
            const DeepCollectionEquality().equals(other._wards, _wards) &&
            (identical(other.isLoadingWards, isLoadingWards) ||
                other.isLoadingWards == isLoadingWards) &&
            (identical(other.selectedProvince, selectedProvince) ||
                other.selectedProvince == selectedProvince) &&
            (identical(other.selectedWard, selectedWard) ||
                other.selectedWard == selectedWard) &&
            (identical(other.selectedCoBorrowerProvince, selectedCoBorrowerProvince) ||
                other.selectedCoBorrowerProvince ==
                    selectedCoBorrowerProvince) &&
            (identical(other.selectedCoBorrowerWard, selectedCoBorrowerWard) ||
                other.selectedCoBorrowerWard == selectedCoBorrowerWard) &&
            (identical(other.selectedProvinceStep6, selectedProvinceStep6) ||
                other.selectedProvinceStep6 == selectedProvinceStep6) &&
            (identical(other.selectedWardStep6, selectedWardStep6) ||
                other.selectedWardStep6 == selectedWardStep6) &&
            const DeepCollectionEquality()
                .equals(other._maritalStatuses, _maritalStatuses) &&
            (identical(other.selectedMaritalStatus, selectedMaritalStatus) ||
                other.selectedMaritalStatus == selectedMaritalStatus) &&
            (identical(other.selectedCoBorrowerMaritalStatus, selectedCoBorrowerMaritalStatus) ||
                other.selectedCoBorrowerMaritalStatus ==
                    selectedCoBorrowerMaritalStatus) &&
            (identical(other.isLoadingMaritalStatuses, isLoadingMaritalStatuses) ||
                other.isLoadingMaritalStatuses == isLoadingMaritalStatuses) &&
            const DeepCollectionEquality()
                .equals(other._loanPurposes, _loanPurposes) &&
            (identical(other.selectedLoanPurpose, selectedLoanPurpose) ||
                other.selectedLoanPurpose == selectedLoanPurpose) &&
            (identical(other.isLoadingLoanPurposes, isLoadingLoanPurposes) ||
                other.isLoadingLoanPurposes == isLoadingLoanPurposes) &&
            const DeepCollectionEquality()
                .equals(other._loanTerms, _loanTerms) &&
            (identical(other.selectedLoanTerm, selectedLoanTerm) ||
                other.selectedLoanTerm == selectedLoanTerm) &&
            (identical(other.isLoadingLoanTerms, isLoadingLoanTerms) || other.isLoadingLoanTerms == isLoadingLoanTerms) &&
            const DeepCollectionEquality().equals(other._incomeSources, _incomeSources) &&
            (identical(other.selectedIncomeSource, selectedIncomeSource) || other.selectedIncomeSource == selectedIncomeSource) &&
            (identical(other.isLoadingIncomeSources, isLoadingIncomeSources) || other.isLoadingIncomeSources == isLoadingIncomeSources) &&
            (identical(other.showQRScanner, showQRScanner) || other.showQRScanner == showQRScanner) &&
            (identical(other.validationAttempt, validationAttempt) || other.validationAttempt == validationAttempt) &&
            const DeepCollectionEquality().equals(other._paymentAccounts, _paymentAccounts) &&
            (identical(other.isLoadingPaymentAccounts, isLoadingPaymentAccounts) || other.isLoadingPaymentAccounts == isLoadingPaymentAccounts) &&
            (identical(other.selectedProvinceStep7, selectedProvinceStep7) || other.selectedProvinceStep7 == selectedProvinceStep7) &&
            (identical(other.selectedWardStep7, selectedWardStep7) || other.selectedWardStep7 == selectedWardStep7) &&
            const DeepCollectionEquality().equals(other._assetTypes, _assetTypes) &&
            (identical(other.selectedAssetType, selectedAssetType) || other.selectedAssetType == selectedAssetType) &&
            (identical(other.isLoadingAssetTypes, isLoadingAssetTypes) || other.isLoadingAssetTypes == isLoadingAssetTypes));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        currentStep,
        step1Data,
        step2Data,
        step3Data,
        step4Data,
        step5Data,
        step6Data,
        step7Data,
        step8Data,
        step9Data,
        step10Data,
        isLoadingStep1,
        isLoadingStep2,
        isLoadingStep3,
        isLoadingStep4,
        isLoadingStep5,
        isLoadingStep6,
        isLoadingStep7,
        isLoadingStep8,
        isLoadingStep9,
        isLoadingStep10,
        const DeepCollectionEquality().hash(_provinces),
        isLoadingProvinces,
        const DeepCollectionEquality().hash(_wards),
        isLoadingWards,
        selectedProvince,
        selectedWard,
        selectedCoBorrowerProvince,
        selectedCoBorrowerWard,
        selectedProvinceStep6,
        selectedWardStep6,
        const DeepCollectionEquality().hash(_maritalStatuses),
        selectedMaritalStatus,
        selectedCoBorrowerMaritalStatus,
        isLoadingMaritalStatuses,
        const DeepCollectionEquality().hash(_loanPurposes),
        selectedLoanPurpose,
        isLoadingLoanPurposes,
        const DeepCollectionEquality().hash(_loanTerms),
        selectedLoanTerm,
        isLoadingLoanTerms,
        const DeepCollectionEquality().hash(_incomeSources),
        selectedIncomeSource,
        isLoadingIncomeSources,
        showQRScanner,
        validationAttempt,
        const DeepCollectionEquality().hash(_paymentAccounts),
        isLoadingPaymentAccounts,
        selectedProvinceStep7,
        selectedWardStep7,
        const DeepCollectionEquality().hash(_assetTypes),
        selectedAssetType,
        isLoadingAssetTypes
      ]);

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateLoanFlowDataImplCopyWith<_$CreateLoanFlowDataImpl> get copyWith =>
      __$$CreateLoanFlowDataImplCopyWithImpl<_$CreateLoanFlowDataImpl>(
          this, _$identity);
}

abstract class _CreateLoanFlowData implements CreateLoanFlowData {
  const factory _CreateLoanFlowData(
      {final LoanStep currentStep,
      final Step1DocumentData? step1Data,
      final Step2BorrowerData? step2Data,
      final Step3CoBorrowerDocumentData? step3Data,
      final Step4CoBorrowerInfoData? step4Data,
      final Step5LoanRequestData? step5Data,
      final Step6FinancialInfoData? step6Data,
      final Step7CollateralInfoData? step7Data,
      final Step8CollateralDetailData? step8Data,
      final Step9DocumentListData? step9Data,
      final Step10LoanConfirmationData? step10Data,
      final bool isLoadingStep1,
      final bool isLoadingStep2,
      final bool isLoadingStep3,
      final bool isLoadingStep4,
      final bool isLoadingStep5,
      final bool isLoadingStep6,
      final bool isLoadingStep7,
      final bool isLoadingStep8,
      final bool isLoadingStep9,
      final bool isLoadingStep10,
      final List<ProvinceEntity> provinces,
      final bool isLoadingProvinces,
      final List<WardEntity> wards,
      final bool isLoadingWards,
      final ProvinceEntity? selectedProvince,
      final WardEntity? selectedWard,
      final ProvinceEntity? selectedCoBorrowerProvince,
      final WardEntity? selectedCoBorrowerWard,
      final ProvinceEntity? selectedProvinceStep6,
      final WardEntity? selectedWardStep6,
      final List<SystemConfigEntity> maritalStatuses,
      final SystemConfigEntity? selectedMaritalStatus,
      final SystemConfigEntity? selectedCoBorrowerMaritalStatus,
      final bool isLoadingMaritalStatuses,
      final List<SystemConfigEntity> loanPurposes,
      final SystemConfigEntity? selectedLoanPurpose,
      final bool isLoadingLoanPurposes,
      final List<SystemConfigEntity> loanTerms,
      final SystemConfigEntity? selectedLoanTerm,
      final bool isLoadingLoanTerms,
      final List<SystemConfigEntity> incomeSources,
      final SystemConfigEntity? selectedIncomeSource,
      final bool isLoadingIncomeSources,
      final bool showQRScanner,
      final int validationAttempt,
      final List<PaymentAccountEntity> paymentAccounts,
      final bool isLoadingPaymentAccounts,
      final ProvinceEntity? selectedProvinceStep7,
      final WardEntity? selectedWardStep7,
      final List<SystemConfigEntity> assetTypes,
      final SystemConfigEntity? selectedAssetType,
      final bool isLoadingAssetTypes}) = _$CreateLoanFlowDataImpl;

  @override
  LoanStep get currentStep;
  @override
  Step1DocumentData? get step1Data;
  @override
  Step2BorrowerData? get step2Data;
  @override
  Step3CoBorrowerDocumentData? get step3Data;
  @override
  Step4CoBorrowerInfoData? get step4Data;
  @override
  Step5LoanRequestData? get step5Data;
  @override
  Step6FinancialInfoData? get step6Data;
  @override
  Step7CollateralInfoData? get step7Data;
  @override
  Step8CollateralDetailData? get step8Data;
  @override
  Step9DocumentListData? get step9Data;
  @override
  Step10LoanConfirmationData? get step10Data; // Loading states cho các step
  @override
  bool get isLoadingStep1;
  @override
  bool get isLoadingStep2;
  @override
  bool get isLoadingStep3;
  @override
  bool get isLoadingStep4;
  @override
  bool get isLoadingStep5;
  @override
  bool get isLoadingStep6;
  @override
  bool get isLoadingStep7;
  @override
  bool get isLoadingStep8;
  @override
  bool get isLoadingStep9;
  @override
  bool
      get isLoadingStep10; // Location data (chung cho cả người vay chính và đồng vay)
  @override
  List<ProvinceEntity> get provinces;
  @override
  bool get isLoadingProvinces;
  @override
  List<WardEntity> get wards;
  @override
  bool get isLoadingWards; // Selected values cho người vay chính
  @override
  ProvinceEntity? get selectedProvince;
  @override
  WardEntity?
      get selectedWard; // Selected values cho người đồng vay (tách biệt)
  @override
  ProvinceEntity? get selectedCoBorrowerProvince;
  @override
  WardEntity?
      get selectedCoBorrowerWard; // Selected values cho bước 6 (tài chính)
  @override
  ProvinceEntity? get selectedProvinceStep6;
  @override
  WardEntity? get selectedWardStep6; // System config data (marital status)
  @override
  List<SystemConfigEntity> get maritalStatuses;
  @override
  SystemConfigEntity? get selectedMaritalStatus;
  @override
  SystemConfigEntity? get selectedCoBorrowerMaritalStatus;
  @override
  bool get isLoadingMaritalStatuses; // System config data (loan purposes)
  @override
  List<SystemConfigEntity> get loanPurposes;
  @override
  SystemConfigEntity? get selectedLoanPurpose;
  @override
  bool get isLoadingLoanPurposes; // System config data (loan terms)
  @override
  List<SystemConfigEntity> get loanTerms;
  @override
  SystemConfigEntity? get selectedLoanTerm;
  @override
  bool get isLoadingLoanTerms; // System config data (income sources)
  @override
  List<SystemConfigEntity> get incomeSources;
  @override
  SystemConfigEntity? get selectedIncomeSource;
  @override
  bool get isLoadingIncomeSources; // QR Scanner state
  @override
  bool get showQRScanner; // Validation
  @override
  int get validationAttempt; // Payment accounts
  @override
  List<PaymentAccountEntity> get paymentAccounts;
  @override
  bool get isLoadingPaymentAccounts; // Step 7 location data
  @override
  ProvinceEntity? get selectedProvinceStep7;
  @override
  WardEntity? get selectedWardStep7; // Step 7 asset types
  @override
  List<SystemConfigEntity> get assetTypes;
  @override
  SystemConfigEntity? get selectedAssetType;
  @override
  bool get isLoadingAssetTypes;

  /// Create a copy of CreateLoanFlowData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateLoanFlowDataImplCopyWith<_$CreateLoanFlowDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
