import 'package:freezed_annotation/freezed_annotation.dart';

part 'step9_document_list_data.freezed.dart';

/// Model cho data của Step 9 - Danh sách tài li<PERSON><PERSON> b<PERSON> sung
@freezed
class Step9DocumentListData with _$Step9DocumentListData {
  const factory Step9DocumentListData({
    @Default([]) List<String> marriageDocuments,
    @Default([]) List<String> residenceDocuments,
    @Default([]) List<String> vehicleAppraisalDocuments,
    @Default([]) List<String> vehicleRegistrationDocuments,
    @Default([]) List<String> businessLicenseDocuments,
  }) = _Step9DocumentListData;

  // Custom getter
  const Step9DocumentListData._();
  
  /// Kiểm tra step đã hoàn thành chưa
  bool get isComplete {
    // C<PERSON> thể customize logic kiểm tra tài liệu bắt buộc ở đây
    return marriageDocuments.isNotEmpty ||
           residenceDocuments.isNotEmpty ||
           vehicleAppraisalDocuments.isNotEmpty ||
           vehicleRegistrationDocuments.isNotEmpty ||
           businessLicenseDocuments.isNotEmpty;
  }
} 