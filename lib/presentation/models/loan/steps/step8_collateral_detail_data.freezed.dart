// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'step8_collateral_detail_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Step8CollateralDetailData {
  String get licensePlate => throw _privateConstructorUsedError;
  String get assetName => throw _privateConstructorUsedError;
  String get chassisNumber => throw _privateConstructorUsedError;
  String get engineNumber => throw _privateConstructorUsedError;
  String get registrationCertificateNumber =>
      throw _privateConstructorUsedError;
  String get issuePlace => throw _privateConstructorUsedError;
  DateTime get issueDate => throw _privateConstructorUsedError;
  String get assetCondition => throw _privateConstructorUsedError;
  int get totalAssetValue => throw _privateConstructorUsedError;

  /// Create a copy of Step8CollateralDetailData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $Step8CollateralDetailDataCopyWith<Step8CollateralDetailData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $Step8CollateralDetailDataCopyWith<$Res> {
  factory $Step8CollateralDetailDataCopyWith(Step8CollateralDetailData value,
          $Res Function(Step8CollateralDetailData) then) =
      _$Step8CollateralDetailDataCopyWithImpl<$Res, Step8CollateralDetailData>;
  @useResult
  $Res call(
      {String licensePlate,
      String assetName,
      String chassisNumber,
      String engineNumber,
      String registrationCertificateNumber,
      String issuePlace,
      DateTime issueDate,
      String assetCondition,
      int totalAssetValue});
}

/// @nodoc
class _$Step8CollateralDetailDataCopyWithImpl<$Res,
        $Val extends Step8CollateralDetailData>
    implements $Step8CollateralDetailDataCopyWith<$Res> {
  _$Step8CollateralDetailDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Step8CollateralDetailData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licensePlate = null,
    Object? assetName = null,
    Object? chassisNumber = null,
    Object? engineNumber = null,
    Object? registrationCertificateNumber = null,
    Object? issuePlace = null,
    Object? issueDate = null,
    Object? assetCondition = null,
    Object? totalAssetValue = null,
  }) {
    return _then(_value.copyWith(
      licensePlate: null == licensePlate
          ? _value.licensePlate
          : licensePlate // ignore: cast_nullable_to_non_nullable
              as String,
      assetName: null == assetName
          ? _value.assetName
          : assetName // ignore: cast_nullable_to_non_nullable
              as String,
      chassisNumber: null == chassisNumber
          ? _value.chassisNumber
          : chassisNumber // ignore: cast_nullable_to_non_nullable
              as String,
      engineNumber: null == engineNumber
          ? _value.engineNumber
          : engineNumber // ignore: cast_nullable_to_non_nullable
              as String,
      registrationCertificateNumber: null == registrationCertificateNumber
          ? _value.registrationCertificateNumber
          : registrationCertificateNumber // ignore: cast_nullable_to_non_nullable
              as String,
      issuePlace: null == issuePlace
          ? _value.issuePlace
          : issuePlace // ignore: cast_nullable_to_non_nullable
              as String,
      issueDate: null == issueDate
          ? _value.issueDate
          : issueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      assetCondition: null == assetCondition
          ? _value.assetCondition
          : assetCondition // ignore: cast_nullable_to_non_nullable
              as String,
      totalAssetValue: null == totalAssetValue
          ? _value.totalAssetValue
          : totalAssetValue // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$Step8CollateralDetailDataImplCopyWith<$Res>
    implements $Step8CollateralDetailDataCopyWith<$Res> {
  factory _$$Step8CollateralDetailDataImplCopyWith(
          _$Step8CollateralDetailDataImpl value,
          $Res Function(_$Step8CollateralDetailDataImpl) then) =
      __$$Step8CollateralDetailDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String licensePlate,
      String assetName,
      String chassisNumber,
      String engineNumber,
      String registrationCertificateNumber,
      String issuePlace,
      DateTime issueDate,
      String assetCondition,
      int totalAssetValue});
}

/// @nodoc
class __$$Step8CollateralDetailDataImplCopyWithImpl<$Res>
    extends _$Step8CollateralDetailDataCopyWithImpl<$Res,
        _$Step8CollateralDetailDataImpl>
    implements _$$Step8CollateralDetailDataImplCopyWith<$Res> {
  __$$Step8CollateralDetailDataImplCopyWithImpl(
      _$Step8CollateralDetailDataImpl _value,
      $Res Function(_$Step8CollateralDetailDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of Step8CollateralDetailData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licensePlate = null,
    Object? assetName = null,
    Object? chassisNumber = null,
    Object? engineNumber = null,
    Object? registrationCertificateNumber = null,
    Object? issuePlace = null,
    Object? issueDate = null,
    Object? assetCondition = null,
    Object? totalAssetValue = null,
  }) {
    return _then(_$Step8CollateralDetailDataImpl(
      licensePlate: null == licensePlate
          ? _value.licensePlate
          : licensePlate // ignore: cast_nullable_to_non_nullable
              as String,
      assetName: null == assetName
          ? _value.assetName
          : assetName // ignore: cast_nullable_to_non_nullable
              as String,
      chassisNumber: null == chassisNumber
          ? _value.chassisNumber
          : chassisNumber // ignore: cast_nullable_to_non_nullable
              as String,
      engineNumber: null == engineNumber
          ? _value.engineNumber
          : engineNumber // ignore: cast_nullable_to_non_nullable
              as String,
      registrationCertificateNumber: null == registrationCertificateNumber
          ? _value.registrationCertificateNumber
          : registrationCertificateNumber // ignore: cast_nullable_to_non_nullable
              as String,
      issuePlace: null == issuePlace
          ? _value.issuePlace
          : issuePlace // ignore: cast_nullable_to_non_nullable
              as String,
      issueDate: null == issueDate
          ? _value.issueDate
          : issueDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      assetCondition: null == assetCondition
          ? _value.assetCondition
          : assetCondition // ignore: cast_nullable_to_non_nullable
              as String,
      totalAssetValue: null == totalAssetValue
          ? _value.totalAssetValue
          : totalAssetValue // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$Step8CollateralDetailDataImpl extends _Step8CollateralDetailData {
  const _$Step8CollateralDetailDataImpl(
      {this.licensePlate = '',
      this.assetName = '',
      this.chassisNumber = '',
      this.engineNumber = '',
      this.registrationCertificateNumber = '',
      this.issuePlace = '',
      required this.issueDate,
      this.assetCondition = '',
      this.totalAssetValue = 0})
      : super._();

  @override
  @JsonKey()
  final String licensePlate;
  @override
  @JsonKey()
  final String assetName;
  @override
  @JsonKey()
  final String chassisNumber;
  @override
  @JsonKey()
  final String engineNumber;
  @override
  @JsonKey()
  final String registrationCertificateNumber;
  @override
  @JsonKey()
  final String issuePlace;
  @override
  final DateTime issueDate;
  @override
  @JsonKey()
  final String assetCondition;
  @override
  @JsonKey()
  final int totalAssetValue;

  @override
  String toString() {
    return 'Step8CollateralDetailData(licensePlate: $licensePlate, assetName: $assetName, chassisNumber: $chassisNumber, engineNumber: $engineNumber, registrationCertificateNumber: $registrationCertificateNumber, issuePlace: $issuePlace, issueDate: $issueDate, assetCondition: $assetCondition, totalAssetValue: $totalAssetValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$Step8CollateralDetailDataImpl &&
            (identical(other.licensePlate, licensePlate) ||
                other.licensePlate == licensePlate) &&
            (identical(other.assetName, assetName) ||
                other.assetName == assetName) &&
            (identical(other.chassisNumber, chassisNumber) ||
                other.chassisNumber == chassisNumber) &&
            (identical(other.engineNumber, engineNumber) ||
                other.engineNumber == engineNumber) &&
            (identical(other.registrationCertificateNumber,
                    registrationCertificateNumber) ||
                other.registrationCertificateNumber ==
                    registrationCertificateNumber) &&
            (identical(other.issuePlace, issuePlace) ||
                other.issuePlace == issuePlace) &&
            (identical(other.issueDate, issueDate) ||
                other.issueDate == issueDate) &&
            (identical(other.assetCondition, assetCondition) ||
                other.assetCondition == assetCondition) &&
            (identical(other.totalAssetValue, totalAssetValue) ||
                other.totalAssetValue == totalAssetValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      licensePlate,
      assetName,
      chassisNumber,
      engineNumber,
      registrationCertificateNumber,
      issuePlace,
      issueDate,
      assetCondition,
      totalAssetValue);

  /// Create a copy of Step8CollateralDetailData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$Step8CollateralDetailDataImplCopyWith<_$Step8CollateralDetailDataImpl>
      get copyWith => __$$Step8CollateralDetailDataImplCopyWithImpl<
          _$Step8CollateralDetailDataImpl>(this, _$identity);
}

abstract class _Step8CollateralDetailData extends Step8CollateralDetailData {
  const factory _Step8CollateralDetailData(
      {final String licensePlate,
      final String assetName,
      final String chassisNumber,
      final String engineNumber,
      final String registrationCertificateNumber,
      final String issuePlace,
      required final DateTime issueDate,
      final String assetCondition,
      final int totalAssetValue}) = _$Step8CollateralDetailDataImpl;
  const _Step8CollateralDetailData._() : super._();

  @override
  String get licensePlate;
  @override
  String get assetName;
  @override
  String get chassisNumber;
  @override
  String get engineNumber;
  @override
  String get registrationCertificateNumber;
  @override
  String get issuePlace;
  @override
  DateTime get issueDate;
  @override
  String get assetCondition;
  @override
  int get totalAssetValue;

  /// Create a copy of Step8CollateralDetailData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$Step8CollateralDetailDataImplCopyWith<_$Step8CollateralDetailDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
