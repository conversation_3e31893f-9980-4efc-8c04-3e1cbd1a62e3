// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'step5_loan_request_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Step5LoanRequestData {
  bool get hasCollateral => throw _privateConstructorUsedError;
  int get ownCapital => throw _privateConstructorUsedError;
  int get loanAmount => throw _privateConstructorUsedError;
  LoanTerm get loanTerm => throw _privateConstructorUsedError;
  int get totalNeed => throw _privateConstructorUsedError;
  String get branchCode => throw _privateConstructorUsedError;
  LoanPurpose get loanPurpose => throw _privateConstructorUsedError;
  String? get customPurposeName => throw _privateConstructorUsedError;
  DisbursementMethod get disbursementMethod =>
      throw _privateConstructorUsedError;
  String? get accountNumber => throw _privateConstructorUsedError;

  /// Create a copy of Step5LoanRequestData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $Step5LoanRequestDataCopyWith<Step5LoanRequestData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $Step5LoanRequestDataCopyWith<$Res> {
  factory $Step5LoanRequestDataCopyWith(Step5LoanRequestData value,
          $Res Function(Step5LoanRequestData) then) =
      _$Step5LoanRequestDataCopyWithImpl<$Res, Step5LoanRequestData>;
  @useResult
  $Res call(
      {bool hasCollateral,
      int ownCapital,
      int loanAmount,
      LoanTerm loanTerm,
      int totalNeed,
      String branchCode,
      LoanPurpose loanPurpose,
      String? customPurposeName,
      DisbursementMethod disbursementMethod,
      String? accountNumber});
}

/// @nodoc
class _$Step5LoanRequestDataCopyWithImpl<$Res,
        $Val extends Step5LoanRequestData>
    implements $Step5LoanRequestDataCopyWith<$Res> {
  _$Step5LoanRequestDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Step5LoanRequestData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasCollateral = null,
    Object? ownCapital = null,
    Object? loanAmount = null,
    Object? loanTerm = null,
    Object? totalNeed = null,
    Object? branchCode = null,
    Object? loanPurpose = null,
    Object? customPurposeName = freezed,
    Object? disbursementMethod = null,
    Object? accountNumber = freezed,
  }) {
    return _then(_value.copyWith(
      hasCollateral: null == hasCollateral
          ? _value.hasCollateral
          : hasCollateral // ignore: cast_nullable_to_non_nullable
              as bool,
      ownCapital: null == ownCapital
          ? _value.ownCapital
          : ownCapital // ignore: cast_nullable_to_non_nullable
              as int,
      loanAmount: null == loanAmount
          ? _value.loanAmount
          : loanAmount // ignore: cast_nullable_to_non_nullable
              as int,
      loanTerm: null == loanTerm
          ? _value.loanTerm
          : loanTerm // ignore: cast_nullable_to_non_nullable
              as LoanTerm,
      totalNeed: null == totalNeed
          ? _value.totalNeed
          : totalNeed // ignore: cast_nullable_to_non_nullable
              as int,
      branchCode: null == branchCode
          ? _value.branchCode
          : branchCode // ignore: cast_nullable_to_non_nullable
              as String,
      loanPurpose: null == loanPurpose
          ? _value.loanPurpose
          : loanPurpose // ignore: cast_nullable_to_non_nullable
              as LoanPurpose,
      customPurposeName: freezed == customPurposeName
          ? _value.customPurposeName
          : customPurposeName // ignore: cast_nullable_to_non_nullable
              as String?,
      disbursementMethod: null == disbursementMethod
          ? _value.disbursementMethod
          : disbursementMethod // ignore: cast_nullable_to_non_nullable
              as DisbursementMethod,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$Step5LoanRequestDataImplCopyWith<$Res>
    implements $Step5LoanRequestDataCopyWith<$Res> {
  factory _$$Step5LoanRequestDataImplCopyWith(_$Step5LoanRequestDataImpl value,
          $Res Function(_$Step5LoanRequestDataImpl) then) =
      __$$Step5LoanRequestDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool hasCollateral,
      int ownCapital,
      int loanAmount,
      LoanTerm loanTerm,
      int totalNeed,
      String branchCode,
      LoanPurpose loanPurpose,
      String? customPurposeName,
      DisbursementMethod disbursementMethod,
      String? accountNumber});
}

/// @nodoc
class __$$Step5LoanRequestDataImplCopyWithImpl<$Res>
    extends _$Step5LoanRequestDataCopyWithImpl<$Res, _$Step5LoanRequestDataImpl>
    implements _$$Step5LoanRequestDataImplCopyWith<$Res> {
  __$$Step5LoanRequestDataImplCopyWithImpl(_$Step5LoanRequestDataImpl _value,
      $Res Function(_$Step5LoanRequestDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of Step5LoanRequestData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasCollateral = null,
    Object? ownCapital = null,
    Object? loanAmount = null,
    Object? loanTerm = null,
    Object? totalNeed = null,
    Object? branchCode = null,
    Object? loanPurpose = null,
    Object? customPurposeName = freezed,
    Object? disbursementMethod = null,
    Object? accountNumber = freezed,
  }) {
    return _then(_$Step5LoanRequestDataImpl(
      hasCollateral: null == hasCollateral
          ? _value.hasCollateral
          : hasCollateral // ignore: cast_nullable_to_non_nullable
              as bool,
      ownCapital: null == ownCapital
          ? _value.ownCapital
          : ownCapital // ignore: cast_nullable_to_non_nullable
              as int,
      loanAmount: null == loanAmount
          ? _value.loanAmount
          : loanAmount // ignore: cast_nullable_to_non_nullable
              as int,
      loanTerm: null == loanTerm
          ? _value.loanTerm
          : loanTerm // ignore: cast_nullable_to_non_nullable
              as LoanTerm,
      totalNeed: null == totalNeed
          ? _value.totalNeed
          : totalNeed // ignore: cast_nullable_to_non_nullable
              as int,
      branchCode: null == branchCode
          ? _value.branchCode
          : branchCode // ignore: cast_nullable_to_non_nullable
              as String,
      loanPurpose: null == loanPurpose
          ? _value.loanPurpose
          : loanPurpose // ignore: cast_nullable_to_non_nullable
              as LoanPurpose,
      customPurposeName: freezed == customPurposeName
          ? _value.customPurposeName
          : customPurposeName // ignore: cast_nullable_to_non_nullable
              as String?,
      disbursementMethod: null == disbursementMethod
          ? _value.disbursementMethod
          : disbursementMethod // ignore: cast_nullable_to_non_nullable
              as DisbursementMethod,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$Step5LoanRequestDataImpl extends _Step5LoanRequestData {
  const _$Step5LoanRequestDataImpl(
      {this.hasCollateral = true,
      this.ownCapital = 0,
      this.loanAmount = 0,
      required this.loanTerm,
      this.totalNeed = 0,
      this.branchCode = '',
      required this.loanPurpose,
      this.customPurposeName,
      this.disbursementMethod = DisbursementMethod.cash,
      this.accountNumber})
      : super._();

  @override
  @JsonKey()
  final bool hasCollateral;
  @override
  @JsonKey()
  final int ownCapital;
  @override
  @JsonKey()
  final int loanAmount;
  @override
  final LoanTerm loanTerm;
  @override
  @JsonKey()
  final int totalNeed;
  @override
  @JsonKey()
  final String branchCode;
  @override
  final LoanPurpose loanPurpose;
  @override
  final String? customPurposeName;
  @override
  @JsonKey()
  final DisbursementMethod disbursementMethod;
  @override
  final String? accountNumber;

  @override
  String toString() {
    return 'Step5LoanRequestData(hasCollateral: $hasCollateral, ownCapital: $ownCapital, loanAmount: $loanAmount, loanTerm: $loanTerm, totalNeed: $totalNeed, branchCode: $branchCode, loanPurpose: $loanPurpose, customPurposeName: $customPurposeName, disbursementMethod: $disbursementMethod, accountNumber: $accountNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$Step5LoanRequestDataImpl &&
            (identical(other.hasCollateral, hasCollateral) ||
                other.hasCollateral == hasCollateral) &&
            (identical(other.ownCapital, ownCapital) ||
                other.ownCapital == ownCapital) &&
            (identical(other.loanAmount, loanAmount) ||
                other.loanAmount == loanAmount) &&
            (identical(other.loanTerm, loanTerm) ||
                other.loanTerm == loanTerm) &&
            (identical(other.totalNeed, totalNeed) ||
                other.totalNeed == totalNeed) &&
            (identical(other.branchCode, branchCode) ||
                other.branchCode == branchCode) &&
            (identical(other.loanPurpose, loanPurpose) ||
                other.loanPurpose == loanPurpose) &&
            (identical(other.customPurposeName, customPurposeName) ||
                other.customPurposeName == customPurposeName) &&
            (identical(other.disbursementMethod, disbursementMethod) ||
                other.disbursementMethod == disbursementMethod) &&
            (identical(other.accountNumber, accountNumber) ||
                other.accountNumber == accountNumber));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      hasCollateral,
      ownCapital,
      loanAmount,
      loanTerm,
      totalNeed,
      branchCode,
      loanPurpose,
      customPurposeName,
      disbursementMethod,
      accountNumber);

  /// Create a copy of Step5LoanRequestData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$Step5LoanRequestDataImplCopyWith<_$Step5LoanRequestDataImpl>
      get copyWith =>
          __$$Step5LoanRequestDataImplCopyWithImpl<_$Step5LoanRequestDataImpl>(
              this, _$identity);
}

abstract class _Step5LoanRequestData extends Step5LoanRequestData {
  const factory _Step5LoanRequestData(
      {final bool hasCollateral,
      final int ownCapital,
      final int loanAmount,
      required final LoanTerm loanTerm,
      final int totalNeed,
      final String branchCode,
      required final LoanPurpose loanPurpose,
      final String? customPurposeName,
      final DisbursementMethod disbursementMethod,
      final String? accountNumber}) = _$Step5LoanRequestDataImpl;
  const _Step5LoanRequestData._() : super._();

  @override
  bool get hasCollateral;
  @override
  int get ownCapital;
  @override
  int get loanAmount;
  @override
  LoanTerm get loanTerm;
  @override
  int get totalNeed;
  @override
  String get branchCode;
  @override
  LoanPurpose get loanPurpose;
  @override
  String? get customPurposeName;
  @override
  DisbursementMethod get disbursementMethod;
  @override
  String? get accountNumber;

  /// Create a copy of Step5LoanRequestData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$Step5LoanRequestDataImplCopyWith<_$Step5LoanRequestDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
