import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/core/enums/enums.dart';

part 'step6_financial_info_data.freezed.dart';

/// Model cho data của Step 6 - Thông tin tài chính
@freezed
class Step6FinancialInfoData with _$Step6FinancialInfoData {
  const factory Step6FinancialInfoData({
    @Default(IncomeSource.salary) IncomeSource incomeSource,
    int? dailyRevenue,
    @Default(0) int dailyIncome,
    String? province,
    String? ward,
    String? specificAddress,
  }) = _Step6FinancialInfoData;

  // Custom getter
  const Step6FinancialInfoData._();
  
  /// Kiểm tra step đã hoàn thành chưa
  bool get isComplete {
    if (dailyIncome <= 0) return false;
    if (incomeSource == IncomeSource.business && dailyRevenue == null) return false;
    return true;
  }

  /// Kiểm tra có lỗi không
  bool get hasError => false; // <PERSON><PERSON> thể mở rộng sau

  /// Kiểm tra thông tin cơ bản đã đầy đủ chưa
  bool get hasBasicInfo {
    return dailyIncome > 0;
  }

  /// Kiểm tra thông tin kinh doanh đã đầy đủ chưa
  bool get hasBusinessInfo {
    if (incomeSource == IncomeSource.business) {
      return dailyRevenue != null && dailyRevenue! > 0;
    }
    return true;
  }

  /// Kiểm tra địa chỉ kinh doanh đã đầy đủ chưa
  bool get hasBusinessAddress {
    if (incomeSource == IncomeSource.business) {
      return province != null && 
             province!.trim().isNotEmpty &&
             ward != null && 
             ward!.trim().isNotEmpty &&
             specificAddress != null && 
             specificAddress!.trim().isNotEmpty;
    }
    return true;
  }

  /// Tính thu nhập hàng tháng
  int get monthlyIncome => dailyIncome * 30;

  /// Tính doanh thu hàng tháng (nếu có)
  int get monthlyRevenue => (dailyRevenue ?? 0) * 30;
} 