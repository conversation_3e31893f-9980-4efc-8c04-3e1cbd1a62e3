import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/core/enums/enums.dart';
import 'package:sales_app/domain/domain.dart';

part 'step1_document_data.freezed.dart';

/// Model cho data của Step 1 - Cung cấp giấy tờ tùy thân
@freezed
class Step1DocumentData with _$Step1DocumentData {
  const factory Step1DocumentData({
    @Default(DocumentType.cccd) DocumentType documentType,
    String? frontImagePath, // Local path cho preview
    String? backImagePath, // Local path cho preview
    String? frontImageUrl, // Server URL sau khi upload
    String? backImageUrl, // Server URL sau khi upload
    CCCDInfoEntity? cccdInfo, // Thông tin từ QR scan
  }) = _Step1DocumentData;

  // Custom getter
  const Step1DocumentData._();
  
  /// Kiểm tra step đã hoàn thành chưa (cần có URL từ server)
  bool get isComplete {
    if (documentType == DocumentType.passport) {
      // Passport chỉ cần ảnh mặt trước
      return frontImageUrl != null && frontImageUrl!.isNotEmpty;
    }
    // CCCD và Thẻ Căn Cước cần cả 2 ảnh
    return frontImageUrl != null && 
           frontImageUrl!.isNotEmpty && 
           backImageUrl != null && 
           backImageUrl!.isNotEmpty;
  }

  /// Kiểm tra đã chọn đủ ảnh để có thể upload (chỉ cần local path)
  bool get isReadyForUpload {
    if (documentType == DocumentType.passport) {
      // Passport chỉ cần ảnh mặt trước
      return hasFrontImage;
    }
    // CCCD và Thẻ Căn Cước cần cả 2 ảnh
    return hasFrontImage && hasBackImage;
  }

  /// Kiểm tra đã có ảnh mặt trước chưa (local preview)
  bool get hasFrontImage => frontImagePath != null && frontImagePath!.isNotEmpty;

  /// Kiểm tra đã có ảnh mặt sau chưa (local preview)
  bool get hasBackImage => backImagePath != null && backImagePath!.isNotEmpty;

  /// Kiểm tra đã upload thành công chưa
  bool get isUploaded {
    if (documentType == DocumentType.passport) {
      // Passport chỉ cần ảnh mặt trước
      return frontImageUrl != null && frontImageUrl!.isNotEmpty;
    }
    // CCCD và Thẻ Căn Cước cần cả 2 ảnh
    return frontImageUrl != null && 
           frontImageUrl!.isNotEmpty && 
           backImageUrl != null && 
           backImageUrl!.isNotEmpty;
  }

  /// Kiểm tra đã có thông tin từ QR scan chưa
  bool get hasQRInfo => cccdInfo != null;

  /// Lấy URL để hiển thị (ưu tiên server URL, fallback local path)
  String? get displayFrontImage => frontImageUrl ?? frontImagePath;
  String? get displayBackImage => backImageUrl ?? backImagePath;
} 