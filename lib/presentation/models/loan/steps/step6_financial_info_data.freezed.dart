// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'step6_financial_info_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Step6FinancialInfoData {
  IncomeSource get incomeSource => throw _privateConstructorUsedError;
  int? get dailyRevenue => throw _privateConstructorUsedError;
  int get dailyIncome => throw _privateConstructorUsedError;
  String? get province => throw _privateConstructorUsedError;
  String? get ward => throw _privateConstructorUsedError;
  String? get specificAddress => throw _privateConstructorUsedError;

  /// Create a copy of Step6FinancialInfoData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $Step6FinancialInfoDataCopyWith<Step6FinancialInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $Step6FinancialInfoDataCopyWith<$Res> {
  factory $Step6FinancialInfoDataCopyWith(Step6FinancialInfoData value,
          $Res Function(Step6FinancialInfoData) then) =
      _$Step6FinancialInfoDataCopyWithImpl<$Res, Step6FinancialInfoData>;
  @useResult
  $Res call(
      {IncomeSource incomeSource,
      int? dailyRevenue,
      int dailyIncome,
      String? province,
      String? ward,
      String? specificAddress});
}

/// @nodoc
class _$Step6FinancialInfoDataCopyWithImpl<$Res,
        $Val extends Step6FinancialInfoData>
    implements $Step6FinancialInfoDataCopyWith<$Res> {
  _$Step6FinancialInfoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Step6FinancialInfoData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? incomeSource = null,
    Object? dailyRevenue = freezed,
    Object? dailyIncome = null,
    Object? province = freezed,
    Object? ward = freezed,
    Object? specificAddress = freezed,
  }) {
    return _then(_value.copyWith(
      incomeSource: null == incomeSource
          ? _value.incomeSource
          : incomeSource // ignore: cast_nullable_to_non_nullable
              as IncomeSource,
      dailyRevenue: freezed == dailyRevenue
          ? _value.dailyRevenue
          : dailyRevenue // ignore: cast_nullable_to_non_nullable
              as int?,
      dailyIncome: null == dailyIncome
          ? _value.dailyIncome
          : dailyIncome // ignore: cast_nullable_to_non_nullable
              as int,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      ward: freezed == ward
          ? _value.ward
          : ward // ignore: cast_nullable_to_non_nullable
              as String?,
      specificAddress: freezed == specificAddress
          ? _value.specificAddress
          : specificAddress // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$Step6FinancialInfoDataImplCopyWith<$Res>
    implements $Step6FinancialInfoDataCopyWith<$Res> {
  factory _$$Step6FinancialInfoDataImplCopyWith(
          _$Step6FinancialInfoDataImpl value,
          $Res Function(_$Step6FinancialInfoDataImpl) then) =
      __$$Step6FinancialInfoDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {IncomeSource incomeSource,
      int? dailyRevenue,
      int dailyIncome,
      String? province,
      String? ward,
      String? specificAddress});
}

/// @nodoc
class __$$Step6FinancialInfoDataImplCopyWithImpl<$Res>
    extends _$Step6FinancialInfoDataCopyWithImpl<$Res,
        _$Step6FinancialInfoDataImpl>
    implements _$$Step6FinancialInfoDataImplCopyWith<$Res> {
  __$$Step6FinancialInfoDataImplCopyWithImpl(
      _$Step6FinancialInfoDataImpl _value,
      $Res Function(_$Step6FinancialInfoDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of Step6FinancialInfoData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? incomeSource = null,
    Object? dailyRevenue = freezed,
    Object? dailyIncome = null,
    Object? province = freezed,
    Object? ward = freezed,
    Object? specificAddress = freezed,
  }) {
    return _then(_$Step6FinancialInfoDataImpl(
      incomeSource: null == incomeSource
          ? _value.incomeSource
          : incomeSource // ignore: cast_nullable_to_non_nullable
              as IncomeSource,
      dailyRevenue: freezed == dailyRevenue
          ? _value.dailyRevenue
          : dailyRevenue // ignore: cast_nullable_to_non_nullable
              as int?,
      dailyIncome: null == dailyIncome
          ? _value.dailyIncome
          : dailyIncome // ignore: cast_nullable_to_non_nullable
              as int,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      ward: freezed == ward
          ? _value.ward
          : ward // ignore: cast_nullable_to_non_nullable
              as String?,
      specificAddress: freezed == specificAddress
          ? _value.specificAddress
          : specificAddress // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$Step6FinancialInfoDataImpl extends _Step6FinancialInfoData {
  const _$Step6FinancialInfoDataImpl(
      {this.incomeSource = IncomeSource.salary,
      this.dailyRevenue,
      this.dailyIncome = 0,
      this.province,
      this.ward,
      this.specificAddress})
      : super._();

  @override
  @JsonKey()
  final IncomeSource incomeSource;
  @override
  final int? dailyRevenue;
  @override
  @JsonKey()
  final int dailyIncome;
  @override
  final String? province;
  @override
  final String? ward;
  @override
  final String? specificAddress;

  @override
  String toString() {
    return 'Step6FinancialInfoData(incomeSource: $incomeSource, dailyRevenue: $dailyRevenue, dailyIncome: $dailyIncome, province: $province, ward: $ward, specificAddress: $specificAddress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$Step6FinancialInfoDataImpl &&
            (identical(other.incomeSource, incomeSource) ||
                other.incomeSource == incomeSource) &&
            (identical(other.dailyRevenue, dailyRevenue) ||
                other.dailyRevenue == dailyRevenue) &&
            (identical(other.dailyIncome, dailyIncome) ||
                other.dailyIncome == dailyIncome) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.ward, ward) || other.ward == ward) &&
            (identical(other.specificAddress, specificAddress) ||
                other.specificAddress == specificAddress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, incomeSource, dailyRevenue,
      dailyIncome, province, ward, specificAddress);

  /// Create a copy of Step6FinancialInfoData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$Step6FinancialInfoDataImplCopyWith<_$Step6FinancialInfoDataImpl>
      get copyWith => __$$Step6FinancialInfoDataImplCopyWithImpl<
          _$Step6FinancialInfoDataImpl>(this, _$identity);
}

abstract class _Step6FinancialInfoData extends Step6FinancialInfoData {
  const factory _Step6FinancialInfoData(
      {final IncomeSource incomeSource,
      final int? dailyRevenue,
      final int dailyIncome,
      final String? province,
      final String? ward,
      final String? specificAddress}) = _$Step6FinancialInfoDataImpl;
  const _Step6FinancialInfoData._() : super._();

  @override
  IncomeSource get incomeSource;
  @override
  int? get dailyRevenue;
  @override
  int get dailyIncome;
  @override
  String? get province;
  @override
  String? get ward;
  @override
  String? get specificAddress;

  /// Create a copy of Step6FinancialInfoData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$Step6FinancialInfoDataImplCopyWith<_$Step6FinancialInfoDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
