// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'step3_co_borrower_document_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Step3CoBorrowerDocumentData {
  DocumentType get documentType => throw _privateConstructorUsedError;
  String? get frontImagePath =>
      throw _privateConstructorUsedError; // Local path cho preview
  String? get backImagePath =>
      throw _privateConstructorUsedError; // Local path cho preview
  String? get frontImageUrl =>
      throw _privateConstructorUsedError; // Server URL sau khi upload
  String? get backImageUrl =>
      throw _privateConstructorUsedError; // Server URL sau khi upload
  CCCDInfoEntity? get cccdInfo => throw _privateConstructorUsedError;

  /// Create a copy of Step3CoBorrowerDocumentData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $Step3CoBorrowerDocumentDataCopyWith<Step3CoBorrowerDocumentData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $Step3CoBorrowerDocumentDataCopyWith<$Res> {
  factory $Step3CoBorrowerDocumentDataCopyWith(
          Step3CoBorrowerDocumentData value,
          $Res Function(Step3CoBorrowerDocumentData) then) =
      _$Step3CoBorrowerDocumentDataCopyWithImpl<$Res,
          Step3CoBorrowerDocumentData>;
  @useResult
  $Res call(
      {DocumentType documentType,
      String? frontImagePath,
      String? backImagePath,
      String? frontImageUrl,
      String? backImageUrl,
      CCCDInfoEntity? cccdInfo});
}

/// @nodoc
class _$Step3CoBorrowerDocumentDataCopyWithImpl<$Res,
        $Val extends Step3CoBorrowerDocumentData>
    implements $Step3CoBorrowerDocumentDataCopyWith<$Res> {
  _$Step3CoBorrowerDocumentDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Step3CoBorrowerDocumentData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? documentType = null,
    Object? frontImagePath = freezed,
    Object? backImagePath = freezed,
    Object? frontImageUrl = freezed,
    Object? backImageUrl = freezed,
    Object? cccdInfo = freezed,
  }) {
    return _then(_value.copyWith(
      documentType: null == documentType
          ? _value.documentType
          : documentType // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      frontImagePath: freezed == frontImagePath
          ? _value.frontImagePath
          : frontImagePath // ignore: cast_nullable_to_non_nullable
              as String?,
      backImagePath: freezed == backImagePath
          ? _value.backImagePath
          : backImagePath // ignore: cast_nullable_to_non_nullable
              as String?,
      frontImageUrl: freezed == frontImageUrl
          ? _value.frontImageUrl
          : frontImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backImageUrl: freezed == backImageUrl
          ? _value.backImageUrl
          : backImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      cccdInfo: freezed == cccdInfo
          ? _value.cccdInfo
          : cccdInfo // ignore: cast_nullable_to_non_nullable
              as CCCDInfoEntity?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$Step3CoBorrowerDocumentDataImplCopyWith<$Res>
    implements $Step3CoBorrowerDocumentDataCopyWith<$Res> {
  factory _$$Step3CoBorrowerDocumentDataImplCopyWith(
          _$Step3CoBorrowerDocumentDataImpl value,
          $Res Function(_$Step3CoBorrowerDocumentDataImpl) then) =
      __$$Step3CoBorrowerDocumentDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DocumentType documentType,
      String? frontImagePath,
      String? backImagePath,
      String? frontImageUrl,
      String? backImageUrl,
      CCCDInfoEntity? cccdInfo});
}

/// @nodoc
class __$$Step3CoBorrowerDocumentDataImplCopyWithImpl<$Res>
    extends _$Step3CoBorrowerDocumentDataCopyWithImpl<$Res,
        _$Step3CoBorrowerDocumentDataImpl>
    implements _$$Step3CoBorrowerDocumentDataImplCopyWith<$Res> {
  __$$Step3CoBorrowerDocumentDataImplCopyWithImpl(
      _$Step3CoBorrowerDocumentDataImpl _value,
      $Res Function(_$Step3CoBorrowerDocumentDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of Step3CoBorrowerDocumentData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? documentType = null,
    Object? frontImagePath = freezed,
    Object? backImagePath = freezed,
    Object? frontImageUrl = freezed,
    Object? backImageUrl = freezed,
    Object? cccdInfo = freezed,
  }) {
    return _then(_$Step3CoBorrowerDocumentDataImpl(
      documentType: null == documentType
          ? _value.documentType
          : documentType // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      frontImagePath: freezed == frontImagePath
          ? _value.frontImagePath
          : frontImagePath // ignore: cast_nullable_to_non_nullable
              as String?,
      backImagePath: freezed == backImagePath
          ? _value.backImagePath
          : backImagePath // ignore: cast_nullable_to_non_nullable
              as String?,
      frontImageUrl: freezed == frontImageUrl
          ? _value.frontImageUrl
          : frontImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backImageUrl: freezed == backImageUrl
          ? _value.backImageUrl
          : backImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      cccdInfo: freezed == cccdInfo
          ? _value.cccdInfo
          : cccdInfo // ignore: cast_nullable_to_non_nullable
              as CCCDInfoEntity?,
    ));
  }
}

/// @nodoc

class _$Step3CoBorrowerDocumentDataImpl extends _Step3CoBorrowerDocumentData {
  const _$Step3CoBorrowerDocumentDataImpl(
      {this.documentType = DocumentType.cccd,
      this.frontImagePath,
      this.backImagePath,
      this.frontImageUrl,
      this.backImageUrl,
      this.cccdInfo})
      : super._();

  @override
  @JsonKey()
  final DocumentType documentType;
  @override
  final String? frontImagePath;
// Local path cho preview
  @override
  final String? backImagePath;
// Local path cho preview
  @override
  final String? frontImageUrl;
// Server URL sau khi upload
  @override
  final String? backImageUrl;
// Server URL sau khi upload
  @override
  final CCCDInfoEntity? cccdInfo;

  @override
  String toString() {
    return 'Step3CoBorrowerDocumentData(documentType: $documentType, frontImagePath: $frontImagePath, backImagePath: $backImagePath, frontImageUrl: $frontImageUrl, backImageUrl: $backImageUrl, cccdInfo: $cccdInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$Step3CoBorrowerDocumentDataImpl &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.frontImagePath, frontImagePath) ||
                other.frontImagePath == frontImagePath) &&
            (identical(other.backImagePath, backImagePath) ||
                other.backImagePath == backImagePath) &&
            (identical(other.frontImageUrl, frontImageUrl) ||
                other.frontImageUrl == frontImageUrl) &&
            (identical(other.backImageUrl, backImageUrl) ||
                other.backImageUrl == backImageUrl) &&
            (identical(other.cccdInfo, cccdInfo) ||
                other.cccdInfo == cccdInfo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, documentType, frontImagePath,
      backImagePath, frontImageUrl, backImageUrl, cccdInfo);

  /// Create a copy of Step3CoBorrowerDocumentData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$Step3CoBorrowerDocumentDataImplCopyWith<_$Step3CoBorrowerDocumentDataImpl>
      get copyWith => __$$Step3CoBorrowerDocumentDataImplCopyWithImpl<
          _$Step3CoBorrowerDocumentDataImpl>(this, _$identity);
}

abstract class _Step3CoBorrowerDocumentData
    extends Step3CoBorrowerDocumentData {
  const factory _Step3CoBorrowerDocumentData(
      {final DocumentType documentType,
      final String? frontImagePath,
      final String? backImagePath,
      final String? frontImageUrl,
      final String? backImageUrl,
      final CCCDInfoEntity? cccdInfo}) = _$Step3CoBorrowerDocumentDataImpl;
  const _Step3CoBorrowerDocumentData._() : super._();

  @override
  DocumentType get documentType;
  @override
  String? get frontImagePath; // Local path cho preview
  @override
  String? get backImagePath; // Local path cho preview
  @override
  String? get frontImageUrl; // Server URL sau khi upload
  @override
  String? get backImageUrl; // Server URL sau khi upload
  @override
  CCCDInfoEntity? get cccdInfo;

  /// Create a copy of Step3CoBorrowerDocumentData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$Step3CoBorrowerDocumentDataImplCopyWith<_$Step3CoBorrowerDocumentDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
