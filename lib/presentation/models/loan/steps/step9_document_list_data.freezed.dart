// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'step9_document_list_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Step9DocumentListData {
  List<String> get marriageDocuments => throw _privateConstructorUsedError;
  List<String> get residenceDocuments => throw _privateConstructorUsedError;
  List<String> get vehicleAppraisalDocuments =>
      throw _privateConstructorUsedError;
  List<String> get vehicleRegistrationDocuments =>
      throw _privateConstructorUsedError;
  List<String> get businessLicenseDocuments =>
      throw _privateConstructorUsedError;

  /// Create a copy of Step9DocumentListData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $Step9DocumentListDataCopyWith<Step9DocumentListData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $Step9DocumentListDataCopyWith<$Res> {
  factory $Step9DocumentListDataCopyWith(Step9DocumentListData value,
          $Res Function(Step9DocumentListData) then) =
      _$Step9DocumentListDataCopyWithImpl<$Res, Step9DocumentListData>;
  @useResult
  $Res call(
      {List<String> marriageDocuments,
      List<String> residenceDocuments,
      List<String> vehicleAppraisalDocuments,
      List<String> vehicleRegistrationDocuments,
      List<String> businessLicenseDocuments});
}

/// @nodoc
class _$Step9DocumentListDataCopyWithImpl<$Res,
        $Val extends Step9DocumentListData>
    implements $Step9DocumentListDataCopyWith<$Res> {
  _$Step9DocumentListDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Step9DocumentListData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? marriageDocuments = null,
    Object? residenceDocuments = null,
    Object? vehicleAppraisalDocuments = null,
    Object? vehicleRegistrationDocuments = null,
    Object? businessLicenseDocuments = null,
  }) {
    return _then(_value.copyWith(
      marriageDocuments: null == marriageDocuments
          ? _value.marriageDocuments
          : marriageDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      residenceDocuments: null == residenceDocuments
          ? _value.residenceDocuments
          : residenceDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      vehicleAppraisalDocuments: null == vehicleAppraisalDocuments
          ? _value.vehicleAppraisalDocuments
          : vehicleAppraisalDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      vehicleRegistrationDocuments: null == vehicleRegistrationDocuments
          ? _value.vehicleRegistrationDocuments
          : vehicleRegistrationDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      businessLicenseDocuments: null == businessLicenseDocuments
          ? _value.businessLicenseDocuments
          : businessLicenseDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$Step9DocumentListDataImplCopyWith<$Res>
    implements $Step9DocumentListDataCopyWith<$Res> {
  factory _$$Step9DocumentListDataImplCopyWith(
          _$Step9DocumentListDataImpl value,
          $Res Function(_$Step9DocumentListDataImpl) then) =
      __$$Step9DocumentListDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String> marriageDocuments,
      List<String> residenceDocuments,
      List<String> vehicleAppraisalDocuments,
      List<String> vehicleRegistrationDocuments,
      List<String> businessLicenseDocuments});
}

/// @nodoc
class __$$Step9DocumentListDataImplCopyWithImpl<$Res>
    extends _$Step9DocumentListDataCopyWithImpl<$Res,
        _$Step9DocumentListDataImpl>
    implements _$$Step9DocumentListDataImplCopyWith<$Res> {
  __$$Step9DocumentListDataImplCopyWithImpl(_$Step9DocumentListDataImpl _value,
      $Res Function(_$Step9DocumentListDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of Step9DocumentListData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? marriageDocuments = null,
    Object? residenceDocuments = null,
    Object? vehicleAppraisalDocuments = null,
    Object? vehicleRegistrationDocuments = null,
    Object? businessLicenseDocuments = null,
  }) {
    return _then(_$Step9DocumentListDataImpl(
      marriageDocuments: null == marriageDocuments
          ? _value._marriageDocuments
          : marriageDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      residenceDocuments: null == residenceDocuments
          ? _value._residenceDocuments
          : residenceDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      vehicleAppraisalDocuments: null == vehicleAppraisalDocuments
          ? _value._vehicleAppraisalDocuments
          : vehicleAppraisalDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      vehicleRegistrationDocuments: null == vehicleRegistrationDocuments
          ? _value._vehicleRegistrationDocuments
          : vehicleRegistrationDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      businessLicenseDocuments: null == businessLicenseDocuments
          ? _value._businessLicenseDocuments
          : businessLicenseDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$Step9DocumentListDataImpl extends _Step9DocumentListData {
  const _$Step9DocumentListDataImpl(
      {final List<String> marriageDocuments = const [],
      final List<String> residenceDocuments = const [],
      final List<String> vehicleAppraisalDocuments = const [],
      final List<String> vehicleRegistrationDocuments = const [],
      final List<String> businessLicenseDocuments = const []})
      : _marriageDocuments = marriageDocuments,
        _residenceDocuments = residenceDocuments,
        _vehicleAppraisalDocuments = vehicleAppraisalDocuments,
        _vehicleRegistrationDocuments = vehicleRegistrationDocuments,
        _businessLicenseDocuments = businessLicenseDocuments,
        super._();

  final List<String> _marriageDocuments;
  @override
  @JsonKey()
  List<String> get marriageDocuments {
    if (_marriageDocuments is EqualUnmodifiableListView)
      return _marriageDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_marriageDocuments);
  }

  final List<String> _residenceDocuments;
  @override
  @JsonKey()
  List<String> get residenceDocuments {
    if (_residenceDocuments is EqualUnmodifiableListView)
      return _residenceDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_residenceDocuments);
  }

  final List<String> _vehicleAppraisalDocuments;
  @override
  @JsonKey()
  List<String> get vehicleAppraisalDocuments {
    if (_vehicleAppraisalDocuments is EqualUnmodifiableListView)
      return _vehicleAppraisalDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vehicleAppraisalDocuments);
  }

  final List<String> _vehicleRegistrationDocuments;
  @override
  @JsonKey()
  List<String> get vehicleRegistrationDocuments {
    if (_vehicleRegistrationDocuments is EqualUnmodifiableListView)
      return _vehicleRegistrationDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vehicleRegistrationDocuments);
  }

  final List<String> _businessLicenseDocuments;
  @override
  @JsonKey()
  List<String> get businessLicenseDocuments {
    if (_businessLicenseDocuments is EqualUnmodifiableListView)
      return _businessLicenseDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_businessLicenseDocuments);
  }

  @override
  String toString() {
    return 'Step9DocumentListData(marriageDocuments: $marriageDocuments, residenceDocuments: $residenceDocuments, vehicleAppraisalDocuments: $vehicleAppraisalDocuments, vehicleRegistrationDocuments: $vehicleRegistrationDocuments, businessLicenseDocuments: $businessLicenseDocuments)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$Step9DocumentListDataImpl &&
            const DeepCollectionEquality()
                .equals(other._marriageDocuments, _marriageDocuments) &&
            const DeepCollectionEquality()
                .equals(other._residenceDocuments, _residenceDocuments) &&
            const DeepCollectionEquality().equals(
                other._vehicleAppraisalDocuments, _vehicleAppraisalDocuments) &&
            const DeepCollectionEquality().equals(
                other._vehicleRegistrationDocuments,
                _vehicleRegistrationDocuments) &&
            const DeepCollectionEquality().equals(
                other._businessLicenseDocuments, _businessLicenseDocuments));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_marriageDocuments),
      const DeepCollectionEquality().hash(_residenceDocuments),
      const DeepCollectionEquality().hash(_vehicleAppraisalDocuments),
      const DeepCollectionEquality().hash(_vehicleRegistrationDocuments),
      const DeepCollectionEquality().hash(_businessLicenseDocuments));

  /// Create a copy of Step9DocumentListData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$Step9DocumentListDataImplCopyWith<_$Step9DocumentListDataImpl>
      get copyWith => __$$Step9DocumentListDataImplCopyWithImpl<
          _$Step9DocumentListDataImpl>(this, _$identity);
}

abstract class _Step9DocumentListData extends Step9DocumentListData {
  const factory _Step9DocumentListData(
          {final List<String> marriageDocuments,
          final List<String> residenceDocuments,
          final List<String> vehicleAppraisalDocuments,
          final List<String> vehicleRegistrationDocuments,
          final List<String> businessLicenseDocuments}) =
      _$Step9DocumentListDataImpl;
  const _Step9DocumentListData._() : super._();

  @override
  List<String> get marriageDocuments;
  @override
  List<String> get residenceDocuments;
  @override
  List<String> get vehicleAppraisalDocuments;
  @override
  List<String> get vehicleRegistrationDocuments;
  @override
  List<String> get businessLicenseDocuments;

  /// Create a copy of Step9DocumentListData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$Step9DocumentListDataImplCopyWith<_$Step9DocumentListDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
