import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/core/enums/enums.dart';

part 'step4_co_borrower_info_data.freezed.dart';

/// Model cho data của Step 4 - Thông tin người đồng vay
@freezed
class Step4CoBorrowerInfoData with _$Step4CoBorrowerInfoData {
  const factory Step4CoBorrowerInfoData({
    @Default('') String fullName,
    @Default('') String documentNumber,
    DateTime? issueDate,
    DateTime? expiryDate,
    @Default('') String issuePlace,
    DateTime? birthDate,
    @Default(Gender.male) Gender gender,
    @Default('') String permanentAddress,
    @Default(MaritalStatus.single) MaritalStatus maritalStatus,
    @Default('') String phoneNumber,
    @Default(false) bool sameAsPermAddress,
    String? province,
    String? district,
    String? ward,
    String? specificAddress,
  }) = _Step4CoBorrowerInfoData;

  // Custom getter
  const Step4CoBorrowerInfoData._();
  
  /// Kiểm tra step đã hoàn thành chưa
  bool get isComplete {
    // Kiểm tra các trường bắt buộc
    if (fullName.trim().isEmpty ||
        documentNumber.trim().isEmpty ||
        issueDate == null ||
        expiryDate == null ||
        issuePlace.trim().isEmpty ||
        birthDate == null ||
        permanentAddress.trim().isEmpty ||
        phoneNumber.trim().isEmpty) {
      return false;
    }
    
    // Nếu không trùng địa chỉ thường trú thì kiểm tra các trường địa chỉ hiện tại
    if (!sameAsPermAddress) {
      final addressFields = [province, district, ward, specificAddress];
      if (addressFields.any((f) => (f ?? '').trim().isEmpty)) {
        return false;
      }
    }

    return true;
  }

  /// Kiểm tra có lỗi không
  bool get hasError => false; // Có thể mở rộng sau

  /// Kiểm tra thông tin cơ bản đã đầy đủ chưa
  bool get hasBasicInfo {
    return fullName.trim().isNotEmpty &&
           documentNumber.trim().isNotEmpty &&
           issueDate != null &&
           expiryDate != null &&
           issuePlace.trim().isNotEmpty &&
           birthDate != null &&
           permanentAddress.trim().isNotEmpty &&
           phoneNumber.trim().isNotEmpty;
  }

  /// Kiểm tra địa chỉ hiện tại đã đầy đủ chưa
  bool get hasCurrentAddress {
    if (sameAsPermAddress) return true;
    return province != null && 
           province!.trim().isNotEmpty &&
           district != null && 
           district!.trim().isNotEmpty &&
           ward != null && 
           ward!.trim().isNotEmpty &&
           specificAddress != null && 
           specificAddress!.trim().isNotEmpty;
  }
} 