import 'package:freezed_annotation/freezed_annotation.dart';

part 'step8_collateral_detail_data.freezed.dart';

/// Model cho data của Step 8 - Chi tiết tài sản bảo đảm
@freezed
class Step8CollateralDetailData with _$Step8CollateralDetailData {
  const factory Step8CollateralDetailData({
    @Default('') String licensePlate,
    @Default('') String assetName,
    @Default('') String chassisNumber,
    @Default('') String engineNumber,
    @Default('') String registrationCertificateNumber,
    @Default('') String issuePlace,
    required DateTime issueDate,
    @Default('') String assetCondition,
    @Default(0) int totalAssetValue,
  }) = _Step8CollateralDetailData;

  // Custom getter
  const Step8CollateralDetailData._();
  
  /// Kiểm tra step đã hoàn thành chưa
  bool get isComplete {
    return licensePlate.trim().isNotEmpty &&
           assetName.trim().isNotEmpty &&
           chassisNumber.trim().isNotEmpty &&
           engineNumber.trim().isNotEmpty &&
           registrationCertificateNumber.trim().isNotEmpty &&
           issuePlace.trim().isNotEmpty &&
           assetCondition.trim().isNotEmpty &&
           totalAssetValue > 0;
  }
} 