import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sales_app/core/enums/enums.dart';

part 'step5_loan_request_data.freezed.dart';

/// Model cho data của Step 5 - Yêu cầu vay
@freezed
class Step5LoanRequestData with _$Step5LoanRequestData {
  const factory Step5LoanRequestData({
    @Default(true) bool hasCollateral,
    @Default(0) int ownCapital,
    @Default(0) int loanAmount,
    required LoanTerm loanTerm,
    @Default(0) int totalNeed,
    @Default('') String branchCode,
    required LoanPurpose loanPurpose,
    String? customPurposeName,
    @Default(DisbursementMethod.cash) DisbursementMethod disbursementMethod,
    String? accountNumber,
  }) = _Step5LoanRequestData;

  // Custom getter
  const Step5LoanRequestData._();
  
  /// Kiểm tra step đã hoàn thành chưa
  bool get isComplete {
    if (loanAmount <= 0 || branchCode.trim().isEmpty) {
      return false;
    }
    if (loanPurpose == LoanPurpose.other && (customPurposeName?.trim().isEmpty ?? true)) {
      return false;
    }
    if (disbursementMethod == DisbursementMethod.transfer && (accountNumber?.trim().isEmpty ?? true)) {
      return false;
    }
    return true;
  }

  /// Kiểm tra có lỗi không
  bool get hasError => false; // Có thể mở rộng sau

  /// Kiểm tra thông tin cơ bản đã đầy đủ chưa
  bool get hasBasicInfo {
    return loanAmount > 0 && branchCode.trim().isNotEmpty;
  }

  /// Kiểm tra mục đích vay đã đầy đủ chưa
  bool get hasLoanPurpose {
    if (loanPurpose == LoanPurpose.other) {
      return customPurposeName?.trim().isNotEmpty ?? false;
    }
    return true;
  }

  /// Kiểm tra phương thức giải ngân đã đầy đủ chưa
  bool get hasDisbursementMethod {
    if (disbursementMethod == DisbursementMethod.transfer) {
      return accountNumber?.trim().isNotEmpty ?? false;
    }
    return true;
  }

  /// Tính tổng nhu cầu vốn
  int get calculatedTotalNeed => ownCapital + loanAmount;

  /// Kiểm tra có tài sản bảo đảm không
  bool get requiresCollateralInfo => hasCollateral;
} 