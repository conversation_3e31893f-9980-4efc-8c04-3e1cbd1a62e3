// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'step2_borrower_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Step2BorrowerData {
  bool get hasCoBorrower => throw _privateConstructorUsedError;
  String get fullName => throw _privateConstructorUsedError;
  String get documentNumber => throw _privateConstructorUsedError;
  DateTime? get issueDate => throw _privateConstructorUsedError;
  DateTime? get expiryDate => throw _privateConstructorUsedError;
  String get issuePlace => throw _privateConstructorUsedError;
  DateTime? get birthDate => throw _privateConstructorUsedError;
  Gender get gender => throw _privateConstructorUsedError;
  String get permanentAddress => throw _privateConstructorUsedError;
  MaritalStatus get maritalStatus => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  bool get sameAsPermAddress => throw _privateConstructorUsedError;
  String? get province => throw _privateConstructorUsedError;
  String? get ward => throw _privateConstructorUsedError;
  String? get specificAddress => throw _privateConstructorUsedError;

  /// Create a copy of Step2BorrowerData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $Step2BorrowerDataCopyWith<Step2BorrowerData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $Step2BorrowerDataCopyWith<$Res> {
  factory $Step2BorrowerDataCopyWith(
          Step2BorrowerData value, $Res Function(Step2BorrowerData) then) =
      _$Step2BorrowerDataCopyWithImpl<$Res, Step2BorrowerData>;
  @useResult
  $Res call(
      {bool hasCoBorrower,
      String fullName,
      String documentNumber,
      DateTime? issueDate,
      DateTime? expiryDate,
      String issuePlace,
      DateTime? birthDate,
      Gender gender,
      String permanentAddress,
      MaritalStatus maritalStatus,
      String phoneNumber,
      bool sameAsPermAddress,
      String? province,
      String? ward,
      String? specificAddress});
}

/// @nodoc
class _$Step2BorrowerDataCopyWithImpl<$Res, $Val extends Step2BorrowerData>
    implements $Step2BorrowerDataCopyWith<$Res> {
  _$Step2BorrowerDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Step2BorrowerData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasCoBorrower = null,
    Object? fullName = null,
    Object? documentNumber = null,
    Object? issueDate = freezed,
    Object? expiryDate = freezed,
    Object? issuePlace = null,
    Object? birthDate = freezed,
    Object? gender = null,
    Object? permanentAddress = null,
    Object? maritalStatus = null,
    Object? phoneNumber = null,
    Object? sameAsPermAddress = null,
    Object? province = freezed,
    Object? ward = freezed,
    Object? specificAddress = freezed,
  }) {
    return _then(_value.copyWith(
      hasCoBorrower: null == hasCoBorrower
          ? _value.hasCoBorrower
          : hasCoBorrower // ignore: cast_nullable_to_non_nullable
              as bool,
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      documentNumber: null == documentNumber
          ? _value.documentNumber
          : documentNumber // ignore: cast_nullable_to_non_nullable
              as String,
      issueDate: freezed == issueDate
          ? _value.issueDate
          : issueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expiryDate: freezed == expiryDate
          ? _value.expiryDate
          : expiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      issuePlace: null == issuePlace
          ? _value.issuePlace
          : issuePlace // ignore: cast_nullable_to_non_nullable
              as String,
      birthDate: freezed == birthDate
          ? _value.birthDate
          : birthDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as Gender,
      permanentAddress: null == permanentAddress
          ? _value.permanentAddress
          : permanentAddress // ignore: cast_nullable_to_non_nullable
              as String,
      maritalStatus: null == maritalStatus
          ? _value.maritalStatus
          : maritalStatus // ignore: cast_nullable_to_non_nullable
              as MaritalStatus,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      sameAsPermAddress: null == sameAsPermAddress
          ? _value.sameAsPermAddress
          : sameAsPermAddress // ignore: cast_nullable_to_non_nullable
              as bool,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      ward: freezed == ward
          ? _value.ward
          : ward // ignore: cast_nullable_to_non_nullable
              as String?,
      specificAddress: freezed == specificAddress
          ? _value.specificAddress
          : specificAddress // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$Step2BorrowerDataImplCopyWith<$Res>
    implements $Step2BorrowerDataCopyWith<$Res> {
  factory _$$Step2BorrowerDataImplCopyWith(_$Step2BorrowerDataImpl value,
          $Res Function(_$Step2BorrowerDataImpl) then) =
      __$$Step2BorrowerDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool hasCoBorrower,
      String fullName,
      String documentNumber,
      DateTime? issueDate,
      DateTime? expiryDate,
      String issuePlace,
      DateTime? birthDate,
      Gender gender,
      String permanentAddress,
      MaritalStatus maritalStatus,
      String phoneNumber,
      bool sameAsPermAddress,
      String? province,
      String? ward,
      String? specificAddress});
}

/// @nodoc
class __$$Step2BorrowerDataImplCopyWithImpl<$Res>
    extends _$Step2BorrowerDataCopyWithImpl<$Res, _$Step2BorrowerDataImpl>
    implements _$$Step2BorrowerDataImplCopyWith<$Res> {
  __$$Step2BorrowerDataImplCopyWithImpl(_$Step2BorrowerDataImpl _value,
      $Res Function(_$Step2BorrowerDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of Step2BorrowerData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasCoBorrower = null,
    Object? fullName = null,
    Object? documentNumber = null,
    Object? issueDate = freezed,
    Object? expiryDate = freezed,
    Object? issuePlace = null,
    Object? birthDate = freezed,
    Object? gender = null,
    Object? permanentAddress = null,
    Object? maritalStatus = null,
    Object? phoneNumber = null,
    Object? sameAsPermAddress = null,
    Object? province = freezed,
    Object? ward = freezed,
    Object? specificAddress = freezed,
  }) {
    return _then(_$Step2BorrowerDataImpl(
      hasCoBorrower: null == hasCoBorrower
          ? _value.hasCoBorrower
          : hasCoBorrower // ignore: cast_nullable_to_non_nullable
              as bool,
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      documentNumber: null == documentNumber
          ? _value.documentNumber
          : documentNumber // ignore: cast_nullable_to_non_nullable
              as String,
      issueDate: freezed == issueDate
          ? _value.issueDate
          : issueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expiryDate: freezed == expiryDate
          ? _value.expiryDate
          : expiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      issuePlace: null == issuePlace
          ? _value.issuePlace
          : issuePlace // ignore: cast_nullable_to_non_nullable
              as String,
      birthDate: freezed == birthDate
          ? _value.birthDate
          : birthDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as Gender,
      permanentAddress: null == permanentAddress
          ? _value.permanentAddress
          : permanentAddress // ignore: cast_nullable_to_non_nullable
              as String,
      maritalStatus: null == maritalStatus
          ? _value.maritalStatus
          : maritalStatus // ignore: cast_nullable_to_non_nullable
              as MaritalStatus,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      sameAsPermAddress: null == sameAsPermAddress
          ? _value.sameAsPermAddress
          : sameAsPermAddress // ignore: cast_nullable_to_non_nullable
              as bool,
      province: freezed == province
          ? _value.province
          : province // ignore: cast_nullable_to_non_nullable
              as String?,
      ward: freezed == ward
          ? _value.ward
          : ward // ignore: cast_nullable_to_non_nullable
              as String?,
      specificAddress: freezed == specificAddress
          ? _value.specificAddress
          : specificAddress // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$Step2BorrowerDataImpl extends _Step2BorrowerData {
  const _$Step2BorrowerDataImpl(
      {this.hasCoBorrower = false,
      this.fullName = '',
      this.documentNumber = '',
      this.issueDate,
      this.expiryDate,
      this.issuePlace = '',
      this.birthDate,
      this.gender = Gender.male,
      this.permanentAddress = '',
      this.maritalStatus = MaritalStatus.single,
      this.phoneNumber = '',
      this.sameAsPermAddress = false,
      this.province,
      this.ward,
      this.specificAddress})
      : super._();

  @override
  @JsonKey()
  final bool hasCoBorrower;
  @override
  @JsonKey()
  final String fullName;
  @override
  @JsonKey()
  final String documentNumber;
  @override
  final DateTime? issueDate;
  @override
  final DateTime? expiryDate;
  @override
  @JsonKey()
  final String issuePlace;
  @override
  final DateTime? birthDate;
  @override
  @JsonKey()
  final Gender gender;
  @override
  @JsonKey()
  final String permanentAddress;
  @override
  @JsonKey()
  final MaritalStatus maritalStatus;
  @override
  @JsonKey()
  final String phoneNumber;
  @override
  @JsonKey()
  final bool sameAsPermAddress;
  @override
  final String? province;
  @override
  final String? ward;
  @override
  final String? specificAddress;

  @override
  String toString() {
    return 'Step2BorrowerData(hasCoBorrower: $hasCoBorrower, fullName: $fullName, documentNumber: $documentNumber, issueDate: $issueDate, expiryDate: $expiryDate, issuePlace: $issuePlace, birthDate: $birthDate, gender: $gender, permanentAddress: $permanentAddress, maritalStatus: $maritalStatus, phoneNumber: $phoneNumber, sameAsPermAddress: $sameAsPermAddress, province: $province, ward: $ward, specificAddress: $specificAddress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$Step2BorrowerDataImpl &&
            (identical(other.hasCoBorrower, hasCoBorrower) ||
                other.hasCoBorrower == hasCoBorrower) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.documentNumber, documentNumber) ||
                other.documentNumber == documentNumber) &&
            (identical(other.issueDate, issueDate) ||
                other.issueDate == issueDate) &&
            (identical(other.expiryDate, expiryDate) ||
                other.expiryDate == expiryDate) &&
            (identical(other.issuePlace, issuePlace) ||
                other.issuePlace == issuePlace) &&
            (identical(other.birthDate, birthDate) ||
                other.birthDate == birthDate) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.permanentAddress, permanentAddress) ||
                other.permanentAddress == permanentAddress) &&
            (identical(other.maritalStatus, maritalStatus) ||
                other.maritalStatus == maritalStatus) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.sameAsPermAddress, sameAsPermAddress) ||
                other.sameAsPermAddress == sameAsPermAddress) &&
            (identical(other.province, province) ||
                other.province == province) &&
            (identical(other.ward, ward) || other.ward == ward) &&
            (identical(other.specificAddress, specificAddress) ||
                other.specificAddress == specificAddress));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      hasCoBorrower,
      fullName,
      documentNumber,
      issueDate,
      expiryDate,
      issuePlace,
      birthDate,
      gender,
      permanentAddress,
      maritalStatus,
      phoneNumber,
      sameAsPermAddress,
      province,
      ward,
      specificAddress);

  /// Create a copy of Step2BorrowerData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$Step2BorrowerDataImplCopyWith<_$Step2BorrowerDataImpl> get copyWith =>
      __$$Step2BorrowerDataImplCopyWithImpl<_$Step2BorrowerDataImpl>(
          this, _$identity);
}

abstract class _Step2BorrowerData extends Step2BorrowerData {
  const factory _Step2BorrowerData(
      {final bool hasCoBorrower,
      final String fullName,
      final String documentNumber,
      final DateTime? issueDate,
      final DateTime? expiryDate,
      final String issuePlace,
      final DateTime? birthDate,
      final Gender gender,
      final String permanentAddress,
      final MaritalStatus maritalStatus,
      final String phoneNumber,
      final bool sameAsPermAddress,
      final String? province,
      final String? ward,
      final String? specificAddress}) = _$Step2BorrowerDataImpl;
  const _Step2BorrowerData._() : super._();

  @override
  bool get hasCoBorrower;
  @override
  String get fullName;
  @override
  String get documentNumber;
  @override
  DateTime? get issueDate;
  @override
  DateTime? get expiryDate;
  @override
  String get issuePlace;
  @override
  DateTime? get birthDate;
  @override
  Gender get gender;
  @override
  String get permanentAddress;
  @override
  MaritalStatus get maritalStatus;
  @override
  String get phoneNumber;
  @override
  bool get sameAsPermAddress;
  @override
  String? get province;
  @override
  String? get ward;
  @override
  String? get specificAddress;

  /// Create a copy of Step2BorrowerData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$Step2BorrowerDataImplCopyWith<_$Step2BorrowerDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
