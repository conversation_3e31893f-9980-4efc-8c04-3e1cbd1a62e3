import 'package:freezed_annotation/freezed_annotation.dart';

part 'step10_loan_confirmation_data.freezed.dart';

/// Model cho data của Step 10 - <PERSON><PERSON><PERSON> <PERSON>n <PERSON>n vay
@freezed
class Step10LoanConfirmationData with _$Step10LoanConfirmationData {
  const factory Step10LoanConfirmationData({
    @Default(false) bool isConfirmed,
    String? confirmationNote,
  }) = _Step10LoanConfirmationData;
} 