// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'step7_collateral_info_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Step7CollateralInfoData {
  String get assetType => throw _privateConstructorUsedError;
  int get assetValue => throw _privateConstructorUsedError;
  String get assetValueInWords => throw _privateConstructorUsedError;
  AssetCondition get assetCondition => throw _privateConstructorUsedError;
  String get ownerName => throw _privateConstructorUsedError;
  DateTime get ownerBirthDate => throw _privateConstructorUsedError;
  String get registrationType => throw _privateConstructorUsedError;
  String get assetLocation => throw _privateConstructorUsedError;

  /// Create a copy of Step7CollateralInfoData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $Step7CollateralInfoDataCopyWith<Step7CollateralInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $Step7CollateralInfoDataCopyWith<$Res> {
  factory $Step7CollateralInfoDataCopyWith(Step7CollateralInfoData value,
          $Res Function(Step7CollateralInfoData) then) =
      _$Step7CollateralInfoDataCopyWithImpl<$Res, Step7CollateralInfoData>;
  @useResult
  $Res call(
      {String assetType,
      int assetValue,
      String assetValueInWords,
      AssetCondition assetCondition,
      String ownerName,
      DateTime ownerBirthDate,
      String registrationType,
      String assetLocation});
}

/// @nodoc
class _$Step7CollateralInfoDataCopyWithImpl<$Res,
        $Val extends Step7CollateralInfoData>
    implements $Step7CollateralInfoDataCopyWith<$Res> {
  _$Step7CollateralInfoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Step7CollateralInfoData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assetType = null,
    Object? assetValue = null,
    Object? assetValueInWords = null,
    Object? assetCondition = null,
    Object? ownerName = null,
    Object? ownerBirthDate = null,
    Object? registrationType = null,
    Object? assetLocation = null,
  }) {
    return _then(_value.copyWith(
      assetType: null == assetType
          ? _value.assetType
          : assetType // ignore: cast_nullable_to_non_nullable
              as String,
      assetValue: null == assetValue
          ? _value.assetValue
          : assetValue // ignore: cast_nullable_to_non_nullable
              as int,
      assetValueInWords: null == assetValueInWords
          ? _value.assetValueInWords
          : assetValueInWords // ignore: cast_nullable_to_non_nullable
              as String,
      assetCondition: null == assetCondition
          ? _value.assetCondition
          : assetCondition // ignore: cast_nullable_to_non_nullable
              as AssetCondition,
      ownerName: null == ownerName
          ? _value.ownerName
          : ownerName // ignore: cast_nullable_to_non_nullable
              as String,
      ownerBirthDate: null == ownerBirthDate
          ? _value.ownerBirthDate
          : ownerBirthDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      registrationType: null == registrationType
          ? _value.registrationType
          : registrationType // ignore: cast_nullable_to_non_nullable
              as String,
      assetLocation: null == assetLocation
          ? _value.assetLocation
          : assetLocation // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$Step7CollateralInfoDataImplCopyWith<$Res>
    implements $Step7CollateralInfoDataCopyWith<$Res> {
  factory _$$Step7CollateralInfoDataImplCopyWith(
          _$Step7CollateralInfoDataImpl value,
          $Res Function(_$Step7CollateralInfoDataImpl) then) =
      __$$Step7CollateralInfoDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String assetType,
      int assetValue,
      String assetValueInWords,
      AssetCondition assetCondition,
      String ownerName,
      DateTime ownerBirthDate,
      String registrationType,
      String assetLocation});
}

/// @nodoc
class __$$Step7CollateralInfoDataImplCopyWithImpl<$Res>
    extends _$Step7CollateralInfoDataCopyWithImpl<$Res,
        _$Step7CollateralInfoDataImpl>
    implements _$$Step7CollateralInfoDataImplCopyWith<$Res> {
  __$$Step7CollateralInfoDataImplCopyWithImpl(
      _$Step7CollateralInfoDataImpl _value,
      $Res Function(_$Step7CollateralInfoDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of Step7CollateralInfoData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assetType = null,
    Object? assetValue = null,
    Object? assetValueInWords = null,
    Object? assetCondition = null,
    Object? ownerName = null,
    Object? ownerBirthDate = null,
    Object? registrationType = null,
    Object? assetLocation = null,
  }) {
    return _then(_$Step7CollateralInfoDataImpl(
      assetType: null == assetType
          ? _value.assetType
          : assetType // ignore: cast_nullable_to_non_nullable
              as String,
      assetValue: null == assetValue
          ? _value.assetValue
          : assetValue // ignore: cast_nullable_to_non_nullable
              as int,
      assetValueInWords: null == assetValueInWords
          ? _value.assetValueInWords
          : assetValueInWords // ignore: cast_nullable_to_non_nullable
              as String,
      assetCondition: null == assetCondition
          ? _value.assetCondition
          : assetCondition // ignore: cast_nullable_to_non_nullable
              as AssetCondition,
      ownerName: null == ownerName
          ? _value.ownerName
          : ownerName // ignore: cast_nullable_to_non_nullable
              as String,
      ownerBirthDate: null == ownerBirthDate
          ? _value.ownerBirthDate
          : ownerBirthDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      registrationType: null == registrationType
          ? _value.registrationType
          : registrationType // ignore: cast_nullable_to_non_nullable
              as String,
      assetLocation: null == assetLocation
          ? _value.assetLocation
          : assetLocation // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$Step7CollateralInfoDataImpl extends _Step7CollateralInfoData {
  const _$Step7CollateralInfoDataImpl(
      {this.assetType = '',
      this.assetValue = 0,
      this.assetValueInWords = '',
      this.assetCondition = AssetCondition.inUse,
      this.ownerName = '',
      required this.ownerBirthDate,
      this.registrationType = '',
      this.assetLocation = ''})
      : super._();

  @override
  @JsonKey()
  final String assetType;
  @override
  @JsonKey()
  final int assetValue;
  @override
  @JsonKey()
  final String assetValueInWords;
  @override
  @JsonKey()
  final AssetCondition assetCondition;
  @override
  @JsonKey()
  final String ownerName;
  @override
  final DateTime ownerBirthDate;
  @override
  @JsonKey()
  final String registrationType;
  @override
  @JsonKey()
  final String assetLocation;

  @override
  String toString() {
    return 'Step7CollateralInfoData(assetType: $assetType, assetValue: $assetValue, assetValueInWords: $assetValueInWords, assetCondition: $assetCondition, ownerName: $ownerName, ownerBirthDate: $ownerBirthDate, registrationType: $registrationType, assetLocation: $assetLocation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$Step7CollateralInfoDataImpl &&
            (identical(other.assetType, assetType) ||
                other.assetType == assetType) &&
            (identical(other.assetValue, assetValue) ||
                other.assetValue == assetValue) &&
            (identical(other.assetValueInWords, assetValueInWords) ||
                other.assetValueInWords == assetValueInWords) &&
            (identical(other.assetCondition, assetCondition) ||
                other.assetCondition == assetCondition) &&
            (identical(other.ownerName, ownerName) ||
                other.ownerName == ownerName) &&
            (identical(other.ownerBirthDate, ownerBirthDate) ||
                other.ownerBirthDate == ownerBirthDate) &&
            (identical(other.registrationType, registrationType) ||
                other.registrationType == registrationType) &&
            (identical(other.assetLocation, assetLocation) ||
                other.assetLocation == assetLocation));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      assetType,
      assetValue,
      assetValueInWords,
      assetCondition,
      ownerName,
      ownerBirthDate,
      registrationType,
      assetLocation);

  /// Create a copy of Step7CollateralInfoData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$Step7CollateralInfoDataImplCopyWith<_$Step7CollateralInfoDataImpl>
      get copyWith => __$$Step7CollateralInfoDataImplCopyWithImpl<
          _$Step7CollateralInfoDataImpl>(this, _$identity);
}

abstract class _Step7CollateralInfoData extends Step7CollateralInfoData {
  const factory _Step7CollateralInfoData(
      {final String assetType,
      final int assetValue,
      final String assetValueInWords,
      final AssetCondition assetCondition,
      final String ownerName,
      required final DateTime ownerBirthDate,
      final String registrationType,
      final String assetLocation}) = _$Step7CollateralInfoDataImpl;
  const _Step7CollateralInfoData._() : super._();

  @override
  String get assetType;
  @override
  int get assetValue;
  @override
  String get assetValueInWords;
  @override
  AssetCondition get assetCondition;
  @override
  String get ownerName;
  @override
  DateTime get ownerBirthDate;
  @override
  String get registrationType;
  @override
  String get assetLocation;

  /// Create a copy of Step7CollateralInfoData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$Step7CollateralInfoDataImplCopyWith<_$Step7CollateralInfoDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
