// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'step10_loan_confirmation_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Step10LoanConfirmationData {
  bool get isConfirmed => throw _privateConstructorUsedError;
  String? get confirmationNote => throw _privateConstructorUsedError;

  /// Create a copy of Step10LoanConfirmationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $Step10LoanConfirmationDataCopyWith<Step10LoanConfirmationData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $Step10LoanConfirmationDataCopyWith<$Res> {
  factory $Step10LoanConfirmationDataCopyWith(Step10LoanConfirmationData value,
          $Res Function(Step10LoanConfirmationData) then) =
      _$Step10LoanConfirmationDataCopyWithImpl<$Res,
          Step10LoanConfirmationData>;
  @useResult
  $Res call({bool isConfirmed, String? confirmationNote});
}

/// @nodoc
class _$Step10LoanConfirmationDataCopyWithImpl<$Res,
        $Val extends Step10LoanConfirmationData>
    implements $Step10LoanConfirmationDataCopyWith<$Res> {
  _$Step10LoanConfirmationDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Step10LoanConfirmationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isConfirmed = null,
    Object? confirmationNote = freezed,
  }) {
    return _then(_value.copyWith(
      isConfirmed: null == isConfirmed
          ? _value.isConfirmed
          : isConfirmed // ignore: cast_nullable_to_non_nullable
              as bool,
      confirmationNote: freezed == confirmationNote
          ? _value.confirmationNote
          : confirmationNote // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$Step10LoanConfirmationDataImplCopyWith<$Res>
    implements $Step10LoanConfirmationDataCopyWith<$Res> {
  factory _$$Step10LoanConfirmationDataImplCopyWith(
          _$Step10LoanConfirmationDataImpl value,
          $Res Function(_$Step10LoanConfirmationDataImpl) then) =
      __$$Step10LoanConfirmationDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isConfirmed, String? confirmationNote});
}

/// @nodoc
class __$$Step10LoanConfirmationDataImplCopyWithImpl<$Res>
    extends _$Step10LoanConfirmationDataCopyWithImpl<$Res,
        _$Step10LoanConfirmationDataImpl>
    implements _$$Step10LoanConfirmationDataImplCopyWith<$Res> {
  __$$Step10LoanConfirmationDataImplCopyWithImpl(
      _$Step10LoanConfirmationDataImpl _value,
      $Res Function(_$Step10LoanConfirmationDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of Step10LoanConfirmationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isConfirmed = null,
    Object? confirmationNote = freezed,
  }) {
    return _then(_$Step10LoanConfirmationDataImpl(
      isConfirmed: null == isConfirmed
          ? _value.isConfirmed
          : isConfirmed // ignore: cast_nullable_to_non_nullable
              as bool,
      confirmationNote: freezed == confirmationNote
          ? _value.confirmationNote
          : confirmationNote // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$Step10LoanConfirmationDataImpl implements _Step10LoanConfirmationData {
  const _$Step10LoanConfirmationDataImpl(
      {this.isConfirmed = false, this.confirmationNote});

  @override
  @JsonKey()
  final bool isConfirmed;
  @override
  final String? confirmationNote;

  @override
  String toString() {
    return 'Step10LoanConfirmationData(isConfirmed: $isConfirmed, confirmationNote: $confirmationNote)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$Step10LoanConfirmationDataImpl &&
            (identical(other.isConfirmed, isConfirmed) ||
                other.isConfirmed == isConfirmed) &&
            (identical(other.confirmationNote, confirmationNote) ||
                other.confirmationNote == confirmationNote));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isConfirmed, confirmationNote);

  /// Create a copy of Step10LoanConfirmationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$Step10LoanConfirmationDataImplCopyWith<_$Step10LoanConfirmationDataImpl>
      get copyWith => __$$Step10LoanConfirmationDataImplCopyWithImpl<
          _$Step10LoanConfirmationDataImpl>(this, _$identity);
}

abstract class _Step10LoanConfirmationData
    implements Step10LoanConfirmationData {
  const factory _Step10LoanConfirmationData(
      {final bool isConfirmed,
      final String? confirmationNote}) = _$Step10LoanConfirmationDataImpl;

  @override
  bool get isConfirmed;
  @override
  String? get confirmationNote;

  /// Create a copy of Step10LoanConfirmationData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$Step10LoanConfirmationDataImplCopyWith<_$Step10LoanConfirmationDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
