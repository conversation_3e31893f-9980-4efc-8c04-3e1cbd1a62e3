/// Data cho màn hình xác nhận thông tin cá nhân trong flow đăng ký CTV
class PersonalInfoConfirmationData {
  final String? fullName;
  final String? documentNumber;
  final String? address;
  final String? phoneNumber;
  final String? provinceId;
  final String? branchId;
  final String? referralCode;
  final String? email;
  final bool isPersonalInfoValid;

  const PersonalInfoConfirmationData({
    this.fullName,
    this.documentNumber,
    this.address,
    this.phoneNumber,
    this.provinceId,
    this.branchId,
    this.referralCode,
    this.email,
    this.isPersonalInfoValid = false,
  });

  PersonalInfoConfirmationData copyWith({
    String? fullName,
    String? documentNumber,
    String? address,
    String? phoneNumber,
    String? provinceId,
    String? branchId,
    String? referralCode,
    String? email,
    bool? isPersonalInfoValid,
  }) {
    return PersonalInfoConfirmationData(
      fullName: fullName ?? this.fullName,
      documentNumber: documentNumber ?? this.documentNumber,
      address: address ?? this.address,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      provinceId: provinceId ?? this.provinceId,
      branchId: branchId ?? this.branchId,
      referralCode: referralCode ?? this.referralCode,
      email: email ?? this.email,
      isPersonalInfoValid: isPersonalInfoValid ?? this.isPersonalInfoValid,
    );
  }

  @override
  String toString() {
    return 'PersonalInfoConfirmationData(fullName: $fullName, documentNumber: $documentNumber, phoneNumber: $phoneNumber, isPersonalInfoValid: $isPersonalInfoValid)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PersonalInfoConfirmationData &&
        other.fullName == fullName &&
        other.documentNumber == documentNumber &&
        other.address == address &&
        other.phoneNumber == phoneNumber &&
        other.provinceId == provinceId &&
        other.branchId == branchId &&
        other.referralCode == referralCode &&
        other.email == email &&
        other.isPersonalInfoValid == isPersonalInfoValid;
  }

  @override
  int get hashCode {
    return fullName.hashCode ^
        documentNumber.hashCode ^
        address.hashCode ^
        phoneNumber.hashCode ^
        provinceId.hashCode ^
        branchId.hashCode ^
        referralCode.hashCode ^
        email.hashCode ^
        isPersonalInfoValid.hashCode;
  }
} 