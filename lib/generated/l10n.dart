// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `KienlongBank Sales App`
  String get appName {
    return Intl.message(
      'KienlongBank Sales App',
      name: 'appName',
      desc: '',
      args: [],
    );
  }

  /// `Home`
  String get home {
    return Intl.message(
      'Home',
      name: 'home',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message(
      'Password',
      name: 'password',
      desc: '',
      args: [],
    );
  }

  /// `Login`
  String get login {
    return Intl.message(
      'Login',
      name: 'login',
      desc: '',
      args: [],
    );
  }

  /// `Introduction`
  String get introduction {
    return Intl.message(
      'Introduction',
      name: 'introduction',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get register {
    return Intl.message(
      'Register',
      name: 'register',
      desc: '',
      args: [],
    );
  }

  /// `Register account`
  String get register_account {
    return Intl.message(
      'Register account',
      name: 'register_account',
      desc: '',
      args: [],
    );
  }

  /// `Document Verification Guide`
  String get documentVerificationGuide {
    return Intl.message(
      'Document Verification Guide',
      name: 'documentVerificationGuide',
      desc: '',
      args: [],
    );
  }

  /// `Notes when taking documents:`
  String get notesWhenTakingDocuments {
    return Intl.message(
      'Notes when taking documents:',
      name: 'notesWhenTakingDocuments',
      desc: '',
      args: [],
    );
  }

  /// `Avoid using images like:`
  String get avoidUsingImages {
    return Intl.message(
      'Avoid using images like:',
      name: 'avoidUsingImages',
      desc: '',
      args: [],
    );
  }

  /// `Check front side of ID`
  String get checkFrontSide {
    return Intl.message(
      'Check front side of ID',
      name: 'checkFrontSide',
      desc: '',
      args: [],
    );
  }

  /// `Check back side of ID`
  String get checkBackSide {
    return Intl.message(
      'Check back side of ID',
      name: 'checkBackSide',
      desc: '',
      args: [],
    );
  }

  /// `Please check the photo`
  String get pleaseCheckPhoto {
    return Intl.message(
      'Please check the photo',
      name: 'pleaseCheckPhoto',
      desc: '',
      args: [],
    );
  }

  /// `Please place the document in the frame for verification`
  String get placeDocumentInFrame {
    return Intl.message(
      'Please place the document in the frame for verification',
      name: 'placeDocumentInFrame',
      desc: '',
      args: [],
    );
  }

  /// `Click to capture`
  String get clickToCapture {
    return Intl.message(
      'Click to capture',
      name: 'clickToCapture',
      desc: '',
      args: [],
    );
  }

  /// `Personal Information Confirmation`
  String get personalInfoConfirmation {
    return Intl.message(
      'Personal Information Confirmation',
      name: 'personalInfoConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Personal Information Confirmation`
  String get personal_info_confirmation_title {
    return Intl.message(
      'Personal Information Confirmation',
      name: 'personal_info_confirmation_title',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Your Information`
  String get personal_info_confirmation_header {
    return Intl.message(
      'Confirm Your Information',
      name: 'personal_info_confirmation_header',
      desc: '',
      args: [],
    );
  }

  /// `Please review and confirm your personal information before proceeding`
  String get personal_info_confirmation_subtitle {
    return Intl.message(
      'Please review and confirm your personal information before proceeding',
      name: 'personal_info_confirmation_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Information from ID`
  String get infoFromId {
    return Intl.message(
      'Information from ID',
      name: 'infoFromId',
      desc: '',
      args: [],
    );
  }

  /// `Additional Information`
  String get additionalInfo {
    return Intl.message(
      'Additional Information',
      name: 'additionalInfo',
      desc: '',
      args: [],
    );
  }

  /// `Phone Number`
  String get phoneNumber {
    return Intl.message(
      'Phone Number',
      name: 'phoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Enter phone number`
  String get enterPhoneNumber {
    return Intl.message(
      'Enter phone number',
      name: 'enterPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Position`
  String get position {
    return Intl.message(
      'Position',
      name: 'position',
      desc: '',
      args: [],
    );
  }

  /// `Province/City`
  String get province {
    return Intl.message(
      'Province/City',
      name: 'province',
      desc: '',
      args: [],
    );
  }

  /// `Select Province/City`
  String get province_selection_label {
    return Intl.message(
      'Select Province/City',
      name: 'province_selection_label',
      desc: '',
      args: [],
    );
  }

  /// `Select province/city`
  String get selectProvince {
    return Intl.message(
      'Select province/city',
      name: 'selectProvince',
      desc: '',
      args: [],
    );
  }

  /// `Choose your province or city`
  String get select_province_hint {
    return Intl.message(
      'Choose your province or city',
      name: 'select_province_hint',
      desc: '',
      args: [],
    );
  }

  /// `Branch`
  String get branch {
    return Intl.message(
      'Branch',
      name: 'branch',
      desc: '',
      args: [],
    );
  }

  /// `Select Branch`
  String get branch_selection_label {
    return Intl.message(
      'Select Branch',
      name: 'branch_selection_label',
      desc: '',
      args: [],
    );
  }

  /// `Select branch`
  String get selectBranch {
    return Intl.message(
      'Select branch',
      name: 'selectBranch',
      desc: '',
      args: [],
    );
  }

  /// `Choose your branch`
  String get select_branch_hint {
    return Intl.message(
      'Choose your branch',
      name: 'select_branch_hint',
      desc: '',
      args: [],
    );
  }

  /// `Branch Address`
  String get branchAddress {
    return Intl.message(
      'Branch Address',
      name: 'branchAddress',
      desc: '',
      args: [],
    );
  }

  /// `Referral Code (if any)`
  String get referralCode {
    return Intl.message(
      'Referral Code (if any)',
      name: 'referralCode',
      desc: '',
      args: [],
    );
  }

  /// `Enter referral code`
  String get enterReferralCode {
    return Intl.message(
      'Enter referral code',
      name: 'enterReferralCode',
      desc: '',
      args: [],
    );
  }

  /// `Registration Successful`
  String get registrationSuccess {
    return Intl.message(
      'Registration Successful',
      name: 'registrationSuccess',
      desc: '',
      args: [],
    );
  }

  /// `KienlongBank will contact you soon to inform the result!`
  String get bankWillContact {
    return Intl.message(
      'KienlongBank will contact you soon to inform the result!',
      name: 'bankWillContact',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message(
      'Close',
      name: 'close',
      desc: '',
      args: [],
    );
  }

  /// `Agree`
  String get agree {
    return Intl.message(
      'Agree',
      name: 'agree',
      desc: '',
      args: [],
    );
  }

  /// `Full Name`
  String get fullName {
    return Intl.message(
      'Full Name',
      name: 'fullName',
      desc: '',
      args: [],
    );
  }

  /// `ID Number`
  String get idNumber {
    return Intl.message(
      'ID Number',
      name: 'idNumber',
      desc: '',
      args: [],
    );
  }

  /// `Residential Address`
  String get residentialAddress {
    return Intl.message(
      'Residential Address',
      name: 'residentialAddress',
      desc: '',
      args: [],
    );
  }

  /// `New CTV`
  String get newCTV {
    return Intl.message(
      'New CTV',
      name: 'newCTV',
      desc: '',
      args: [],
    );
  }

  /// `Daily Installment Loan`
  String get dailyInstallmentLoan {
    return Intl.message(
      'Daily Installment Loan',
      name: 'dailyInstallmentLoan',
      desc: '',
      args: [],
    );
  }

  /// `Benefits as CTV`
  String get benefitsAsCTV {
    return Intl.message(
      'Benefits as CTV',
      name: 'benefitsAsCTV',
      desc: '',
      args: [],
    );
  }

  /// `Steps to become a CTV`
  String get stepsToBecomeCTV {
    return Intl.message(
      'Steps to become a CTV',
      name: 'stepsToBecomeCTV',
      desc: '',
      args: [],
    );
  }

  /// `Daily Installment Loan is a loan product in the form of daily installments (principal, interest) to serve living needs and business activities, other activities.`
  String get dailyInstallmentLoanDesc {
    return Intl.message(
      'Daily Installment Loan is a loan product in the form of daily installments (principal, interest) to serve living needs and business activities, other activities.',
      name: 'dailyInstallmentLoanDesc',
      desc: '',
      args: [],
    );
  }

  /// `Suitable for individuals with sudden, short-term spending needs; small businesses and individual businesses; customers with stable income and good credit history.`
  String get suitableFor {
    return Intl.message(
      'Suitable for individuals with sudden, short-term spending needs; small businesses and individual businesses; customers with stable income and good credit history.',
      name: 'suitableFor',
      desc: '',
      args: [],
    );
  }

  /// `Flexible working hours`
  String get flexibleWorkingHours {
    return Intl.message(
      'Flexible working hours',
      name: 'flexibleWorkingHours',
      desc: '',
      args: [],
    );
  }

  /// `Monthly service fee`
  String get monthlyServiceFee {
    return Intl.message(
      'Monthly service fee',
      name: 'monthlyServiceFee',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited income`
  String get unlimitedIncome {
    return Intl.message(
      'Unlimited income',
      name: 'unlimitedIncome',
      desc: '',
      args: [],
    );
  }

  /// `Work uniform provided`
  String get workUniform {
    return Intl.message(
      'Work uniform provided',
      name: 'workUniform',
      desc: '',
      args: [],
    );
  }

  /// `Health insurance and accident insurance provided by Kienlongbank when meeting outstanding balance conditions`
  String get healthInsurance {
    return Intl.message(
      'Health insurance and accident insurance provided by Kienlongbank when meeting outstanding balance conditions',
      name: 'healthInsurance',
      desc: '',
      args: [],
    );
  }

  /// `Holiday bonus for 30/04, National Day 02/9, ...`
  String get holidayBonus {
    return Intl.message(
      'Holiday bonus for 30/04, National Day 02/9, ...',
      name: 'holidayBonus',
      desc: '',
      args: [],
    );
  }

  /// `Requirements:`
  String get requirements {
    return Intl.message(
      'Requirements:',
      name: 'requirements',
      desc: '',
      args: [],
    );
  }

  /// `Vietnamese citizen with legal capacity, civil act capacity and civil liability according to law`
  String get vietnameseCitizen {
    return Intl.message(
      'Vietnamese citizen with legal capacity, civil act capacity and civil liability according to law',
      name: 'vietnameseCitizen',
      desc: '',
      args: [],
    );
  }

  /// `Good background and character; No criminal record`
  String get goodBackground {
    return Intl.message(
      'Good background and character; No criminal record',
      name: 'goodBackground',
      desc: '',
      args: [],
    );
  }

  /// `Age from 18 to 60, graduated from secondary school or higher`
  String get ageRequirement {
    return Intl.message(
      'Age from 18 to 60, graduated from secondary school or higher',
      name: 'ageRequirement',
      desc: '',
      args: [],
    );
  }

  /// `Good health, clear mind`
  String get healthRequirement {
    return Intl.message(
      'Good health, clear mind',
      name: 'healthRequirement',
      desc: '',
      args: [],
    );
  }

  /// `Minimum collateral of 150 million VND and commitment to comply with Kienlongbank's minimum average outstanding balance regulations`
  String get assetRequirement {
    return Intl.message(
      'Minimum collateral of 150 million VND and commitment to comply with Kienlongbank\'s minimum average outstanding balance regulations',
      name: 'assetRequirement',
      desc: '',
      args: [],
    );
  }

  /// `Understanding of working area`
  String get areaKnowledge {
    return Intl.message(
      'Understanding of working area',
      name: 'areaKnowledge',
      desc: '',
      args: [],
    );
  }

  /// `Not working for other credit institutions or finance companies`
  String get noOtherBank {
    return Intl.message(
      'Not working for other credit institutions or finance companies',
      name: 'noOtherBank',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueText {
    return Intl.message(
      'Continue',
      name: 'continueText',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continue_button {
    return Intl.message(
      'Continue',
      name: 'continue_button',
      desc: '',
      args: [],
    );
  }

  /// `Retake`
  String get retake {
    return Intl.message(
      'Retake',
      name: 'retake',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirm {
    return Intl.message(
      'Confirm',
      name: 'confirm',
      desc: '',
      args: [],
    );
  }

  /// `ID Card`
  String get idCard {
    return Intl.message(
      'ID Card',
      name: 'idCard',
      desc: '',
      args: [],
    );
  }

  /// `Passport`
  String get passport {
    return Intl.message(
      'Passport',
      name: 'passport',
      desc: '',
      args: [],
    );
  }

  /// `No provinces available`
  String get no_provinces_available {
    return Intl.message(
      'No provinces available',
      name: 'no_provinces_available',
      desc: '',
      args: [],
    );
  }

  /// `No branches available for selected province`
  String get no_branches_available {
    return Intl.message(
      'No branches available for selected province',
      name: 'no_branches_available',
      desc: '',
      args: [],
    );
  }

  /// `Server error. Please try again later.`
  String get errorServerInternal {
    return Intl.message(
      'Server error. Please try again later.',
      name: 'errorServerInternal',
      desc: '',
      args: [],
    );
  }

  /// `The requested resource was not found.`
  String get errorServerNotFound {
    return Intl.message(
      'The requested resource was not found.',
      name: 'errorServerNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Too many requests. Please try again later.`
  String get errorServerTooManyRequests {
    return Intl.message(
      'Too many requests. Please try again later.',
      name: 'errorServerTooManyRequests',
      desc: '',
      args: [],
    );
  }

  /// `Invalid request data.`
  String get errorServerBadRequest {
    return Intl.message(
      'Invalid request data.',
      name: 'errorServerBadRequest',
      desc: '',
      args: [],
    );
  }

  /// `An error occurred. Please try again.`
  String get errorServerUnknown {
    return Intl.message(
      'An error occurred. Please try again.',
      name: 'errorServerUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Connection timeout. Please check your internet connection.`
  String get errorNetworkConnectionTimeout {
    return Intl.message(
      'Connection timeout. Please check your internet connection.',
      name: 'errorNetworkConnectionTimeout',
      desc: '',
      args: [],
    );
  }

  /// `Request timeout. Please try again.`
  String get errorNetworkSendTimeout {
    return Intl.message(
      'Request timeout. Please try again.',
      name: 'errorNetworkSendTimeout',
      desc: '',
      args: [],
    );
  }

  /// `Response timeout. Please try again.`
  String get errorNetworkReceiveTimeout {
    return Intl.message(
      'Response timeout. Please try again.',
      name: 'errorNetworkReceiveTimeout',
      desc: '',
      args: [],
    );
  }

  /// `No internet connection. Please check your network settings.`
  String get errorNetworkNoConnection {
    return Intl.message(
      'No internet connection. Please check your network settings.',
      name: 'errorNetworkNoConnection',
      desc: '',
      args: [],
    );
  }

  /// `Request was cancelled.`
  String get errorNetworkRequestCancelled {
    return Intl.message(
      'Request was cancelled.',
      name: 'errorNetworkRequestCancelled',
      desc: '',
      args: [],
    );
  }

  /// `Certificate verification failed.`
  String get errorNetworkCertificate {
    return Intl.message(
      'Certificate verification failed.',
      name: 'errorNetworkCertificate',
      desc: '',
      args: [],
    );
  }

  /// `Network error. Please try again.`
  String get errorNetworkUnknown {
    return Intl.message(
      'Network error. Please try again.',
      name: 'errorNetworkUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Failed to read cached data.`
  String get errorCacheRead {
    return Intl.message(
      'Failed to read cached data.',
      name: 'errorCacheRead',
      desc: '',
      args: [],
    );
  }

  /// `Failed to save data to cache.`
  String get errorCacheWrite {
    return Intl.message(
      'Failed to save data to cache.',
      name: 'errorCacheWrite',
      desc: '',
      args: [],
    );
  }

  /// `Cached data is corrupted.`
  String get errorCacheDataCorrupted {
    return Intl.message(
      'Cached data is corrupted.',
      name: 'errorCacheDataCorrupted',
      desc: '',
      args: [],
    );
  }

  /// `Storage is full.`
  String get errorCacheStorageFull {
    return Intl.message(
      'Storage is full.',
      name: 'errorCacheStorageFull',
      desc: '',
      args: [],
    );
  }

  /// `Cache error occurred.`
  String get errorCacheUnknown {
    return Intl.message(
      'Cache error occurred.',
      name: 'errorCacheUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Authentication failed. Please login again.`
  String get errorAuthInvalidCredentials {
    return Intl.message(
      'Authentication failed. Please login again.',
      name: 'errorAuthInvalidCredentials',
      desc: '',
      args: [],
    );
  }

  /// `Access denied. You don't have permission to perform this action.`
  String get errorAuthAccessDenied {
    return Intl.message(
      'Access denied. You don\'t have permission to perform this action.',
      name: 'errorAuthAccessDenied',
      desc: '',
      args: [],
    );
  }

  /// `Session expired. Please login again.`
  String get errorAuthTokenExpired {
    return Intl.message(
      'Session expired. Please login again.',
      name: 'errorAuthTokenExpired',
      desc: '',
      args: [],
    );
  }

  /// `User not found.`
  String get errorAuthUserNotFound {
    return Intl.message(
      'User not found.',
      name: 'errorAuthUserNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Account is locked or disabled.`
  String get errorAuthAccountLocked {
    return Intl.message(
      'Account is locked or disabled.',
      name: 'errorAuthAccountLocked',
      desc: '',
      args: [],
    );
  }

  /// `Authentication error occurred.`
  String get errorAuthUnknown {
    return Intl.message(
      'Authentication error occurred.',
      name: 'errorAuthUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid email address.`
  String get errorValidationInvalidEmail {
    return Intl.message(
      'Please enter a valid email address.',
      name: 'errorValidationInvalidEmail',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid password.`
  String get errorValidationInvalidPassword {
    return Intl.message(
      'Please enter a valid password.',
      name: 'errorValidationInvalidPassword',
      desc: '',
      args: [],
    );
  }

  /// `This field is required.`
  String get errorValidationRequiredField {
    return Intl.message(
      'This field is required.',
      name: 'errorValidationRequiredField',
      desc: '',
      args: [],
    );
  }

  /// `Input is too short.`
  String get errorValidationTooShort {
    return Intl.message(
      'Input is too short.',
      name: 'errorValidationTooShort',
      desc: '',
      args: [],
    );
  }

  /// `Input is too long.`
  String get errorValidationTooLong {
    return Intl.message(
      'Input is too long.',
      name: 'errorValidationTooLong',
      desc: '',
      args: [],
    );
  }

  /// `Invalid format.`
  String get errorValidationInvalidFormat {
    return Intl.message(
      'Invalid format.',
      name: 'errorValidationInvalidFormat',
      desc: '',
      args: [],
    );
  }

  /// `Validation failed.`
  String get errorValidationServer {
    return Intl.message(
      'Validation failed.',
      name: 'errorValidationServer',
      desc: '',
      args: [],
    );
  }

  /// `Validation error occurred.`
  String get errorValidationUnknown {
    return Intl.message(
      'Validation error occurred.',
      name: 'errorValidationUnknown',
      desc: '',
      args: [],
    );
  }

  /// `Connected`
  String get networkConnected {
    return Intl.message(
      'Connected',
      name: 'networkConnected',
      desc: '',
      args: [],
    );
  }

  /// `No internet connection`
  String get networkDisconnected {
    return Intl.message(
      'No internet connection',
      name: 'networkDisconnected',
      desc: '',
      args: [],
    );
  }

  /// `Unstable connection`
  String get networkUnstable {
    return Intl.message(
      'Unstable connection',
      name: 'networkUnstable',
      desc: '',
      args: [],
    );
  }

  /// `Checking connection...`
  String get networkChecking {
    return Intl.message(
      'Checking connection...',
      name: 'networkChecking',
      desc: '',
      args: [],
    );
  }

  /// `Successfully reconnected`
  String get networkReconnectedSuccess {
    return Intl.message(
      'Successfully reconnected',
      name: 'networkReconnectedSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Unstable connection`
  String get networkUnstableWarning {
    return Intl.message(
      'Unstable connection',
      name: 'networkUnstableWarning',
      desc: '',
      args: [],
    );
  }

  /// `Network is unstable - Some features may be affected`
  String get networkUnstableDescription {
    return Intl.message(
      'Network is unstable - Some features may be affected',
      name: 'networkUnstableDescription',
      desc: '',
      args: [],
    );
  }

  /// `Checking connection...`
  String get networkCheckingConnection {
    return Intl.message(
      'Checking connection...',
      name: 'networkCheckingConnection',
      desc: '',
      args: [],
    );
  }

  /// `Please check your internet connection and try again`
  String get networkDisconnectedDescription {
    return Intl.message(
      'Please check your internet connection and try again',
      name: 'networkDisconnectedDescription',
      desc: '',
      args: [],
    );
  }

  /// `Network connection is unstable. Some features may be affected`
  String get networkUnstableConnectionDescription {
    return Intl.message(
      'Network connection is unstable. Some features may be affected',
      name: 'networkUnstableConnectionDescription',
      desc: '',
      args: [],
    );
  }

  /// `Checking connection status...`
  String get networkCheckingConnectionDescription {
    return Intl.message(
      'Checking connection status...',
      name: 'networkCheckingConnectionDescription',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get networkRetry {
    return Intl.message(
      'Retry',
      name: 'networkRetry',
      desc: '',
      args: [],
    );
  }

  /// `Checking...`
  String get networkRetryChecking {
    return Intl.message(
      'Checking...',
      name: 'networkRetryChecking',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get networkContinue {
    return Intl.message(
      'Continue',
      name: 'networkContinue',
      desc: '',
      args: [],
    );
  }

  /// `Check`
  String get networkCheck {
    return Intl.message(
      'Check',
      name: 'networkCheck',
      desc: '',
      args: [],
    );
  }

  /// `Create New Loan`
  String get createNewLoan {
    return Intl.message(
      'Create New Loan',
      name: 'createNewLoan',
      desc: '',
      args: [],
    );
  }

  /// `Provide Identity Documents`
  String get loanStep1Title {
    return Intl.message(
      'Provide Identity Documents',
      name: 'loanStep1Title',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Primary Borrower Information`
  String get loanStep2Title {
    return Intl.message(
      'Confirm Primary Borrower Information',
      name: 'loanStep2Title',
      desc: '',
      args: [],
    );
  }

  /// `Provide Identity Documents (Co-borrower)`
  String get loanStep3Title {
    return Intl.message(
      'Provide Identity Documents (Co-borrower)',
      name: 'loanStep3Title',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Co-borrower Information`
  String get loanStep4Title {
    return Intl.message(
      'Confirm Co-borrower Information',
      name: 'loanStep4Title',
      desc: '',
      args: [],
    );
  }

  /// `Provide Loan Request Information`
  String get loanStep5Title {
    return Intl.message(
      'Provide Loan Request Information',
      name: 'loanStep5Title',
      desc: '',
      args: [],
    );
  }

  /// `Provide Financial Information`
  String get loanStep6Title {
    return Intl.message(
      'Provide Financial Information',
      name: 'loanStep6Title',
      desc: '',
      args: [],
    );
  }

  /// `Provide Collateral Information`
  String get loanStep7Title {
    return Intl.message(
      'Provide Collateral Information',
      name: 'loanStep7Title',
      desc: '',
      args: [],
    );
  }

  /// `Detailed Collateral Information`
  String get loanStep8Title {
    return Intl.message(
      'Detailed Collateral Information',
      name: 'loanStep8Title',
      desc: '',
      args: [],
    );
  }

  /// `Provide Document List`
  String get loanStep9Title {
    return Intl.message(
      'Provide Document List',
      name: 'loanStep9Title',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Loan Information`
  String get loanStep10Title {
    return Intl.message(
      'Confirm Loan Information',
      name: 'loanStep10Title',
      desc: '',
      args: [],
    );
  }

  /// `Loan Creation Successful`
  String get loanStep11Title {
    return Intl.message(
      'Loan Creation Successful',
      name: 'loanStep11Title',
      desc: '',
      args: [],
    );
  }

  /// `In Development...`
  String get inDevelopment {
    return Intl.message(
      'In Development...',
      name: 'inDevelopment',
      desc: '',
      args: [],
    );
  }

  /// `Bước`
  String get step {
    return Intl.message(
      'Bước',
      name: 'step',
      desc: '',
      args: [],
    );
  }

  /// `No data available`
  String get noData {
    return Intl.message(
      'No data available',
      name: 'noData',
      desc: '',
      args: [],
    );
  }

  /// `Logging in...`
  String get submitting_login {
    return Intl.message(
      'Logging in...',
      name: 'submitting_login',
      desc: '',
      args: [],
    );
  }

  /// `Registering...`
  String get submitting_register {
    return Intl.message(
      'Registering...',
      name: 'submitting_register',
      desc: '',
      args: [],
    );
  }

  /// `Saving...`
  String get submitting_save {
    return Intl.message(
      'Saving...',
      name: 'submitting_save',
      desc: '',
      args: [],
    );
  }

  /// `Deleting...`
  String get submitting_delete {
    return Intl.message(
      'Deleting...',
      name: 'submitting_delete',
      desc: '',
      args: [],
    );
  }

  /// `Updating...`
  String get submitting_update {
    return Intl.message(
      'Updating...',
      name: 'submitting_update',
      desc: '',
      args: [],
    );
  }

  /// `Submitting form...`
  String get submitting_form {
    return Intl.message(
      'Submitting form...',
      name: 'submitting_form',
      desc: '',
      args: [],
    );
  }

  /// `CTV Policy`
  String get cvt_policy_title {
    return Intl.message(
      'CTV Policy',
      name: 'cvt_policy_title',
      desc: '',
      args: [],
    );
  }

  /// `Capture Front Side`
  String get captureFrontSide {
    return Intl.message(
      'Capture Front Side',
      name: 'captureFrontSide',
      desc: '',
      args: [],
    );
  }

  /// `Capture Back Side`
  String get captureBackSide {
    return Intl.message(
      'Capture Back Side',
      name: 'captureBackSide',
      desc: '',
      args: [],
    );
  }

  /// `Please enter email`
  String get validationEmailRequired {
    return Intl.message(
      'Please enter email',
      name: 'validationEmailRequired',
      desc: '',
      args: [],
    );
  }

  /// `Invalid email format`
  String get validationEmailInvalid {
    return Intl.message(
      'Invalid email format',
      name: 'validationEmailInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Please enter phone number`
  String get validationPhoneRequired {
    return Intl.message(
      'Please enter phone number',
      name: 'validationPhoneRequired',
      desc: '',
      args: [],
    );
  }

  /// `Phone number must start with 0`
  String get validationPhoneStartWithZero {
    return Intl.message(
      'Phone number must start with 0',
      name: 'validationPhoneStartWithZero',
      desc: '',
      args: [],
    );
  }

  /// `Phone number must have 10 digits`
  String get validationPhoneLength {
    return Intl.message(
      'Phone number must have 10 digits',
      name: 'validationPhoneLength',
      desc: '',
      args: [],
    );
  }

  /// `Invalid phone number`
  String get validationPhoneInvalid {
    return Intl.message(
      'Invalid phone number',
      name: 'validationPhoneInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Please enter ID number`
  String get validationIdNumberRequired {
    return Intl.message(
      'Please enter ID number',
      name: 'validationIdNumberRequired',
      desc: '',
      args: [],
    );
  }

  /// `ID number must have 12 digits`
  String get validationIdNumberLength {
    return Intl.message(
      'ID number must have 12 digits',
      name: 'validationIdNumberLength',
      desc: '',
      args: [],
    );
  }

  /// `ID number must contain only digits`
  String get validationIdNumberNumeric {
    return Intl.message(
      'ID number must contain only digits',
      name: 'validationIdNumberNumeric',
      desc: '',
      args: [],
    );
  }

  /// `Please enter full name`
  String get validationFullNameRequired {
    return Intl.message(
      'Please enter full name',
      name: 'validationFullNameRequired',
      desc: '',
      args: [],
    );
  }

  /// `Full name must have at least 2 characters`
  String get validationFullNameMinLength {
    return Intl.message(
      'Full name must have at least 2 characters',
      name: 'validationFullNameMinLength',
      desc: '',
      args: [],
    );
  }

  /// `Full name cannot exceed 100 characters`
  String get validationFullNameMaxLength {
    return Intl.message(
      'Full name cannot exceed 100 characters',
      name: 'validationFullNameMaxLength',
      desc: '',
      args: [],
    );
  }

  /// `Full name can only contain letters and spaces`
  String get validationFullNameCharacters {
    return Intl.message(
      'Full name can only contain letters and spaces',
      name: 'validationFullNameCharacters',
      desc: '',
      args: [],
    );
  }

  /// `Please enter address`
  String get validationAddressRequired {
    return Intl.message(
      'Please enter address',
      name: 'validationAddressRequired',
      desc: '',
      args: [],
    );
  }

  /// `Address must have at least 10 characters`
  String get validationAddressMinLength {
    return Intl.message(
      'Address must have at least 10 characters',
      name: 'validationAddressMinLength',
      desc: '',
      args: [],
    );
  }

  /// `Address cannot exceed 200 characters`
  String get validationAddressMaxLength {
    return Intl.message(
      'Address cannot exceed 200 characters',
      name: 'validationAddressMaxLength',
      desc: '',
      args: [],
    );
  }

  /// `Address must not contain special characters`
  String get validationAddressCharacters {
    return Intl.message(
      'Address must not contain special characters',
      name: 'validationAddressCharacters',
      desc: '',
      args: [],
    );
  }

  /// `Please enter {fieldName}`
  String validationRequired(Object fieldName) {
    return Intl.message(
      'Please enter $fieldName',
      name: 'validationRequired',
      desc: '',
      args: [fieldName],
    );
  }

  /// `{fieldName} must have at least {minLength} characters`
  String validationMinLength(Object fieldName, Object minLength) {
    return Intl.message(
      '$fieldName must have at least $minLength characters',
      name: 'validationMinLength',
      desc: '',
      args: [fieldName, minLength],
    );
  }

  /// `{fieldName} cannot exceed {maxLength} characters`
  String validationMaxLength(Object fieldName, Object maxLength) {
    return Intl.message(
      '$fieldName cannot exceed $maxLength characters',
      name: 'validationMaxLength',
      desc: '',
      args: [fieldName, maxLength],
    );
  }

  /// `{fieldName} must contain only digits`
  String validationNumericOnly(Object fieldName) {
    return Intl.message(
      '$fieldName must contain only digits',
      name: 'validationNumericOnly',
      desc: '',
      args: [fieldName],
    );
  }

  /// `Please enter password`
  String get validationPasswordRequired {
    return Intl.message(
      'Please enter password',
      name: 'validationPasswordRequired',
      desc: '',
      args: [],
    );
  }

  /// `Password must have at least 8 characters`
  String get validationPasswordMinLength {
    return Intl.message(
      'Password must have at least 8 characters',
      name: 'validationPasswordMinLength',
      desc: '',
      args: [],
    );
  }

  /// `Password cannot exceed 50 characters`
  String get validationPasswordMaxLength {
    return Intl.message(
      'Password cannot exceed 50 characters',
      name: 'validationPasswordMaxLength',
      desc: '',
      args: [],
    );
  }

  /// `Password must contain uppercase, lowercase and numbers`
  String get validationPasswordStrength {
    return Intl.message(
      'Password must contain uppercase, lowercase and numbers',
      name: 'validationPasswordStrength',
      desc: '',
      args: [],
    );
  }

  /// `Please enter confirm password`
  String get validationConfirmPasswordRequired {
    return Intl.message(
      'Please enter confirm password',
      name: 'validationConfirmPasswordRequired',
      desc: '',
      args: [],
    );
  }

  /// `Confirm password does not match`
  String get validationConfirmPasswordMismatch {
    return Intl.message(
      'Confirm password does not match',
      name: 'validationConfirmPasswordMismatch',
      desc: '',
      args: [],
    );
  }

  /// `Please select date of birth`
  String get validationDateOfBirthRequired {
    return Intl.message(
      'Please select date of birth',
      name: 'validationDateOfBirthRequired',
      desc: '',
      args: [],
    );
  }

  /// `You must be at least 18 years old`
  String get validationDateOfBirthAge {
    return Intl.message(
      'You must be at least 18 years old',
      name: 'validationDateOfBirthAge',
      desc: '',
      args: [],
    );
  }

  /// `Invalid date of birth`
  String get validationDateOfBirthInvalid {
    return Intl.message(
      'Invalid date of birth',
      name: 'validationDateOfBirthInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Referrer code must have at least 3 characters`
  String get validationReferrerCodeMinLength {
    return Intl.message(
      'Referrer code must have at least 3 characters',
      name: 'validationReferrerCodeMinLength',
      desc: '',
      args: [],
    );
  }

  /// `Referrer code cannot exceed 20 characters`
  String get validationReferrerCodeMaxLength {
    return Intl.message(
      'Referrer code cannot exceed 20 characters',
      name: 'validationReferrerCodeMaxLength',
      desc: '',
      args: [],
    );
  }

  /// `Referrer code can only contain letters, numbers and hyphens`
  String get validationReferrerCodeCharacters {
    return Intl.message(
      'Referrer code can only contain letters, numbers and hyphens',
      name: 'validationReferrerCodeCharacters',
      desc: '',
      args: [],
    );
  }

  /// `Please enter amount`
  String get validationAmountRequired {
    return Intl.message(
      'Please enter amount',
      name: 'validationAmountRequired',
      desc: '',
      args: [],
    );
  }

  /// `Amount can only contain digits and commas`
  String get validationAmountFormat {
    return Intl.message(
      'Amount can only contain digits and commas',
      name: 'validationAmountFormat',
      desc: '',
      args: [],
    );
  }

  /// `Amount must be greater than 0`
  String get validationAmountPositive {
    return Intl.message(
      'Amount must be greater than 0',
      name: 'validationAmountPositive',
      desc: '',
      args: [],
    );
  }

  /// `Amount is too large`
  String get validationAmountTooLarge {
    return Intl.message(
      'Amount is too large',
      name: 'validationAmountTooLarge',
      desc: '',
      args: [],
    );
  }

  /// `Please enter URL`
  String get validationUrlRequired {
    return Intl.message(
      'Please enter URL',
      name: 'validationUrlRequired',
      desc: '',
      args: [],
    );
  }

  /// `Invalid URL format`
  String get validationUrlInvalid {
    return Intl.message(
      'Invalid URL format',
      name: 'validationUrlInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Please enter OTP code`
  String get validationOtpRequired {
    return Intl.message(
      'Please enter OTP code',
      name: 'validationOtpRequired',
      desc: '',
      args: [],
    );
  }

  /// `OTP code must have {length} digits`
  String validationOtpLength(Object length) {
    return Intl.message(
      'OTP code must have $length digits',
      name: 'validationOtpLength',
      desc: '',
      args: [length],
    );
  }

  /// `OTP code must contain only digits`
  String get validationOtpNumeric {
    return Intl.message(
      'OTP code must contain only digits',
      name: 'validationOtpNumeric',
      desc: '',
      args: [],
    );
  }

  /// `Please enter bank account number`
  String get validationBankAccountRequired {
    return Intl.message(
      'Please enter bank account number',
      name: 'validationBankAccountRequired',
      desc: '',
      args: [],
    );
  }

  /// `Bank account number must have 8-20 digits`
  String get validationBankAccountLength {
    return Intl.message(
      'Bank account number must have 8-20 digits',
      name: 'validationBankAccountLength',
      desc: '',
      args: [],
    );
  }

  /// `Bank account number must contain only digits`
  String get validationBankAccountNumeric {
    return Intl.message(
      'Bank account number must contain only digits',
      name: 'validationBankAccountNumeric',
      desc: '',
      args: [],
    );
  }

  /// `Please enter tax code`
  String get validationTaxCodeRequired {
    return Intl.message(
      'Please enter tax code',
      name: 'validationTaxCodeRequired',
      desc: '',
      args: [],
    );
  }

  /// `Tax code must have 10 or 13 digits`
  String get validationTaxCodeLength {
    return Intl.message(
      'Tax code must have 10 or 13 digits',
      name: 'validationTaxCodeLength',
      desc: '',
      args: [],
    );
  }

  /// `Tax code must contain only digits`
  String get validationTaxCodeNumeric {
    return Intl.message(
      'Tax code must contain only digits',
      name: 'validationTaxCodeNumeric',
      desc: '',
      args: [],
    );
  }

  /// `Please select issue date`
  String get validationIssueDateRequired {
    return Intl.message(
      'Please select issue date',
      name: 'validationIssueDateRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please select expiry date`
  String get validationExpiryDateRequired {
    return Intl.message(
      'Please select expiry date',
      name: 'validationExpiryDateRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please select birth date`
  String get validationBirthDateRequired {
    return Intl.message(
      'Please select birth date',
      name: 'validationBirthDateRequired',
      desc: '',
      args: [],
    );
  }

  /// `Please select the type of identity document of the borrower to verify information`
  String get step1_identity_document_description {
    return Intl.message(
      'Please select the type of identity document of the borrower to verify information',
      name: 'step1_identity_document_description',
      desc: '',
      args: [],
    );
  }

  /// `Document Type`
  String get document_type_label {
    return Intl.message(
      'Document Type',
      name: 'document_type_label',
      desc: '',
      args: [],
    );
  }

  /// `Front Side of Document`
  String get document_front_side_label {
    return Intl.message(
      'Front Side of Document',
      name: 'document_front_side_label',
      desc: '',
      args: [],
    );
  }

  /// `Back Side of Document`
  String get document_back_side_label {
    return Intl.message(
      'Back Side of Document',
      name: 'document_back_side_label',
      desc: '',
      args: [],
    );
  }

  /// `CTV registration successful`
  String get success_ctv_message {
    return Intl.message(
      'CTV registration successful',
      name: 'success_ctv_message',
      desc: '',
      args: [],
    );
  }

  /// `Account`
  String get account {
    return Intl.message(
      'Account',
      name: 'account',
      desc: '',
      args: [],
    );
  }

  /// `Business Unit Address`
  String get dvkd_address {
    return Intl.message(
      'Business Unit Address',
      name: 'dvkd_address',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get profile {
    return Intl.message(
      'Profile',
      name: 'profile',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout {
    return Intl.message(
      'Logout',
      name: 'logout',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout_title {
    return Intl.message(
      'Logout',
      name: 'logout_title',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to logout?`
  String get logout_message {
    return Intl.message(
      'Are you sure you want to logout?',
      name: 'logout_message',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout_confirm {
    return Intl.message(
      'Logout',
      name: 'logout_confirm',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get logout_cancel {
    return Intl.message(
      'Cancel',
      name: 'logout_cancel',
      desc: '',
      args: [],
    );
  }

  /// `An error occurred while logging out`
  String get logout_error {
    return Intl.message(
      'An error occurred while logging out',
      name: 'logout_error',
      desc: '',
      args: [],
    );
  }

  /// `Session Expired`
  String get session_expired_title {
    return Intl.message(
      'Session Expired',
      name: 'session_expired_title',
      desc: '',
      args: [],
    );
  }

  /// `Your session has expired. Please login again to continue using the app.`
  String get session_expired_message {
    return Intl.message(
      'Your session has expired. Please login again to continue using the app.',
      name: 'session_expired_message',
      desc: '',
      args: [],
    );
  }

  /// `Login Again`
  String get session_expired_button {
    return Intl.message(
      'Login Again',
      name: 'session_expired_button',
      desc: '',
      args: [],
    );
  }

  /// `CTV Referral`
  String get ctv_referral_title {
    return Intl.message(
      'CTV Referral',
      name: 'ctv_referral_title',
      desc: '',
      args: [],
    );
  }

  /// `Information from ID documents`
  String get info_from_id_documents {
    return Intl.message(
      'Information from ID documents',
      name: 'info_from_id_documents',
      desc: '',
      args: [],
    );
  }

  /// `Additional Information`
  String get additional_information {
    return Intl.message(
      'Additional Information',
      name: 'additional_information',
      desc: '',
      args: [],
    );
  }

  /// `Registration Branch`
  String get registration_branch {
    return Intl.message(
      'Registration Branch',
      name: 'registration_branch',
      desc: '',
      args: [],
    );
  }

  /// `Referred person email (optional)`
  String get referred_person_email {
    return Intl.message(
      'Referred person email (optional)',
      name: 'referred_person_email',
      desc: '',
      args: [],
    );
  }

  /// `Collaborator`
  String get collaborator {
    return Intl.message(
      'Collaborator',
      name: 'collaborator',
      desc: '',
      args: [],
    );
  }

  /// `Province/City`
  String get province_city {
    return Intl.message(
      'Province/City',
      name: 'province_city',
      desc: '',
      args: [],
    );
  }

  /// `Select province/city`
  String get select_province_city {
    return Intl.message(
      'Select province/city',
      name: 'select_province_city',
      desc: '',
      args: [],
    );
  }

  /// `Business Unit`
  String get business_unit {
    return Intl.message(
      'Business Unit',
      name: 'business_unit',
      desc: '',
      args: [],
    );
  }

  /// `Business Unit Registration`
  String get business_unit_registration {
    return Intl.message(
      'Business Unit Registration',
      name: 'business_unit_registration',
      desc: '',
      args: [],
    );
  }

  /// `No province data available`
  String get no_province_data {
    return Intl.message(
      'No province data available',
      name: 'no_province_data',
      desc: '',
      args: [],
    );
  }

  /// `Loading branches...`
  String get loading_branches {
    return Intl.message(
      'Loading branches...',
      name: 'loading_branches',
      desc: '',
      args: [],
    );
  }

  /// `No branches available for selected province`
  String get no_branches_for_province {
    return Intl.message(
      'No branches available for selected province',
      name: 'no_branches_for_province',
      desc: '',
      args: [],
    );
  }

  /// `Try again`
  String get try_again {
    return Intl.message(
      'Try again',
      name: 'try_again',
      desc: '',
      args: [],
    );
  }

  /// `Referral code (if any)`
  String get referral_code_if_any {
    return Intl.message(
      'Referral code (if any)',
      name: 'referral_code_if_any',
      desc: '',
      args: [],
    );
  }

  /// `Enter referral code`
  String get enter_referral_code {
    return Intl.message(
      'Enter referral code',
      name: 'enter_referral_code',
      desc: '',
      args: [],
    );
  }

  /// `Referral code`
  String get referral_code {
    return Intl.message(
      'Referral code',
      name: 'referral_code',
      desc: '',
      args: [],
    );
  }

  /// `Searching for referrer...`
  String get searching_referrer {
    return Intl.message(
      'Searching for referrer...',
      name: 'searching_referrer',
      desc: '',
      args: [],
    );
  }

  /// `Referrer`
  String get referrer {
    return Intl.message(
      'Referrer',
      name: 'referrer',
      desc: '',
      args: [],
    );
  }

  /// `ID document number`
  String get id_document_number {
    return Intl.message(
      'ID document number',
      name: 'id_document_number',
      desc: '',
      args: [],
    );
  }

  /// `Email (if any)`
  String get email_optional {
    return Intl.message(
      'Email (if any)',
      name: 'email_optional',
      desc: '',
      args: [],
    );
  }

  /// `Referrer Name`
  String get name_seller {
    return Intl.message(
      'Referrer Name',
      name: 'name_seller',
      desc: '',
      args: [],
    );
  }

  /// `Referrer Code`
  String get code_seller {
    return Intl.message(
      'Referrer Code',
      name: 'code_seller',
      desc: '',
      args: [],
    );
  }

  /// `Scan QR code`
  String get qrScanTitle {
    return Intl.message(
      'Scan QR code',
      name: 'qrScanTitle',
      desc: '',
      args: [],
    );
  }

  /// `Skip`
  String get skip {
    return Intl.message(
      'Skip',
      name: 'skip',
      desc: '',
      args: [],
    );
  }

  /// `Cannot read information from QR code. Please try again or select an image from the gallery.`
  String get qr_scan_invalid_data {
    return Intl.message(
      'Cannot read information from QR code. Please try again or select an image from the gallery.',
      name: 'qr_scan_invalid_data',
      desc: '',
      args: [],
    );
  }

  /// `Has co-borrower`
  String get has_co_borrower {
    return Intl.message(
      'Has co-borrower',
      name: 'has_co_borrower',
      desc: '',
      args: [],
    );
  }

  /// `Identity document information`
  String get identity_document_info {
    return Intl.message(
      'Identity document information',
      name: 'identity_document_info',
      desc: '',
      args: [],
    );
  }

  /// `Full name`
  String get full_name {
    return Intl.message(
      'Full name',
      name: 'full_name',
      desc: '',
      args: [],
    );
  }

  /// `Document number`
  String get document_number {
    return Intl.message(
      'Document number',
      name: 'document_number',
      desc: '',
      args: [],
    );
  }

  /// `Issue date`
  String get issue_date {
    return Intl.message(
      'Issue date',
      name: 'issue_date',
      desc: '',
      args: [],
    );
  }

  /// `Expiry date`
  String get expiry_date {
    return Intl.message(
      'Expiry date',
      name: 'expiry_date',
      desc: '',
      args: [],
    );
  }

  /// `Issue place`
  String get issue_place {
    return Intl.message(
      'Issue place',
      name: 'issue_place',
      desc: '',
      args: [],
    );
  }

  /// `Birth date`
  String get birth_date {
    return Intl.message(
      'Birth date',
      name: 'birth_date',
      desc: '',
      args: [],
    );
  }

  /// `Gender`
  String get gender {
    return Intl.message(
      'Gender',
      name: 'gender',
      desc: '',
      args: [],
    );
  }

  /// `Permanent address`
  String get permanent_address {
    return Intl.message(
      'Permanent address',
      name: 'permanent_address',
      desc: '',
      args: [],
    );
  }

  /// `Personal information`
  String get personal_info {
    return Intl.message(
      'Personal information',
      name: 'personal_info',
      desc: '',
      args: [],
    );
  }

  /// `Marital status`
  String get marital_status {
    return Intl.message(
      'Marital status',
      name: 'marital_status',
      desc: '',
      args: [],
    );
  }

  /// `Phone number`
  String get phone_number {
    return Intl.message(
      'Phone number',
      name: 'phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Current address is the same as permanent address`
  String get same_as_permanent_address {
    return Intl.message(
      'Current address is the same as permanent address',
      name: 'same_as_permanent_address',
      desc: '',
      args: [],
    );
  }

  /// `District`
  String get district {
    return Intl.message(
      'District',
      name: 'district',
      desc: '',
      args: [],
    );
  }

  /// `Ward`
  String get ward {
    return Intl.message(
      'Ward',
      name: 'ward',
      desc: '',
      args: [],
    );
  }

  /// `Specific address`
  String get specific_address {
    return Intl.message(
      'Specific address',
      name: 'specific_address',
      desc: '',
      args: [],
    );
  }

  /// `Error`
  String get error {
    return Intl.message(
      'Error',
      name: 'error',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get retry {
    return Intl.message(
      'Retry',
      name: 'retry',
      desc: '',
      args: [],
    );
  }

  /// `No camera found on device`
  String get camera_not_found {
    return Intl.message(
      'No camera found on device',
      name: 'camera_not_found',
      desc: '',
      args: [],
    );
  }

  /// `Cannot initialize camera`
  String get camera_init_error {
    return Intl.message(
      'Cannot initialize camera',
      name: 'camera_init_error',
      desc: '',
      args: [],
    );
  }

  /// `Camera permission error`
  String get camera_permission_error {
    return Intl.message(
      'Camera permission error',
      name: 'camera_permission_error',
      desc: '',
      args: [],
    );
  }

  /// `Cannot decode image`
  String get cannot_decode_image {
    return Intl.message(
      'Cannot decode image',
      name: 'cannot_decode_image',
      desc: '',
      args: [],
    );
  }

  /// `No wards available for the selected province/district.`
  String get no_ward_data {
    return Intl.message(
      'No wards available for the selected province/district.',
      name: 'no_ward_data',
      desc: '',
      args: [],
    );
  }

  /// `Document Information`
  String get document_info_label {
    return Intl.message(
      'Document Information',
      name: 'document_info_label',
      desc: '',
      args: [],
    );
  }

  /// `Capture document`
  String get capturePassportGuidanceTitle {
    return Intl.message(
      'Capture document',
      name: 'capturePassportGuidanceTitle',
      desc: '',
      args: [],
    );
  }

  /// `Check passport photo`
  String get previewPassportGuidanceTitle {
    return Intl.message(
      'Check passport photo',
      name: 'previewPassportGuidanceTitle',
      desc: '',
      args: [],
    );
  }

  /// `Please make sure the photo is clear, not blurry or obscured`
  String get passportGuidanceSubtitle {
    return Intl.message(
      'Please make sure the photo is clear, not blurry or obscured',
      name: 'passportGuidanceSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Loan purpose`
  String get loan_purpose {
    return Intl.message(
      'Loan purpose',
      name: 'loan_purpose',
      desc: '',
      args: [],
    );
  }

  /// `Loan term`
  String get loan_term {
    return Intl.message(
      'Loan term',
      name: 'loan_term',
      desc: '',
      args: [],
    );
  }

  /// `Loading loan purposes...`
  String get loading_loan_purpose {
    return Intl.message(
      'Loading loan purposes...',
      name: 'loading_loan_purpose',
      desc: '',
      args: [],
    );
  }

  /// `No loan purpose data available`
  String get no_loan_purpose_data {
    return Intl.message(
      'No loan purpose data available',
      name: 'no_loan_purpose_data',
      desc: '',
      args: [],
    );
  }

  /// `Loading loan terms...`
  String get loading_loan_term {
    return Intl.message(
      'Loading loan terms...',
      name: 'loading_loan_term',
      desc: '',
      args: [],
    );
  }

  /// `No loan term data available`
  String get no_loan_term_data {
    return Intl.message(
      'No loan term data available',
      name: 'no_loan_term_data',
      desc: '',
      args: [],
    );
  }

  /// `Requested loan amount`
  String get loan_amount {
    return Intl.message(
      'Requested loan amount',
      name: 'loan_amount',
      desc: '',
      args: [],
    );
  }

  /// `Total need`
  String get total_need {
    return Intl.message(
      'Total need',
      name: 'total_need',
      desc: '',
      args: [],
    );
  }

  /// `Own capital`
  String get own_capital {
    return Intl.message(
      'Own capital',
      name: 'own_capital',
      desc: '',
      args: [],
    );
  }

  /// `Branch code`
  String get branch_code {
    return Intl.message(
      'Branch code',
      name: 'branch_code',
      desc: '',
      args: [],
    );
  }

  /// `Loan method`
  String get loan_method {
    return Intl.message(
      'Loan method',
      name: 'loan_method',
      desc: '',
      args: [],
    );
  }

  /// `Custom purpose name`
  String get custom_purpose_name {
    return Intl.message(
      'Custom purpose name',
      name: 'custom_purpose_name',
      desc: '',
      args: [],
    );
  }

  /// `Loan type`
  String get loan_type {
    return Intl.message(
      'Loan type',
      name: 'loan_type',
      desc: '',
      args: [],
    );
  }

  /// `Loan plan`
  String get loan_plan {
    return Intl.message(
      'Loan plan',
      name: 'loan_plan',
      desc: '',
      args: [],
    );
  }

  /// `Repayment method`
  String get repayment_method {
    return Intl.message(
      'Repayment method',
      name: 'repayment_method',
      desc: '',
      args: [],
    );
  }

  /// `Disbursement method`
  String get disbursement_method {
    return Intl.message(
      'Disbursement method',
      name: 'disbursement_method',
      desc: '',
      args: [],
    );
  }

  /// `Recipient account number`
  String get recipient_account_number {
    return Intl.message(
      'Recipient account number',
      name: 'recipient_account_number',
      desc: '',
      args: [],
    );
  }

  /// `Daily repayment of principal and interest`
  String get daily_repayment {
    return Intl.message(
      'Daily repayment of principal and interest',
      name: 'daily_repayment',
      desc: '',
      args: [],
    );
  }

  /// `With collateral`
  String get collateral_yes {
    return Intl.message(
      'With collateral',
      name: 'collateral_yes',
      desc: '',
      args: [],
    );
  }

  /// `Without collateral`
  String get collateral_no {
    return Intl.message(
      'Without collateral',
      name: 'collateral_no',
      desc: '',
      args: [],
    );
  }

  /// `Installment loan`
  String get installment_loan {
    return Intl.message(
      'Installment loan',
      name: 'installment_loan',
      desc: '',
      args: [],
    );
  }

  /// `Cash`
  String get cash {
    return Intl.message(
      'Cash',
      name: 'cash',
      desc: '',
      args: [],
    );
  }

  /// `Transfer`
  String get transfer {
    return Intl.message(
      'Transfer',
      name: 'transfer',
      desc: '',
      args: [],
    );
  }

  /// `Loading...`
  String get loading {
    return Intl.message(
      'Loading...',
      name: 'loading',
      desc: '',
      args: [],
    );
  }

  /// `Loading payment accounts...`
  String get loading_payment_accounts {
    return Intl.message(
      'Loading payment accounts...',
      name: 'loading_payment_accounts',
      desc: '',
      args: [],
    );
  }

  /// `No payment accounts found for this ID number.`
  String get no_payment_account_data {
    return Intl.message(
      'No payment accounts found for this ID number.',
      name: 'no_payment_account_data',
      desc: '',
      args: [],
    );
  }

  /// `Please enter passport number`
  String get validationPassportNumberRequired {
    return Intl.message(
      'Please enter passport number',
      name: 'validationPassportNumberRequired',
      desc: '',
      args: [],
    );
  }

  /// `Passport number cannot exceed 50 characters`
  String get validationPassportNumberMaxLength {
    return Intl.message(
      'Passport number cannot exceed 50 characters',
      name: 'validationPassportNumberMaxLength',
      desc: '',
      args: [],
    );
  }

  /// `Passport number can only contain letters and numbers (no special characters)`
  String get validationPassportNumberAlphanumeric {
    return Intl.message(
      'Passport number can only contain letters and numbers (no special characters)',
      name: 'validationPassportNumberAlphanumeric',
      desc: '',
      args: [],
    );
  }

  /// `Please select the type of identity document for the borrower to verify information`
  String get step3_identity_document_description {
    return Intl.message(
      'Please select the type of identity document for the borrower to verify information',
      name: 'step3_identity_document_description',
      desc: '',
      args: [],
    );
  }

  /// `Please enter your own capital`
  String get validationOwnCapitalRequired {
    return Intl.message(
      'Please enter your own capital',
      name: 'validationOwnCapitalRequired',
      desc: '',
      args: [],
    );
  }

  /// `Own capital must be a valid number`
  String get validationOwnCapitalInvalid {
    return Intl.message(
      'Own capital must be a valid number',
      name: 'validationOwnCapitalInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Own capital cannot exceed 1,000,000,000`
  String get validationOwnCapitalTooLarge {
    return Intl.message(
      'Own capital cannot exceed 1,000,000,000',
      name: 'validationOwnCapitalTooLarge',
      desc: '',
      args: [],
    );
  }

  /// `Please enter the loan amount`
  String get validationLoanAmountRequired {
    return Intl.message(
      'Please enter the loan amount',
      name: 'validationLoanAmountRequired',
      desc: '',
      args: [],
    );
  }

  /// `Loan amount must be at least 1,000,000`
  String get validationLoanAmountTooSmall {
    return Intl.message(
      'Loan amount must be at least 1,000,000',
      name: 'validationLoanAmountTooSmall',
      desc: '',
      args: [],
    );
  }

  /// `Loan amount cannot exceed 1,000,000,000`
  String get validationLoanAmountTooLarge {
    return Intl.message(
      'Loan amount cannot exceed 1,000,000,000',
      name: 'validationLoanAmountTooLarge',
      desc: '',
      args: [],
    );
  }

  /// `Please enter issue place`
  String get validationIssuePlaceRequired {
    return Intl.message(
      'Please enter issue place',
      name: 'validationIssuePlaceRequired',
      desc: '',
      args: [],
    );
  }

  /// `Issue place cannot exceed 50 characters`
  String get validationIssuePlaceMaxLength {
    return Intl.message(
      'Issue place cannot exceed 50 characters',
      name: 'validationIssuePlaceMaxLength',
      desc: '',
      args: [],
    );
  }

  /// `Passport number can only contain letters and numbers`
  String get validationPassportNumberCharacters {
    return Intl.message(
      'Passport number can only contain letters and numbers',
      name: 'validationPassportNumberCharacters',
      desc: '',
      args: [],
    );
  }

  /// `Income source`
  String get income_source {
    return Intl.message(
      'Income source',
      name: 'income_source',
      desc: '',
      args: [],
    );
  }

  /// `Average daily revenue`
  String get daily_revenue {
    return Intl.message(
      'Average daily revenue',
      name: 'daily_revenue',
      desc: '',
      args: [],
    );
  }

  /// `Average daily income`
  String get daily_income {
    return Intl.message(
      'Average daily income',
      name: 'daily_income',
      desc: '',
      args: [],
    );
  }

  /// `Financial Information`
  String get financial_info_title {
    return Intl.message(
      'Financial Information',
      name: 'financial_info_title',
      desc: '',
      args: [],
    );
  }

  /// `Business Location`
  String get business_location_title {
    return Intl.message(
      'Business Location',
      name: 'business_location_title',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
