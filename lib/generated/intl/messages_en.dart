// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(fieldName, maxLength) =>
      "${fieldName} cannot exceed ${maxLength} characters";

  static String m1(fieldName, minLength) =>
      "${fieldName} must have at least ${minLength} characters";

  static String m2(fieldName) => "${fieldName} must contain only digits";

  static String m3(length) => "OTP code must have ${length} digits";

  static String m4(fieldName) => "Please enter ${fieldName}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "account": MessageLookupByLibrary.simpleMessage("Account"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("Additional Information"),
        "additional_information":
            MessageLookupByLibrary.simpleMessage("Additional Information"),
        "ageRequirement": MessageLookupByLibrary.simpleMessage(
            "Age from 18 to 60, graduated from secondary school or higher"),
        "agree": MessageLookupByLibrary.simpleMessage("Agree"),
        "appName":
            MessageLookupByLibrary.simpleMessage("KienlongBank Sales App"),
        "areaKnowledge": MessageLookupByLibrary.simpleMessage(
            "Understanding of working area"),
        "assetRequirement": MessageLookupByLibrary.simpleMessage(
            "Minimum collateral of 150 million VND and commitment to comply with Kienlongbank\'s minimum average outstanding balance regulations"),
        "avoidUsingImages":
            MessageLookupByLibrary.simpleMessage("Avoid using images like:"),
        "bankWillContact": MessageLookupByLibrary.simpleMessage(
            "KienlongBank will contact you soon to inform the result!"),
        "benefitsAsCTV":
            MessageLookupByLibrary.simpleMessage("Benefits as CTV"),
        "birth_date": MessageLookupByLibrary.simpleMessage("Birth date"),
        "branch": MessageLookupByLibrary.simpleMessage("Branch"),
        "branchAddress": MessageLookupByLibrary.simpleMessage("Branch Address"),
        "branch_code": MessageLookupByLibrary.simpleMessage("Branch code"),
        "branch_selection_label":
            MessageLookupByLibrary.simpleMessage("Select Branch"),
        "business_location_title":
            MessageLookupByLibrary.simpleMessage("Business Location"),
        "business_unit": MessageLookupByLibrary.simpleMessage("Business Unit"),
        "business_unit_registration":
            MessageLookupByLibrary.simpleMessage("Business Unit Registration"),
        "camera_init_error":
            MessageLookupByLibrary.simpleMessage("Cannot initialize camera"),
        "camera_not_found":
            MessageLookupByLibrary.simpleMessage("No camera found on device"),
        "camera_permission_error":
            MessageLookupByLibrary.simpleMessage("Camera permission error"),
        "cannot_decode_image":
            MessageLookupByLibrary.simpleMessage("Cannot decode image"),
        "captureBackSide":
            MessageLookupByLibrary.simpleMessage("Capture Back Side"),
        "captureFrontSide":
            MessageLookupByLibrary.simpleMessage("Capture Front Side"),
        "capturePassportGuidanceTitle":
            MessageLookupByLibrary.simpleMessage("Capture document"),
        "cash": MessageLookupByLibrary.simpleMessage("Cash"),
        "checkBackSide":
            MessageLookupByLibrary.simpleMessage("Check back side of ID"),
        "checkFrontSide":
            MessageLookupByLibrary.simpleMessage("Check front side of ID"),
        "clickToCapture":
            MessageLookupByLibrary.simpleMessage("Click to capture"),
        "close": MessageLookupByLibrary.simpleMessage("Close"),
        "code_seller": MessageLookupByLibrary.simpleMessage("Referrer Code"),
        "collaborator": MessageLookupByLibrary.simpleMessage("Collaborator"),
        "collateral_no":
            MessageLookupByLibrary.simpleMessage("Without collateral"),
        "collateral_yes":
            MessageLookupByLibrary.simpleMessage("With collateral"),
        "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "continueText": MessageLookupByLibrary.simpleMessage("Continue"),
        "continue_button": MessageLookupByLibrary.simpleMessage("Continue"),
        "createNewLoan":
            MessageLookupByLibrary.simpleMessage("Create New Loan"),
        "ctv_referral_title":
            MessageLookupByLibrary.simpleMessage("CTV Referral"),
        "custom_purpose_name":
            MessageLookupByLibrary.simpleMessage("Custom purpose name"),
        "cvt_policy_title": MessageLookupByLibrary.simpleMessage("CTV Policy"),
        "dailyInstallmentLoan":
            MessageLookupByLibrary.simpleMessage("Daily Installment Loan"),
        "dailyInstallmentLoanDesc": MessageLookupByLibrary.simpleMessage(
            "Daily Installment Loan is a loan product in the form of daily installments (principal, interest) to serve living needs and business activities, other activities."),
        "daily_income":
            MessageLookupByLibrary.simpleMessage("Average daily income"),
        "daily_repayment": MessageLookupByLibrary.simpleMessage(
            "Daily repayment of principal and interest"),
        "daily_revenue":
            MessageLookupByLibrary.simpleMessage("Average daily revenue"),
        "disbursement_method":
            MessageLookupByLibrary.simpleMessage("Disbursement method"),
        "district": MessageLookupByLibrary.simpleMessage("District"),
        "documentVerificationGuide":
            MessageLookupByLibrary.simpleMessage("Document Verification Guide"),
        "document_back_side_label":
            MessageLookupByLibrary.simpleMessage("Back Side of Document"),
        "document_front_side_label":
            MessageLookupByLibrary.simpleMessage("Front Side of Document"),
        "document_info_label":
            MessageLookupByLibrary.simpleMessage("Document Information"),
        "document_number":
            MessageLookupByLibrary.simpleMessage("Document number"),
        "document_type_label":
            MessageLookupByLibrary.simpleMessage("Document Type"),
        "dvkd_address":
            MessageLookupByLibrary.simpleMessage("Business Unit Address"),
        "email_optional":
            MessageLookupByLibrary.simpleMessage("Email (if any)"),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Enter phone number"),
        "enterReferralCode":
            MessageLookupByLibrary.simpleMessage("Enter referral code"),
        "enter_referral_code":
            MessageLookupByLibrary.simpleMessage("Enter referral code"),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "errorAuthAccessDenied": MessageLookupByLibrary.simpleMessage(
            "Access denied. You don\'t have permission to perform this action."),
        "errorAuthAccountLocked": MessageLookupByLibrary.simpleMessage(
            "Account is locked or disabled."),
        "errorAuthInvalidCredentials": MessageLookupByLibrary.simpleMessage(
            "Authentication failed. Please login again."),
        "errorAuthTokenExpired": MessageLookupByLibrary.simpleMessage(
            "Session expired. Please login again."),
        "errorAuthUnknown": MessageLookupByLibrary.simpleMessage(
            "Authentication error occurred."),
        "errorAuthUserNotFound":
            MessageLookupByLibrary.simpleMessage("User not found."),
        "errorCacheDataCorrupted":
            MessageLookupByLibrary.simpleMessage("Cached data is corrupted."),
        "errorCacheRead":
            MessageLookupByLibrary.simpleMessage("Failed to read cached data."),
        "errorCacheStorageFull":
            MessageLookupByLibrary.simpleMessage("Storage is full."),
        "errorCacheUnknown":
            MessageLookupByLibrary.simpleMessage("Cache error occurred."),
        "errorCacheWrite": MessageLookupByLibrary.simpleMessage(
            "Failed to save data to cache."),
        "errorNetworkCertificate": MessageLookupByLibrary.simpleMessage(
            "Certificate verification failed."),
        "errorNetworkConnectionTimeout": MessageLookupByLibrary.simpleMessage(
            "Connection timeout. Please check your internet connection."),
        "errorNetworkNoConnection": MessageLookupByLibrary.simpleMessage(
            "No internet connection. Please check your network settings."),
        "errorNetworkReceiveTimeout": MessageLookupByLibrary.simpleMessage(
            "Response timeout. Please try again."),
        "errorNetworkRequestCancelled":
            MessageLookupByLibrary.simpleMessage("Request was cancelled."),
        "errorNetworkSendTimeout": MessageLookupByLibrary.simpleMessage(
            "Request timeout. Please try again."),
        "errorNetworkUnknown": MessageLookupByLibrary.simpleMessage(
            "Network error. Please try again."),
        "errorServerBadRequest":
            MessageLookupByLibrary.simpleMessage("Invalid request data."),
        "errorServerInternal": MessageLookupByLibrary.simpleMessage(
            "Server error. Please try again later."),
        "errorServerNotFound": MessageLookupByLibrary.simpleMessage(
            "The requested resource was not found."),
        "errorServerTooManyRequests": MessageLookupByLibrary.simpleMessage(
            "Too many requests. Please try again later."),
        "errorServerUnknown": MessageLookupByLibrary.simpleMessage(
            "An error occurred. Please try again."),
        "errorValidationInvalidEmail": MessageLookupByLibrary.simpleMessage(
            "Please enter a valid email address."),
        "errorValidationInvalidFormat":
            MessageLookupByLibrary.simpleMessage("Invalid format."),
        "errorValidationInvalidPassword": MessageLookupByLibrary.simpleMessage(
            "Please enter a valid password."),
        "errorValidationRequiredField":
            MessageLookupByLibrary.simpleMessage("This field is required."),
        "errorValidationServer":
            MessageLookupByLibrary.simpleMessage("Validation failed."),
        "errorValidationTooLong":
            MessageLookupByLibrary.simpleMessage("Input is too long."),
        "errorValidationTooShort":
            MessageLookupByLibrary.simpleMessage("Input is too short."),
        "errorValidationUnknown":
            MessageLookupByLibrary.simpleMessage("Validation error occurred."),
        "expiry_date": MessageLookupByLibrary.simpleMessage("Expiry date"),
        "financial_info_title":
            MessageLookupByLibrary.simpleMessage("Financial Information"),
        "flexibleWorkingHours":
            MessageLookupByLibrary.simpleMessage("Flexible working hours"),
        "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
        "full_name": MessageLookupByLibrary.simpleMessage("Full name"),
        "gender": MessageLookupByLibrary.simpleMessage("Gender"),
        "goodBackground": MessageLookupByLibrary.simpleMessage(
            "Good background and character; No criminal record"),
        "has_co_borrower":
            MessageLookupByLibrary.simpleMessage("Has co-borrower"),
        "healthInsurance": MessageLookupByLibrary.simpleMessage(
            "Health insurance and accident insurance provided by Kienlongbank when meeting outstanding balance conditions"),
        "healthRequirement":
            MessageLookupByLibrary.simpleMessage("Good health, clear mind"),
        "holidayBonus": MessageLookupByLibrary.simpleMessage(
            "Holiday bonus for 30/04, National Day 02/9, ..."),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "idCard": MessageLookupByLibrary.simpleMessage("ID Card"),
        "idNumber": MessageLookupByLibrary.simpleMessage("ID Number"),
        "id_document_number":
            MessageLookupByLibrary.simpleMessage("ID document number"),
        "identity_document_info": MessageLookupByLibrary.simpleMessage(
            "Identity document information"),
        "inDevelopment":
            MessageLookupByLibrary.simpleMessage("In Development..."),
        "income_source": MessageLookupByLibrary.simpleMessage("Income source"),
        "infoFromId":
            MessageLookupByLibrary.simpleMessage("Information from ID"),
        "info_from_id_documents": MessageLookupByLibrary.simpleMessage(
            "Information from ID documents"),
        "installment_loan":
            MessageLookupByLibrary.simpleMessage("Installment loan"),
        "introduction": MessageLookupByLibrary.simpleMessage("Introduction"),
        "issue_date": MessageLookupByLibrary.simpleMessage("Issue date"),
        "issue_place": MessageLookupByLibrary.simpleMessage("Issue place"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading..."),
        "loading_branches":
            MessageLookupByLibrary.simpleMessage("Loading branches..."),
        "loading_loan_purpose":
            MessageLookupByLibrary.simpleMessage("Loading loan purposes..."),
        "loading_loan_term":
            MessageLookupByLibrary.simpleMessage("Loading loan terms..."),
        "loading_payment_accounts":
            MessageLookupByLibrary.simpleMessage("Loading payment accounts..."),
        "loanStep10Title":
            MessageLookupByLibrary.simpleMessage("Confirm Loan Information"),
        "loanStep11Title":
            MessageLookupByLibrary.simpleMessage("Loan Creation Successful"),
        "loanStep1Title":
            MessageLookupByLibrary.simpleMessage("Provide Identity Documents"),
        "loanStep2Title": MessageLookupByLibrary.simpleMessage(
            "Confirm Primary Borrower Information"),
        "loanStep3Title": MessageLookupByLibrary.simpleMessage(
            "Provide Identity Documents (Co-borrower)"),
        "loanStep4Title": MessageLookupByLibrary.simpleMessage(
            "Confirm Co-borrower Information"),
        "loanStep5Title": MessageLookupByLibrary.simpleMessage(
            "Provide Loan Request Information"),
        "loanStep6Title": MessageLookupByLibrary.simpleMessage(
            "Provide Financial Information"),
        "loanStep7Title": MessageLookupByLibrary.simpleMessage(
            "Provide Collateral Information"),
        "loanStep8Title": MessageLookupByLibrary.simpleMessage(
            "Detailed Collateral Information"),
        "loanStep9Title":
            MessageLookupByLibrary.simpleMessage("Provide Document List"),
        "loan_amount":
            MessageLookupByLibrary.simpleMessage("Requested loan amount"),
        "loan_method": MessageLookupByLibrary.simpleMessage("Loan method"),
        "loan_plan": MessageLookupByLibrary.simpleMessage("Loan plan"),
        "loan_purpose": MessageLookupByLibrary.simpleMessage("Loan purpose"),
        "loan_term": MessageLookupByLibrary.simpleMessage("Loan term"),
        "loan_type": MessageLookupByLibrary.simpleMessage("Loan type"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "logout": MessageLookupByLibrary.simpleMessage("Logout"),
        "logout_cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "logout_confirm": MessageLookupByLibrary.simpleMessage("Logout"),
        "logout_error": MessageLookupByLibrary.simpleMessage(
            "An error occurred while logging out"),
        "logout_message": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to logout?"),
        "logout_title": MessageLookupByLibrary.simpleMessage("Logout"),
        "marital_status":
            MessageLookupByLibrary.simpleMessage("Marital status"),
        "monthlyServiceFee":
            MessageLookupByLibrary.simpleMessage("Monthly service fee"),
        "name_seller": MessageLookupByLibrary.simpleMessage("Referrer Name"),
        "networkCheck": MessageLookupByLibrary.simpleMessage("Check"),
        "networkChecking":
            MessageLookupByLibrary.simpleMessage("Checking connection..."),
        "networkCheckingConnection":
            MessageLookupByLibrary.simpleMessage("Checking connection..."),
        "networkCheckingConnectionDescription":
            MessageLookupByLibrary.simpleMessage(
                "Checking connection status..."),
        "networkConnected": MessageLookupByLibrary.simpleMessage("Connected"),
        "networkContinue": MessageLookupByLibrary.simpleMessage("Continue"),
        "networkDisconnected":
            MessageLookupByLibrary.simpleMessage("No internet connection"),
        "networkDisconnectedDescription": MessageLookupByLibrary.simpleMessage(
            "Please check your internet connection and try again"),
        "networkReconnectedSuccess":
            MessageLookupByLibrary.simpleMessage("Successfully reconnected"),
        "networkRetry": MessageLookupByLibrary.simpleMessage("Retry"),
        "networkRetryChecking":
            MessageLookupByLibrary.simpleMessage("Checking..."),
        "networkUnstable":
            MessageLookupByLibrary.simpleMessage("Unstable connection"),
        "networkUnstableConnectionDescription":
            MessageLookupByLibrary.simpleMessage(
                "Network connection is unstable. Some features may be affected"),
        "networkUnstableDescription": MessageLookupByLibrary.simpleMessage(
            "Network is unstable - Some features may be affected"),
        "networkUnstableWarning":
            MessageLookupByLibrary.simpleMessage("Unstable connection"),
        "newCTV": MessageLookupByLibrary.simpleMessage("New CTV"),
        "noData": MessageLookupByLibrary.simpleMessage("No data available"),
        "noOtherBank": MessageLookupByLibrary.simpleMessage(
            "Not working for other credit institutions or finance companies"),
        "no_branches_available": MessageLookupByLibrary.simpleMessage(
            "No branches available for selected province"),
        "no_branches_for_province": MessageLookupByLibrary.simpleMessage(
            "No branches available for selected province"),
        "no_loan_purpose_data": MessageLookupByLibrary.simpleMessage(
            "No loan purpose data available"),
        "no_loan_term_data":
            MessageLookupByLibrary.simpleMessage("No loan term data available"),
        "no_payment_account_data": MessageLookupByLibrary.simpleMessage(
            "No payment accounts found for this ID number."),
        "no_province_data":
            MessageLookupByLibrary.simpleMessage("No province data available"),
        "no_provinces_available":
            MessageLookupByLibrary.simpleMessage("No provinces available"),
        "no_ward_data": MessageLookupByLibrary.simpleMessage(
            "No wards available for the selected province/district."),
        "notesWhenTakingDocuments": MessageLookupByLibrary.simpleMessage(
            "Notes when taking documents:"),
        "own_capital": MessageLookupByLibrary.simpleMessage("Own capital"),
        "passport": MessageLookupByLibrary.simpleMessage("Passport"),
        "passportGuidanceSubtitle": MessageLookupByLibrary.simpleMessage(
            "Please make sure the photo is clear, not blurry or obscured"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "permanent_address":
            MessageLookupByLibrary.simpleMessage("Permanent address"),
        "personalInfoConfirmation": MessageLookupByLibrary.simpleMessage(
            "Personal Information Confirmation"),
        "personal_info":
            MessageLookupByLibrary.simpleMessage("Personal information"),
        "personal_info_confirmation_header":
            MessageLookupByLibrary.simpleMessage("Confirm Your Information"),
        "personal_info_confirmation_subtitle": MessageLookupByLibrary.simpleMessage(
            "Please review and confirm your personal information before proceeding"),
        "personal_info_confirmation_title":
            MessageLookupByLibrary.simpleMessage(
                "Personal Information Confirmation"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone Number"),
        "phone_number": MessageLookupByLibrary.simpleMessage("Phone number"),
        "placeDocumentInFrame": MessageLookupByLibrary.simpleMessage(
            "Please place the document in the frame for verification"),
        "pleaseCheckPhoto":
            MessageLookupByLibrary.simpleMessage("Please check the photo"),
        "position": MessageLookupByLibrary.simpleMessage("Position"),
        "previewPassportGuidanceTitle":
            MessageLookupByLibrary.simpleMessage("Check passport photo"),
        "profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "province": MessageLookupByLibrary.simpleMessage("Province/City"),
        "province_city": MessageLookupByLibrary.simpleMessage("Province/City"),
        "province_selection_label":
            MessageLookupByLibrary.simpleMessage("Select Province/City"),
        "qrScanTitle": MessageLookupByLibrary.simpleMessage("Scan QR code"),
        "qr_scan_invalid_data": MessageLookupByLibrary.simpleMessage(
            "Cannot read information from QR code. Please try again or select an image from the gallery."),
        "recipient_account_number":
            MessageLookupByLibrary.simpleMessage("Recipient account number"),
        "referralCode":
            MessageLookupByLibrary.simpleMessage("Referral Code (if any)"),
        "referral_code": MessageLookupByLibrary.simpleMessage("Referral code"),
        "referral_code_if_any":
            MessageLookupByLibrary.simpleMessage("Referral code (if any)"),
        "referred_person_email": MessageLookupByLibrary.simpleMessage(
            "Referred person email (optional)"),
        "referrer": MessageLookupByLibrary.simpleMessage("Referrer"),
        "register": MessageLookupByLibrary.simpleMessage("Register"),
        "register_account":
            MessageLookupByLibrary.simpleMessage("Register account"),
        "registrationSuccess":
            MessageLookupByLibrary.simpleMessage("Registration Successful"),
        "registration_branch":
            MessageLookupByLibrary.simpleMessage("Registration Branch"),
        "repayment_method":
            MessageLookupByLibrary.simpleMessage("Repayment method"),
        "requirements": MessageLookupByLibrary.simpleMessage("Requirements:"),
        "residentialAddress":
            MessageLookupByLibrary.simpleMessage("Residential Address"),
        "retake": MessageLookupByLibrary.simpleMessage("Retake"),
        "retry": MessageLookupByLibrary.simpleMessage("Retry"),
        "same_as_permanent_address": MessageLookupByLibrary.simpleMessage(
            "Current address is the same as permanent address"),
        "searching_referrer":
            MessageLookupByLibrary.simpleMessage("Searching for referrer..."),
        "selectBranch": MessageLookupByLibrary.simpleMessage("Select branch"),
        "selectProvince":
            MessageLookupByLibrary.simpleMessage("Select province/city"),
        "select_branch_hint":
            MessageLookupByLibrary.simpleMessage("Choose your branch"),
        "select_province_city":
            MessageLookupByLibrary.simpleMessage("Select province/city"),
        "select_province_hint": MessageLookupByLibrary.simpleMessage(
            "Choose your province or city"),
        "session_expired_button":
            MessageLookupByLibrary.simpleMessage("Login Again"),
        "session_expired_message": MessageLookupByLibrary.simpleMessage(
            "Your session has expired. Please login again to continue using the app."),
        "session_expired_title":
            MessageLookupByLibrary.simpleMessage("Session Expired"),
        "skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "specific_address":
            MessageLookupByLibrary.simpleMessage("Specific address"),
        "step": MessageLookupByLibrary.simpleMessage("Bước"),
        "step1_identity_document_description": MessageLookupByLibrary.simpleMessage(
            "Please select the type of identity document of the borrower to verify information"),
        "step3_identity_document_description": MessageLookupByLibrary.simpleMessage(
            "Please select the type of identity document for the borrower to verify information"),
        "stepsToBecomeCTV":
            MessageLookupByLibrary.simpleMessage("Steps to become a CTV"),
        "submitting_delete":
            MessageLookupByLibrary.simpleMessage("Deleting..."),
        "submitting_form":
            MessageLookupByLibrary.simpleMessage("Submitting form..."),
        "submitting_login":
            MessageLookupByLibrary.simpleMessage("Logging in..."),
        "submitting_register":
            MessageLookupByLibrary.simpleMessage("Registering..."),
        "submitting_save": MessageLookupByLibrary.simpleMessage("Saving..."),
        "submitting_update":
            MessageLookupByLibrary.simpleMessage("Updating..."),
        "success_ctv_message":
            MessageLookupByLibrary.simpleMessage("CTV registration successful"),
        "suitableFor": MessageLookupByLibrary.simpleMessage(
            "Suitable for individuals with sudden, short-term spending needs; small businesses and individual businesses; customers with stable income and good credit history."),
        "total_need": MessageLookupByLibrary.simpleMessage("Total need"),
        "transfer": MessageLookupByLibrary.simpleMessage("Transfer"),
        "try_again": MessageLookupByLibrary.simpleMessage("Try again"),
        "unlimitedIncome":
            MessageLookupByLibrary.simpleMessage("Unlimited income"),
        "validationAddressCharacters": MessageLookupByLibrary.simpleMessage(
            "Address must not contain special characters"),
        "validationAddressMaxLength": MessageLookupByLibrary.simpleMessage(
            "Address cannot exceed 200 characters"),
        "validationAddressMinLength": MessageLookupByLibrary.simpleMessage(
            "Address must have at least 10 characters"),
        "validationAddressRequired":
            MessageLookupByLibrary.simpleMessage("Please enter address"),
        "validationAmountFormat": MessageLookupByLibrary.simpleMessage(
            "Amount can only contain digits and commas"),
        "validationAmountPositive": MessageLookupByLibrary.simpleMessage(
            "Amount must be greater than 0"),
        "validationAmountRequired":
            MessageLookupByLibrary.simpleMessage("Please enter amount"),
        "validationAmountTooLarge":
            MessageLookupByLibrary.simpleMessage("Amount is too large"),
        "validationBankAccountLength": MessageLookupByLibrary.simpleMessage(
            "Bank account number must have 8-20 digits"),
        "validationBankAccountNumeric": MessageLookupByLibrary.simpleMessage(
            "Bank account number must contain only digits"),
        "validationBankAccountRequired": MessageLookupByLibrary.simpleMessage(
            "Please enter bank account number"),
        "validationBirthDateRequired":
            MessageLookupByLibrary.simpleMessage("Please select birth date"),
        "validationConfirmPasswordMismatch":
            MessageLookupByLibrary.simpleMessage(
                "Confirm password does not match"),
        "validationConfirmPasswordRequired":
            MessageLookupByLibrary.simpleMessage(
                "Please enter confirm password"),
        "validationDateOfBirthAge": MessageLookupByLibrary.simpleMessage(
            "You must be at least 18 years old"),
        "validationDateOfBirthInvalid":
            MessageLookupByLibrary.simpleMessage("Invalid date of birth"),
        "validationDateOfBirthRequired":
            MessageLookupByLibrary.simpleMessage("Please select date of birth"),
        "validationEmailInvalid":
            MessageLookupByLibrary.simpleMessage("Invalid email format"),
        "validationEmailRequired":
            MessageLookupByLibrary.simpleMessage("Please enter email"),
        "validationExpiryDateRequired":
            MessageLookupByLibrary.simpleMessage("Please select expiry date"),
        "validationFullNameCharacters": MessageLookupByLibrary.simpleMessage(
            "Full name can only contain letters and spaces"),
        "validationFullNameMaxLength": MessageLookupByLibrary.simpleMessage(
            "Full name cannot exceed 100 characters"),
        "validationFullNameMinLength": MessageLookupByLibrary.simpleMessage(
            "Full name must have at least 2 characters"),
        "validationFullNameRequired":
            MessageLookupByLibrary.simpleMessage("Please enter full name"),
        "validationIdNumberLength": MessageLookupByLibrary.simpleMessage(
            "ID number must have 12 digits"),
        "validationIdNumberNumeric": MessageLookupByLibrary.simpleMessage(
            "ID number must contain only digits"),
        "validationIdNumberRequired":
            MessageLookupByLibrary.simpleMessage("Please enter ID number"),
        "validationIssueDateRequired":
            MessageLookupByLibrary.simpleMessage("Please select issue date"),
        "validationIssuePlaceMaxLength": MessageLookupByLibrary.simpleMessage(
            "Issue place cannot exceed 50 characters"),
        "validationIssuePlaceRequired":
            MessageLookupByLibrary.simpleMessage("Please enter issue place"),
        "validationLoanAmountRequired": MessageLookupByLibrary.simpleMessage(
            "Please enter the loan amount"),
        "validationLoanAmountTooLarge": MessageLookupByLibrary.simpleMessage(
            "Loan amount cannot exceed 1,000,000,000"),
        "validationLoanAmountTooSmall": MessageLookupByLibrary.simpleMessage(
            "Loan amount must be at least 1,000,000"),
        "validationMaxLength": m0,
        "validationMinLength": m1,
        "validationNumericOnly": m2,
        "validationOtpLength": m3,
        "validationOtpNumeric": MessageLookupByLibrary.simpleMessage(
            "OTP code must contain only digits"),
        "validationOtpRequired":
            MessageLookupByLibrary.simpleMessage("Please enter OTP code"),
        "validationOwnCapitalInvalid": MessageLookupByLibrary.simpleMessage(
            "Own capital must be a valid number"),
        "validationOwnCapitalRequired": MessageLookupByLibrary.simpleMessage(
            "Please enter your own capital"),
        "validationOwnCapitalTooLarge": MessageLookupByLibrary.simpleMessage(
            "Own capital cannot exceed 1,000,000,000"),
        "validationPassportNumberAlphanumeric":
            MessageLookupByLibrary.simpleMessage(
                "Passport number can only contain letters and numbers (no special characters)"),
        "validationPassportNumberCharacters":
            MessageLookupByLibrary.simpleMessage(
                "Passport number can only contain letters and numbers"),
        "validationPassportNumberMaxLength":
            MessageLookupByLibrary.simpleMessage(
                "Passport number cannot exceed 50 characters"),
        "validationPassportNumberRequired":
            MessageLookupByLibrary.simpleMessage(
                "Please enter passport number"),
        "validationPasswordMaxLength": MessageLookupByLibrary.simpleMessage(
            "Password cannot exceed 50 characters"),
        "validationPasswordMinLength": MessageLookupByLibrary.simpleMessage(
            "Password must have at least 8 characters"),
        "validationPasswordRequired":
            MessageLookupByLibrary.simpleMessage("Please enter password"),
        "validationPasswordStrength": MessageLookupByLibrary.simpleMessage(
            "Password must contain uppercase, lowercase and numbers"),
        "validationPhoneInvalid":
            MessageLookupByLibrary.simpleMessage("Invalid phone number"),
        "validationPhoneLength": MessageLookupByLibrary.simpleMessage(
            "Phone number must have 10 digits"),
        "validationPhoneRequired":
            MessageLookupByLibrary.simpleMessage("Please enter phone number"),
        "validationPhoneStartWithZero": MessageLookupByLibrary.simpleMessage(
            "Phone number must start with 0"),
        "validationReferrerCodeCharacters":
            MessageLookupByLibrary.simpleMessage(
                "Referrer code can only contain letters, numbers and hyphens"),
        "validationReferrerCodeMaxLength": MessageLookupByLibrary.simpleMessage(
            "Referrer code cannot exceed 20 characters"),
        "validationReferrerCodeMinLength": MessageLookupByLibrary.simpleMessage(
            "Referrer code must have at least 3 characters"),
        "validationRequired": m4,
        "validationTaxCodeLength": MessageLookupByLibrary.simpleMessage(
            "Tax code must have 10 or 13 digits"),
        "validationTaxCodeNumeric": MessageLookupByLibrary.simpleMessage(
            "Tax code must contain only digits"),
        "validationTaxCodeRequired":
            MessageLookupByLibrary.simpleMessage("Please enter tax code"),
        "validationUrlInvalid":
            MessageLookupByLibrary.simpleMessage("Invalid URL format"),
        "validationUrlRequired":
            MessageLookupByLibrary.simpleMessage("Please enter URL"),
        "vietnameseCitizen": MessageLookupByLibrary.simpleMessage(
            "Vietnamese citizen with legal capacity, civil act capacity and civil liability according to law"),
        "ward": MessageLookupByLibrary.simpleMessage("Ward"),
        "workUniform":
            MessageLookupByLibrary.simpleMessage("Work uniform provided")
      };
}
