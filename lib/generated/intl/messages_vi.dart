// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  static String m0(fieldName, maxLength) =>
      "${fieldName} không được quá ${maxLength} ký tự";

  static String m1(fieldName, minLength) =>
      "${fieldName} phải có ít nhất ${minLength} ký tự";

  static String m2(fieldName) => "${fieldName} chỉ được chứa chữ số";

  static String m3(length) => "Mã OTP phải có ${length} chữ số";

  static String m4(fieldName) => "Vui lòng nhập ${fieldName}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "account": MessageLookupByLibrary.simpleMessage("Tài khoản"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("Thông tin bổ sung"),
        "additional_information":
            MessageLookupByLibrary.simpleMessage("Thông tin bổ sung"),
        "ageRequirement": MessageLookupByLibrary.simpleMessage(
            "Độ tuổi từ 18 đến 60, tốt nghiệp THCS trở lên"),
        "agree": MessageLookupByLibrary.simpleMessage("Đồng ý"),
        "appName":
            MessageLookupByLibrary.simpleMessage("Ứng dụng KienlongBank Sales"),
        "areaKnowledge": MessageLookupByLibrary.simpleMessage(
            "Hiểu biết về khu vực hoạt động"),
        "assetRequirement": MessageLookupByLibrary.simpleMessage(
            "Có tài sản đảm bảo tối thiểu 150 triệu đồng và cam kết tuân thủ quy định về dư nợ bình quân tối thiểu của Kienlongbank"),
        "avoidUsingImages":
            MessageLookupByLibrary.simpleMessage("Tránh sử dụng hình ảnh như:"),
        "bankWillContact": MessageLookupByLibrary.simpleMessage(
            "KienlongBank sẽ liên hệ với bạn trong thời gian sớm nhất để thông báo kết quả!"),
        "benefitsAsCTV":
            MessageLookupByLibrary.simpleMessage("Quyền lợi khi làm CTV"),
        "birth_date": MessageLookupByLibrary.simpleMessage("Ngày sinh"),
        "branch": MessageLookupByLibrary.simpleMessage("Chi nhánh"),
        "branchAddress":
            MessageLookupByLibrary.simpleMessage("Địa chỉ chi nhánh"),
        "branch_code": MessageLookupByLibrary.simpleMessage("CN/PGD"),
        "branch_selection_label":
            MessageLookupByLibrary.simpleMessage("Chọn Chi nhánh"),
        "business_location_title": MessageLookupByLibrary.simpleMessage(
            "Địa điểm sản xuất - kinh doanh"),
        "business_unit": MessageLookupByLibrary.simpleMessage("ĐVKD"),
        "business_unit_registration":
            MessageLookupByLibrary.simpleMessage("ĐVKD đăng ký"),
        "camera_init_error":
            MessageLookupByLibrary.simpleMessage("Không thể khởi tạo camera"),
        "camera_not_found": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy camera trên thiết bị"),
        "camera_permission_error":
            MessageLookupByLibrary.simpleMessage("Lỗi quyền truy cập camera"),
        "cannot_decode_image":
            MessageLookupByLibrary.simpleMessage("Không thể giải mã ảnh"),
        "captureBackSide": MessageLookupByLibrary.simpleMessage("Chụp mặt sau"),
        "captureFrontSide":
            MessageLookupByLibrary.simpleMessage("Chụp mặt trước"),
        "capturePassportGuidanceTitle":
            MessageLookupByLibrary.simpleMessage("Chụp giấy tờ"),
        "cash": MessageLookupByLibrary.simpleMessage("Tiền mặt"),
        "checkBackSide":
            MessageLookupByLibrary.simpleMessage("Kiểm tra mặt sau GTTT"),
        "checkFrontSide":
            MessageLookupByLibrary.simpleMessage("Kiểm tra mặt trước GTTT"),
        "clickToCapture": MessageLookupByLibrary.simpleMessage("Nhấn để chụp"),
        "close": MessageLookupByLibrary.simpleMessage("Đóng"),
        "code_seller":
            MessageLookupByLibrary.simpleMessage("Mã người giới thiệu"),
        "collaborator": MessageLookupByLibrary.simpleMessage("Cộng tác viên"),
        "collateral_no": MessageLookupByLibrary.simpleMessage("Không có TSĐB"),
        "collateral_yes": MessageLookupByLibrary.simpleMessage("Có TSĐB"),
        "confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
        "continueText": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "continue_button": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "createNewLoan":
            MessageLookupByLibrary.simpleMessage("Tạo khoản vay mới"),
        "ctv_referral_title":
            MessageLookupByLibrary.simpleMessage("Giới thiệu CTV"),
        "custom_purpose_name":
            MessageLookupByLibrary.simpleMessage("Tên mục đích"),
        "cvt_policy_title":
            MessageLookupByLibrary.simpleMessage("Chính sách CTV"),
        "dailyInstallmentLoan":
            MessageLookupByLibrary.simpleMessage("Vay trả góp ngày"),
        "dailyInstallmentLoanDesc": MessageLookupByLibrary.simpleMessage(
            "Vay trả góp ngày là sản phẩm cho vay dưới hình thức trả góp theo ngày (gốc, lãi) để phục vụ nhu cầu sinh hoạt và hoạt động kinh doanh, các hoạt động khác."),
        "daily_income":
            MessageLookupByLibrary.simpleMessage("Thu nhập bình quân/ngày"),
        "daily_repayment": MessageLookupByLibrary.simpleMessage(
            "Trả góp nợ gốc và lãi tiền vay hàng ngày"),
        "daily_revenue":
            MessageLookupByLibrary.simpleMessage("Doanh số bán bình quân/ngày"),
        "disbursement_method":
            MessageLookupByLibrary.simpleMessage("Phương thức giải ngân"),
        "district": MessageLookupByLibrary.simpleMessage("Quận/Huyện"),
        "documentVerificationGuide":
            MessageLookupByLibrary.simpleMessage("Hướng dẫn xác minh giấy tờ"),
        "document_back_side_label":
            MessageLookupByLibrary.simpleMessage("Mặt sau giấy tờ"),
        "document_front_side_label":
            MessageLookupByLibrary.simpleMessage("Mặt trước giấy tờ"),
        "document_info_label":
            MessageLookupByLibrary.simpleMessage("Thông tin giấy tờ"),
        "document_number": MessageLookupByLibrary.simpleMessage("Số giấy tờ"),
        "document_type_label":
            MessageLookupByLibrary.simpleMessage("Loại giấy tờ"),
        "dvkd_address": MessageLookupByLibrary.simpleMessage("Địa chỉ ĐVKD"),
        "email_optional":
            MessageLookupByLibrary.simpleMessage("Email (nếu có)"),
        "enterPhoneNumber":
            MessageLookupByLibrary.simpleMessage("Nhập số điện thoại"),
        "enterReferralCode":
            MessageLookupByLibrary.simpleMessage("Nhập mã giới thiệu"),
        "enter_referral_code":
            MessageLookupByLibrary.simpleMessage("Nhập mã giới thiệu"),
        "error": MessageLookupByLibrary.simpleMessage("Lỗi"),
        "errorAuthAccessDenied": MessageLookupByLibrary.simpleMessage(
            "Truy cập bị từ chối. Bạn không có quyền thực hiện hành động này."),
        "errorAuthAccountLocked": MessageLookupByLibrary.simpleMessage(
            "Tài khoản đã bị khóa hoặc vô hiệu hóa."),
        "errorAuthInvalidCredentials": MessageLookupByLibrary.simpleMessage(
            "Xác thực thất bại. Vui lòng đăng nhập lại."),
        "errorAuthTokenExpired": MessageLookupByLibrary.simpleMessage(
            "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại."),
        "errorAuthUnknown":
            MessageLookupByLibrary.simpleMessage("Đã xảy ra lỗi xác thực."),
        "errorAuthUserNotFound":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy người dùng."),
        "errorCacheDataCorrupted":
            MessageLookupByLibrary.simpleMessage("Dữ liệu đã lưu bị hỏng."),
        "errorCacheRead": MessageLookupByLibrary.simpleMessage(
            "Không thể đọc dữ liệu đã lưu."),
        "errorCacheStorageFull":
            MessageLookupByLibrary.simpleMessage("Bộ nhớ đã đầy."),
        "errorCacheUnknown":
            MessageLookupByLibrary.simpleMessage("Đã xảy ra lỗi bộ nhớ đệm."),
        "errorCacheWrite": MessageLookupByLibrary.simpleMessage(
            "Không thể lưu dữ liệu vào bộ nhớ đệm."),
        "errorNetworkCertificate": MessageLookupByLibrary.simpleMessage(
            "Xác minh chứng chỉ thất bại."),
        "errorNetworkConnectionTimeout": MessageLookupByLibrary.simpleMessage(
            "Hết thời gian kết nối. Vui lòng kiểm tra kết nối internet."),
        "errorNetworkNoConnection": MessageLookupByLibrary.simpleMessage(
            "Không có kết nối internet. Vui lòng kiểm tra cài đặt mạng."),
        "errorNetworkReceiveTimeout": MessageLookupByLibrary.simpleMessage(
            "Hết thời gian nhận phản hồi. Vui lòng thử lại."),
        "errorNetworkRequestCancelled":
            MessageLookupByLibrary.simpleMessage("Yêu cầu đã bị hủy."),
        "errorNetworkSendTimeout": MessageLookupByLibrary.simpleMessage(
            "Hết thời gian gửi yêu cầu. Vui lòng thử lại."),
        "errorNetworkUnknown":
            MessageLookupByLibrary.simpleMessage("Lỗi mạng. Vui lòng thử lại."),
        "errorServerBadRequest": MessageLookupByLibrary.simpleMessage(
            "Dữ liệu yêu cầu không hợp lệ."),
        "errorServerInternal": MessageLookupByLibrary.simpleMessage(
            "Lỗi máy chủ. Vui lòng thử lại sau."),
        "errorServerNotFound": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy tài nguyên được yêu cầu."),
        "errorServerTooManyRequests": MessageLookupByLibrary.simpleMessage(
            "Quá nhiều yêu cầu. Vui lòng thử lại sau."),
        "errorServerUnknown": MessageLookupByLibrary.simpleMessage(
            "Đã xảy ra lỗi. Vui lòng thử lại."),
        "errorValidationInvalidEmail": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập địa chỉ email hợp lệ."),
        "errorValidationInvalidFormat":
            MessageLookupByLibrary.simpleMessage("Định dạng không hợp lệ."),
        "errorValidationInvalidPassword": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập mật khẩu hợp lệ."),
        "errorValidationRequiredField":
            MessageLookupByLibrary.simpleMessage("Trường này là bắt buộc."),
        "errorValidationServer":
            MessageLookupByLibrary.simpleMessage("Xác thực thất bại."),
        "errorValidationTooLong":
            MessageLookupByLibrary.simpleMessage("Dữ liệu nhập quá dài."),
        "errorValidationTooShort":
            MessageLookupByLibrary.simpleMessage("Dữ liệu nhập quá ngắn."),
        "errorValidationUnknown":
            MessageLookupByLibrary.simpleMessage("Đã xảy ra lỗi xác thực."),
        "expiry_date": MessageLookupByLibrary.simpleMessage("Ngày hết hạn"),
        "financial_info_title":
            MessageLookupByLibrary.simpleMessage("Tình hình tài chính"),
        "flexibleWorkingHours": MessageLookupByLibrary.simpleMessage(
            "Thời gian làm việc linh hoạt"),
        "fullName": MessageLookupByLibrary.simpleMessage("Họ và tên"),
        "full_name": MessageLookupByLibrary.simpleMessage("Họ và tên"),
        "gender": MessageLookupByLibrary.simpleMessage("Giới tính"),
        "goodBackground": MessageLookupByLibrary.simpleMessage(
            "Có lý lịch rõ ràng, phẩm chất đạo đức tốt; Không có tiền án, tiền sự"),
        "has_co_borrower":
            MessageLookupByLibrary.simpleMessage("Có người đồng vay"),
        "healthInsurance": MessageLookupByLibrary.simpleMessage(
            "Được Kienlongbank cấp bảo hiểm sức khỏe và bảo hiểm tai nạn khi đạt điều kiện dư nợ"),
        "healthRequirement":
            MessageLookupByLibrary.simpleMessage("Có sức khỏe tốt, minh mẫn"),
        "holidayBonus": MessageLookupByLibrary.simpleMessage(
            "Thưởng lễ 30/04, Quốc khánh 02/9, ..."),
        "home": MessageLookupByLibrary.simpleMessage("Trang chủ"),
        "idCard": MessageLookupByLibrary.simpleMessage("CMND/CCCD"),
        "idNumber": MessageLookupByLibrary.simpleMessage("Số CMND"),
        "id_document_number":
            MessageLookupByLibrary.simpleMessage("Số giấy tờ tuỳ thân"),
        "identity_document_info":
            MessageLookupByLibrary.simpleMessage("Thông tin nhận dạng giấy tờ"),
        "inDevelopment":
            MessageLookupByLibrary.simpleMessage("Đang phát triển..."),
        "income_source": MessageLookupByLibrary.simpleMessage("Nguồn thu"),
        "infoFromId": MessageLookupByLibrary.simpleMessage("Thông tin từ GTTT"),
        "info_from_id_documents": MessageLookupByLibrary.simpleMessage(
            "Thông tin từ giấy tờ tùy thân"),
        "installment_loan": MessageLookupByLibrary.simpleMessage("Vay trả góp"),
        "introduction": MessageLookupByLibrary.simpleMessage("Giới thiệu"),
        "issue_date": MessageLookupByLibrary.simpleMessage("Ngày cấp"),
        "issue_place": MessageLookupByLibrary.simpleMessage("Nơi cấp"),
        "loading": MessageLookupByLibrary.simpleMessage("Đang tải..."),
        "loading_branches":
            MessageLookupByLibrary.simpleMessage("Đang tải chi nhánh..."),
        "loading_loan_purpose": MessageLookupByLibrary.simpleMessage(
            "Đang tải mục đích sử dụng vốn..."),
        "loading_loan_term":
            MessageLookupByLibrary.simpleMessage("Đang tải thời hạn vay..."),
        "loading_payment_accounts": MessageLookupByLibrary.simpleMessage(
            "Đang tải danh sách tài khoản..."),
        "loanStep10Title": MessageLookupByLibrary.simpleMessage(
            "Xác nhận thông tin khoản vay"),
        "loanStep11Title": MessageLookupByLibrary.simpleMessage(
            "Khởi tạo khoản vay thành công"),
        "loanStep1Title":
            MessageLookupByLibrary.simpleMessage("Cung cấp giấy tờ tùy thân"),
        "loanStep2Title": MessageLookupByLibrary.simpleMessage(
            "Xác nhận thông tin người vay chính"),
        "loanStep3Title": MessageLookupByLibrary.simpleMessage(
            "Cung cấp giấy tờ tùy thân (người đồng vay)"),
        "loanStep4Title": MessageLookupByLibrary.simpleMessage(
            "Xác nhận thông tin người đồng vay"),
        "loanStep5Title": MessageLookupByLibrary.simpleMessage(
            "Cung cấp đề nghị và phương án vay vốn"),
        "loanStep6Title": MessageLookupByLibrary.simpleMessage(
            "Cung cấp thông tin tài chính"),
        "loanStep7Title": MessageLookupByLibrary.simpleMessage(
            "Cung cấp thông tin tài sản bảo đảm"),
        "loanStep8Title": MessageLookupByLibrary.simpleMessage(
            "Chi tiết thông tin tài sản bảo đảm"),
        "loanStep9Title":
            MessageLookupByLibrary.simpleMessage("Cung cấp danh mục chứng từ"),
        "loan_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền đề nghị vay"),
        "loan_method": MessageLookupByLibrary.simpleMessage("Phương thức vay"),
        "loan_plan": MessageLookupByLibrary.simpleMessage("Phương án vay vốn"),
        "loan_purpose":
            MessageLookupByLibrary.simpleMessage("Mục đích sử dụng vốn"),
        "loan_term": MessageLookupByLibrary.simpleMessage("Thời hạn vay"),
        "loan_type": MessageLookupByLibrary.simpleMessage("Hình thức vay vốn"),
        "login": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
        "logout": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
        "logout_cancel": MessageLookupByLibrary.simpleMessage("Hủy"),
        "logout_confirm": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
        "logout_error":
            MessageLookupByLibrary.simpleMessage("Đã xảy ra lỗi khi đăng xuất"),
        "logout_message": MessageLookupByLibrary.simpleMessage(
            "Bạn có chắc chắn muốn đăng xuất?"),
        "logout_title": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
        "marital_status":
            MessageLookupByLibrary.simpleMessage("Tình trạng hôn nhân"),
        "monthlyServiceFee":
            MessageLookupByLibrary.simpleMessage("Phí dịch vụ hàng tháng"),
        "name_seller":
            MessageLookupByLibrary.simpleMessage("Tên người giới thiệu"),
        "networkCheck": MessageLookupByLibrary.simpleMessage("Kiểm tra"),
        "networkChecking":
            MessageLookupByLibrary.simpleMessage("Đang kiểm tra kết nối..."),
        "networkCheckingConnection":
            MessageLookupByLibrary.simpleMessage("Đang kiểm tra kết nối..."),
        "networkCheckingConnectionDescription":
            MessageLookupByLibrary.simpleMessage(
                "Đang kiểm tra trạng thái kết nối..."),
        "networkConnected": MessageLookupByLibrary.simpleMessage("Đã kết nối"),
        "networkContinue": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "networkDisconnected":
            MessageLookupByLibrary.simpleMessage("Mất kết nối mạng"),
        "networkDisconnectedDescription": MessageLookupByLibrary.simpleMessage(
            "Vui lòng kiểm tra kết nối internet và thử lại"),
        "networkReconnectedSuccess":
            MessageLookupByLibrary.simpleMessage("Đã kết nối lại thành công"),
        "networkRetry": MessageLookupByLibrary.simpleMessage("Thử lại"),
        "networkRetryChecking":
            MessageLookupByLibrary.simpleMessage("Đang kiểm tra..."),
        "networkUnstable":
            MessageLookupByLibrary.simpleMessage("Kết nối không ổn định"),
        "networkUnstableConnectionDescription":
            MessageLookupByLibrary.simpleMessage(
                "Kết nối mạng không ổn định. Một số tính năng có thể bị ảnh hưởng"),
        "networkUnstableDescription": MessageLookupByLibrary.simpleMessage(
            "Mạng không ổn định - Một số tính năng có thể bị ảnh hưởng"),
        "networkUnstableWarning":
            MessageLookupByLibrary.simpleMessage("Kết nối không ổn định"),
        "newCTV": MessageLookupByLibrary.simpleMessage("CTV mới"),
        "noData": MessageLookupByLibrary.simpleMessage("Không có dữ liệu"),
        "noOtherBank": MessageLookupByLibrary.simpleMessage(
            "Không làm việc cho các tổ chức tín dụng hoặc công ty tài chính khác"),
        "no_branches_available": MessageLookupByLibrary.simpleMessage(
            "Không có chi nhánh nào cho tỉnh đã chọn"),
        "no_branches_for_province": MessageLookupByLibrary.simpleMessage(
            "Không có chi nhánh cho tỉnh đã chọn"),
        "no_loan_purpose_data": MessageLookupByLibrary.simpleMessage(
            "Không có dữ liệu mục đích sử dụng vốn"),
        "no_loan_term_data": MessageLookupByLibrary.simpleMessage(
            "Không có dữ liệu thời hạn vay"),
        "no_payment_account_data": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy tài khoản thanh toán nào cho số giấy tờ này."),
        "no_province_data":
            MessageLookupByLibrary.simpleMessage("Không có dữ liệu tỉnh/thành"),
        "no_provinces_available":
            MessageLookupByLibrary.simpleMessage("Không có tỉnh thành nào"),
        "no_ward_data": MessageLookupByLibrary.simpleMessage(
            "Không có phường/xã cho tỉnh đã chọn."),
        "notesWhenTakingDocuments":
            MessageLookupByLibrary.simpleMessage("Lưu ý khi chụp giấy tờ:"),
        "own_capital": MessageLookupByLibrary.simpleMessage("Vốn tự có"),
        "passport": MessageLookupByLibrary.simpleMessage("Hộ chiếu"),
        "passportGuidanceSubtitle": MessageLookupByLibrary.simpleMessage(
            "Vui lòng đảm bảo ảnh rõ nét, không bị mờ hoặc che khuất thông tin"),
        "password": MessageLookupByLibrary.simpleMessage("Mật khẩu"),
        "permanent_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ thường trú"),
        "personalInfoConfirmation":
            MessageLookupByLibrary.simpleMessage("Xác nhận thông tin cá nhân"),
        "personal_info":
            MessageLookupByLibrary.simpleMessage("Thông tin cá nhân"),
        "personal_info_confirmation_header":
            MessageLookupByLibrary.simpleMessage("Xác nhận thông tin của bạn"),
        "personal_info_confirmation_subtitle": MessageLookupByLibrary.simpleMessage(
            "Vui lòng xem lại và xác nhận thông tin cá nhân trước khi tiếp tục"),
        "personal_info_confirmation_title":
            MessageLookupByLibrary.simpleMessage("Xác nhận thông tin cá nhân"),
        "phoneNumber": MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "phone_number": MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "placeDocumentInFrame": MessageLookupByLibrary.simpleMessage(
            "Vui lòng đặt giấy tờ vào khung để xác minh"),
        "pleaseCheckPhoto": MessageLookupByLibrary.simpleMessage(
            "Vui lòng kiểm tra lại ảnh đã chụp"),
        "position": MessageLookupByLibrary.simpleMessage("Chức vụ"),
        "previewPassportGuidanceTitle":
            MessageLookupByLibrary.simpleMessage("Kiểm tra ảnh hộ chiếu"),
        "profile": MessageLookupByLibrary.simpleMessage("Cá nhân"),
        "province": MessageLookupByLibrary.simpleMessage("Tỉnh/Thành phố"),
        "province_city": MessageLookupByLibrary.simpleMessage("Tỉnh/Thành phố"),
        "province_selection_label":
            MessageLookupByLibrary.simpleMessage("Chọn Tỉnh/Thành phố"),
        "qrScanTitle": MessageLookupByLibrary.simpleMessage("Quét mã QR"),
        "qr_scan_invalid_data": MessageLookupByLibrary.simpleMessage(
            "Không thể đọc thông tin từ mã QR. Vui lòng thử lại hoặc chọn ảnh từ thư viện."),
        "recipient_account_number":
            MessageLookupByLibrary.simpleMessage("Số tài khoản nhận tiền"),
        "referralCode":
            MessageLookupByLibrary.simpleMessage("Mã giới thiệu (nếu có)"),
        "referral_code": MessageLookupByLibrary.simpleMessage("Mã giới thiệu"),
        "referral_code_if_any": MessageLookupByLibrary.simpleMessage(
            "Mã người giới thiệu (nếu có)"),
        "referred_person_email": MessageLookupByLibrary.simpleMessage(
            "Email người được giới thiệu (tùy chọn)"),
        "referrer": MessageLookupByLibrary.simpleMessage("Người giới thiệu"),
        "register": MessageLookupByLibrary.simpleMessage("Đăng ký"),
        "register_account":
            MessageLookupByLibrary.simpleMessage("Đăng ký tài khoản"),
        "registrationSuccess":
            MessageLookupByLibrary.simpleMessage("Đăng ký thành công"),
        "registration_branch":
            MessageLookupByLibrary.simpleMessage("Chi nhánh đăng ký"),
        "repayment_method":
            MessageLookupByLibrary.simpleMessage("Hình thức trả nợ"),
        "requirements": MessageLookupByLibrary.simpleMessage("Yêu cầu:"),
        "residentialAddress":
            MessageLookupByLibrary.simpleMessage("Địa chỉ thường trú"),
        "retake": MessageLookupByLibrary.simpleMessage("Chụp lại"),
        "retry": MessageLookupByLibrary.simpleMessage("Thử lại"),
        "same_as_permanent_address": MessageLookupByLibrary.simpleMessage(
            "Địa chỉ hiện tại trùng với địa chỉ thường trú"),
        "searching_referrer": MessageLookupByLibrary.simpleMessage(
            "Đang tìm kiếm người giới thiệu..."),
        "selectBranch": MessageLookupByLibrary.simpleMessage("Chọn chi nhánh"),
        "selectProvince":
            MessageLookupByLibrary.simpleMessage("Chọn tỉnh/thành phố"),
        "select_branch_hint":
            MessageLookupByLibrary.simpleMessage("Chọn chi nhánh của bạn"),
        "select_province_city":
            MessageLookupByLibrary.simpleMessage("Chọn tỉnh/thành phố"),
        "select_province_hint": MessageLookupByLibrary.simpleMessage(
            "Chọn tỉnh hoặc thành phố của bạn"),
        "session_expired_button":
            MessageLookupByLibrary.simpleMessage("Đăng nhập lại"),
        "session_expired_message": MessageLookupByLibrary.simpleMessage(
            "Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại để tiếp tục sử dụng ứng dụng."),
        "session_expired_title":
            MessageLookupByLibrary.simpleMessage("Phiên đăng nhập hết hạn"),
        "skip": MessageLookupByLibrary.simpleMessage("Bỏ qua"),
        "specific_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ cụ thể"),
        "step": MessageLookupByLibrary.simpleMessage("Bước"),
        "step1_identity_document_description": MessageLookupByLibrary.simpleMessage(
            "Vui lòng chọn loại giấy tờ tùy thân của người vay để xác thực thông tin"),
        "step3_identity_document_description": MessageLookupByLibrary.simpleMessage(
            "Vui lòng chọn loại giấy tờ tùy thân của người vay để xác thực thông tin"),
        "stepsToBecomeCTV":
            MessageLookupByLibrary.simpleMessage("Các bước trở thành CTV"),
        "submitting_delete":
            MessageLookupByLibrary.simpleMessage("Đang xóa..."),
        "submitting_form":
            MessageLookupByLibrary.simpleMessage("Đang gửi biểu mẫu..."),
        "submitting_login":
            MessageLookupByLibrary.simpleMessage("Đang đăng nhập..."),
        "submitting_register":
            MessageLookupByLibrary.simpleMessage("Đang đăng ký..."),
        "submitting_save": MessageLookupByLibrary.simpleMessage("Đang lưu..."),
        "submitting_update":
            MessageLookupByLibrary.simpleMessage("Đang cập nhật..."),
        "success_ctv_message":
            MessageLookupByLibrary.simpleMessage("Đăng ký CTV thành công"),
        "suitableFor": MessageLookupByLibrary.simpleMessage(
            "Phù hợp với cá nhân có nhu cầu chi tiêu đột xuất, ngắn hạn; hộ kinh doanh và doanh nghiệp cá thể; khách hàng có thu nhập ổn định và lịch sử tín dụng tốt."),
        "total_need": MessageLookupByLibrary.simpleMessage("Tổng nhu cầu"),
        "transfer": MessageLookupByLibrary.simpleMessage("Chuyển khoản"),
        "try_again": MessageLookupByLibrary.simpleMessage("Thử lại"),
        "unlimitedIncome":
            MessageLookupByLibrary.simpleMessage("Thu nhập không giới hạn"),
        "validationAddressCharacters": MessageLookupByLibrary.simpleMessage(
            "Địa chỉ không được chứa kí tự đặc biệt"),
        "validationAddressMaxLength": MessageLookupByLibrary.simpleMessage(
            "Địa chỉ không được quá 200 ký tự"),
        "validationAddressMinLength": MessageLookupByLibrary.simpleMessage(
            "Địa chỉ phải có ít nhất 10 ký tự"),
        "validationAddressRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập địa chỉ"),
        "validationAmountFormat": MessageLookupByLibrary.simpleMessage(
            "Số tiền chỉ được chứa chữ số và dấu phẩy"),
        "validationAmountPositive":
            MessageLookupByLibrary.simpleMessage("Số tiền phải lớn hơn 0"),
        "validationAmountRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập số tiền"),
        "validationAmountTooLarge":
            MessageLookupByLibrary.simpleMessage("Số tiền quá lớn"),
        "validationBankAccountLength": MessageLookupByLibrary.simpleMessage(
            "Số tài khoản phải có từ 8-20 chữ số"),
        "validationBankAccountNumeric": MessageLookupByLibrary.simpleMessage(
            "Số tài khoản chỉ được chứa chữ số"),
        "validationBankAccountRequired": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập số tài khoản ngân hàng"),
        "validationBirthDateRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng chọn ngày sinh"),
        "validationConfirmPasswordMismatch":
            MessageLookupByLibrary.simpleMessage(
                "Mật khẩu xác nhận không khớp"),
        "validationConfirmPasswordRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập lại mật khẩu"),
        "validationDateOfBirthAge":
            MessageLookupByLibrary.simpleMessage("Bạn phải đủ 18 tuổi"),
        "validationDateOfBirthInvalid":
            MessageLookupByLibrary.simpleMessage("Ngày sinh không hợp lệ"),
        "validationDateOfBirthRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng chọn ngày sinh"),
        "validationEmailInvalid":
            MessageLookupByLibrary.simpleMessage("Email không hợp lệ"),
        "validationEmailRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập email"),
        "validationExpiryDateRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng chọn ngày hết hạn"),
        "validationFullNameCharacters": MessageLookupByLibrary.simpleMessage(
            "Họ tên chỉ được chứa chữ cái và dấu cách"),
        "validationFullNameMaxLength": MessageLookupByLibrary.simpleMessage(
            "Họ tên không được quá 100 ký tự"),
        "validationFullNameMinLength": MessageLookupByLibrary.simpleMessage(
            "Họ tên phải có ít nhất 2 ký tự"),
        "validationFullNameRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập họ tên"),
        "validationIdNumberLength": MessageLookupByLibrary.simpleMessage(
            "Số giấy tờ tùy thân phải có 12 chữ số"),
        "validationIdNumberNumeric": MessageLookupByLibrary.simpleMessage(
            "Số giấy tờ tùy thân chỉ được chứa chữ số"),
        "validationIdNumberRequired": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập số giấy tờ tùy thân"),
        "validationIssueDateRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng chọn ngày cấp"),
        "validationIssuePlaceMaxLength": MessageLookupByLibrary.simpleMessage(
            "Nơi cấp không được quá 50 ký tự"),
        "validationIssuePlaceRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập nơi cấp"),
        "validationLoanAmountRequired": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập số tiền đề nghị vay"),
        "validationLoanAmountTooLarge": MessageLookupByLibrary.simpleMessage(
            "Số tiền đề nghị vay không được vượt quá 1.000.000.000"),
        "validationLoanAmountTooSmall": MessageLookupByLibrary.simpleMessage(
            "Số tiền đề nghị vay tối thiểu là 1.000.000"),
        "validationMaxLength": m0,
        "validationMinLength": m1,
        "validationNumericOnly": m2,
        "validationOtpLength": m3,
        "validationOtpNumeric":
            MessageLookupByLibrary.simpleMessage("Mã OTP chỉ được chứa chữ số"),
        "validationOtpRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập mã OTP"),
        "validationOwnCapitalInvalid":
            MessageLookupByLibrary.simpleMessage("Vốn tự có phải là số hợp lệ"),
        "validationOwnCapitalRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập vốn tự có"),
        "validationOwnCapitalTooLarge": MessageLookupByLibrary.simpleMessage(
            "Vốn tự có không được vượt quá 1.000.000.000"),
        "validationPassportNumberAlphanumeric":
            MessageLookupByLibrary.simpleMessage(
                "Số hộ chiếu chỉ được chứa chữ và số, không chứa ký tự đặc biệt"),
        "validationPassportNumberCharacters":
            MessageLookupByLibrary.simpleMessage(
                "Số hộ chiếu chỉ được chứa chữ và số"),
        "validationPassportNumberMaxLength":
            MessageLookupByLibrary.simpleMessage(
                "Số hộ chiếu không được quá 50 ký tự"),
        "validationPassportNumberRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập số hộ chiếu"),
        "validationPasswordMaxLength": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu không được quá 50 ký tự"),
        "validationPasswordMinLength": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu phải có ít nhất 8 ký tự"),
        "validationPasswordRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập mật khẩu"),
        "validationPasswordStrength": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu phải chứa chữ hoa, chữ thường và số"),
        "validationPhoneInvalid":
            MessageLookupByLibrary.simpleMessage("Số điện thoại không hợp lệ"),
        "validationPhoneLength": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại phải có 10 chữ số"),
        "validationPhoneRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập số điện thoại"),
        "validationPhoneStartWithZero": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại phải bắt đầu bằng số 0"),
        "validationReferrerCodeCharacters":
            MessageLookupByLibrary.simpleMessage(
                "Mã giới thiệu chỉ được chứa chữ cái, số và dấu gạch ngang"),
        "validationReferrerCodeMaxLength": MessageLookupByLibrary.simpleMessage(
            "Mã giới thiệu không được quá 20 ký tự"),
        "validationReferrerCodeMinLength": MessageLookupByLibrary.simpleMessage(
            "Mã giới thiệu phải có ít nhất 3 ký tự"),
        "validationRequired": m4,
        "validationTaxCodeLength": MessageLookupByLibrary.simpleMessage(
            "Mã số thuế phải có 10 hoặc 13 chữ số"),
        "validationTaxCodeNumeric": MessageLookupByLibrary.simpleMessage(
            "Mã số thuế chỉ được chứa chữ số"),
        "validationTaxCodeRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập mã số thuế"),
        "validationUrlInvalid":
            MessageLookupByLibrary.simpleMessage("URL không hợp lệ"),
        "validationUrlRequired":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập URL"),
        "vietnameseCitizen": MessageLookupByLibrary.simpleMessage(
            "Là công dân Việt Nam có năng lực pháp luật dân sự, năng lực hành vi dân sự và chịu trách nhiệm dân sự theo quy định của pháp luật"),
        "ward": MessageLookupByLibrary.simpleMessage("Phường/Xã"),
        "workUniform":
            MessageLookupByLibrary.simpleMessage("Được cấp đồng phục làm việc")
      };
}
